package extraction

import (
	"context"
	"fmt"
	"io"
	"strings"

	"gobackend-hvac-kratos/internal/data" // Assuming data.ExtractionMetadata is defined here

	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
)

// XLSXExtractor extracts content from XLSX files
type XLSXExtractor struct {
	log *log.Helper
}

// NewXLSXExtractor creates a new XLSXExtractor
func NewXLSXExtractor(logger log.Logger) *XLSXExtractor {
	return &XLSXExtractor{
		log: log.NewHelper(logger),
	}
}

// Extract extracts text and metadata from an XLSX file
func (e *XLSXExtractor) Extract(ctx context.Context, reader io.Reader) (string, *data.ExtractionMetadata, error) {
	e.log.WithContext(ctx).Debug("📊 Extracting XLSX content...")

	f, err := excelize.OpenReader(reader)
	if err != nil {
		return "", nil, fmt.Errorf("failed to open XLSX file: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			e.log.WithContext(ctx).Errorf("failed to close XLSX file: %v", err)
		}
	}()

	var textBuilder strings.Builder
	for _, sheetName := range f.GetSheetList() {
		rows, err := f.GetRows(sheetName)
		if err != nil {
			e.log.WithContext(ctx).Warnf("failed to get rows from sheet %s: %v", sheetName, err)
			continue
		}
		for _, row := range rows {
			for _, cell := range row {
				textBuilder.WriteString(cell)
				textBuilder.WriteString(" ") // Add space between cells
			}
			textBuilder.WriteString("\n") // Add newline after each row
		}
	}

	extractedText := textBuilder.String()

	// Basic metadata extraction
	metadata := &data.ExtractionMetadata{
		FileType:      "xlsx",
		WordCount:     len(strings.Fields(extractedText)),
		CharCount:     len(extractedText),
		ProcessorUsed: "excelize",
	}

	e.log.WithContext(ctx).Debugf("✅ XLSX extraction completed. Chars: %d", metadata.CharCount)
	return extractedText, metadata, nil
}
