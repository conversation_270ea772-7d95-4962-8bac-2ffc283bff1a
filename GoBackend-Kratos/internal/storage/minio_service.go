package storage

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/minio/minio-go/v7/pkg/ssec"
)

// 🗄️ MinIO Storage Service for Dolores Email Intelligence
// High-performance object storage with S3 compatibility

type MinIOService struct {
	log           *log.Helper
	client        *minio.Client
	config        *MinIOConfig
	defaultBucket string
}

type MinIOConfig struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyID     string `yaml:"access_key_id"`
	SecretAccessKey string `yaml:"secret_access_key"`
	UseSSL          bool   `yaml:"use_ssl"`
	Region          string `yaml:"region"`
	DefaultBucket   string `yaml:"default_bucket"`

	// Performance settings
	PartSize       int64  `yaml:"part_size"`       // Default: 64MB
	MaxRetries     int    `yaml:"max_retries"`     // Default: 3
	ConnectTimeout string `yaml:"connect_timeout"` // Default: 30s
	RequestTimeout string `yaml:"request_timeout"` // Default: 5m

	// Security settings
	EnableEncryption bool   `yaml:"enable_encryption"`
	KMSKeyID         string `yaml:"kms_key_id"`

	// Lifecycle settings
	DefaultExpiration string `yaml:"default_expiration"` // e.g., "30d", "1y"
}

type UploadOptions struct {
	ContentType    string
	Metadata       map[string]string
	Tags           map[string]string
	Encryption     bool
	ExpirationDays int
	StorageClass   string
}

type DownloadOptions struct {
	Range           *ByteRange
	IfModifiedSince *time.Time
	IfNoneMatch     string
}

type ByteRange struct {
	Start int64
	End   int64
}

type FileInfo struct {
	Key          string
	Size         int64
	ContentType  string
	LastModified time.Time
	ETag         string
	Metadata     map[string]string
	Tags         map[string]string
}

// NewMinIOService creates a new MinIO storage service
func NewMinIOService(logger log.Logger, config *MinIOConfig) (*MinIOService, error) {
	// Initialize MinIO client
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
		Region: config.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	service := &MinIOService{
		log:           log.NewHelper(logger),
		client:        client,
		config:        config,
		defaultBucket: config.DefaultBucket,
	}

	// Ensure default bucket exists
	if err := service.ensureBucket(context.Background(), config.DefaultBucket); err != nil {
		return nil, fmt.Errorf("failed to ensure default bucket: %w", err)
	}

	return service, nil
}

// UploadFile uploads a file to MinIO storage
func (ms *MinIOService) UploadFile(ctx context.Context, key string, reader io.Reader, size int64, opts *UploadOptions) (*FileInfo, error) {
	ms.log.WithContext(ctx).Infof("📤 Uploading file to MinIO: %s (%d bytes)", key, size)

	if opts == nil {
		opts = &UploadOptions{}
	}

	// Prepare upload options
	uploadOpts := minio.PutObjectOptions{
		ContentType:  opts.ContentType,
		UserMetadata: opts.Metadata,
		UserTags:     opts.Tags,
	}

	// Set part size for large files
	if size > ms.config.PartSize {
		uploadOpts.PartSize = uint64(ms.config.PartSize)
	}

	// Enable encryption if requested
	if opts.Encryption && ms.config.EnableEncryption {
		if ms.config.KMSKeyID != "" {
			uploadOpts.ServerSideEncryption = &ssec.SSEKMS{KeyID: ms.config.KMSKeyID}
		} else {
			uploadOpts.ServerSideEncryption = &ssec.SSEC{}
		}
	}

	// Set storage class
	if opts.StorageClass != "" {
		uploadOpts.StorageClass = opts.StorageClass
	}

	// Upload file
	uploadInfo, err := ms.client.PutObject(ctx, ms.defaultBucket, key, reader, size, uploadOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Set lifecycle policy if expiration is specified
	if opts.ExpirationDays > 0 {
		if err := ms.setObjectExpiration(ctx, key, opts.ExpirationDays); err != nil {
			ms.log.WithContext(ctx).Warnf("Failed to set expiration for %s: %v", key, err)
		}
	}

	ms.log.WithContext(ctx).Infof("✅ File uploaded successfully: %s (ETag: %s)", key, uploadInfo.ETag)

	// Return file info
	return &FileInfo{
		Key:          key,
		Size:         uploadInfo.Size,
		ETag:         uploadInfo.ETag,
		LastModified: time.Now(),
		ContentType:  opts.ContentType,
		Metadata:     opts.Metadata,
		Tags:         opts.Tags,
	}, nil
}

// DownloadFile downloads a file from MinIO storage
func (ms *MinIOService) DownloadFile(ctx context.Context, key string, opts *DownloadOptions) (io.ReadCloser, *FileInfo, error) {
	ms.log.WithContext(ctx).Infof("📥 Downloading file from MinIO: %s", key)

	if opts == nil {
		opts = &DownloadOptions{}
	}

	// Prepare get options
	getOpts := minio.GetObjectOptions{}

	// Set range if specified
	if opts.Range != nil {
		if err := getOpts.SetRange(opts.Range.Start, opts.Range.End); err != nil {
			return nil, nil, fmt.Errorf("invalid range: %w", err)
		}
	}

	// Set conditional headers
	if opts.IfModifiedSince != nil {
		getOpts.IfModifiedSince = *opts.IfModifiedSince
	}
	if opts.IfNoneMatch != "" {
		getOpts.IfNoneMatch = opts.IfNoneMatch
	}

	// Get object
	object, err := ms.client.GetObject(ctx, ms.defaultBucket, key, getOpts)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get object: %w", err)
	}

	// Get object info
	objInfo, err := object.Stat()
	if err != nil {
		object.Close()
		return nil, nil, fmt.Errorf("failed to get object info: %w", err)
	}

	fileInfo := &FileInfo{
		Key:          key,
		Size:         objInfo.Size,
		ContentType:  objInfo.ContentType,
		LastModified: objInfo.LastModified,
		ETag:         objInfo.ETag,
		Metadata:     objInfo.UserMetadata,
		// Tags:         objInfo.UserTags, // Removed as objInfo.UserTags is not available in v7.0.92
	}

	ms.log.WithContext(ctx).Infof("✅ File downloaded successfully: %s (%d bytes)", key, objInfo.Size)

	return object, fileInfo, nil
}

// DeleteFile deletes a file from MinIO storage
func (ms *MinIOService) DeleteFile(ctx context.Context, key string) error {
	ms.log.WithContext(ctx).Infof("🗑️ Deleting file from MinIO: %s", key)

	err := ms.client.RemoveObject(ctx, ms.defaultBucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	ms.log.WithContext(ctx).Infof("✅ File deleted successfully: %s", key)
	return nil
}

// GetFileInfo gets file metadata without downloading
func (ms *MinIOService) GetFileInfo(ctx context.Context, key string) (*FileInfo, error) {
	objInfo, err := ms.client.StatObject(ctx, ms.defaultBucket, key, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &FileInfo{
		Key:          key,
		Size:         objInfo.Size,
		ContentType:  objInfo.ContentType,
		LastModified: objInfo.LastModified,
		ETag:         objInfo.ETag,
		Metadata:     objInfo.UserMetadata,
		// Tags:         objInfo.UserTags, // Removed as objInfo.UserTags is not available in v7.0.92
	}, nil
}

// ListFiles lists files with optional prefix
func (ms *MinIOService) ListFiles(ctx context.Context, prefix string, recursive bool) ([]FileInfo, error) {
	ms.log.WithContext(ctx).Infof("📋 Listing files with prefix: %s", prefix)

	var files []FileInfo

	objectCh := ms.client.ListObjects(ctx, ms.defaultBucket, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: recursive,
	})

	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("error listing objects: %w", object.Err)
		}

		files = append(files, FileInfo{
			Key:          object.Key,
			Size:         object.Size,
			LastModified: object.LastModified,
			ETag:         object.ETag,
		})
	}

	ms.log.WithContext(ctx).Infof("📋 Found %d files", len(files))
	return files, nil
}

// GeneratePresignedURL generates a presigned URL for temporary access
func (ms *MinIOService) GeneratePresignedURL(ctx context.Context, key string, expiration time.Duration, method string) (string, error) {
	var reqParams map[string][]string

	url, err := ms.client.PresignedGetObject(ctx, ms.defaultBucket, key, expiration, reqParams)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	ms.log.WithContext(ctx).Infof("🔗 Generated presigned URL for %s (expires in %v)", key, expiration)
	return url.String(), nil
}

// CopyFile copies a file within MinIO storage
func (ms *MinIOService) CopyFile(ctx context.Context, srcKey, destKey string) error {
	ms.log.WithContext(ctx).Infof("📋 Copying file: %s -> %s", srcKey, destKey)

	srcOpts := minio.CopySrcOptions{
		Bucket: ms.defaultBucket,
		Object: srcKey,
	}

	destOpts := minio.CopyDestOptions{
		Bucket: ms.defaultBucket,
		Object: destKey,
	}

	_, err := ms.client.CopyObject(ctx, destOpts, srcOpts)
	if err != nil {
		return fmt.Errorf("failed to copy file: %w", err)
	}

	ms.log.WithContext(ctx).Infof("✅ File copied successfully: %s -> %s", srcKey, destKey)
	return nil
}

// Helper methods

// ensureBucket ensures bucket exists, creates if not
func (ms *MinIOService) ensureBucket(ctx context.Context, bucketName string) error {
	exists, err := ms.client.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check bucket existence: %w", err)
	}

	if !exists {
		err = ms.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{
			Region: ms.config.Region,
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		ms.log.WithContext(ctx).Infof("✅ Created bucket: %s", bucketName)
	}

	return nil
}

// setObjectExpiration sets lifecycle policy for object expiration
func (ms *MinIOService) setObjectExpiration(ctx context.Context, key string, days int) error {
	// This would typically be implemented using bucket lifecycle policies
	// For now, we'll just log the intention
	ms.log.WithContext(ctx).Infof("📅 Setting expiration for %s: %d days", key, days)
	return nil
}

// GenerateStorageKey generates a unique storage key for files
func (ms *MinIOService) GenerateStorageKey(originalName string, emailID *int64) string {
	// Create a unique key based on timestamp and hash
	timestamp := time.Now().Format("2006/01/02")

	// Create hash from original name and current time
	hasher := md5.New()
	hasher.Write([]byte(fmt.Sprintf("%s-%d", originalName, time.Now().UnixNano())))
	hash := hex.EncodeToString(hasher.Sum(nil))[:8]

	// Clean filename
	ext := filepath.Ext(originalName)
	baseName := strings.TrimSuffix(originalName, ext)
	cleanName := strings.ReplaceAll(baseName, " ", "_")

	// Build key
	var keyParts []string
	keyParts = append(keyParts, "dolores-files", timestamp)

	if emailID != nil {
		keyParts = append(keyParts, fmt.Sprintf("email-%d", *emailID))
	}

	keyParts = append(keyParts, fmt.Sprintf("%s-%s%s", cleanName, hash, ext))

	return strings.Join(keyParts, "/")
}

// CalculateChecksums calculates MD5 and SHA256 checksums
func (ms *MinIOService) CalculateChecksums(reader io.Reader) (string, string, error) {
	md5Hash := md5.New()
	sha256Hash := sha256.New()

	// Use TeeReader to calculate both hashes simultaneously
	teeReader := io.TeeReader(reader, md5Hash)
	_, err := io.Copy(sha256Hash, teeReader)
	if err != nil {
		return "", "", fmt.Errorf("failed to calculate checksums: %w", err)
	}

	md5Sum := hex.EncodeToString(md5Hash.Sum(nil))
	sha256Sum := hex.EncodeToString(sha256Hash.Sum(nil))

	return md5Sum, sha256Sum, nil
}
