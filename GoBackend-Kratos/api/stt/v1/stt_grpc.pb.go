// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: stt/v1/stt.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	STTService_TranscribeAudio_FullMethodName     = "/api.stt.v1.STTService/TranscribeAudio"
	STTService_TranscribeStream_FullMethodName    = "/api.stt.v1.STTService/TranscribeStream"
	STTService_TranscribePhoneCall_FullMethodName = "/api.stt.v1.STTService/TranscribePhoneCall"
	STTService_AnalyzeHVACCall_FullMethodName     = "/api.stt.v1.STTService/AnalyzeHVACCall"
	STTService_GetModelStatus_FullMethodName      = "/api.stt.v1.STTService/GetModelStatus"
	STTService_ConfigureModel_FullMethodName      = "/api.stt.v1.STTService/ConfigureModel"
)

// STTServiceClient is the client API for STTService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 🎤 NVIDIA STT Service - Polish FastConformer Hybrid Large PC
// Potężny model do rozpoznawania mowy polskiej z najwyższą dokładnością
type STTServiceClient interface {
	// 🎯 Transkrypcja audio na tekst (główna funkcja)
	TranscribeAudio(ctx context.Context, in *TranscribeRequest, opts ...grpc.CallOption) (*TranscribeResponse, error)
	// 🎤 Transkrypcja w czasie rzeczywistym (streaming)
	TranscribeStream(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[StreamTranscribeRequest, StreamTranscribeResponse], error)
	// 📞 Transkrypcja rozmów telefonicznych HVAC
	TranscribePhoneCall(ctx context.Context, in *PhoneCallRequest, opts ...grpc.CallOption) (*PhoneCallResponse, error)
	// 🔧 Analiza techniczna rozmów HVAC
	AnalyzeHVACCall(ctx context.Context, in *HVACCallAnalysisRequest, opts ...grpc.CallOption) (*HVACCallAnalysisResponse, error)
	// 📊 Status modelu i metryki
	GetModelStatus(ctx context.Context, in *ModelStatusRequest, opts ...grpc.CallOption) (*ModelStatusResponse, error)
	// ⚙️ Konfiguracja modelu
	ConfigureModel(ctx context.Context, in *ModelConfigRequest, opts ...grpc.CallOption) (*ModelConfigResponse, error)
}

type sTTServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSTTServiceClient(cc grpc.ClientConnInterface) STTServiceClient {
	return &sTTServiceClient{cc}
}

func (c *sTTServiceClient) TranscribeAudio(ctx context.Context, in *TranscribeRequest, opts ...grpc.CallOption) (*TranscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscribeResponse)
	err := c.cc.Invoke(ctx, STTService_TranscribeAudio_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sTTServiceClient) TranscribeStream(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[StreamTranscribeRequest, StreamTranscribeResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &STTService_ServiceDesc.Streams[0], STTService_TranscribeStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StreamTranscribeRequest, StreamTranscribeResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type STTService_TranscribeStreamClient = grpc.BidiStreamingClient[StreamTranscribeRequest, StreamTranscribeResponse]

func (c *sTTServiceClient) TranscribePhoneCall(ctx context.Context, in *PhoneCallRequest, opts ...grpc.CallOption) (*PhoneCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PhoneCallResponse)
	err := c.cc.Invoke(ctx, STTService_TranscribePhoneCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sTTServiceClient) AnalyzeHVACCall(ctx context.Context, in *HVACCallAnalysisRequest, opts ...grpc.CallOption) (*HVACCallAnalysisResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HVACCallAnalysisResponse)
	err := c.cc.Invoke(ctx, STTService_AnalyzeHVACCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sTTServiceClient) GetModelStatus(ctx context.Context, in *ModelStatusRequest, opts ...grpc.CallOption) (*ModelStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModelStatusResponse)
	err := c.cc.Invoke(ctx, STTService_GetModelStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sTTServiceClient) ConfigureModel(ctx context.Context, in *ModelConfigRequest, opts ...grpc.CallOption) (*ModelConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModelConfigResponse)
	err := c.cc.Invoke(ctx, STTService_ConfigureModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// STTServiceServer is the server API for STTService service.
// All implementations must embed UnimplementedSTTServiceServer
// for forward compatibility.
//
// 🎤 NVIDIA STT Service - Polish FastConformer Hybrid Large PC
// Potężny model do rozpoznawania mowy polskiej z najwyższą dokładnością
type STTServiceServer interface {
	// 🎯 Transkrypcja audio na tekst (główna funkcja)
	TranscribeAudio(context.Context, *TranscribeRequest) (*TranscribeResponse, error)
	// 🎤 Transkrypcja w czasie rzeczywistym (streaming)
	TranscribeStream(grpc.BidiStreamingServer[StreamTranscribeRequest, StreamTranscribeResponse]) error
	// 📞 Transkrypcja rozmów telefonicznych HVAC
	TranscribePhoneCall(context.Context, *PhoneCallRequest) (*PhoneCallResponse, error)
	// 🔧 Analiza techniczna rozmów HVAC
	AnalyzeHVACCall(context.Context, *HVACCallAnalysisRequest) (*HVACCallAnalysisResponse, error)
	// 📊 Status modelu i metryki
	GetModelStatus(context.Context, *ModelStatusRequest) (*ModelStatusResponse, error)
	// ⚙️ Konfiguracja modelu
	ConfigureModel(context.Context, *ModelConfigRequest) (*ModelConfigResponse, error)
	mustEmbedUnimplementedSTTServiceServer()
}

// UnimplementedSTTServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSTTServiceServer struct{}

func (UnimplementedSTTServiceServer) TranscribeAudio(context.Context, *TranscribeRequest) (*TranscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TranscribeAudio not implemented")
}
func (UnimplementedSTTServiceServer) TranscribeStream(grpc.BidiStreamingServer[StreamTranscribeRequest, StreamTranscribeResponse]) error {
	return status.Errorf(codes.Unimplemented, "method TranscribeStream not implemented")
}
func (UnimplementedSTTServiceServer) TranscribePhoneCall(context.Context, *PhoneCallRequest) (*PhoneCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TranscribePhoneCall not implemented")
}
func (UnimplementedSTTServiceServer) AnalyzeHVACCall(context.Context, *HVACCallAnalysisRequest) (*HVACCallAnalysisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnalyzeHVACCall not implemented")
}
func (UnimplementedSTTServiceServer) GetModelStatus(context.Context, *ModelStatusRequest) (*ModelStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelStatus not implemented")
}
func (UnimplementedSTTServiceServer) ConfigureModel(context.Context, *ModelConfigRequest) (*ModelConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfigureModel not implemented")
}
func (UnimplementedSTTServiceServer) mustEmbedUnimplementedSTTServiceServer() {}
func (UnimplementedSTTServiceServer) testEmbeddedByValue()                    {}

// UnsafeSTTServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to STTServiceServer will
// result in compilation errors.
type UnsafeSTTServiceServer interface {
	mustEmbedUnimplementedSTTServiceServer()
}

func RegisterSTTServiceServer(s grpc.ServiceRegistrar, srv STTServiceServer) {
	// If the following call pancis, it indicates UnimplementedSTTServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&STTService_ServiceDesc, srv)
}

func _STTService_TranscribeAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TranscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(STTServiceServer).TranscribeAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: STTService_TranscribeAudio_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(STTServiceServer).TranscribeAudio(ctx, req.(*TranscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _STTService_TranscribeStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(STTServiceServer).TranscribeStream(&grpc.GenericServerStream[StreamTranscribeRequest, StreamTranscribeResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type STTService_TranscribeStreamServer = grpc.BidiStreamingServer[StreamTranscribeRequest, StreamTranscribeResponse]

func _STTService_TranscribePhoneCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PhoneCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(STTServiceServer).TranscribePhoneCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: STTService_TranscribePhoneCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(STTServiceServer).TranscribePhoneCall(ctx, req.(*PhoneCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _STTService_AnalyzeHVACCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HVACCallAnalysisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(STTServiceServer).AnalyzeHVACCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: STTService_AnalyzeHVACCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(STTServiceServer).AnalyzeHVACCall(ctx, req.(*HVACCallAnalysisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _STTService_GetModelStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(STTServiceServer).GetModelStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: STTService_GetModelStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(STTServiceServer).GetModelStatus(ctx, req.(*ModelStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _STTService_ConfigureModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(STTServiceServer).ConfigureModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: STTService_ConfigureModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(STTServiceServer).ConfigureModel(ctx, req.(*ModelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// STTService_ServiceDesc is the grpc.ServiceDesc for STTService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var STTService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.stt.v1.STTService",
	HandlerType: (*STTServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TranscribeAudio",
			Handler:    _STTService_TranscribeAudio_Handler,
		},
		{
			MethodName: "TranscribePhoneCall",
			Handler:    _STTService_TranscribePhoneCall_Handler,
		},
		{
			MethodName: "AnalyzeHVACCall",
			Handler:    _STTService_AnalyzeHVACCall_Handler,
		},
		{
			MethodName: "GetModelStatus",
			Handler:    _STTService_GetModelStatus_Handler,
		},
		{
			MethodName: "ConfigureModel",
			Handler:    _STTService_ConfigureModel_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TranscribeStream",
			Handler:       _STTService_TranscribeStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "stt/v1/stt.proto",
}
