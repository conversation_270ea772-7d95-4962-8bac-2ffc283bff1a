// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: ai/v1/ai.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAIServiceAnalyze = "/api.ai.v1.AIService/Analyze"
const OperationAIServiceChat = "/api.ai.v1.AIService/Chat"
const OperationAIServiceListModels = "/api.ai.v1.AIService/ListModels"

type AIServiceHTTPServer interface {
	Analyze(context.Context, *AnalyzeRequest) (*AnalyzeResponse, error)
	Chat(context.Context, *ChatRequest) (*ChatResponse, error)
	ListModels(context.Context, *ListModelsRequest) (*ListModelsResponse, error)
}

func RegisterAIServiceHTTPServer(s *http.Server, srv AIServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/ai/chat", _AIService_Chat0_HTTP_Handler(srv))
	r.POST("/api/v1/ai/analyze", _AIService_Analyze0_HTTP_Handler(srv))
	r.GET("/api/v1/ai/models", _AIService_ListModels0_HTTP_Handler(srv))
}

func _AIService_Chat0_HTTP_Handler(srv AIServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAIServiceChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Chat(ctx, req.(*ChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChatResponse)
		return ctx.Result(200, reply)
	}
}

func _AIService_Analyze0_HTTP_Handler(srv AIServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AnalyzeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAIServiceAnalyze)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Analyze(ctx, req.(*AnalyzeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AnalyzeResponse)
		return ctx.Result(200, reply)
	}
}

func _AIService_ListModels0_HTTP_Handler(srv AIServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListModelsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAIServiceListModels)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListModels(ctx, req.(*ListModelsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListModelsResponse)
		return ctx.Result(200, reply)
	}
}

type AIServiceHTTPClient interface {
	Analyze(ctx context.Context, req *AnalyzeRequest, opts ...http.CallOption) (rsp *AnalyzeResponse, err error)
	Chat(ctx context.Context, req *ChatRequest, opts ...http.CallOption) (rsp *ChatResponse, err error)
	ListModels(ctx context.Context, req *ListModelsRequest, opts ...http.CallOption) (rsp *ListModelsResponse, err error)
}

type AIServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAIServiceHTTPClient(client *http.Client) AIServiceHTTPClient {
	return &AIServiceHTTPClientImpl{client}
}

func (c *AIServiceHTTPClientImpl) Analyze(ctx context.Context, in *AnalyzeRequest, opts ...http.CallOption) (*AnalyzeResponse, error) {
	var out AnalyzeResponse
	pattern := "/api/v1/ai/analyze"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAIServiceAnalyze))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AIServiceHTTPClientImpl) Chat(ctx context.Context, in *ChatRequest, opts ...http.CallOption) (*ChatResponse, error) {
	var out ChatResponse
	pattern := "/api/v1/ai/chat"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAIServiceChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AIServiceHTTPClientImpl) ListModels(ctx context.Context, in *ListModelsRequest, opts ...http.CallOption) (*ListModelsResponse, error) {
	var out ListModelsResponse
	pattern := "/api/v1/ai/models"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAIServiceListModels))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
