// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: hvac/v1/hvac.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message Definitions
type Customer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Customer) Reset() {
	*x = Customer{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{0}
}

func (x *Customer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Customer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Customer) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Customer) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *Customer) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Customer) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Customer) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type Lead struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Source        string                 `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Lead) Reset() {
	*x = Lead{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Lead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lead) ProtoMessage() {}

func (x *Lead) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lead.ProtoReflect.Descriptor instead.
func (*Lead) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{1}
}

func (x *Lead) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Lead) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lead) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Lead) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *Lead) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Lead) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Lead) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Lead) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type Job struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomerId    int64                  `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Priority      string                 `protobuf:"bytes,6,opt,name=priority,proto3" json:"priority,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Job) Reset() {
	*x = Job{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{2}
}

func (x *Job) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Job) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Job) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Job) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Job) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Job) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *Job) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Job) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Job) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response Messages
type CreateCustomerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCustomerRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCustomerRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateCustomerRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateCustomerRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type CreateCustomerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Customer      *Customer              `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerResponse) Reset() {
	*x = CreateCustomerResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResponse) ProtoMessage() {}

func (x *CreateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{4}
}

func (x *CreateCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

type GetCustomerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRequest) Reset() {
	*x = GetCustomerRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRequest) ProtoMessage() {}

func (x *GetCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{5}
}

func (x *GetCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetCustomerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Customer      *Customer              `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerResponse) Reset() {
	*x = GetCustomerResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerResponse) ProtoMessage() {}

func (x *GetCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{6}
}

func (x *GetCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

type ListCustomersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersRequest) Reset() {
	*x = ListCustomersRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest) ProtoMessage() {}

func (x *ListCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{7}
}

func (x *ListCustomersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCustomersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListCustomersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Customers     []*Customer            `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersResponse) Reset() {
	*x = ListCustomersResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersResponse) ProtoMessage() {}

func (x *ListCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListCustomersResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{8}
}

func (x *ListCustomersResponse) GetCustomers() []*Customer {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *ListCustomersResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustomerId    int64                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Priority      string                 `protobuf:"bytes,4,opt,name=priority,proto3" json:"priority,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{9}
}

func (x *CreateJobRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateJobRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateJobRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateJobRequest) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *CreateJobRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

type CreateJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *Job                   `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{10}
}

func (x *CreateJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type GetJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{11}
}

func (x *GetJobRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *Job                   `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobResponse) Reset() {
	*x = GetJobResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobResponse) ProtoMessage() {}

func (x *GetJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobResponse.ProtoReflect.Descriptor instead.
func (*GetJobResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{12}
}

func (x *GetJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type ListJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	CustomerId    int64                  `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListJobsRequest) Reset() {
	*x = ListJobsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsRequest) ProtoMessage() {}

func (x *ListJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsRequest.ProtoReflect.Descriptor instead.
func (*ListJobsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{13}
}

func (x *ListJobsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListJobsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListJobsRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListJobsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{14}
}

func (x *ListJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *ListJobsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateLeadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Source        string                 `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLeadRequest) Reset() {
	*x = CreateLeadRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadRequest) ProtoMessage() {}

func (x *CreateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadRequest.ProtoReflect.Descriptor instead.
func (*CreateLeadRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{15}
}

func (x *CreateLeadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLeadRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateLeadRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateLeadRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateLeadRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type CreateLeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lead          *Lead                  `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLeadResponse) Reset() {
	*x = CreateLeadResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadResponse) ProtoMessage() {}

func (x *CreateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadResponse.ProtoReflect.Descriptor instead.
func (*CreateLeadResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{16}
}

func (x *CreateLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

type GetLeadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeadRequest) Reset() {
	*x = GetLeadRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeadRequest) ProtoMessage() {}

func (x *GetLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeadRequest.ProtoReflect.Descriptor instead.
func (*GetLeadRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{17}
}

func (x *GetLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetLeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lead          *Lead                  `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeadResponse) Reset() {
	*x = GetLeadResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeadResponse) ProtoMessage() {}

func (x *GetLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeadResponse.ProtoReflect.Descriptor instead.
func (*GetLeadResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{18}
}

func (x *GetLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

type ListLeadsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Source        string                 `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest) Reset() {
	*x = ListLeadsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest) ProtoMessage() {}

func (x *ListLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{19}
}

func (x *ListLeadsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLeadsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLeadsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListLeadsRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type ListLeadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Leads         []*Lead                `protobuf:"bytes,1,rep,name=leads,proto3" json:"leads,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsResponse) Reset() {
	*x = ListLeadsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsResponse) ProtoMessage() {}

func (x *ListLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsResponse.ProtoReflect.Descriptor instead.
func (*ListLeadsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{20}
}

func (x *ListLeadsResponse) GetLeads() []*Lead {
	if x != nil {
		return x.Leads
	}
	return nil
}

func (x *ListLeadsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type UpdateLeadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Source        string                 `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadRequest) Reset() {
	*x = UpdateLeadRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest) ProtoMessage() {}

func (x *UpdateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLeadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateLeadRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UpdateLeadRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UpdateLeadRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateLeadRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type UpdateLeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lead          *Lead                  `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadResponse) Reset() {
	*x = UpdateLeadResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadResponse) ProtoMessage() {}

func (x *UpdateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadResponse.ProtoReflect.Descriptor instead.
func (*UpdateLeadResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

type DeleteLeadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLeadRequest) Reset() {
	*x = DeleteLeadRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeadRequest) ProtoMessage() {}

func (x *DeleteLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeadRequest.ProtoReflect.Descriptor instead.
func (*DeleteLeadRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteLeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLeadResponse) Reset() {
	*x = DeleteLeadResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeadResponse) ProtoMessage() {}

func (x *DeleteLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeadResponse.ProtoReflect.Descriptor instead.
func (*DeleteLeadResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteLeadResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type ImportLeadsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileContent   []byte                 `protobuf:"bytes,1,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"` // CSV or Excel file content
	FileType      string                 `protobuf:"bytes,2,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`          // e.g., "csv", "xlsx"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportLeadsRequest) Reset() {
	*x = ImportLeadsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportLeadsRequest) ProtoMessage() {}

func (x *ImportLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportLeadsRequest.ProtoReflect.Descriptor instead.
func (*ImportLeadsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{25}
}

func (x *ImportLeadsRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *ImportLeadsRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

type ImportLeadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImportedCount int32                  `protobuf:"varint,1,opt,name=imported_count,json=importedCount,proto3" json:"imported_count,omitempty"`
	FailedCount   int32                  `protobuf:"varint,2,opt,name=failed_count,json=failedCount,proto3" json:"failed_count,omitempty"`
	Errors        []string               `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportLeadsResponse) Reset() {
	*x = ImportLeadsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportLeadsResponse) ProtoMessage() {}

func (x *ImportLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportLeadsResponse.ProtoReflect.Descriptor instead.
func (*ImportLeadsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{26}
}

func (x *ImportLeadsResponse) GetImportedCount() int32 {
	if x != nil {
		return x.ImportedCount
	}
	return 0
}

func (x *ImportLeadsResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *ImportLeadsResponse) GetErrors() []string {
	if x != nil {
		return x.Errors
	}
	return nil
}

type ExportLeadsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileType      string                 `protobuf:"bytes,1,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"` // e.g., "csv", "xlsx"
	StatusFilter  string                 `protobuf:"bytes,2,opt,name=status_filter,json=statusFilter,proto3" json:"status_filter,omitempty"`
	SourceFilter  string                 `protobuf:"bytes,3,opt,name=source_filter,json=sourceFilter,proto3" json:"source_filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportLeadsRequest) Reset() {
	*x = ExportLeadsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportLeadsRequest) ProtoMessage() {}

func (x *ExportLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportLeadsRequest.ProtoReflect.Descriptor instead.
func (*ExportLeadsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{27}
}

func (x *ExportLeadsRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *ExportLeadsRequest) GetStatusFilter() string {
	if x != nil {
		return x.StatusFilter
	}
	return ""
}

func (x *ExportLeadsRequest) GetSourceFilter() string {
	if x != nil {
		return x.SourceFilter
	}
	return ""
}

type ExportLeadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileContent   []byte                 `protobuf:"bytes,1,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
	FileName      string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportLeadsResponse) Reset() {
	*x = ExportLeadsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportLeadsResponse) ProtoMessage() {}

func (x *ExportLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportLeadsResponse.ProtoReflect.Descriptor instead.
func (*ExportLeadsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{28}
}

func (x *ExportLeadsResponse) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *ExportLeadsResponse) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type DuplicateLeadGroup struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LeadIds       []int64                `protobuf:"varint,1,rep,packed,name=lead_ids,json=leadIds,proto3" json:"lead_ids,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DuplicateLeadGroup) Reset() {
	*x = DuplicateLeadGroup{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DuplicateLeadGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateLeadGroup) ProtoMessage() {}

func (x *DuplicateLeadGroup) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateLeadGroup.ProtoReflect.Descriptor instead.
func (*DuplicateLeadGroup) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{29}
}

func (x *DuplicateLeadGroup) GetLeadIds() []int64 {
	if x != nil {
		return x.LeadIds
	}
	return nil
}

func (x *DuplicateLeadGroup) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type DetectDuplicateLeadsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional: criteria for detection, e.g., "email", "phone"
	DetectionCriteria []string `protobuf:"bytes,1,rep,name=detection_criteria,json=detectionCriteria,proto3" json:"detection_criteria,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DetectDuplicateLeadsRequest) Reset() {
	*x = DetectDuplicateLeadsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectDuplicateLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectDuplicateLeadsRequest) ProtoMessage() {}

func (x *DetectDuplicateLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectDuplicateLeadsRequest.ProtoReflect.Descriptor instead.
func (*DetectDuplicateLeadsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{30}
}

func (x *DetectDuplicateLeadsRequest) GetDetectionCriteria() []string {
	if x != nil {
		return x.DetectionCriteria
	}
	return nil
}

type DetectDuplicateLeadsResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	DuplicateGroups []*DuplicateLeadGroup  `protobuf:"bytes,1,rep,name=duplicate_groups,json=duplicateGroups,proto3" json:"duplicate_groups,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DetectDuplicateLeadsResponse) Reset() {
	*x = DetectDuplicateLeadsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectDuplicateLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectDuplicateLeadsResponse) ProtoMessage() {}

func (x *DetectDuplicateLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectDuplicateLeadsResponse.ProtoReflect.Descriptor instead.
func (*DetectDuplicateLeadsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{31}
}

func (x *DetectDuplicateLeadsResponse) GetDuplicateGroups() []*DuplicateLeadGroup {
	if x != nil {
		return x.DuplicateGroups
	}
	return nil
}

type MergeLeadsRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	LeadIdsToMerge []int64                `protobuf:"varint,1,rep,packed,name=lead_ids_to_merge,json=leadIdsToMerge,proto3" json:"lead_ids_to_merge,omitempty"`
	PrimaryLeadId  int64                  `protobuf:"varint,2,opt,name=primary_lead_id,json=primaryLeadId,proto3" json:"primary_lead_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MergeLeadsRequest) Reset() {
	*x = MergeLeadsRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MergeLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeLeadsRequest) ProtoMessage() {}

func (x *MergeLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeLeadsRequest.ProtoReflect.Descriptor instead.
func (*MergeLeadsRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{32}
}

func (x *MergeLeadsRequest) GetLeadIdsToMerge() []int64 {
	if x != nil {
		return x.LeadIdsToMerge
	}
	return nil
}

func (x *MergeLeadsRequest) GetPrimaryLeadId() int64 {
	if x != nil {
		return x.PrimaryLeadId
	}
	return 0
}

type MergeLeadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MergedLead    *Lead                  `protobuf:"bytes,1,opt,name=merged_lead,json=mergedLead,proto3" json:"merged_lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MergeLeadsResponse) Reset() {
	*x = MergeLeadsResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MergeLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeLeadsResponse) ProtoMessage() {}

func (x *MergeLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeLeadsResponse.ProtoReflect.Descriptor instead.
func (*MergeLeadsResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{33}
}

func (x *MergeLeadsResponse) GetMergedLead() *Lead {
	if x != nil {
		return x.MergedLead
	}
	return nil
}

type Campaign struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source        string                 `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{34}
}

func (x *Campaign) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Campaign) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *Campaign) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type TrackCampaignRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	LeadId         int64                  `protobuf:"varint,1,opt,name=lead_id,json=leadId,proto3" json:"lead_id,omitempty"`
	CampaignId     int64                  `protobuf:"varint,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	EventType      string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"` // e.g., "click", "conversion"
	EventTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=event_timestamp,json=eventTimestamp,proto3" json:"event_timestamp,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TrackCampaignRequest) Reset() {
	*x = TrackCampaignRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackCampaignRequest) ProtoMessage() {}

func (x *TrackCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackCampaignRequest.ProtoReflect.Descriptor instead.
func (*TrackCampaignRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{35}
}

func (x *TrackCampaignRequest) GetLeadId() int64 {
	if x != nil {
		return x.LeadId
	}
	return 0
}

func (x *TrackCampaignRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *TrackCampaignRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *TrackCampaignRequest) GetEventTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.EventTimestamp
	}
	return nil
}

type TrackCampaignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackCampaignResponse) Reset() {
	*x = TrackCampaignResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackCampaignResponse) ProtoMessage() {}

func (x *TrackCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackCampaignResponse.ProtoReflect.Descriptor instead.
func (*TrackCampaignResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{36}
}

func (x *TrackCampaignResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetCampaignROIRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    int64                  `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignROIRequest) Reset() {
	*x = GetCampaignROIRequest{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignROIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignROIRequest) ProtoMessage() {}

func (x *GetCampaignROIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignROIRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignROIRequest) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{37}
}

func (x *GetCampaignROIRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

type GetCampaignROIResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roi           float64                `protobuf:"fixed64,1,opt,name=roi,proto3" json:"roi,omitempty"`
	TotalRevenue  float64                `protobuf:"fixed64,2,opt,name=total_revenue,json=totalRevenue,proto3" json:"total_revenue,omitempty"`
	TotalCost     float64                `protobuf:"fixed64,3,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	Conversions   int32                  `protobuf:"varint,4,opt,name=conversions,proto3" json:"conversions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignROIResponse) Reset() {
	*x = GetCampaignROIResponse{}
	mi := &file_hvac_v1_hvac_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignROIResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignROIResponse) ProtoMessage() {}

func (x *GetCampaignROIResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hvac_v1_hvac_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignROIResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignROIResponse) Descriptor() ([]byte, []int) {
	return file_hvac_v1_hvac_proto_rawDescGZIP(), []int{38}
}

func (x *GetCampaignROIResponse) GetRoi() float64 {
	if x != nil {
		return x.Roi
	}
	return 0
}

func (x *GetCampaignROIResponse) GetTotalRevenue() float64 {
	if x != nil {
		return x.TotalRevenue
	}
	return 0
}

func (x *GetCampaignROIResponse) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *GetCampaignROIResponse) GetConversions() int32 {
	if x != nil {
		return x.Conversions
	}
	return 0
}

var File_hvac_v1_hvac_proto protoreflect.FileDescriptor

const file_hvac_v1_hvac_proto_rawDesc = "" +
	"\n" +
	"\x12hvac/v1/hvac.proto\x12\vapi.hvac.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xea\x01\n" +
	"\bCustomer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x04 \x01(\tR\x05phone\x12\x18\n" +
	"\aaddress\x18\x05 \x01(\tR\aaddress\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xfc\x01\n" +
	"\x04Lead\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x04 \x01(\tR\x05phone\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x16\n" +
	"\x06source\x18\x06 \x01(\tR\x06source\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xd7\x02\n" +
	"\x03Job\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x1a\n" +
	"\bpriority\x18\x06 \x01(\tR\bpriority\x12=\n" +
	"\fscheduled_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"q\n" +
	"\x15CreateCustomerRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x03 \x01(\tR\x05phone\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\"K\n" +
	"\x16CreateCustomerResponse\x121\n" +
	"\bcustomer\x18\x01 \x01(\v2\x15.api.hvac.v1.CustomerR\bcustomer\"$\n" +
	"\x12GetCustomerRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"\x13GetCustomerResponse\x121\n" +
	"\bcustomer\x18\x01 \x01(\v2\x15.api.hvac.v1.CustomerR\bcustomer\"G\n" +
	"\x14ListCustomersRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"b\n" +
	"\x15ListCustomersResponse\x123\n" +
	"\tcustomers\x18\x01 \x03(\v2\x15.api.hvac.v1.CustomerR\tcustomers\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\xc6\x01\n" +
	"\x10CreateJobRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\bpriority\x18\x04 \x01(\tR\bpriority\x12=\n" +
	"\fscheduled_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\"7\n" +
	"\x11CreateJobResponse\x12\"\n" +
	"\x03job\x18\x01 \x01(\v2\x10.api.hvac.v1.JobR\x03job\"\x1f\n" +
	"\rGetJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"4\n" +
	"\x0eGetJobResponse\x12\"\n" +
	"\x03job\x18\x01 \x01(\v2\x10.api.hvac.v1.JobR\x03job\"{\n" +
	"\x0fListJobsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"N\n" +
	"\x10ListJobsResponse\x12$\n" +
	"\x04jobs\x18\x01 \x03(\v2\x10.api.hvac.v1.JobR\x04jobs\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x83\x01\n" +
	"\x11CreateLeadRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x03 \x01(\tR\x05phone\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\";\n" +
	"\x12CreateLeadResponse\x12%\n" +
	"\x04lead\x18\x01 \x01(\v2\x11.api.hvac.v1.LeadR\x04lead\" \n" +
	"\x0eGetLeadRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"8\n" +
	"\x0fGetLeadResponse\x12%\n" +
	"\x04lead\x18\x01 \x01(\v2\x11.api.hvac.v1.LeadR\x04lead\"s\n" +
	"\x10ListLeadsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x16\n" +
	"\x06source\x18\x04 \x01(\tR\x06source\"R\n" +
	"\x11ListLeadsResponse\x12'\n" +
	"\x05leads\x18\x01 \x03(\v2\x11.api.hvac.v1.LeadR\x05leads\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x93\x01\n" +
	"\x11UpdateLeadRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x04 \x01(\tR\x05phone\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x16\n" +
	"\x06source\x18\x06 \x01(\tR\x06source\";\n" +
	"\x12UpdateLeadResponse\x12%\n" +
	"\x04lead\x18\x01 \x01(\v2\x11.api.hvac.v1.LeadR\x04lead\"#\n" +
	"\x11DeleteLeadRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\".\n" +
	"\x12DeleteLeadResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"T\n" +
	"\x12ImportLeadsRequest\x12!\n" +
	"\ffile_content\x18\x01 \x01(\fR\vfileContent\x12\x1b\n" +
	"\tfile_type\x18\x02 \x01(\tR\bfileType\"w\n" +
	"\x13ImportLeadsResponse\x12%\n" +
	"\x0eimported_count\x18\x01 \x01(\x05R\rimportedCount\x12!\n" +
	"\ffailed_count\x18\x02 \x01(\x05R\vfailedCount\x12\x16\n" +
	"\x06errors\x18\x03 \x03(\tR\x06errors\"{\n" +
	"\x12ExportLeadsRequest\x12\x1b\n" +
	"\tfile_type\x18\x01 \x01(\tR\bfileType\x12#\n" +
	"\rstatus_filter\x18\x02 \x01(\tR\fstatusFilter\x12#\n" +
	"\rsource_filter\x18\x03 \x01(\tR\fsourceFilter\"U\n" +
	"\x13ExportLeadsResponse\x12!\n" +
	"\ffile_content\x18\x01 \x01(\fR\vfileContent\x12\x1b\n" +
	"\tfile_name\x18\x02 \x01(\tR\bfileName\"G\n" +
	"\x12DuplicateLeadGroup\x12\x19\n" +
	"\blead_ids\x18\x01 \x03(\x03R\aleadIds\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"L\n" +
	"\x1bDetectDuplicateLeadsRequest\x12-\n" +
	"\x12detection_criteria\x18\x01 \x03(\tR\x11detectionCriteria\"j\n" +
	"\x1cDetectDuplicateLeadsResponse\x12J\n" +
	"\x10duplicate_groups\x18\x01 \x03(\v2\x1f.api.hvac.v1.DuplicateLeadGroupR\x0fduplicateGroups\"f\n" +
	"\x11MergeLeadsRequest\x12)\n" +
	"\x11lead_ids_to_merge\x18\x01 \x03(\x03R\x0eleadIdsToMerge\x12&\n" +
	"\x0fprimary_lead_id\x18\x02 \x01(\x03R\rprimaryLeadId\"H\n" +
	"\x12MergeLeadsResponse\x122\n" +
	"\vmerged_lead\x18\x01 \x01(\v2\x11.api.hvac.v1.LeadR\n" +
	"mergedLead\"\xb8\x01\n" +
	"\bCampaign\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06source\x18\x03 \x01(\tR\x06source\x129\n" +
	"\n" +
	"start_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\xb4\x01\n" +
	"\x14TrackCampaignRequest\x12\x17\n" +
	"\alead_id\x18\x01 \x01(\x03R\x06leadId\x12\x1f\n" +
	"\vcampaign_id\x18\x02 \x01(\x03R\n" +
	"campaignId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x03 \x01(\tR\teventType\x12C\n" +
	"\x0fevent_timestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0eeventTimestamp\"1\n" +
	"\x15TrackCampaignResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"8\n" +
	"\x15GetCampaignROIRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\x03R\n" +
	"campaignId\"\x90\x01\n" +
	"\x16GetCampaignROIResponse\x12\x10\n" +
	"\x03roi\x18\x01 \x01(\x01R\x03roi\x12#\n" +
	"\rtotal_revenue\x18\x02 \x01(\x01R\ftotalRevenue\x12\x1d\n" +
	"\n" +
	"total_cost\x18\x03 \x01(\x01R\ttotalCost\x12 \n" +
	"\vconversions\x18\x04 \x01(\x05R\vconversions2\x83\x0f\n" +
	"\vHVACService\x12w\n" +
	"\x0eCreateCustomer\x12\".api.hvac.v1.CreateCustomerRequest\x1a#.api.hvac.v1.CreateCustomerResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/api/v1/customers\x12p\n" +
	"\vGetCustomer\x12\x1f.api.hvac.v1.GetCustomerRequest\x1a .api.hvac.v1.GetCustomerResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/customers/{id}\x12q\n" +
	"\rListCustomers\x12!.api.hvac.v1.ListCustomersRequest\x1a\".api.hvac.v1.ListCustomersResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/customers\x12c\n" +
	"\tCreateJob\x12\x1d.api.hvac.v1.CreateJobRequest\x1a\x1e.api.hvac.v1.CreateJobResponse\"\x17\x82\xd3\xe4\x93\x02\x11:\x01*\"\f/api/v1/jobs\x12\\\n" +
	"\x06GetJob\x12\x1a.api.hvac.v1.GetJobRequest\x1a\x1b.api.hvac.v1.GetJobResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/jobs/{id}\x12]\n" +
	"\bListJobs\x12\x1c.api.hvac.v1.ListJobsRequest\x1a\x1d.api.hvac.v1.ListJobsResponse\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/api/v1/jobs\x12g\n" +
	"\n" +
	"CreateLead\x12\x1e.api.hvac.v1.CreateLeadRequest\x1a\x1f.api.hvac.v1.CreateLeadResponse\"\x18\x82\xd3\xe4\x93\x02\x12:\x01*\"\r/api/v1/leads\x12`\n" +
	"\aGetLead\x12\x1b.api.hvac.v1.GetLeadRequest\x1a\x1c.api.hvac.v1.GetLeadResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/api/v1/leads/{id}\x12a\n" +
	"\tListLeads\x12\x1d.api.hvac.v1.ListLeadsRequest\x1a\x1e.api.hvac.v1.ListLeadsResponse\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/api/v1/leads\x12l\n" +
	"\n" +
	"UpdateLead\x12\x1e.api.hvac.v1.UpdateLeadRequest\x1a\x1f.api.hvac.v1.UpdateLeadResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\x1a\x12/api/v1/leads/{id}\x12i\n" +
	"\n" +
	"DeleteLead\x12\x1e.api.hvac.v1.DeleteLeadRequest\x1a\x1f.api.hvac.v1.DeleteLeadResponse\"\x1a\x82\xd3\xe4\x93\x02\x14*\x12/api/v1/leads/{id}\x12q\n" +
	"\vImportLeads\x12\x1f.api.hvac.v1.ImportLeadsRequest\x1a .api.hvac.v1.ImportLeadsResponse\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/api/v1/leads/import\x12n\n" +
	"\vExportLeads\x12\x1f.api.hvac.v1.ExportLeadsRequest\x1a .api.hvac.v1.ExportLeadsResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/leads/export\x12\x98\x01\n" +
	"\x14DetectDuplicateLeads\x12(.api.hvac.v1.DetectDuplicateLeadsRequest\x1a).api.hvac.v1.DetectDuplicateLeadsResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /api/v1/leads/deduplicate/detect\x12y\n" +
	"\n" +
	"MergeLeads\x12\x1e.api.hvac.v1.MergeLeadsRequest\x1a\x1f.api.hvac.v1.MergeLeadsResponse\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/leads/deduplicate/merge\x12z\n" +
	"\rTrackCampaign\x12!.api.hvac.v1.TrackCampaignRequest\x1a\".api.hvac.v1.TrackCampaignResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/api/v1/campaigns/track\x12x\n" +
	"\x0eGetCampaignROI\x12\".api.hvac.v1.GetCampaignROIRequest\x1a#.api.hvac.v1.GetCampaignROIResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/api/v1/campaigns/roiB&Z$gobackend-hvac-kratos/api/hvac/v1;v1b\x06proto3"

var (
	file_hvac_v1_hvac_proto_rawDescOnce sync.Once
	file_hvac_v1_hvac_proto_rawDescData []byte
)

func file_hvac_v1_hvac_proto_rawDescGZIP() []byte {
	file_hvac_v1_hvac_proto_rawDescOnce.Do(func() {
		file_hvac_v1_hvac_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_hvac_v1_hvac_proto_rawDesc), len(file_hvac_v1_hvac_proto_rawDesc)))
	})
	return file_hvac_v1_hvac_proto_rawDescData
}

var file_hvac_v1_hvac_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_hvac_v1_hvac_proto_goTypes = []any{
	(*Customer)(nil),                     // 0: api.hvac.v1.Customer
	(*Lead)(nil),                         // 1: api.hvac.v1.Lead
	(*Job)(nil),                          // 2: api.hvac.v1.Job
	(*CreateCustomerRequest)(nil),        // 3: api.hvac.v1.CreateCustomerRequest
	(*CreateCustomerResponse)(nil),       // 4: api.hvac.v1.CreateCustomerResponse
	(*GetCustomerRequest)(nil),           // 5: api.hvac.v1.GetCustomerRequest
	(*GetCustomerResponse)(nil),          // 6: api.hvac.v1.GetCustomerResponse
	(*ListCustomersRequest)(nil),         // 7: api.hvac.v1.ListCustomersRequest
	(*ListCustomersResponse)(nil),        // 8: api.hvac.v1.ListCustomersResponse
	(*CreateJobRequest)(nil),             // 9: api.hvac.v1.CreateJobRequest
	(*CreateJobResponse)(nil),            // 10: api.hvac.v1.CreateJobResponse
	(*GetJobRequest)(nil),                // 11: api.hvac.v1.GetJobRequest
	(*GetJobResponse)(nil),               // 12: api.hvac.v1.GetJobResponse
	(*ListJobsRequest)(nil),              // 13: api.hvac.v1.ListJobsRequest
	(*ListJobsResponse)(nil),             // 14: api.hvac.v1.ListJobsResponse
	(*CreateLeadRequest)(nil),            // 15: api.hvac.v1.CreateLeadRequest
	(*CreateLeadResponse)(nil),           // 16: api.hvac.v1.CreateLeadResponse
	(*GetLeadRequest)(nil),               // 17: api.hvac.v1.GetLeadRequest
	(*GetLeadResponse)(nil),              // 18: api.hvac.v1.GetLeadResponse
	(*ListLeadsRequest)(nil),             // 19: api.hvac.v1.ListLeadsRequest
	(*ListLeadsResponse)(nil),            // 20: api.hvac.v1.ListLeadsResponse
	(*UpdateLeadRequest)(nil),            // 21: api.hvac.v1.UpdateLeadRequest
	(*UpdateLeadResponse)(nil),           // 22: api.hvac.v1.UpdateLeadResponse
	(*DeleteLeadRequest)(nil),            // 23: api.hvac.v1.DeleteLeadRequest
	(*DeleteLeadResponse)(nil),           // 24: api.hvac.v1.DeleteLeadResponse
	(*ImportLeadsRequest)(nil),           // 25: api.hvac.v1.ImportLeadsRequest
	(*ImportLeadsResponse)(nil),          // 26: api.hvac.v1.ImportLeadsResponse
	(*ExportLeadsRequest)(nil),           // 27: api.hvac.v1.ExportLeadsRequest
	(*ExportLeadsResponse)(nil),          // 28: api.hvac.v1.ExportLeadsResponse
	(*DuplicateLeadGroup)(nil),           // 29: api.hvac.v1.DuplicateLeadGroup
	(*DetectDuplicateLeadsRequest)(nil),  // 30: api.hvac.v1.DetectDuplicateLeadsRequest
	(*DetectDuplicateLeadsResponse)(nil), // 31: api.hvac.v1.DetectDuplicateLeadsResponse
	(*MergeLeadsRequest)(nil),            // 32: api.hvac.v1.MergeLeadsRequest
	(*MergeLeadsResponse)(nil),           // 33: api.hvac.v1.MergeLeadsResponse
	(*Campaign)(nil),                     // 34: api.hvac.v1.Campaign
	(*TrackCampaignRequest)(nil),         // 35: api.hvac.v1.TrackCampaignRequest
	(*TrackCampaignResponse)(nil),        // 36: api.hvac.v1.TrackCampaignResponse
	(*GetCampaignROIRequest)(nil),        // 37: api.hvac.v1.GetCampaignROIRequest
	(*GetCampaignROIResponse)(nil),       // 38: api.hvac.v1.GetCampaignROIResponse
	(*timestamppb.Timestamp)(nil),        // 39: google.protobuf.Timestamp
}
var file_hvac_v1_hvac_proto_depIdxs = []int32{
	39, // 0: api.hvac.v1.Customer.created_at:type_name -> google.protobuf.Timestamp
	39, // 1: api.hvac.v1.Customer.updated_at:type_name -> google.protobuf.Timestamp
	39, // 2: api.hvac.v1.Lead.created_at:type_name -> google.protobuf.Timestamp
	39, // 3: api.hvac.v1.Lead.updated_at:type_name -> google.protobuf.Timestamp
	39, // 4: api.hvac.v1.Job.scheduled_at:type_name -> google.protobuf.Timestamp
	39, // 5: api.hvac.v1.Job.created_at:type_name -> google.protobuf.Timestamp
	39, // 6: api.hvac.v1.Job.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 7: api.hvac.v1.CreateCustomerResponse.customer:type_name -> api.hvac.v1.Customer
	0,  // 8: api.hvac.v1.GetCustomerResponse.customer:type_name -> api.hvac.v1.Customer
	0,  // 9: api.hvac.v1.ListCustomersResponse.customers:type_name -> api.hvac.v1.Customer
	39, // 10: api.hvac.v1.CreateJobRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	2,  // 11: api.hvac.v1.CreateJobResponse.job:type_name -> api.hvac.v1.Job
	2,  // 12: api.hvac.v1.GetJobResponse.job:type_name -> api.hvac.v1.Job
	2,  // 13: api.hvac.v1.ListJobsResponse.jobs:type_name -> api.hvac.v1.Job
	1,  // 14: api.hvac.v1.CreateLeadResponse.lead:type_name -> api.hvac.v1.Lead
	1,  // 15: api.hvac.v1.GetLeadResponse.lead:type_name -> api.hvac.v1.Lead
	1,  // 16: api.hvac.v1.ListLeadsResponse.leads:type_name -> api.hvac.v1.Lead
	1,  // 17: api.hvac.v1.UpdateLeadResponse.lead:type_name -> api.hvac.v1.Lead
	29, // 18: api.hvac.v1.DetectDuplicateLeadsResponse.duplicate_groups:type_name -> api.hvac.v1.DuplicateLeadGroup
	1,  // 19: api.hvac.v1.MergeLeadsResponse.merged_lead:type_name -> api.hvac.v1.Lead
	39, // 20: api.hvac.v1.Campaign.start_date:type_name -> google.protobuf.Timestamp
	39, // 21: api.hvac.v1.Campaign.end_date:type_name -> google.protobuf.Timestamp
	39, // 22: api.hvac.v1.TrackCampaignRequest.event_timestamp:type_name -> google.protobuf.Timestamp
	3,  // 23: api.hvac.v1.HVACService.CreateCustomer:input_type -> api.hvac.v1.CreateCustomerRequest
	5,  // 24: api.hvac.v1.HVACService.GetCustomer:input_type -> api.hvac.v1.GetCustomerRequest
	7,  // 25: api.hvac.v1.HVACService.ListCustomers:input_type -> api.hvac.v1.ListCustomersRequest
	9,  // 26: api.hvac.v1.HVACService.CreateJob:input_type -> api.hvac.v1.CreateJobRequest
	11, // 27: api.hvac.v1.HVACService.GetJob:input_type -> api.hvac.v1.GetJobRequest
	13, // 28: api.hvac.v1.HVACService.ListJobs:input_type -> api.hvac.v1.ListJobsRequest
	15, // 29: api.hvac.v1.HVACService.CreateLead:input_type -> api.hvac.v1.CreateLeadRequest
	17, // 30: api.hvac.v1.HVACService.GetLead:input_type -> api.hvac.v1.GetLeadRequest
	19, // 31: api.hvac.v1.HVACService.ListLeads:input_type -> api.hvac.v1.ListLeadsRequest
	21, // 32: api.hvac.v1.HVACService.UpdateLead:input_type -> api.hvac.v1.UpdateLeadRequest
	23, // 33: api.hvac.v1.HVACService.DeleteLead:input_type -> api.hvac.v1.DeleteLeadRequest
	25, // 34: api.hvac.v1.HVACService.ImportLeads:input_type -> api.hvac.v1.ImportLeadsRequest
	27, // 35: api.hvac.v1.HVACService.ExportLeads:input_type -> api.hvac.v1.ExportLeadsRequest
	30, // 36: api.hvac.v1.HVACService.DetectDuplicateLeads:input_type -> api.hvac.v1.DetectDuplicateLeadsRequest
	32, // 37: api.hvac.v1.HVACService.MergeLeads:input_type -> api.hvac.v1.MergeLeadsRequest
	35, // 38: api.hvac.v1.HVACService.TrackCampaign:input_type -> api.hvac.v1.TrackCampaignRequest
	37, // 39: api.hvac.v1.HVACService.GetCampaignROI:input_type -> api.hvac.v1.GetCampaignROIRequest
	4,  // 40: api.hvac.v1.HVACService.CreateCustomer:output_type -> api.hvac.v1.CreateCustomerResponse
	6,  // 41: api.hvac.v1.HVACService.GetCustomer:output_type -> api.hvac.v1.GetCustomerResponse
	8,  // 42: api.hvac.v1.HVACService.ListCustomers:output_type -> api.hvac.v1.ListCustomersResponse
	10, // 43: api.hvac.v1.HVACService.CreateJob:output_type -> api.hvac.v1.CreateJobResponse
	12, // 44: api.hvac.v1.HVACService.GetJob:output_type -> api.hvac.v1.GetJobResponse
	14, // 45: api.hvac.v1.HVACService.ListJobs:output_type -> api.hvac.v1.ListJobsResponse
	16, // 46: api.hvac.v1.HVACService.CreateLead:output_type -> api.hvac.v1.CreateLeadResponse
	18, // 47: api.hvac.v1.HVACService.GetLead:output_type -> api.hvac.v1.GetLeadResponse
	20, // 48: api.hvac.v1.HVACService.ListLeads:output_type -> api.hvac.v1.ListLeadsResponse
	22, // 49: api.hvac.v1.HVACService.UpdateLead:output_type -> api.hvac.v1.UpdateLeadResponse
	24, // 50: api.hvac.v1.HVACService.DeleteLead:output_type -> api.hvac.v1.DeleteLeadResponse
	26, // 51: api.hvac.v1.HVACService.ImportLeads:output_type -> api.hvac.v1.ImportLeadsResponse
	28, // 52: api.hvac.v1.HVACService.ExportLeads:output_type -> api.hvac.v1.ExportLeadsResponse
	31, // 53: api.hvac.v1.HVACService.DetectDuplicateLeads:output_type -> api.hvac.v1.DetectDuplicateLeadsResponse
	33, // 54: api.hvac.v1.HVACService.MergeLeads:output_type -> api.hvac.v1.MergeLeadsResponse
	36, // 55: api.hvac.v1.HVACService.TrackCampaign:output_type -> api.hvac.v1.TrackCampaignResponse
	38, // 56: api.hvac.v1.HVACService.GetCampaignROI:output_type -> api.hvac.v1.GetCampaignROIResponse
	40, // [40:57] is the sub-list for method output_type
	23, // [23:40] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_hvac_v1_hvac_proto_init() }
func file_hvac_v1_hvac_proto_init() {
	if File_hvac_v1_hvac_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_hvac_v1_hvac_proto_rawDesc), len(file_hvac_v1_hvac_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hvac_v1_hvac_proto_goTypes,
		DependencyIndexes: file_hvac_v1_hvac_proto_depIdxs,
		MessageInfos:      file_hvac_v1_hvac_proto_msgTypes,
	}.Build()
	File_hvac_v1_hvac_proto = out.File
	file_hvac_v1_hvac_proto_goTypes = nil
	file_hvac_v1_hvac_proto_depIdxs = nil
}
