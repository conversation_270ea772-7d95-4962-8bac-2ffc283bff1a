package customer

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// 👤 Core Customer Model
type Customer struct {
	ID                      uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Email                   string    `gorm:"unique;size:255" json:"email"`
	PrimaryPhone            string    `gorm:"size:50" json:"primary_phone"`
	Name                    string    `gorm:"size:255" json:"name"`
	Company                 string    `gorm:"size:255" json:"company"`
	CustomerType            string    `gorm:"size:50" json:"customer_type"` // residential, commercial, industrial
	CommunicationPreference string    `gorm:"size:50;default:email" json:"communication_preference"`
	KnowledgeLevel          string    `gorm:"size:50;default:basic" json:"knowledge_level"`
	SatisfactionScore       float64   `gorm:"type:decimal(3,2);default:3.00" json:"satisfaction_score"`
	LifetimeValue           float64   `gorm:"type:decimal(10,2);default:0.00" json:"lifetime_value"`

	// Contact Details (JSON fields)
	Address          map[string]interface{} `gorm:"type:jsonb" json:"address,omitempty"`
	EmergencyContact map[string]interface{} `gorm:"type:jsonb" json:"emergency_contact,omitempty"`

	// Business Intelligence
	CustomerSegment    string     `gorm:"size:50" json:"customer_segment"`    // vip, regular, new, at_risk
	AcquisitionSource  string     `gorm:"size:100" json:"acquisition_source"` // website, referral, advertising
	FirstContactDate   *time.Time `json:"first_contact_date,omitempty"`
	LastContactDate    *time.Time `json:"last_contact_date,omitempty"`
	PreferredTechnician uuid.UUID `gorm:"type:uuid" json:"preferred_technician,omitempty"`

	// Relations
	Phones       []CustomerPhone       `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"phones,omitempty"`
	Emails       []CustomerEmail       `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"emails,omitempty"`
	Interactions []CustomerInteraction `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"interactions,omitempty"`
	Analytics    *CustomerAnalytics    `gorm:"foreignKey:CustomerID" json:"analytics,omitempty"`
	Tags         []CustomerTag         `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"tags,omitempty"`
	Journey      []CustomerJourney     `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"journey,omitempty"`

	// Metadata
	TagsArray []string `gorm:"type:text[]" json:"tags_array,omitempty"`
	Notes     string   `gorm:"type:text" json:"notes,omitempty"`
	Status    string   `gorm:"size:50;default:active" json:"status"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// 📞 Customer Phone Numbers
type CustomerPhone struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	CustomerID  uuid.UUID `gorm:"type:uuid;not null" json:"customer_id"`
	PhoneNumber string    `gorm:"size:50;not null" json:"phone_number"`
	PhoneType   string    `gorm:"size:50;default:mobile" json:"phone_type"` // mobile, home, work, emergency
	IsPrimary   bool      `gorm:"default:false" json:"is_primary"`
	Verified    bool      `gorm:"default:false" json:"verified"`
	LastUsed    *time.Time `json:"last_used,omitempty"`

	// Relations
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`

	CreatedAt time.Time `json:"created_at"`
}

// 📧 Customer Email Addresses
type CustomerEmail struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	CustomerID   uuid.UUID `gorm:"type:uuid;not null" json:"customer_id"`
	EmailAddress string    `gorm:"size:255;not null" json:"email_address"`
	EmailType    string    `gorm:"size:50;default:primary" json:"email_type"` // primary, work, billing, emergency
	IsPrimary    bool      `gorm:"default:false" json:"is_primary"`
	Verified     bool      `gorm:"default:false" json:"verified"`
	LastUsed     *time.Time `json:"last_used,omitempty"`

	// Relations
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`

	CreatedAt time.Time `json:"created_at"`
}

// 🔗 Customer Interactions (All touchpoints)
type CustomerInteraction struct {
	ID              uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	CustomerID      uuid.UUID `gorm:"type:uuid;not null" json:"customer_id"`
	InteractionType string    `gorm:"size:50;not null" json:"interaction_type"` // email, phone_call, service_visit, quote, complaint
	Direction       string    `gorm:"size:20;not null" json:"direction"`        // inbound, outbound
	Channel         string    `gorm:"size:50" json:"channel"`                   // gmail, outlook, phone, in_person, website
	Timestamp       time.Time `gorm:"default:now()" json:"timestamp"`

	// Content
	Subject string `gorm:"size:500" json:"subject,omitempty"`
	Content string `gorm:"type:text" json:"content,omitempty"`
	Summary string `gorm:"type:text" json:"summary,omitempty"`

	// Source Tracking
	SourceSystem      string `gorm:"size:50" json:"source_system,omitempty"`       // email_intelligence, phone_transcription, crm_manual
	SourceID          string `gorm:"size:255" json:"source_id,omitempty"`          // original email/call ID
	ExternalReference string `gorm:"size:255" json:"external_reference,omitempty"` // ticket number, order ID, etc.

	// AI Analysis Results
	Sentiment       string  `gorm:"size:50" json:"sentiment,omitempty"`
	SentimentScore  float64 `gorm:"type:decimal(3,2)" json:"sentiment_score,omitempty"`
	PriorityLevel   string  `gorm:"size:50" json:"priority_level,omitempty"`
	UrgencyScore    int     `gorm:"check:urgency_score BETWEEN 1 AND 10" json:"urgency_score,omitempty"`
	HVACRelevance   bool    `gorm:"default:false" json:"hvac_relevance"`
	ConfidenceScore float64 `gorm:"type:decimal(3,2)" json:"confidence_score,omitempty"`

	// Business Data
	EstimatedValue     float64  `gorm:"type:decimal(10,2)" json:"estimated_value,omitempty"`
	ActualValue        float64  `gorm:"type:decimal(10,2)" json:"actual_value,omitempty"`
	ServiceCategory    string   `gorm:"size:100" json:"service_category,omitempty"` // repair, maintenance, installation, consultation
	EquipmentMentioned []string `gorm:"type:text[]" json:"equipment_mentioned,omitempty"`

	// Follow-up
	ActionItems       []string   `gorm:"type:text[]" json:"action_items,omitempty"`
	FollowUpRequired  bool       `gorm:"default:false" json:"follow_up_required"`
	FollowUpDate      *time.Time `json:"follow_up_date,omitempty"`
	FollowUpCompleted bool       `gorm:"default:false" json:"follow_up_completed"`

	// Resolution
	ResolutionStatus     string        `gorm:"size:50" json:"resolution_status,omitempty"` // open, in_progress, resolved, closed
	ResolutionTime       *time.Duration `json:"resolution_time,omitempty"`
	CustomerSatisfaction *int          `gorm:"check:customer_satisfaction BETWEEN 1 AND 5" json:"customer_satisfaction,omitempty"`

	// Relations
	Customer  Customer   `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`
	PhoneCall *PhoneCall `gorm:"foreignKey:InteractionID" json:"phone_call,omitempty"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// 📞 Phone Call Specific Data
type PhoneCall struct {
	ID                      uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	InteractionID           uuid.UUID `gorm:"type:uuid;not null" json:"interaction_id"`
	PhoneNumber             string    `gorm:"size:50;not null" json:"phone_number"`
	DurationSeconds         int       `json:"duration_seconds,omitempty"`
	CallDirection           string    `gorm:"size:20;not null" json:"call_direction"` // inbound, outbound
	CallStatus              string    `gorm:"size:50" json:"call_status,omitempty"`   // completed, missed, voicemail, busy, failed
	CallQualityScore        float64   `gorm:"type:decimal(3,2)" json:"call_quality_score,omitempty"`

	// Transcription Data
	TranscriptionText       string  `gorm:"type:text" json:"transcription_text,omitempty"`
	TranscriptionConfidence float64 `gorm:"type:decimal(3,2)" json:"transcription_confidence,omitempty"`
	TranscriptionLanguage   string  `gorm:"size:10;default:en" json:"transcription_language"`
	TranscriptionProvider   string  `gorm:"size:50" json:"transcription_provider,omitempty"` // whisper, google, azure

	// Call Analysis
	CallPurpose         string `gorm:"size:100" json:"call_purpose,omitempty"`         // emergency, quote_request, follow_up, complaint
	TechnicalComplexity string `gorm:"size:50" json:"technical_complexity,omitempty"` // simple, moderate, complex, expert_required
	CustomerMood        string `gorm:"size:50" json:"customer_mood,omitempty"`        // calm, frustrated, angry, satisfied, confused

	// Business Intelligence
	SalesOpportunity   bool   `gorm:"default:false" json:"sales_opportunity"`
	UpsellPotential    string `gorm:"size:50" json:"upsell_potential,omitempty"` // high, medium, low, none
	CompetitiveMention bool   `gorm:"default:false" json:"competitive_mention"`

	// Metadata
	RecordingAvailable bool   `gorm:"default:false" json:"recording_available"`
	RecordingURL       string `gorm:"size:500" json:"recording_url,omitempty"`
	CallerID           string `gorm:"size:100" json:"caller_id,omitempty"`

	// Relations
	Interaction CustomerInteraction `gorm:"foreignKey:InteractionID" json:"interaction,omitempty"`

	CreatedAt time.Time `json:"created_at"`
}

// 📊 Customer Analytics Cache
type CustomerAnalytics struct {
	CustomerID uuid.UUID `gorm:"type:uuid;primary_key" json:"customer_id"`

	// Interaction Counts
	TotalInteractions int `gorm:"default:0" json:"total_interactions"`
	EmailCount        int `gorm:"default:0" json:"email_count"`
	PhoneCallCount    int `gorm:"default:0" json:"phone_call_count"`
	ServiceVisitCount int `gorm:"default:0" json:"service_visit_count"`

	// Response Metrics
	AvgResponseTime   *time.Duration `json:"avg_response_time,omitempty"`
	FirstResponseTime *time.Duration `json:"first_response_time,omitempty"`
	ResolutionRate    float64        `gorm:"type:decimal(5,2)" json:"resolution_rate,omitempty"`

	// Satisfaction Metrics
	AvgSatisfaction    float64 `gorm:"type:decimal(3,2)" json:"avg_satisfaction,omitempty"`
	SatisfactionTrend  string  `gorm:"size:20" json:"satisfaction_trend,omitempty"` // improving, declining, stable
	NPSScore           *int    `json:"nps_score,omitempty"`                         // Net Promoter Score

	// Business Metrics
	TotalRevenue      float64 `gorm:"type:decimal(10,2);default:0.00" json:"total_revenue"`
	AvgOrderValue     float64 `gorm:"type:decimal(10,2);default:0.00" json:"avg_order_value"`
	PurchaseFrequency float64 `gorm:"type:decimal(5,2);default:0.00" json:"purchase_frequency"`

	// Risk Assessment
	ChurnProbability float64 `gorm:"type:decimal(3,2);default:0.00" json:"churn_probability"`
	HealthScore      *int    `gorm:"check:health_score BETWEEN 1 AND 100" json:"health_score,omitempty"`

	// Relations
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`

	LastCalculated time.Time `gorm:"default:now()" json:"last_calculated"`
	CreatedAt      time.Time `json:"created_at"`
}

// 🏷️ Customer Tags for Segmentation
type CustomerTag struct {
	ID         uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	CustomerID uuid.UUID `gorm:"type:uuid;not null" json:"customer_id"`
	TagName    string    `gorm:"size:100;not null" json:"tag_name"`
	TagValue   string    `gorm:"size:255" json:"tag_value,omitempty"`
	TagType    string    `gorm:"size:50" json:"tag_type,omitempty"` // system, manual, ai_generated
	Confidence float64   `gorm:"type:decimal(3,2)" json:"confidence,omitempty"`
	CreatedBy  string    `gorm:"size:100" json:"created_by,omitempty"` // user_id or 'system'

	// Relations
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`

	CreatedAt time.Time `json:"created_at"`
}

// 📈 Customer Journey Tracking
type CustomerJourney struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	CustomerID   uuid.UUID `gorm:"type:uuid;not null" json:"customer_id"`
	Stage        string    `gorm:"size:50;not null" json:"stage"` // awareness, consideration, purchase, retention, advocacy
	StageEntered time.Time `gorm:"default:now()" json:"stage_entered"`
	StageDuration *time.Duration `json:"stage_duration,omitempty"`

	// Touchpoints
	TouchpointCount int        `gorm:"default:0" json:"touchpoint_count"`
	LastTouchpoint  *time.Time `json:"last_touchpoint,omitempty"`

	// Conversion Tracking
	ConversionEvent string  `gorm:"size:100" json:"conversion_event,omitempty"` // quote_requested, service_booked, contract_signed
	ConversionValue float64 `gorm:"type:decimal(10,2)" json:"conversion_value,omitempty"`

	// Attribution
	AttributionSource   string `gorm:"size:100" json:"attribution_source,omitempty"`
	AttributionMedium   string `gorm:"size:100" json:"attribution_medium,omitempty"`
	AttributionCampaign string `gorm:"size:100" json:"attribution_campaign,omitempty"`

	// Relations
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`

	CreatedAt time.Time `json:"created_at"`
}

// 🔧 GORM Hooks and Methods

// BeforeCreate hook for Customer
func (c *Customer) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	if c.FirstContactDate == nil {
		now := time.Now()
		c.FirstContactDate = &now
	}
	return nil
}

// BeforeCreate hook for CustomerInteraction
func (ci *CustomerInteraction) BeforeCreate(tx *gorm.DB) error {
	if ci.ID == uuid.Nil {
		ci.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for PhoneCall
func (pc *PhoneCall) BeforeCreate(tx *gorm.DB) error {
	if pc.ID == uuid.Nil {
		pc.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for CustomerPhone
func (cp *CustomerPhone) BeforeCreate(tx *gorm.DB) error {
	if cp.ID == uuid.Nil {
		cp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for CustomerEmail
func (ce *CustomerEmail) BeforeCreate(tx *gorm.DB) error {
	if ce.ID == uuid.Nil {
		ce.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for CustomerTag
func (ct *CustomerTag) BeforeCreate(tx *gorm.DB) error {
	if ct.ID == uuid.Nil {
		ct.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for CustomerJourney
func (cj *CustomerJourney) BeforeCreate(tx *gorm.DB) error {
	if cj.ID == uuid.Nil {
		cj.ID = uuid.New()
	}
	return nil
}

// 📋 Table Names (optional, for custom naming)
func (Customer) TableName() string {
	return "customers"
}

func (CustomerPhone) TableName() string {
	return "customer_phones"
}

func (CustomerEmail) TableName() string {
	return "customer_emails"
}

func (CustomerInteraction) TableName() string {
	return "customer_interactions"
}

func (PhoneCall) TableName() string {
	return "phone_calls"
}

func (CustomerAnalytics) TableName() string {
	return "customer_analytics"
}

func (CustomerTag) TableName() string {
	return "customer_tags"
}

func (CustomerJourney) TableName() string {
	return "customer_journey"
}
