package service

import (
	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/ai"
)

// 🚀 Enhanced AI Service - gRPC/HTTP service layer for enhanced AI features
// Provides unified interface for LangChain, Vector DB, and JSON-Iterator features

type EnhancedAIService struct {
	// Note: Protobuf types temporarily removed for compilation
	// TODO: Generate proper protobuf files for enhanced AI service

	enhancedAI *ai.EnhancedService
	log        *log.Helper
}

// NewEnhancedAIService creates a new enhanced AI service
func NewEnhancedAIService(enhancedAI *ai.EnhancedService, logger log.Logger) *EnhancedAIService {
	return &EnhancedAIService{
		enhancedAI: enhancedAI,
		log:        log.New<PERSON><PERSON>per(logger),
	}
}

// Note: Enhanced AI methods temporarily commented out due to missing protobuf types
// TODO: Implement proper protobuf generation and restore these methods

/*
// EnhancedChat processes chat requests with full AI pipeline
func (s *EnhancedAIService) EnhancedChat(ctx context.Context, req *pb.EnhancedChatRequest) (*pb.EnhancedChatResponse, error) {
	s.log.WithContext(ctx).Infof("🤖 Enhanced chat request: %s", req.Message)

	// Convert protobuf request to internal request
	enhancedReq := &ai.EnhancedChatRequest{
		ChatRequest: &biz.ChatRequest{
			Message: req.Message,
			Model:   req.Model,
			Context: req.Context,
		},
		UseVectorSearch:    req.UseVectorSearch,
		SearchCollections:  req.SearchCollections,
		MaxContextDocs:     int(req.MaxContextDocs),
		EnableCaching:      req.EnableCaching,
		RequiredConfidence: req.RequiredConfidence,
	}

	// Process with enhanced AI service
	response, err := s.enhancedAI.EnhancedChat(ctx, enhancedReq)
	if err != nil {
		return nil, fmt.Errorf("enhanced chat failed: %w", err)
	}

	// Convert context sources
	var contextSources []*pb.ContextSource
	for _, source := range response.ContextSources {
		metadata, _ := common.Marshal(source.Metadata)
		contextSources = append(contextSources, &pb.ContextSource{
			Id:         source.ID,
			Content:    source.Content,
			Similarity: source.Similarity,
			Collection: source.Collection,
			Metadata:   string(metadata),
		})
	}

	return &pb.EnhancedChatResponse{
		Response:         response.Response,
		ModelUsed:        response.ModelUsed,
		TokensUsed:       response.TokensUsed,
		ContextSources:   contextSources,
		VectorSearchUsed: response.VectorSearchUsed,
		CacheUsed:        response.CacheUsed,
		ProcessingTime:   response.ProcessingTime,
		ConfidenceScore:  response.ConfidenceScore,
		Recommendations:  response.Recommendations,
	}, nil
}

// IntelligentHVACAnalysis performs comprehensive HVAC issue analysis
func (s *EnhancedAIService) IntelligentHVACAnalysis(ctx context.Context, req *pb.HVACAnalysisRequest) (*pb.HVACAnalysisResponse, error) {
	s.log.WithContext(ctx).Infof("🔧 HVAC analysis request: %s", req.CustomerIssue)

	result, err := s.enhancedAI.IntelligentHVACAnalysis(ctx, req.CustomerIssue, req.SystemType)
	if err != nil {
		return nil, fmt.Errorf("HVAC analysis failed: %w", err)
	}

	// Convert immediate actions
	var immediateActions []*pb.ImmediateAction
	for _, action := range result.ImmediateActions {
		immediateActions = append(immediateActions, &pb.ImmediateAction{
			Action:         action.Action,
			Description:    action.Description,
			SafetyLevel:    action.SafetyLevel,
			RequiredTools:  action.RequiredTools,
			EstimatedTime:  action.EstimatedTime,
			WarningNotes:   action.WarningNotes,
			StopConditions: action.StopConditions,
		})
	}

	// Convert required parts
	var requiredParts []*pb.RequiredPart
	for _, part := range result.Assessment.RequiredParts {
		requiredParts = append(requiredParts, &pb.RequiredPart{
			PartName:      part.PartName,
			PartNumber:    part.PartNumber,
			Quantity:      int32(part.Quantity),
			EstimatedCost: part.EstimatedCost,
			Availability:  part.Availability,
			Alternatives:  part.Alternatives,
		})
	}

	// Convert metadata
	metadata, _ := common.Marshal(result.Metadata)

	return &pb.HVACAnalysisResponse{
		IssueId:          result.IssueID,
		PrimaryProblem:   result.PrimaryProblem,
		Category:         string(result.Category),
		UrgencyLevel:     string(result.UrgencyLevel),
		RootCauses:       result.RootCauses,
		ImmediateActions: immediateActions,
		Assessment: &pb.ProfessionalAssessment{
			RepairComplexity: result.Assessment.RepairComplexity,
			EstimatedCost: &pb.CostRange{
				MinCost:  result.Assessment.EstimatedCost.MinCost,
				MaxCost:  result.Assessment.EstimatedCost.MaxCost,
				Currency: result.Assessment.EstimatedCost.Currency,
				Notes:    result.Assessment.EstimatedCost.Notes,
			},
			RequiredParts:   requiredParts,
			RequiredTools:   result.Assessment.RequiredTools,
			EstimatedTime:   result.Assessment.EstimatedTime,
			SkillLevel:      result.Assessment.SkillLevel,
			Recommendations: result.Assessment.Recommendations,
		},
		Confidence: result.Confidence,
		Metadata:   string(metadata),
	}, nil
}

// SmartEmailProcessing processes emails with full AI pipeline
func (s *EnhancedAIService) SmartEmailProcessing(ctx context.Context, req *pb.EmailProcessingRequest) (*pb.EmailProcessingResponse, error) {
	s.log.WithContext(ctx).Infof("📧 Email processing request from: %s", req.Sender)

	result, err := s.enhancedAI.SmartEmailProcessing(ctx, req.EmailContent, req.Sender)
	if err != nil {
		return nil, fmt.Errorf("email processing failed: %w", err)
	}

	// Convert metadata
	metadata, _ := common.Marshal(result.Metadata)

	return &pb.EmailProcessingResponse{
		EmailId: result.EmailID,
		Classification: &pb.EmailClassification{
			Category:  string(result.Classification.Category),
			Priority:  string(result.Classification.Priority),
			Sentiment: string(result.Classification.Sentiment),
			Language:  result.Classification.Language,
			IsSpam:    result.Classification.IsSpam,
			IsUrgent:  result.Classification.IsUrgent,
		},
		ContentAnalysis: &pb.EmailContentAnalysis{
			MainTopic:         result.ContentAnalysis.MainTopic,
			KeyIssues:         result.ContentAnalysis.KeyIssues,
			CustomerIntent:    result.ContentAnalysis.CustomerIntent,
			ActionRequired:    result.ContentAnalysis.ActionRequired,
			MentionedEquipment: result.ContentAnalysis.MentionedEquipment,
			TechnicalTerms:    result.ContentAnalysis.TechnicalTerms,
		},
		RoutingInfo: &pb.EmailRoutingInfo{
			Department:         string(result.RoutingInfo.Department),
			SuggestedAssignee:  result.RoutingInfo.SuggestedAssignee,
			ResponseTimeline:   result.RoutingInfo.ResponseTimeline,
			EscalationNeeded:   result.RoutingInfo.EscalationNeeded,
			RequiredSkills:     result.RoutingInfo.RequiredSkills,
			SimilarCases:       result.RoutingInfo.SimilarCases,
		},
		ExtractedInfo: &pb.ExtractedEmailInfo{
			CustomerContact: &pb.ContactInfo{
				Name:           result.ExtractedInfo.CustomerContact.Name,
				Email:          result.ExtractedInfo.CustomerContact.Email,
				Phone:          result.ExtractedInfo.CustomerContact.Phone,
				AlternatePhone: result.ExtractedInfo.CustomerContact.AlternatePhone,
				Company:        result.ExtractedInfo.CustomerContact.Company,
			},
			PreferredContact: string(result.ExtractedInfo.PreferredContact),
			BudgetMentioned:  result.ExtractedInfo.BudgetMentioned,
			TimelineMentioned: result.ExtractedInfo.TimelineMentioned,
		},
		ResponseSuggestions: &pb.ResponseSuggestions{
			RecommendedTone:       result.ResponseSuggestions.RecommendedTone,
			KeyPointsToAddress:    result.ResponseSuggestions.KeyPointsToAddress,
			InfoNeeded:            result.ResponseSuggestions.InfoNeeded,
			FollowUpActions:       result.ResponseSuggestions.FollowUpActions,
			TemplateToUse:         result.ResponseSuggestions.TemplateToUse,
			EstimatedResponseTime: result.ResponseSuggestions.EstimatedResponseTime,
		},
		Confidence: result.Confidence,
		Metadata:   string(metadata),
	}, nil
}

// SemanticKnowledgeSearch performs semantic search across knowledge base
func (s *EnhancedAIService) SemanticKnowledgeSearch(ctx context.Context, req *pb.KnowledgeSearchRequest) (*pb.KnowledgeSearchResponse, error) {
	s.log.WithContext(ctx).Infof("🔍 Knowledge search request: %s", req.Query)

	results, err := s.enhancedAI.SemanticKnowledgeSearch(ctx, req.Query, req.Collections, int(req.TopK))
	if err != nil {
		return nil, fmt.Errorf("knowledge search failed: %w", err)
	}

	// Convert results
	searchResults := make(map[string]*pb.SearchResultList)
	for collection, collectionResults := range results {
		var pbResults []*pb.SearchResult
		for _, result := range collectionResults {
			metadata, _ := common.Marshal(result.Metadata)
			pbResults = append(pbResults, &pb.SearchResult{
				Id:         result.ID,
				Content:    result.Content,
				Similarity: result.Similarity,
				Score:      result.Score,
				Metadata:   string(metadata),
			})
		}
		searchResults[collection] = &pb.SearchResultList{Results: pbResults}
	}

	return &pb.KnowledgeSearchResponse{
		Results: searchResults,
	}, nil
}

// GetEnhancedMetrics returns comprehensive AI metrics
func (s *EnhancedAIService) GetEnhancedMetrics(ctx context.Context, req *pb.MetricsRequest) (*pb.EnhancedMetricsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Enhanced metrics request")

	metrics := s.enhancedAI.GetEnhancedMetrics()

	return &pb.EnhancedMetricsResponse{
		TotalRequests:       metrics.TotalRequests,
		SuccessfulRequests:  metrics.SuccessfulRequests,
		FailedRequests:      metrics.FailedRequests,
		AvgResponseTime:     metrics.AvgResponseTime,
		AvgEmbeddingTime:    metrics.AvgEmbeddingTime,
		AvgVectorSearchTime: metrics.AvgVectorSearchTime,
		LangchainUsage:      metrics.LangChainUsage,
		VectorSearchUsage:   metrics.VectorSearchUsage,
		CacheHits:           metrics.CacheHits,
		CacheMisses:         metrics.CacheMisses,
		AvgConfidenceScore:  metrics.AvgConfidenceScore,
		HighConfidenceCount: metrics.HighConfidenceCount,
		LowConfidenceCount:  metrics.LowConfidenceCount,
	}, nil
}

// HealthCheck performs comprehensive health check
func (s *EnhancedAIService) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	s.log.WithContext(ctx).Info("🏥 Enhanced AI health check")

	err := s.enhancedAI.HealthCheck(ctx)
	if err != nil {
		return &pb.HealthCheckResponse{
			Status:  "unhealthy",
			Message: err.Error(),
		}, nil
	}

	return &pb.HealthCheckResponse{
		Status:  "healthy",
		Message: "All enhanced AI services are operational",
	}, nil
}
*/
