package biz

import (
	"errors"
	"testing"
	"time"

	kratosErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/stretchr/testify/assert"
)

func TestKratosErrors(t *testing.T) {
	assert.Equal(t, "CUSTOMER_NOT_FOUND", ErrCustomerNotFound.Reason)
	assert.Equal(t, "customer not found", ErrCustomerNotFound.Message)
	assert.Equal(t, int32(404), ErrCustomerNotFound.Code)

	assert.Equal(t, "CUSTOMER_NAME_REQUIRED", ErrCustomerNameRequired.Reason)
	assert.Equal(t, "customer name is required", ErrCustomerNameRequired.Message)
	assert.Equal(t, int32(400), ErrCustomerNameRequired.Code)

	assert.Equal(t, "JOB_NOT_FOUND", ErrJobNotFound.Reason)
	assert.Equal(t, "job not found", ErrJobNotFound.Message)
	assert.Equal(t, int32(404), ErrJobNotFound.Code)

	assert.Equal(t, "AI_MODEL_NOT_FOUND", ErrAIModelNotFound.Reason)
	assert.Equal(t, "AI model not found", ErrAIModelNotFound.Message)
	assert.Equal(t, int32(404), ErrAIModelNotFound.Code)

	assert.Equal(t, "EMAIL_SEND_FAILED", ErrEmailSendFailed.Reason)
	assert.Equal(t, "failed to send email", ErrEmailSendFailed.Message)
	assert.Equal(t, int32(500), ErrEmailSendFailed.Code)

	assert.Equal(t, "MCP_SERVER_UNAVAILABLE", ErrMCPServerUnavailable.Reason)
	assert.Equal(t, "MCP server is unavailable", ErrMCPServerUnavailable.Message)
	assert.Equal(t, int32(503), ErrMCPServerUnavailable.Code)
}

func TestCustomError_Error(t *testing.T) {
	err := &CustomError{Code: 500, Message: "Internal Error", Data: "some data"}
	expected := "code: 500, message: Internal Error, data: some data"
	assert.Equal(t, expected, err.Error())

	err2 := &CustomError{Code: 400, Message: "Bad Request"}
	expected2 := "code: 400, message: Bad Request, data: <nil>"
	assert.Equal(t, expected2, err2.Error())
}

func TestCustomError_WithContext(t *testing.T) {
	err := &CustomError{Code: 500, Message: "Internal Error"}
	errWithContext := err.WithContext("user_id", 123).WithContext("operation", "save")

	assert.NotNil(t, errWithContext.Context)
	assert.Equal(t, 123, errWithContext.Context["user_id"])
	assert.Equal(t, "save", errWithContext.Context["operation"])
	assert.Equal(t, err, errWithContext) // Should return the same error instance
}

func TestCustomError_WithRequestID(t *testing.T) {
	err := &CustomError{Code: 500, Message: "Internal Error"}
	errWithRequestID := err.WithRequestID("req-123")

	assert.Equal(t, "req-123", errWithRequestID.RequestID)
	assert.Equal(t, err, errWithRequestID) // Should return the same error instance
}

func TestNewCustomError(t *testing.T) {
	err := NewCustomError(404, "Not Found", nil)
	assert.Equal(t, 404, err.Code)
	assert.Equal(t, "Not Found", err.Message)
	assert.Nil(t, err.Data)
	assert.WithinDuration(t, time.Now(), err.Timestamp, time.Second)

	errWithData := NewCustomError(400, "Invalid Input", map[string]string{"field": "name"})
	assert.Equal(t, 400, errWithData.Code)
	assert.Equal(t, "Invalid Input", errWithData.Message)
	assert.Equal(t, map[string]string{"field": "name"}, errWithData.Data)
	assert.WithinDuration(t, time.Now(), errWithData.Timestamp, time.Second)
}

func TestPredefinedCommonErrors(t *testing.T) {
	assert.Equal(t, ErrCodeParseError, ErrParseError.Code)
	assert.Equal(t, "Parse error", ErrParseError.Message)
	assert.Equal(t, "Invalid JSON was received by the server", ErrParseError.Data)

	assert.Equal(t, ErrCodeInvalidRequest, ErrInvalidRequest.Code)
	assert.Equal(t, "Invalid Request", ErrInvalidRequest.Message)
	assert.Equal(t, "The JSON sent is not a valid Request object", ErrInvalidRequest.Data)

	assert.Equal(t, ErrCodeMethodNotFound, ErrMethodNotFound.Code)
	assert.Equal(t, "Method not found", ErrMethodNotFound.Message)
	assert.Equal(t, "The method does not exist / is not available", ErrMethodNotFound.Data)

	assert.Equal(t, ErrCodeInvalidParams, ErrInvalidParams.Code)
	assert.Equal(t, "Invalid params", ErrInvalidParams.Message)
	assert.Equal(t, "Invalid method parameter(s)", ErrInvalidParams.Data)

	assert.Equal(t, ErrCodeInternalError, ErrInternalError.Code)
	assert.Equal(t, "Internal error", ErrInternalError.Message)
	assert.Equal(t, "Internal JSON-RPC error", ErrInternalError.Data)
}

func TestErrorIs(t *testing.T) {
	// Test if errors.Is works with Kratos errors
	err := kratosErrors.NotFound("TEST_NOT_FOUND", "test not found")
	assert.True(t, errors.Is(err, kratosErrors.NotFound("TEST_NOT_FOUND", "")))
	assert.False(t, errors.Is(err, kratosErrors.BadRequest("TEST_BAD_REQUEST", "")))

	// Test if errors.Is works with CustomError
	customErr := NewCustomError(400, "Custom Bad Request", nil)
	assert.True(t, errors.Is(customErr, customErr)) // Should be true for the same instance

	// Test with a different instance but same values (errors.Is typically checks for wrapped errors or specific error types)
	// For CustomError, errors.Is will only return true if it's the exact same instance or if it wraps another error.
	// Since CustomError doesn't wrap, this will be false for different instances.
	anotherCustomErr := NewCustomError(400, "Custom Bad Request", nil)
	assert.False(t, errors.Is(customErr, anotherCustomErr))
}
