package biz

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
)

// Customer errors
var (
	ErrCustomerNotFound      = errors.NotFound("CUSTOMER_NOT_FOUND", "customer not found")
	ErrCustomerNameRequired  = errors.BadRequest("CUSTOMER_NAME_REQUIRED", "customer name is required")
	ErrCustomerEmailRequired = errors.BadRequest("CUSTOMER_EMAIL_REQUIRED", "customer email is required")
	ErrInvalidCustomerID     = errors.BadRequest("INVALID_CUSTOMER_ID", "invalid customer ID")
)

// Job errors
var (
	ErrJobNotFound        = errors.NotFound("JOB_NOT_FOUND", "job not found")
	ErrJobTitleRequired   = errors.BadRequest("JOB_TITLE_REQUIRED", "job title is required")
	ErrInvalidJobID       = errors.BadRequest("INVALID_JOB_ID", "invalid job ID")
	ErrInvalidJobStatus   = errors.BadRequest("INVALID_JOB_STATUS", "invalid job status")
	ErrInvalidJobPriority = errors.BadRequest("INVALID_JOB_PRIORITY", "invalid job priority")
)

// AI errors
var (
	ErrAIModelNotFound    = errors.NotFound("AI_MODEL_NOT_FOUND", "AI model not found")
	ErrAIModelUnavailable = errors.ServiceUnavailable("AI_MODEL_UNAVAILABLE", "AI model is unavailable")
	ErrInvalidAIRequest   = errors.BadRequest("INVALID_AI_REQUEST", "invalid AI request")
	ErrAIProcessingFailed = errors.InternalServer("AI_PROCESSING_FAILED", "AI processing failed")
)

// Email errors
var (
	ErrEmailSendFailed       = errors.InternalServer("EMAIL_SEND_FAILED", "failed to send email")
	ErrInvalidEmailAddress   = errors.BadRequest("INVALID_EMAIL_ADDRESS", "invalid email address")
	ErrEmailNotFound         = errors.NotFound("EMAIL_NOT_FOUND", "email not found")
	ErrEmailRequired         = errors.BadRequest("EMAIL_REQUIRED", "email is required")
	ErrEmailSubjectRequired  = errors.BadRequest("EMAIL_SUBJECT_REQUIRED", "email subject is required")
	ErrEmailFromRequired     = errors.BadRequest("EMAIL_FROM_REQUIRED", "email from address is required")
	ErrInvalidEmailID        = errors.BadRequest("INVALID_EMAIL_ID", "invalid email ID")
	ErrEmailAnalysisNotFound = errors.NotFound("EMAIL_ANALYSIS_NOT_FOUND", "email analysis not found")
	ErrEmailAnalysisFailed   = errors.InternalServer("EMAIL_ANALYSIS_FAILED", "email analysis failed")
	ErrAttachmentNotFound    = errors.NotFound("ATTACHMENT_NOT_FOUND", "attachment not found")
)

// MCP errors
var (
	ErrMCPServerUnavailable = errors.ServiceUnavailable("MCP_SERVER_UNAVAILABLE", "MCP server is unavailable")
	ErrMCPToolNotFound      = errors.NotFound("MCP_TOOL_NOT_FOUND", "MCP tool not found")
	ErrMCPInvalidRequest    = errors.BadRequest("MCP_INVALID_REQUEST", "invalid MCP request")
)

// Lead errors
var (
	ErrLeadNotFound            = errors.NotFound("LEAD_NOT_FOUND", "lead not found")
	ErrLeadNameRequired        = errors.BadRequest("LEAD_NAME_REQUIRED", "lead name is required")
	ErrLeadContactRequired     = errors.BadRequest("LEAD_CONTACT_REQUIRED", "lead email or phone is required")
	ErrInvalidLeadID           = errors.BadRequest("INVALID_LEAD_ID", "invalid lead ID")
	ErrLeadAlreadyExists       = errors.Conflict("LEAD_ALREADY_EXISTS", "lead with this email or phone already exists")
	ErrLeadImportFailed        = errors.InternalServer("LEAD_IMPORT_FAILED", "lead import failed")
	ErrLeadExportFailed        = errors.InternalServer("LEAD_EXPORT_FAILED", "lead export failed")
	ErrLeadDeduplicationFailed = errors.InternalServer("LEAD_DEDUPLICATION_FAILED", "lead deduplication failed")
	ErrLeadMergeFailed         = errors.InternalServer("LEAD_MERGE_FAILED", "lead merge failed")
)

// Campaign errors
var (
	ErrCampaignNotFound          = errors.NotFound("CAMPAIGN_NOT_FOUND", "campaign not found")
	ErrCampaignNameRequired      = errors.BadRequest("CAMPAIGN_NAME_REQUIRED", "campaign name is required")
	ErrInvalidCampaignID         = errors.BadRequest("INVALID_CAMPAIGN_ID", "invalid campaign ID")
	ErrCampaignAttributionFailed = errors.InternalServer("CAMPAIGN_ATTRIBUTION_FAILED", "campaign attribution failed")
)

// 🔧 Enhanced Error Handling - CustomError represents a comprehensive error structure
type CustomError struct {
	Code      int                    `json:"code"`
	Message   string                 `json:"message"`
	Data      interface{}            `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Stack     string                 `json:"stack,omitempty"`
}

// Error implements the error interface
func (e *CustomError) Error() string {
	return fmt.Sprintf("code: %d, message: %s, data: %+v", e.Code, e.Message, e.Data)
}

// WithContext adds context information to the error
func (e *CustomError) WithContext(key string, value interface{}) *CustomError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithRequestID adds request ID to the error
func (e *CustomError) WithRequestID(requestID string) *CustomError {
	e.RequestID = requestID
	return e
}

// NewCustomError creates a new CustomError with timestamp
func NewCustomError(code int, message string, data interface{}) *CustomError {
	return &CustomError{
		Code:      code,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// Common error codes for tRPC
const (
	// Standard JSON-RPC 2.0 error codes
	ErrCodeParseError     = -32700
	ErrCodeInvalidRequest = -32600
	ErrCodeMethodNotFound = -32601
	ErrCodeInvalidParams  = -32602
	ErrCodeInternalError  = -32603

	// Custom application error codes
	ErrCodeValidationFailed   = -32000
	ErrCodeUnauthorized       = -32001
	ErrCodeForbidden          = -32002
	ErrCodeNotFound           = -32003
	ErrCodeConflict           = -32004
	ErrCodeRateLimit          = -32005
	ErrCodeServiceUnavailable = -32006
	ErrCodeCircuitBreakerOpen = -32007
)

// Predefined common errors
var (
	ErrParseError     = NewCustomError(ErrCodeParseError, "Parse error", "Invalid JSON was received by the server")
	ErrInvalidRequest = NewCustomError(ErrCodeInvalidRequest, "Invalid Request", "The JSON sent is not a valid Request object")
	ErrMethodNotFound = NewCustomError(ErrCodeMethodNotFound, "Method not found", "The method does not exist / is not available")
	ErrInvalidParams  = NewCustomError(ErrCodeInvalidParams, "Invalid params", "Invalid method parameter(s)")
	ErrInternalError  = NewCustomError(ErrCodeInternalError, "Internal error", "Internal JSON-RPC error")
)
