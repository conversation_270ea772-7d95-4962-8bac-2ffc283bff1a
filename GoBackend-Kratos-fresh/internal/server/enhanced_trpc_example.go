package server

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"

	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/service"
)

// 🚀 Enhanced TRPCAdapter - Example implementation with all improvements
type EnhancedTRPCAdapter struct {
	hvacService      *service.HVACService
	aiService        *service.AIService
	analyticsService *service.AnalyticsService
	workflowService  *service.WorkflowService
	logger           *log.Helper
	zapLogger        *zap.Logger
	validator        *ValidationService
	circuitBreaker   *CircuitBreakerService
}

// NewEnhancedTRPCAdapter creates a new enhanced tRPC adapter with all improvements
func NewEnhancedTRPCAdapter(
	hvacService *service.HVACService,
	aiService *service.AIService,
	analyticsService *service.AnalyticsService,
	workflowService *service.WorkflowService,
	logger log.Logger,
) *EnhancedTRPCAdapter {
	// Initialize zap logger
	zapLogger, _ := zap.NewProduction()

	// Initialize validation service
	validator := NewValidationService(logger)

	// Initialize circuit breaker service
	circuitBreaker := NewCircuitBreakerService(logger)

	return &EnhancedTRPCAdapter{
		hvacService:      hvacService,
		aiService:        aiService,
		analyticsService: analyticsService,
		workflowService:  workflowService,
		logger:           log.NewHelper(logger),
		zapLogger:        zapLogger,
		validator:        validator,
		circuitBreaker:   circuitBreaker,
	}
}

// Enhanced request validation structure
type ValidatedCustomerRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"required,min=10"`
	Type     string `json:"type" validate:"required,customer_type"`
	Priority string `json:"priority" validate:"omitempty,hvac_priority"`
	Address  string `json:"address" validate:"required,min=10,max=500"`
}

// handleEnhancedCustomerRequest demonstrates the enhanced customer request handling
func (t *EnhancedTRPCAdapter) handleEnhancedCustomerRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	// Add request ID to context for tracking
	requestID := fmt.Sprintf("req-%d", time.Now().UnixNano())
	ctx = context.WithValue(ctx, "requestID", requestID)

	t.zapLogger.Info("Processing enhanced customer request",
		zap.String("method", method),
		zap.String("request_id", requestID),
		zap.Time("timestamp", time.Now()),
	)

	switch method {
	case "createCustomer":
		return t.handleCreateCustomerEnhanced(ctx, id, params, requestID)
	case "getCustomer":
		return t.handleGetCustomerEnhanced(ctx, id, params, requestID)
	case "updateCustomer":
		return t.handleUpdateCustomerEnhanced(ctx, id, params, requestID)
	case "deleteCustomer":
		return t.handleDeleteCustomerEnhanced(ctx, id, params, requestID)
	default:
		customErr := biz.NewCustomError(biz.ErrCodeMethodNotFound, "Method not found", method)
		customErr.WithRequestID(requestID)
		return t.createErrorResponse(id, customErr)
	}
}

// handleCreateCustomerEnhanced demonstrates enhanced customer creation with validation and circuit breaker
func (t *EnhancedTRPCAdapter) handleCreateCustomerEnhanced(ctx context.Context, id interface{}, params interface{}, requestID string) TRPCResponse {
	// Step 1: Parse and validate request
	var validatedReq ValidatedCustomerRequest
	if err := t.parseAndValidateParams(ctx, params, &validatedReq); err != nil {
		err.WithRequestID(requestID)
		return t.createErrorResponse(id, err)
	}

	// Step 2: Convert to service request
	serviceReq := &hvacv1.CreateCustomerRequest{
		Name:    validatedReq.Name,
		Email:   validatedReq.Email,
		Phone:   validatedReq.Phone,
		Address: validatedReq.Address,
	}

	// Step 3: Execute through circuit breaker
	result, err := t.circuitBreaker.Execute(ctx, "customer-service", func() (interface{}, error) {
		return t.hvacService.CreateCustomer(ctx, serviceReq)
	})

	if err != nil {
		err.WithRequestID(requestID).WithContext("method", "createCustomer")
		t.zapLogger.Error("Failed to create customer",
			zap.String("request_id", requestID),
			zap.Error(fmt.Errorf(err.Error())),
			zap.Any("request", validatedReq),
		)
		return t.createErrorResponse(id, err)
	}

	// Step 4: Success response
	customer := result.(*hvacv1.CreateCustomerResponse).Customer
	t.zapLogger.Info("Customer created successfully",
		zap.String("request_id", requestID),
		zap.Int64("customer_id", customer.Id),
		zap.String("customer_name", customer.Name),
	)

	respData, _ := json.Marshal(customer)
	return TRPCResponse{Result: respData}
}

// handleGetCustomerEnhanced demonstrates enhanced customer retrieval
func (t *EnhancedTRPCAdapter) handleGetCustomerEnhanced(ctx context.Context, id interface{}, params interface{}, requestID string) TRPCResponse {
	// Simple validation for get request
	var req hvacv1.GetCustomerRequest
	if err := t.parseParams(params, &req); err != nil {
		customErr := biz.NewCustomError(biz.ErrCodeInvalidParams, "Invalid parameters", err.Error())
		customErr.WithRequestID(requestID)
		return t.createErrorResponse(id, customErr)
	}

	// Validate customer ID
	if req.Id == 0 {
		customErr := biz.NewCustomError(biz.ErrCodeInvalidParams, "Customer ID is required", "id field cannot be empty")
		customErr.WithRequestID(requestID)
		return t.createErrorResponse(id, customErr)
	}

	// Execute through circuit breaker
	result, err := t.circuitBreaker.Execute(ctx, "customer-service", func() (interface{}, error) {
		return t.hvacService.GetCustomer(ctx, &req)
	})

	if err != nil {
		err.WithRequestID(requestID).WithContext("method", "getCustomer").WithContext("customer_id", req.Id)
		return t.createErrorResponse(id, err)
	}

	customer := result.(*hvacv1.GetCustomerResponse).Customer
	t.zapLogger.Info("Customer retrieved successfully",
		zap.String("request_id", requestID),
		zap.Int64("customer_id", customer.Id),
	)

	respData, _ := json.Marshal(customer)
	return TRPCResponse{Result: respData}
}

// handleUpdateCustomerEnhanced demonstrates enhanced customer update
// NOTE: UpdateCustomer is not implemented in the current protobuf definition
func (t *EnhancedTRPCAdapter) handleUpdateCustomerEnhanced(ctx context.Context, id interface{}, params interface{}, requestID string) TRPCResponse {
	// TODO: Implement when UpdateCustomerRequest is added to protobuf
	customErr := biz.NewCustomError(biz.ErrCodeMethodNotFound, "UpdateCustomer not implemented", "method not available in current API")
	customErr.WithRequestID(requestID)
	return t.createErrorResponse(id, customErr)
}

// handleDeleteCustomerEnhanced demonstrates enhanced customer deletion
// NOTE: DeleteCustomer is not implemented in the current protobuf definition
func (t *EnhancedTRPCAdapter) handleDeleteCustomerEnhanced(ctx context.Context, id interface{}, params interface{}, requestID string) TRPCResponse {
	// TODO: Implement when DeleteCustomerRequest is added to protobuf
	customErr := biz.NewCustomError(biz.ErrCodeMethodNotFound, "DeleteCustomer not implemented", "method not available in current API")
	customErr.WithRequestID(requestID)
	return t.createErrorResponse(id, customErr)
}

// parseAndValidateParams combines parsing and validation in one step
func (t *EnhancedTRPCAdapter) parseAndValidateParams(ctx context.Context, params interface{}, target interface{}) *biz.CustomError {
	// Parse parameters
	if err := t.parseParams(params, target); err != nil {
		return biz.NewCustomError(biz.ErrCodeInvalidParams, "Failed to parse parameters", err.Error())
	}

	// Validate parsed parameters
	if err := t.validator.ValidateStruct(ctx, target); err != nil {
		return err
	}

	return nil
}

// parseParams helper method (same as original)
func (t *EnhancedTRPCAdapter) parseParams(params interface{}, target interface{}) error {
	jsonData, err := json.Marshal(params)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, target)
}

// createErrorResponse creates a standardized error response
func (t *EnhancedTRPCAdapter) createErrorResponse(id interface{}, customErr *biz.CustomError) TRPCResponse {
	return TRPCResponse{
		Error: &TRPCError{
			Code:    customErr.Code,
			Message: customErr.Message,
		},
	}
}

// GetHealthStatus returns the health status of all services
func (t *EnhancedTRPCAdapter) GetHealthStatus() map[string]interface{} {
	return t.circuitBreaker.GetHealthStatus()
}

// Cleanup cleans up all resources
func (t *EnhancedTRPCAdapter) Cleanup() {
	t.logger.Info("🧹 Cleaning up enhanced tRPC adapter")

	if t.circuitBreaker != nil {
		t.circuitBreaker.Cleanup()
	}

	if t.zapLogger != nil {
		t.zapLogger.Sync()
	}
}
