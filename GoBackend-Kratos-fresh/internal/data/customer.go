package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gobackend-hvac-kratos/internal/biz"
)

// Customer represents the database model
type Customer struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name      string    `gorm:"not null" json:"name"`
	Email     string    `gorm:"uniqueIndex;not null" json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// CustomerRepo implements the customer repository
type CustomerRepo struct {
	data *Data
	log  *log.Helper
}

// NewCustomerRepo creates a new customer repository
func NewCustomerRepo(data *Data, logger log.Logger) biz.CustomerRepo {
	return &CustomerRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateCustomer creates a new customer in the database
func (r *CustomerRepo) CreateCustomer(ctx context.Context, customer *biz.Customer) (*biz.Customer, error) {
	dbCustomer := &Customer{
		Name:    customer.Name,
		Email:   customer.Email,
		Phone:   customer.Phone,
		Address: customer.Address,
	}
	
	if err := r.data.db.WithContext(ctx).Create(dbCustomer).Error; err != nil {
		return nil, err
	}
	
	return r.convertToBiz(dbCustomer), nil
}

// GetCustomer retrieves a customer by ID
func (r *CustomerRepo) GetCustomer(ctx context.Context, id int64) (*biz.Customer, error) {
	var customer Customer
	if err := r.data.db.WithContext(ctx).First(&customer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrCustomerNotFound
		}
		return nil, err
	}
	
	return r.convertToBiz(&customer), nil
}// ListCustomers retrieves customers with pagination
func (r *CustomerRepo) ListCustomers(ctx context.Context, page, pageSize int32) ([]*biz.Customer, int32, error) {
	var customers []Customer
	var total int64
	
	// Count total records
	if err := r.data.db.WithContext(ctx).Model(&Customer{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Get paginated results
	offset := (page - 1) * pageSize
	if err := r.data.db.WithContext(ctx).
		Offset(int(offset)).
		Limit(int(pageSize)).
		Find(&customers).Error; err != nil {
		return nil, 0, err
	}
	
	// Convert to business entities
	bizCustomers := make([]*biz.Customer, len(customers))
	for i, customer := range customers {
		bizCustomers[i] = r.convertToBiz(&customer)
	}
	
	return bizCustomers, int32(total), nil
}

// UpdateCustomer updates an existing customer
func (r *CustomerRepo) UpdateCustomer(ctx context.Context, customer *biz.Customer) (*biz.Customer, error) {
	dbCustomer := &Customer{
		ID:      customer.ID,
		Name:    customer.Name,
		Email:   customer.Email,
		Phone:   customer.Phone,
		Address: customer.Address,
	}
	
	if err := r.data.db.WithContext(ctx).Save(dbCustomer).Error; err != nil {
		return nil, err
	}
	
	return r.convertToBiz(dbCustomer), nil
}

// DeleteCustomer deletes a customer by ID
func (r *CustomerRepo) DeleteCustomer(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&Customer{}, id).Error
}

// convertToBiz converts database model to business entity
func (r *CustomerRepo) convertToBiz(customer *Customer) *biz.Customer {
	return &biz.Customer{
		ID:        customer.ID,
		Name:      customer.Name,
		Email:     customer.Email,
		Phone:     customer.Phone,
		Address:   customer.Address,
		CreatedAt: customer.CreatedAt,
		UpdatedAt: customer.UpdatedAt,
	}
}