package data

import (
	"time"
)

// Lead represents a lead in the system (GORM model)
type Lead struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name      string    `gorm:"type:varchar(255);not null" json:"name"`
	Email     string    `gorm:"type:varchar(255);unique" json:"email"`
	Phone     string    `gorm:"type:varchar(50)" json:"phone"`
	Status    string    `gorm:"type:varchar(50);not null;default:'New'" json:"status"`
	Source    string    `gorm:"type:varchar(255)" json:"source"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// Campaign represents a marketing campaign (GORM model)
type Campaign struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name      string    `gorm:"type:varchar(255);not null" json:"name"`
	Source    string    `gorm:"type:varchar(255)" json:"source"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// LeadCampaignInteraction represents an interaction between a lead and a campaign (GORM model)
type LeadCampaignInteraction struct {
	ID             int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	LeadID         int64     `gorm:"not null" json:"lead_id"`
	CampaignID     int64     `gorm:"not null" json:"campaign_id"`
	EventType      string    `gorm:"type:varchar(50);not null" json:"event_type"`
	EventTimestamp time.Time `json:"event_timestamp"`
	CreatedAt      time.Time `gorm:"autoCreateTime" json:"created_at"`

	// GORM associations
	Lead     Lead     `gorm:"foreignKey:LeadID"`
	Campaign Campaign `gorm:"foreignKey:CampaignID"`
}
