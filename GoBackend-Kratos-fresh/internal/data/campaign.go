package data

import (
	"context"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// CampaignRepo implements the campaign repository
type CampaignRepo struct {
	data *Data
	log  *log.Helper
}

// NewCampaignRepo creates a new campaign repository
func NewCampaignRepo(data *Data, logger log.Logger) biz.CampaignRepo {
	return &CampaignRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateCampaign creates a new campaign in the database
func (r *CampaignRepo) CreateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error) {
	dbCampaign := &Campaign{ // This Campaign refers to data.Campaign (GORM model)
		Name:      campaign.Name,
		Source:    campaign.Source,
		StartDate: campaign.StartDate,
		EndDate:   campaign.EndDate,
	}

	if err := r.data.db.WithContext(ctx).Create(dbCampaign).Error; err != nil {
		return nil, err
	}

	return r.convertTo<PERSON><PERSON>(dbCampaign), nil
}

// GetCampaign retrieves a campaign by ID
func (r *CampaignRepo) GetCampaign(ctx context.Context, id int64) (*entity.Campaign, error) {
	var campaign Campaign // This Campaign refers to data.Campaign (GORM model)
	if err := r.data.db.WithContext(ctx).First(&campaign, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrCampaignNotFound
		}
		return nil, err
	}

	return r.convertToBiz(&campaign), nil
}

// ListCampaigns retrieves campaigns with pagination
func (r *CampaignRepo) ListCampaigns(ctx context.Context, page, pageSize int32) ([]*entity.Campaign, int32, error) {
	var campaigns []Campaign // This Campaign refers to data.Campaign (GORM model)
	var total int64
	query := r.data.db.WithContext(ctx).Model(&Campaign{}) // This Campaign refers to data.Campaign (GORM model)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := query.Offset(int(offset)).Limit(int(pageSize)).Find(&campaigns).Error; err != nil {
		return nil, 0, err
	}

	// Convert to business entities
	bizCampaigns := make([]*entity.Campaign, len(campaigns))
	for i, campaign := range campaigns {
		bizCampaigns[i] = r.convertToBiz(&campaign)
	}

	return bizCampaigns, int32(total), nil
}

// UpdateCampaign updates an existing campaign
func (r *CampaignRepo) UpdateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error) {
	dbCampaign := &Campaign{ // This Campaign refers to data.Campaign (GORM model)
		ID:        campaign.ID,
		Name:      campaign.Name,
		Source:    campaign.Source,
		StartDate: campaign.StartDate,
		EndDate:   campaign.EndDate,
	}

	if err := r.data.db.WithContext(ctx).Save(dbCampaign).Error; err != nil {
		return nil, err
	}

	return r.convertToBiz(dbCampaign), nil
}

// DeleteCampaign deletes a campaign by ID
func (r *CampaignRepo) DeleteCampaign(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&Campaign{}, id).Error // This Campaign refers to data.Campaign (GORM model)
}

// TrackCampaignInteraction tracks an interaction between a lead and a campaign
func (r *CampaignRepo) TrackCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) error {
	dbInteraction := &LeadCampaignInteraction{ // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
		LeadID:         interaction.LeadID,
		CampaignID:     interaction.CampaignID,
		EventType:      interaction.EventType,
		EventTimestamp: interaction.EventTimestamp,
	}
	return r.data.db.WithContext(ctx).Create(dbInteraction).Error
}

// GetCampaignInteractions retrieves interactions for a given campaign
func (r *CampaignRepo) GetCampaignInteractions(ctx context.Context, campaignID int64) ([]*entity.LeadCampaignInteraction, error) {
	var interactions []LeadCampaignInteraction // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
	if err := r.data.db.WithContext(ctx).Where("campaign_id = ?", campaignID).Find(&interactions).Error; err != nil {
		return nil, err
	}

	bizInteractions := make([]*entity.LeadCampaignInteraction, len(interactions))
	for i, interaction := range interactions {
		bizInteractions[i] = r.convertToBizInteraction(&interaction)
	}
	return bizInteractions, nil
}

// convertToBiz converts database model to business entity
func (r *CampaignRepo) convertToBiz(campaign *Campaign) *entity.Campaign { // This Campaign refers to data.Campaign (GORM model)
	return &entity.Campaign{
		ID:        campaign.ID,
		Name:      campaign.Name,
		Source:    campaign.Source,
		StartDate: campaign.StartDate,
		EndDate:   campaign.EndDate,
		CreatedAt: campaign.CreatedAt,
		UpdatedAt: campaign.UpdatedAt,
	}
}

// convertToBizInteraction converts database model to business entity
func (r *CampaignRepo) convertToBizInteraction(interaction *LeadCampaignInteraction) *entity.LeadCampaignInteraction {
	return &entity.LeadCampaignInteraction{
		ID:             interaction.ID,
		LeadID:         interaction.LeadID,
		CampaignID:     interaction.CampaignID,
		EventType:      interaction.EventType,
		EventTimestamp: interaction.EventTimestamp,
		CreatedAt:      interaction.CreatedAt,
	}
}
