package transcription

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
)

// 📞 Enhanced Transcription Parser - Advanced AI-powered call analysis with Gemma awareness
type TranscriptionParser struct {
	log                    *log.Helper
	gemma3                 *ai.Gemma3Service
	enhancedGemma         *ai.EnhancedGemmaService
	customerService       *customer.CustomerIntelligenceService
	config                *TranscriptionConfig
	contextManager        *TranscriptionContextManager
	qualityAssurance      *TranscriptionQuality
	performanceMonitor    *TranscriptionPerformance
	adaptiveProcessor     *AdaptiveProcessor
	limitationHandler     *LimitationHandler
	mutex                 sync.RWMutex
	isActive              bool
}

// ⚙️ Transcription Configuration
type TranscriptionConfig struct {
	SupportedProviders []string `yaml:"supported_providers"` // whisper, google, azure, rev, otter
	DefaultLanguage    string   `yaml:"default_language"`    // en
	MinConfidence      float64  `yaml:"min_confidence"`      // 0.7
	MaxDuration        int      `yaml:"max_duration"`        // 3600 seconds (1 hour)
	EnableAIAnalysis   bool     `yaml:"enable_ai_analysis"`
}

// 📧 Transcription Email Structure
type TranscriptionEmail struct {
	// Call Metadata
	PhoneNumber   string        `json:"phone_number"`
	CallDirection string        `json:"call_direction"` // inbound, outbound
	CallDuration  time.Duration `json:"call_duration"`
	CallTimestamp time.Time     `json:"call_timestamp"`
	CallStatus    string        `json:"call_status"` // completed, missed, voicemail

	// Transcription Data
	Transcription string  `json:"transcription"`
	Confidence    float64 `json:"confidence"`
	Language      string  `json:"language"`
	Provider      string  `json:"provider"`

	// Caller Information
	CallerID     string `json:"caller_id,omitempty"`
	CallerName   string `json:"caller_name,omitempty"`
	CallerEmail  string `json:"caller_email,omitempty"`
	CallerCompany string `json:"caller_company,omitempty"`

	// Parsed Analysis
	CallPurpose    string   `json:"call_purpose,omitempty"`
	UrgencyLevel   string   `json:"urgency_level,omitempty"`
	ActionItems    []string `json:"action_items,omitempty"`
	FollowUpNeeded bool     `json:"follow_up_needed"`

	// Business Intelligence
	HVACRelevance      bool     `json:"hvac_relevance"`
	ServiceType        string   `json:"service_type,omitempty"`
	EquipmentMentioned []string `json:"equipment_mentioned,omitempty"`
	EstimatedValue     float64  `json:"estimated_value,omitempty"`

	// Source Information
	SourceEmailID string `json:"source_email_id"`
	SourceSubject string `json:"source_subject"`
}

// 🔍 Call Analysis Result
type CallAnalysis struct {
	// Core Analysis
	CallPurpose      string   `json:"call_purpose"`      // emergency, quote_request, follow_up, complaint, information
	UrgencyLevel     string   `json:"urgency_level"`     // critical, high, medium, low
	TechnicalIssues  []string `json:"technical_issues"`
	ServiceRequests  []string `json:"service_requests"`

	// Customer Intelligence
	CustomerMood     string   `json:"customer_mood"`     // frustrated, satisfied, neutral, angry, confused
	SatisfactionLevel string  `json:"satisfaction_level"` // very_satisfied, satisfied, neutral, dissatisfied, very_dissatisfied
	KnowledgeLevel   string   `json:"knowledge_level"`   // expert, intermediate, basic, novice

	// Business Value
	BusinessValue    string   `json:"business_value"`    // high, medium, low
	SalesOpportunity bool     `json:"sales_opportunity"`
	UpsellPotential  string   `json:"upsell_potential"`  // high, medium, low, none

	// Action Planning
	NextActions      []string   `json:"next_actions"`
	FollowUpDate     *time.Time `json:"follow_up_date,omitempty"`
	AssignedTo       string     `json:"assigned_to,omitempty"`
	Priority         int        `json:"priority"` // 1-10 scale
}

// 📋 Context Item - Individual context item for priority queue
type ContextItem struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Content     string                 `json:"content"`
	Priority    float64               `json:"priority"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// 📸 Context Snapshot - Historical context snapshot
type ContextSnapshot struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Context     string                 `json:"context"`
	TokenCount  int                    `json:"token_count"`
	Quality     float64               `json:"quality"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// 🚨 Error Pattern - Pattern for error detection
type ErrorPattern struct {
	ID          string    `json:"id"`
	Pattern     string    `json:"pattern"`
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// 🔧 Auto Fix Rule - Rule for automatic error fixing
type AutoFixRule struct {
	ID          string    `json:"id"`
	Pattern     string    `json:"pattern"`
	Replacement string    `json:"replacement"`
	Type        string    `json:"type"`
	IsEnabled   bool      `json:"is_enabled"`
	CreatedAt   time.Time `json:"created_at"`
}

// 📊 Error Event - Error occurrence event
type ErrorEvent struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Message     string                 `json:"message"`
	Severity    string                 `json:"severity"`
	Timestamp   time.Time              `json:"timestamp"`
	Context     string                 `json:"context"`
	Fixed       bool                   `json:"fixed"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// 💡 Optimization Suggestion - Performance optimization suggestion
type OptimizationSuggestion struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Impact      string    `json:"impact"`
	Effort      string    `json:"effort"`
	Priority    float64   `json:"priority"`
	CreatedAt   time.Time `json:"created_at"`
}

// 📈 Performance Snapshot - Performance metrics snapshot
type PerformanceSnapshot struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Metrics     map[string]float64     `json:"metrics"`
	Quality     float64               `json:"quality"`
	Throughput  float64               `json:"throughput"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// 🔧 Resolution Strategy - Strategy for resolving bottlenecks
type ResolutionStrategy struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Steps       []string  `json:"steps"`
	Impact      float64   `json:"impact"`
	Effort      string    `json:"effort"`
	CreatedAt   time.Time `json:"created_at"`
}

// 🔄 Adaptation Rule - Rule for adaptive processing
type AdaptationRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Condition   string                 `json:"condition"`
	Action      string                 `json:"action"`
	Priority    float64               `json:"priority"`
	IsEnabled   bool                   `json:"is_enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
}

// 🔧 Processing Step - Individual processing step
type ProcessingStep struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Order       int                    `json:"order"`
	IsRequired  bool                   `json:"is_required"`
	Parameters  map[string]interface{} `json:"parameters"`
	CreatedAt   time.Time              `json:"created_at"`
}

// 🧠 Transcription Context Manager - Intelligent context handling for Gemma
type TranscriptionContextManager struct {
	ContextWindows        []*TranscriptionContext `json:"context_windows"`
	MaxContextLength      int                     `json:"max_context_length"`
	OptimalContextLength  int                     `json:"optimal_context_length"`
	ContextCompressionRatio float64              `json:"context_compression_ratio"`
	PriorityQueue         []*ContextItem          `json:"priority_queue"`
	ContextHistory        []*ContextSnapshot      `json:"context_history"`
	AdaptiveAdjustment    bool                    `json:"adaptive_adjustment"`
	LastOptimization      time.Time               `json:"last_optimization"`
}

// 📋 Transcription Context
type TranscriptionContext struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"` // call_metadata, transcription, analysis, customer_history
	Content               string                 `json:"content"`
	Priority              float64               `json:"priority"`
	TokenCount            int                    `json:"token_count"`
	Relevance             float64               `json:"relevance"`
	CreatedAt             time.Time              `json:"created_at"`
	LastAccessed          time.Time              `json:"last_accessed"`
	AccessCount           int                    `json:"access_count"`
	Metadata              map[string]interface{} `json:"metadata"`
}

// ✅ Transcription Quality - Quality assurance for transcription processing
type TranscriptionQuality struct {
	QualityMetrics        *QualityMetrics        `json:"quality_metrics"`
	QualityRules          []*QualityRule         `json:"quality_rules"`
	QualityHistory        []*QualityEvent        `json:"quality_history"`
	ValidationRules       []*ValidationRule      `json:"validation_rules"`
	ErrorDetection        *ErrorDetection        `json:"error_detection"`
	QualityThresholds     map[string]float64     `json:"quality_thresholds"`
	AutoCorrection        bool                   `json:"auto_correction"`
	LastQualityCheck      time.Time              `json:"last_quality_check"`
}

// 📊 Quality Metrics
type QualityMetrics struct {
	TranscriptionAccuracy float64               `json:"transcription_accuracy"`
	AnalysisAccuracy      float64               `json:"analysis_accuracy"`
	ProcessingSpeed       time.Duration          `json:"processing_speed"`
	ErrorRate             float64               `json:"error_rate"`
	ConfidenceScore       float64               `json:"confidence_score"`
	ConsistencyScore      float64               `json:"consistency_score"`
	CompletenessScore     float64               `json:"completeness_score"`
	RelevanceScore        float64               `json:"relevance_score"`
	OverallQuality        float64               `json:"overall_quality"`
	LastUpdated           time.Time              `json:"last_updated"`
}

// 📏 Quality Rule
type QualityRule struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	Type                  string                 `json:"type"` // accuracy, completeness, consistency, relevance
	Condition             string                 `json:"condition"`
	Threshold             float64               `json:"threshold"`
	Severity              string                 `json:"severity"`
	Action                string                 `json:"action"`
	IsEnabled             bool                   `json:"is_enabled"`
	ViolationCount        int64                  `json:"violation_count"`
	LastViolation         time.Time              `json:"last_violation"`
}

// 📊 Quality Event
type QualityEvent struct {
	Timestamp             time.Time              `json:"timestamp"`
	Type                  string                 `json:"type"`
	Quality               float64               `json:"quality"`
	Issues                []string               `json:"issues"`
	Improvements          []string               `json:"improvements"`
	TranscriptionID       string                 `json:"transcription_id"`
	ProcessingTime        time.Duration          `json:"processing_time"`
}

// ✅ Validation Rule
type ValidationRule struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	Field                 string                 `json:"field"`
	Type                  string                 `json:"type"` // required, format, range, pattern
	Pattern               string                 `json:"pattern"`
	MinValue              interface{}            `json:"min_value"`
	MaxValue              interface{}            `json:"max_value"`
	IsRequired            bool                   `json:"is_required"`
	ErrorMessage          string                 `json:"error_message"`
	IsEnabled             bool                   `json:"is_enabled"`
}

// 🚨 Error Detection
type ErrorDetection struct {
	DetectionRules        []*ErrorRule           `json:"detection_rules"`
	CommonErrors          map[string]int64       `json:"common_errors"`
	ErrorPatterns         []*ErrorPattern        `json:"error_patterns"`
	AutoFixRules          []*AutoFixRule         `json:"auto_fix_rules"`
	ErrorHistory          []*ErrorEvent          `json:"error_history"`
	IsEnabled             bool                   `json:"is_enabled"`
	DetectionAccuracy     float64               `json:"detection_accuracy"`
}

// 🚫 Error Rule
type ErrorRule struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	Type                  string                 `json:"type"` // transcription, analysis, format, logic
	Pattern               string                 `json:"pattern"`
	Severity              string                 `json:"severity"`
	AutoFix               bool                   `json:"auto_fix"`
	FixAction             string                 `json:"fix_action"`
	IsEnabled             bool                   `json:"is_enabled"`
	DetectionCount        int64                  `json:"detection_count"`
}

// 📈 Transcription Performance - Performance monitoring and optimization
type TranscriptionPerformance struct {
	PerformanceMetrics    *PerformanceMetrics    `json:"performance_metrics"`
	ProcessingTimes       []time.Duration        `json:"processing_times"`
	ThroughputMetrics     *ThroughputMetrics     `json:"throughput_metrics"`
	ResourceUtilization   *ResourceUtilization   `json:"resource_utilization"`
	BottleneckAnalysis    *BottleneckAnalysis    `json:"bottleneck_analysis"`
	OptimizationSuggestions []*OptimizationSuggestion `json:"optimization_suggestions"`
	PerformanceHistory    []*PerformanceSnapshot `json:"performance_history"`
	IsMonitoring          bool                   `json:"is_monitoring"`
	LastOptimization      time.Time              `json:"last_optimization"`
}

// 📊 Performance Metrics
type PerformanceMetrics struct {
	AverageProcessingTime time.Duration          `json:"average_processing_time"`
	MedianProcessingTime  time.Duration          `json:"median_processing_time"`
	P95ProcessingTime     time.Duration          `json:"p95_processing_time"`
	P99ProcessingTime     time.Duration          `json:"p99_processing_time"`
	TotalProcessed        int64                  `json:"total_processed"`
	SuccessRate           float64               `json:"success_rate"`
	ErrorRate             float64               `json:"error_rate"`
	ThroughputPerSecond   float64               `json:"throughput_per_second"`
	ThroughputPerMinute   float64               `json:"throughput_per_minute"`
	ThroughputPerHour     float64               `json:"throughput_per_hour"`
	LastUpdated           time.Time              `json:"last_updated"`
}

// 🚀 Throughput Metrics
type ThroughputMetrics struct {
	CurrentThroughput     float64               `json:"current_throughput"`
	PeakThroughput        float64               `json:"peak_throughput"`
	AverageThroughput     float64               `json:"average_throughput"`
	ThroughputTrend       []float64             `json:"throughput_trend"`
	CapacityUtilization   float64               `json:"capacity_utilization"`
	MaxCapacity           float64               `json:"max_capacity"`
	BottleneckFactor      float64               `json:"bottleneck_factor"`
}

// 💻 Resource Utilization
type ResourceUtilization struct {
	CPUUsage              float64               `json:"cpu_usage"`
	MemoryUsage           float64               `json:"memory_usage"`
	GemmaModelUsage       float64               `json:"gemma_model_usage"`
	ContextWindowUsage    float64               `json:"context_window_usage"`
	NetworkUsage          float64               `json:"network_usage"`
	DiskUsage             float64               `json:"disk_usage"`
	ResourceEfficiency    float64               `json:"resource_efficiency"`
	OptimizationPotential float64               `json:"optimization_potential"`
}

// 🔍 Bottleneck Analysis
type BottleneckAnalysis struct {
	IdentifiedBottlenecks []*Bottleneck          `json:"identified_bottlenecks"`
	BottleneckImpact      map[string]float64     `json:"bottleneck_impact"`
	ResolutionStrategies  []*ResolutionStrategy  `json:"resolution_strategies"`
	PerformanceGains      map[string]float64     `json:"performance_gains"`
	LastAnalysis          time.Time              `json:"last_analysis"`
	AnalysisFrequency     time.Duration          `json:"analysis_frequency"`
}

// 🚧 Bottleneck
type Bottleneck struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"` // cpu, memory, network, model, context
	Severity              string                 `json:"severity"`
	Impact                float64               `json:"impact"`
	Description           string                 `json:"description"`
	DetectedAt            time.Time              `json:"detected_at"`
	IsResolved            bool                   `json:"is_resolved"`
	ResolutionTime        *time.Time             `json:"resolution_time"`
}

// 🔧 Adaptive Processor - Adaptive processing based on content and context
type AdaptiveProcessor struct {
	ProcessingStrategies  []*ProcessingStrategy  `json:"processing_strategies"`
	AdaptationRules       []*AdaptationRule      `json:"adaptation_rules"`
	ContextAnalyzer       *ContextAnalyzer       `json:"context_analyzer"`
	ContentClassifier     *ContentClassifier     `json:"content_classifier"`
	DynamicOptimization   *DynamicOptimization   `json:"dynamic_optimization"`
	LearningEngine        *LearningEngine        `json:"learning_engine"`
	AdaptationHistory     []*AdaptationEvent     `json:"adaptation_history"`
	IsAdaptive            bool                   `json:"is_adaptive"`
	LastAdaptation        time.Time              `json:"last_adaptation"`
}

// 🧠 Context Analyzer
type ContextAnalyzer struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	AnalysisRules         []string               `json:"analysis_rules"`
	IsActive              bool                   `json:"is_active"`
}

// 📊 Content Classifier
type ContentClassifier struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	ClassificationRules   []string               `json:"classification_rules"`
	IsActive              bool                   `json:"is_active"`
}

// ⚡ Dynamic Optimization
type DynamicOptimization struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	OptimizationRules     []string               `json:"optimization_rules"`
	IsActive              bool                   `json:"is_active"`
}

// 🎓 Learning Engine
type LearningEngine struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	LearningRules         []string               `json:"learning_rules"`
	IsActive              bool                   `json:"is_active"`
}

// 📝 Adaptation Event
type AdaptationEvent struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"`
	Description           string                 `json:"description"`
	Timestamp             time.Time              `json:"timestamp"`
}

// 🎯 Processing Strategy
type ProcessingStrategy struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	Type                  string                 `json:"type"` // fast, accurate, balanced, comprehensive
	Description           string                 `json:"description"`
	ContextRequirements   []string               `json:"context_requirements"`
	ProcessingSteps       []*ProcessingStep      `json:"processing_steps"`
	ExpectedQuality       float64               `json:"expected_quality"`
	ExpectedSpeed         time.Duration          `json:"expected_speed"`
	ResourceRequirements  map[string]float64     `json:"resource_requirements"`
	IsActive              bool                   `json:"is_active"`
	SuccessRate           float64               `json:"success_rate"`
	UsageCount            int64                  `json:"usage_count"`
}

// ⚠️ Limitation Handler - Handle Gemma model limitations intelligently
type LimitationHandler struct {
	KnownLimitations      []*GemmaLimitation     `json:"known_limitations"`
	LimitationDetectors   []*LimitationDetector  `json:"limitation_detectors"`
	MitigationStrategies  []*MitigationStrategy  `json:"mitigation_strategies"`
	FallbackMechanisms    []*FallbackMechanism   `json:"fallback_mechanisms"`
	LimitationHistory     []*LimitationEvent     `json:"limitation_history"`
	AdaptiveResponses     []*AdaptiveResponse    `json:"adaptive_responses"`
	IsMonitoring          bool                   `json:"is_monitoring"`
	LastDetection         time.Time              `json:"last_detection"`
}

// 🔍 Limitation Detector
type LimitationDetector struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	DetectionRules        []string               `json:"detection_rules"`
	IsActive              bool                   `json:"is_active"`
}

// 🛠️ Mitigation Strategy
type MitigationStrategy struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	MitigationRules       []string               `json:"mitigation_rules"`
	IsActive              bool                   `json:"is_active"`
}

// 🔄 Fallback Mechanism
type FallbackMechanism struct {
	ID                    string                 `json:"id"`
	Name                  string                 `json:"name"`
	FallbackRules         []string               `json:"fallback_rules"`
	IsActive              bool                   `json:"is_active"`
}

// ⚠️ Limitation Event
type LimitationEvent struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"`
	Description           string                 `json:"description"`
	Timestamp             time.Time              `json:"timestamp"`
}

// 🔄 Adaptive Response
type AdaptiveResponse struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"`
	Description           string                 `json:"description"`
	Timestamp             time.Time              `json:"timestamp"`
}

// 🚫 Gemma Limitation
type GemmaLimitation struct {
	ID                    string                 `json:"id"`
	Type                  string                 `json:"type"` // context_length, reasoning_depth, factual_accuracy, temporal_understanding
	Description           string                 `json:"description"`
	Severity              string                 `json:"severity"`
	Impact                string                 `json:"impact"`
	TriggerConditions     []string               `json:"trigger_conditions"`
	DetectionMethods      []string               `json:"detection_methods"`
	Workarounds           []string               `json:"workarounds"`
	IsActive              bool                   `json:"is_active"`
	OccurrenceFrequency   float64               `json:"occurrence_frequency"`
	LastOccurrence        time.Time              `json:"last_occurrence"`
}

// 📱 Supported Transcription Providers
var SupportedProviders = map[string]*ProviderConfig{
	"whisper": {
		Name:           "OpenAI Whisper",
		EmailPatterns:  []string{"whisper", "openai"},
		PhoneRegex:     `(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)duration[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
	"google": {
		Name:           "Google Speech-to-Text",
		EmailPatterns:  []string{"google", "speech", "cloud"},
		PhoneRegex:     `(?i)caller[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)length[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)accuracy[:\s]*(\d+\.?\d*)`,
	},
	"azure": {
		Name:           "Azure Speech Services",
		EmailPatterns:  []string{"azure", "microsoft", "cognitive"},
		PhoneRegex:     `(?i)number[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)time[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
	"rev": {
		Name:           "Rev.ai",
		EmailPatterns:  []string{"rev.ai", "rev"},
		PhoneRegex:     `(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)duration[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
}

// 🔧 Provider Configuration
type ProviderConfig struct {
	Name            string   `json:"name"`
	EmailPatterns   []string `json:"email_patterns"`
	PhoneRegex      string   `json:"phone_regex"`
	DurationRegex   string   `json:"duration_regex"`
	ConfidenceRegex string   `json:"confidence_regex"`
}

// NewTranscriptionParser creates a new transcription parser
func NewTranscriptionParser(
	gemma3 *ai.Gemma3Service,
	customerService *customer.CustomerIntelligenceService,
	config *TranscriptionConfig,
	logger log.Logger,
) *TranscriptionParser {
	return &TranscriptionParser{
		log:             log.NewHelper(logger),
		gemma3:          gemma3,
		customerService: customerService,
		config:          config,
	}
}

// 📧 Parse Transcription Email
func (p *TranscriptionParser) ParseTranscriptionEmail(
	ctx context.Context,
	emailContent string,
	emailSubject string,
	emailID string,
) (*TranscriptionEmail, error) {
	p.log.WithContext(ctx).Infof("Parsing transcription email: %s", emailSubject)

	transcription := &TranscriptionEmail{
		SourceEmailID: emailID,
		SourceSubject: emailSubject,
		Language:      p.config.DefaultLanguage,
	}

	// Detect provider
	provider := p.detectProvider(emailContent, emailSubject)
	transcription.Provider = provider

	// Extract phone number
	phoneNumber, err := p.extractPhoneNumber(emailContent, provider)
	if err != nil {
		p.log.WithContext(ctx).Warnf("Failed to extract phone number: %v", err)
	}
	transcription.PhoneNumber = phoneNumber

	// Extract call duration
	duration, err := p.extractCallDuration(emailContent, provider)
	if err != nil {
		p.log.WithContext(ctx).Warnf("Failed to extract call duration: %v", err)
	}
	transcription.CallDuration = duration

	// Extract transcription text
	transcriptionText := p.extractTranscriptionText(emailContent)
	transcription.Transcription = transcriptionText

	// Extract confidence score
	confidence := p.extractConfidence(emailContent, provider)
	transcription.Confidence = confidence

	// Extract call timestamp
	timestamp := p.extractCallTimestamp(emailContent, emailSubject)
	transcription.CallTimestamp = timestamp

	// Determine call direction
	direction := p.determineCallDirection(emailContent, emailSubject)
	transcription.CallDirection = direction

	// Extract caller information
	p.extractCallerInfo(emailContent, transcription)

	// Perform AI analysis if enabled
	if p.config.EnableAIAnalysis && transcriptionText != "" {
		analysis, err := p.analyzeCallContent(ctx, transcription)
		if err != nil {
			p.log.WithContext(ctx).Warnf("AI analysis failed: %v", err)
		} else {
			p.mapAnalysisToTranscription(analysis, transcription)
		}
	}

	p.log.WithContext(ctx).Infof("Transcription parsed successfully: %s (%s)", phoneNumber, provider)
	return transcription, nil
}

// 🔍 Detect Transcription Provider
func (p *TranscriptionParser) detectProvider(content, subject string) string {
	contentLower := strings.ToLower(content + " " + subject)

	for providerName, config := range SupportedProviders {
		for _, pattern := range config.EmailPatterns {
			if strings.Contains(contentLower, strings.ToLower(pattern)) {
				return providerName
			}
		}
	}

	return "unknown"
}

// 📞 Extract Phone Number
func (p *TranscriptionParser) extractPhoneNumber(content, provider string) (string, error) {
	config, exists := SupportedProviders[provider]
	if !exists {
		// Use generic phone regex
		config = &ProviderConfig{
			PhoneRegex: `(?i)(?:phone|number|caller)[:\s]*([+]?[\d\s\-\(\)]{10,})`,
		}
	}

	re := regexp.MustCompile(config.PhoneRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) > 1 {
		phone := strings.TrimSpace(matches[1])
		// Clean phone number
		phone = regexp.MustCompile(`[^\d+]`).ReplaceAllString(phone, "")
		return phone, nil
	}

	// Try generic phone patterns
	genericPatterns := []string{
		`(\+?1?[\s\-]?\(?[\d]{3}\)?[\s\-]?[\d]{3}[\s\-]?[\d]{4})`,
		`(\+?[\d]{1,3}[\s\-]?[\d]{3,4}[\s\-]?[\d]{3,4}[\s\-]?[\d]{3,4})`,
	}

	for _, pattern := range genericPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			phone := regexp.MustCompile(`[^\d+]`).ReplaceAllString(matches[1], "")
			if len(phone) >= 10 {
				return phone, nil
			}
		}
	}

	return "", fmt.Errorf("phone number not found")
}

// ⏱️ Extract Call Duration
func (p *TranscriptionParser) extractCallDuration(content, provider string) (time.Duration, error) {
	config, exists := SupportedProviders[provider]
	if !exists {
		config = &ProviderConfig{
			DurationRegex: `(?i)(?:duration|length|time)[:\s]*(\d+)[:\s]*(\d+)`,
		}
	}

	re := regexp.MustCompile(config.DurationRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) >= 3 {
		minutes, _ := strconv.Atoi(matches[1])
		seconds, _ := strconv.Atoi(matches[2])
		return time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second, nil
	}

	// Try seconds only
	secondsRegex := regexp.MustCompile(`(?i)(?:duration|length)[:\s]*(\d+)[\s]*(?:seconds?|secs?)`)
	matches = secondsRegex.FindStringSubmatch(content)
	if len(matches) > 1 {
		seconds, _ := strconv.Atoi(matches[1])
		return time.Duration(seconds) * time.Second, nil
	}

	return 0, fmt.Errorf("call duration not found")
}

// 📝 Extract Transcription Text
func (p *TranscriptionParser) extractTranscriptionText(content string) string {
	// Common patterns for transcription text
	patterns := []string{
		`(?i)transcription[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)transcript[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)text[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)content[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			text := strings.TrimSpace(matches[1])
			if len(text) > 10 { // Minimum reasonable transcription length
				return text
			}
		}
	}

	// If no specific pattern found, try to extract the largest text block
	lines := strings.Split(content, "\n")
	var longestBlock string
	var currentBlock strings.Builder

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 20 && !strings.Contains(strings.ToLower(line), "phone") &&
			!strings.Contains(strings.ToLower(line), "duration") &&
			!strings.Contains(strings.ToLower(line), "confidence") {
			currentBlock.WriteString(line + " ")
		} else {
			if currentBlock.Len() > len(longestBlock) {
				longestBlock = currentBlock.String()
			}
			currentBlock.Reset()
		}
	}

	if currentBlock.Len() > len(longestBlock) {
		longestBlock = currentBlock.String()
	}

	return strings.TrimSpace(longestBlock)
}

// 📊 Extract Confidence Score
func (p *TranscriptionParser) extractConfidence(content, provider string) float64 {
	config, exists := SupportedProviders[provider]
	if !exists {
		config = &ProviderConfig{
			ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
		}
	}

	re := regexp.MustCompile(config.ConfidenceRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) > 1 {
		confidence, err := strconv.ParseFloat(matches[1], 64)
		if err == nil {
			// Normalize to 0-1 range if it's in percentage
			if confidence > 1.0 {
				confidence = confidence / 100.0
			}
			return confidence
		}
	}

	return 0.8 // Default confidence
}

// 🕐 Extract Call Timestamp
func (p *TranscriptionParser) extractCallTimestamp(content, subject string) time.Time {
	// Try to extract timestamp from content or subject
	timePatterns := []string{
		`(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})`,
		`(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2})`,
		`(\d{2}-\d{2}-\d{4}\s+\d{2}:\d{2})`,
	}

	text := content + " " + subject
	for _, pattern := range timePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) > 1 {
			// Try different time formats
			formats := []string{
				"2006-01-02 15:04:05",
				"01/02/2006 15:04",
				"01-02-2006 15:04",
			}

			for _, format := range formats {
				if t, err := time.Parse(format, matches[1]); err == nil {
					return t
				}
			}
		}
	}

	// Default to current time if not found
	return time.Now()
}

// 🔄 Determine Call Direction
func (p *TranscriptionParser) determineCallDirection(content, subject string) string {
	text := strings.ToLower(content + " " + subject)

	inboundKeywords := []string{"incoming", "inbound", "received", "caller"}
	outboundKeywords := []string{"outgoing", "outbound", "made", "dialed"}

	for _, keyword := range inboundKeywords {
		if strings.Contains(text, keyword) {
			return "inbound"
		}
	}

	for _, keyword := range outboundKeywords {
		if strings.Contains(text, keyword) {
			return "outbound"
		}
	}

	return "inbound" // Default assumption
}

// 👤 Extract Caller Information
func (p *TranscriptionParser) extractCallerInfo(content string, transcription *TranscriptionEmail) {
	// Extract caller name
	namePatterns := []string{
		`(?i)caller[:\s]*([A-Za-z\s]+)`,
		`(?i)name[:\s]*([A-Za-z\s]+)`,
		`(?i)from[:\s]*([A-Za-z\s]+)`,
	}

	for _, pattern := range namePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			name := strings.TrimSpace(matches[1])
			if len(name) > 2 && len(name) < 50 {
				transcription.CallerName = name
				break
			}
		}
	}

	// Extract caller email
	emailRegex := regexp.MustCompile(`([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})`)
	if matches := emailRegex.FindStringSubmatch(content); len(matches) > 0 {
		transcription.CallerEmail = matches[0]
	}

	// Extract company name from transcription text
	if transcription.Transcription != "" {
		companyPatterns := []string{
			`(?i)(?:from|at|with)\s+([A-Za-z\s&]+(?:Inc|LLC|Corp|Company|Co\.))`,
			`(?i)([A-Za-z\s&]+(?:Inc|LLC|Corp|Company|Co\.))`,
		}

		for _, pattern := range companyPatterns {
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(transcription.Transcription)
			if len(matches) > 1 {
				company := strings.TrimSpace(matches[1])
				if len(company) > 2 && len(company) < 100 {
					transcription.CallerCompany = company
					break
				}
			}
		}
	}
}

// 🧠 Analyze Call Content with Gemma 3
func (p *TranscriptionParser) analyzeCallContent(
	ctx context.Context,
	transcription *TranscriptionEmail,
) (*CallAnalysis, error) {
	// Prepare analysis request for Gemma 3
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: transcription.Transcription,
		Subject:      fmt.Sprintf("Phone Call Analysis - %s", transcription.PhoneNumber),
		AnalysisType: "phone_call",
		HVACContext: &ai.HVACContextData{
			ServiceType: "phone_support",
		},
	}

	// Get AI analysis
	response, err := p.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze call content: %w", err)
	}

	// Map AI response to call analysis
	analysis := &CallAnalysis{
		CallPurpose:      "general",
		UrgencyLevel:     "medium",
		CustomerMood:     "neutral",
		SatisfactionLevel: "neutral",
		KnowledgeLevel:   "basic",
		BusinessValue:    "medium",
		Priority:         5,
	}

	// Map from Gemma 3 response
	if response.HVACRelevance != nil {
		analysis.CallPurpose = response.HVACRelevance.ServiceCategory
		analysis.UrgencyLevel = response.HVACRelevance.UrgencyLevel
	}

	if response.SentimentAnalysis != nil {
		analysis.CustomerMood = response.SentimentAnalysis.OverallSentiment
		analysis.SatisfactionLevel = response.SentimentAnalysis.CustomerSatisfaction
	}

	if response.TechnicalAnalysis != nil {
		analysis.TechnicalIssues = response.TechnicalAnalysis.DiagnosticClues
		analysis.KnowledgeLevel = response.TechnicalAnalysis.RequiredExpertise
	}

	if response.ActionPlan != nil {
		analysis.NextActions = response.ActionPlan.ImmediateActions
		if response.ActionPlan.FollowUpRequired {
			followUpTime := time.Now().Add(24 * time.Hour)
			analysis.FollowUpDate = &followUpTime
		}
	}

	if response.BusinessInsights != nil {
		analysis.BusinessValue = response.BusinessInsights.RevenueOpportunity
		analysis.SalesOpportunity = len(response.BusinessInsights.ServiceUpsell) > 0
		analysis.UpsellPotential = "medium"
		if analysis.SalesOpportunity {
			analysis.UpsellPotential = "high"
		}
	}

	// Calculate priority based on urgency and business value
	analysis.Priority = p.calculatePriority(analysis.UrgencyLevel, analysis.BusinessValue)

	return analysis, nil
}

// 🗺️ Map Analysis to Transcription
func (p *TranscriptionParser) mapAnalysisToTranscription(analysis *CallAnalysis, transcription *TranscriptionEmail) {
	transcription.CallPurpose = analysis.CallPurpose
	transcription.UrgencyLevel = analysis.UrgencyLevel
	transcription.ActionItems = analysis.NextActions
	transcription.FollowUpNeeded = analysis.FollowUpDate != nil

	// Set HVAC relevance
	hvacKeywords := []string{"hvac", "heating", "cooling", "air conditioning", "furnace", "ac", "heat pump"}
	transcriptionLower := strings.ToLower(transcription.Transcription)

	for _, keyword := range hvacKeywords {
		if strings.Contains(transcriptionLower, keyword) {
			transcription.HVACRelevance = true
			break
		}
	}

	// Extract service type and equipment
	if transcription.HVACRelevance {
		transcription.ServiceType = analysis.CallPurpose
		transcription.EquipmentMentioned = p.extractEquipmentMentions(transcription.Transcription)
	}

	// Estimate business value
	if analysis.BusinessValue == "high" {
		transcription.EstimatedValue = 1000.0
	} else if analysis.BusinessValue == "medium" {
		transcription.EstimatedValue = 500.0
	} else {
		transcription.EstimatedValue = 100.0
	}
}

// 🔧 Extract Equipment Mentions
func (p *TranscriptionParser) extractEquipmentMentions(transcription string) []string {
	equipment := []string{}
	transcriptionLower := strings.ToLower(transcription)

	equipmentKeywords := map[string][]string{
		"furnace":         {"furnace", "heater", "heating unit"},
		"air_conditioner": {"air conditioner", "ac unit", "cooling unit", "central air"},
		"heat_pump":       {"heat pump", "heatpump"},
		"thermostat":      {"thermostat", "temperature control"},
		"ductwork":        {"duct", "ductwork", "air duct", "ventilation"},
		"filter":          {"filter", "air filter"},
		"compressor":      {"compressor", "condenser"},
		"evaporator":      {"evaporator", "evap coil"},
	}

	for equipmentType, keywords := range equipmentKeywords {
		for _, keyword := range keywords {
			if strings.Contains(transcriptionLower, keyword) {
				equipment = append(equipment, equipmentType)
				break
			}
		}
	}

	return equipment
}

// 📊 Calculate Priority Score
func (p *TranscriptionParser) calculatePriority(urgency, businessValue string) int {
	urgencyScore := map[string]int{
		"critical": 10,
		"high":     8,
		"medium":   5,
		"low":      2,
	}

	businessScore := map[string]int{
		"high":   3,
		"medium": 2,
		"low":    1,
	}

	priority := urgencyScore[urgency] + businessScore[businessValue]
	if priority > 10 {
		priority = 10
	}
	if priority < 1 {
		priority = 1
	}

	return priority
}
