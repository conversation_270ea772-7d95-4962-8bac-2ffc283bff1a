package langchain

import (
	"context"
	"fmt"
	"strings"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/prompts"
)

// 🔗 LangChain Chains - Specialized Processing Pipelines for HVAC CRM
// Each chain is optimized for specific domain tasks

// createHVACAnalysis<PERSON><PERSON><PERSON> creates a specialized chain for HVAC issue analysis
func (s *Service) createHVACAnalysisChain() (chains.Chain, error) {
	s.log.Info("🔧 Creating HVAC Analysis Chain...")

	// HVAC-specific prompt template
	hvacPrompt := prompts.NewPromptTemplate(
		`You are an expert HVAC technician with 20+ years of experience. Analyze the following customer issue and provide professional guidance.

Customer Issue: {{.customer_issue}}
System Type: {{.system_type}}
Context: {{.context}}

Please provide a comprehensive analysis including:

1. ISSUE IDENTIFICATION:
   - Primary problem category
   - Potential root causes
   - Urgency level (Low/Medium/High/Emergency)

2. IMMEDIATE ACTIONS:
   - What the customer can safely do now
   - Safety precautions to take
   - When to stop and call professionals

3. PROFESSIONAL ASSESSMENT:
   - Likely repair complexity
   - Estimated cost range
   - Required tools/parts
   - Time to complete

4. PREVENTIVE MEASURES:
   - How to prevent this issue in future
   - Maintenance recommendations
   - Warning signs to watch for

Format your response professionally and clearly. Use technical terms when necessary but explain them for customer understanding.`,
		[]string{"customer_issue", "system_type", "context"},
	)

	// Create LLM chain with HVAC prompt
	hvacChain := chains.NewLLMChain(s.llm, hvacPrompt)

	s.log.Info("✅ HVAC Analysis Chain created successfully!")
	return hvacChain, nil
}

// createEmailProcessingChain creates a specialized chain for email classification and routing
func (s *Service) createEmailProcessingChain() (chains.Chain, error) {
	s.log.Info("📧 Creating Email Processing Chain...")

	// Email processing prompt template
	emailPrompt := prompts.NewPromptTemplate(
		`You are an intelligent email processing system for an HVAC company. Analyze the following email and provide structured classification.

Email Content: {{.email_content}}
Sender: {{.sender}}
Context: {{.context}}

Please analyze and provide:

1. EMAIL CLASSIFICATION:
   - Category: [service_request|complaint|inquiry|appointment|billing|emergency|sales|maintenance]
   - Priority: [low|medium|high|urgent]
   - Sentiment: [positive|neutral|negative|angry]

2. CONTENT ANALYSIS:
   - Main topic/subject
   - Key issues mentioned
   - Customer intent
   - Required action type

3. ROUTING RECOMMENDATION:
   - Department: [technical|sales|billing|customer_service|emergency]
   - Suggested assignee type
   - Response timeline
   - Escalation needed: [yes|no]

4. EXTRACTED INFORMATION:
   - Customer contact details
   - Service address (if mentioned)
   - Equipment details (if mentioned)
   - Preferred contact method
   - Appointment preferences (if mentioned)

5. RESPONSE SUGGESTIONS:
   - Tone to use in response
   - Key points to address
   - Additional information needed
   - Follow-up actions required

Format as structured data for easy processing by the CRM system.`,
		[]string{"email_content", "sender", "context"},
	)

	// Create LLM chain with email prompt
	emailChain := chains.NewLLMChain(s.llm, emailPrompt)

	s.log.Info("✅ Email Processing Chain created successfully!")
	return emailChain, nil
}

// createAnalysisChain creates a general-purpose analysis chain
func (s *Service) createAnalysisChain() (chains.Chain, error) {
	s.log.Info("📊 Creating General Analysis Chain...")

	// General analysis prompt template
	analysisPrompt := prompts.NewPromptTemplate(
		`You are an intelligent content analysis system. Analyze the provided content based on the specified analysis type.

Content: {{.content}}
Analysis Type: {{.analysis_type}}
Context: {{.context}}

Based on the analysis type, provide appropriate insights:

FOR SENTIMENT ANALYSIS:
- Overall sentiment: [positive|negative|neutral]
- Confidence score: [0-100]%
- Key emotional indicators
- Tone characteristics

FOR HVAC_ISSUE ANALYSIS:
- Issue category
- Severity level
- Technical complexity
- Recommended response

FOR EMAIL_CLASSIFICATION:
- Email type and priority
- Required department
- Response urgency
- Key action items

FOR GENERAL ANALYSIS:
- Main themes and topics
- Key insights and patterns
- Recommendations
- Action items

Provide clear, actionable insights that can be used by the HVAC CRM system for decision making.`,
		[]string{"content", "analysis_type", "context"},
	)

	// Create LLM chain with analysis prompt
	analysisChain := chains.NewLLMChain(s.llm, analysisPrompt)

	s.log.Info("✅ General Analysis Chain created successfully!")
	return analysisChain, nil
}

// Note: Sequential and RAG chains removed due to API compatibility issues
// These can be re-implemented when LangChain Go API stabilizes

// Chain execution helpers

// ExecuteChainWithRetry executes a chain with retry logic
func (s *Service) ExecuteChainWithRetry(ctx context.Context, chain chains.Chain, input map[string]interface{}, maxRetries int) (interface{}, error) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			s.log.WithContext(ctx).Warnf("🔄 Retrying chain execution (attempt %d/%d)", attempt, maxRetries)
		}

		result, err := chains.Run(ctx, chain, input)
		if err == nil {
			return result, nil
		}

		lastErr = err
		s.log.WithContext(ctx).Errorf("❌ Chain execution failed (attempt %d): %v", attempt+1, err)
	}

	return nil, fmt.Errorf("chain execution failed after %d attempts: %w", maxRetries, lastErr)
}

// ValidateChainInput validates input for chain execution
func (s *Service) ValidateChainInput(input map[string]interface{}, requiredKeys []string) error {
	for _, key := range requiredKeys {
		if _, exists := input[key]; !exists {
			return fmt.Errorf("missing required input key: %s", key)
		}
	}
	return nil
}

// FormatChainOutput formats chain output for consistent structure
func (s *Service) FormatChainOutput(output interface{}) string {
	if output == nil {
		return ""
	}

	switch v := output.(type) {
	case string:
		return v
	case map[string]interface{}:
		// Convert map to formatted string
		var parts []string
		for key, value := range v {
			parts = append(parts, fmt.Sprintf("%s: %v", key, value))
		}
		return strings.Join(parts, "\n")
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ChainMetrics tracks chain performance
type ChainMetrics struct {
	ChainName       string
	ExecutionCount  int64
	SuccessCount    int64
	ErrorCount      int64
	AvgExecutionTime float64
	LastError       string
}

// GetChainMetrics returns metrics for all chains
func (s *Service) GetChainMetrics() map[string]*ChainMetrics {
	return map[string]*ChainMetrics{
		"hvac_analysis": {
			ChainName:      "HVAC Analysis",
			ExecutionCount: s.metrics.ChainExecutions,
			SuccessCount:   s.metrics.ChainExecutions - s.metrics.ErrorCount,
			ErrorCount:     s.metrics.ErrorCount,
		},
		"email_processing": {
			ChainName:      "Email Processing",
			ExecutionCount: s.metrics.ChainExecutions,
			SuccessCount:   s.metrics.ChainExecutions - s.metrics.ErrorCount,
			ErrorCount:     s.metrics.ErrorCount,
		},
		"general_analysis": {
			ChainName:      "General Analysis",
			ExecutionCount: s.metrics.ChainExecutions,
			SuccessCount:   s.metrics.ChainExecutions - s.metrics.ErrorCount,
			ErrorCount:     s.metrics.ErrorCount,
		},
	}
}

// OptimizeChainPerformance applies performance optimizations
func (s *Service) OptimizeChainPerformance() {
	s.log.Info("⚡ Optimizing chain performance...")

	// Implement caching for frequently used prompts
	// Optimize token usage
	// Implement parallel processing where possible
	// Add result caching

	s.log.Info("✅ Chain performance optimization completed!")
}

// CreateCustomChain allows creating custom chains for specific use cases
func (s *Service) CreateCustomChain(name string, promptTemplate string, inputKeys []string) (chains.Chain, error) {
	s.log.Infof("🔧 Creating custom chain: %s", name)

	prompt := prompts.NewPromptTemplate(promptTemplate, inputKeys)
	customChain := chains.NewLLMChain(s.llm, prompt)

	s.log.Infof("✅ Custom chain '%s' created successfully!", name)
	return customChain, nil
}
