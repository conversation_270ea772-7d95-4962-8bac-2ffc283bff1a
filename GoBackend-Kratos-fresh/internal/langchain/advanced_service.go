package langchain

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
	"github.com/tmc/langchaingo/prompts"
)

// 🧠 Advanced LangChain Service for HVAC CRM
// Integrates LangChain Go and LangGraph Go for sophisticated AI workflows

type AdvancedLangChainService struct {
	log           *log.Helper
	llm           llms.Model
	memory        schema.Memory
	chains        map[string]chains.Chain
	config        *LangChainConfig
}

type LangChainConfig struct {
	OpenAIAPIKey    string `yaml:"openai_api_key"`
	LMStudioURL     string `yaml:"lm_studio_url"`
	Model           string `yaml:"model"`
	Temperature     float64 `yaml:"temperature"`
	MaxTokens       int    `yaml:"max_tokens"`
	MemorySize      int    `yaml:"memory_size"`
	EnableLogging   bool   `yaml:"enable_logging"`
}

// HVAC-specific workflow types
type HVACWorkflowType string

const (
	WorkflowCustomerAnalysis    HVACWorkflowType = "customer_analysis"
	WorkflowMaintenancePlanning HVACWorkflowType = "maintenance_planning"
	WorkflowTroubleshooting     HVACWorkflowType = "troubleshooting"
	WorkflowQuoteGeneration     HVACWorkflowType = "quote_generation"
	WorkflowEmailTriage         HVACWorkflowType = "email_triage"
	WorkflowCallAnalysis        HVACWorkflowType = "call_analysis"
)

type HVACWorkflowRequest struct {
	Type        HVACWorkflowType       `json:"type"`
	Input       map[string]interface{} `json:"input"`
	Context     map[string]interface{} `json:"context"`
	CustomerID  string                 `json:"customer_id,omitempty"`
	SessionID   string                 `json:"session_id,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type HVACWorkflowResponse struct {
	Result      interface{}            `json:"result"`
	Confidence  float64                `json:"confidence"`
	Reasoning   string                 `json:"reasoning"`
	Actions     []string               `json:"actions,omitempty"`
	NextSteps   []string               `json:"next_steps,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ProcessTime time.Duration          `json:"process_time"`
}

// NewAdvancedLangChainService creates a new advanced LangChain service
func NewAdvancedLangChainService(config *LangChainConfig, logger log.Logger) (*AdvancedLangChainService, error) {
	log := log.NewHelper(logger)

	// Initialize LLM (LM Studio or OpenAI)
	var llm llms.Model
	var err error

	if config.LMStudioURL != "" {
		// Use LM Studio (local Gemma model)
		llm, err = openai.New(
			openai.WithBaseURL(config.LMStudioURL),
			openai.WithModel(config.Model),
			openai.WithToken("not-needed"), // LM Studio doesn't require API key
		)
	} else {
		// Use OpenAI
		llm, err = openai.New(
			openai.WithToken(config.OpenAIAPIKey),
			openai.WithModel(config.Model),
		)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to initialize LLM: %w", err)
	}

	// Initialize memory
	mem := memory.NewConversationBuffer()

	service := &AdvancedLangChainService{
		log:    log,
		llm:    llm,
		memory: mem,
		chains: make(map[string]chains.Chain),
		config: config,
	}

	// Initialize HVAC-specific chains
	if err := service.initializeHVACChains(); err != nil {
		return nil, fmt.Errorf("failed to initialize HVAC chains: %w", err)
	}

	log.Info("🧠 Advanced LangChain Service initialized successfully")
	return service, nil
}

// initializeHVACChains sets up specialized chains for HVAC workflows
func (s *AdvancedLangChainService) initializeHVACChains() error {
	// Customer Analysis Chain
	customerPrompt := prompts.NewPromptTemplate(
		`You are an HVAC customer analysis expert. Analyze the following customer data and provide insights:

Customer Data: {{.customer_data}}
Service History: {{.service_history}}
Communication History: {{.communication_history}}

Provide analysis including:
1. Customer satisfaction level
2. Potential issues or concerns
3. Upselling opportunities
4. Maintenance recommendations
5. Risk assessment (churn probability)

Analysis:`,
		[]string{"customer_data", "service_history", "communication_history"},
	)
	customerChain := chains.NewLLMChain(s.llm, customerPrompt)
	s.chains["customer_analysis"] = customerChain

	// Maintenance Planning Chain
	maintenancePrompt := prompts.NewPromptTemplate(
		`You are an HVAC maintenance planning specialist. Create a maintenance plan based on:

Equipment Data: {{.equipment_data}}
Usage Patterns: {{.usage_patterns}}
Environmental Factors: {{.environmental_factors}}
Service History: {{.service_history}}

Create a comprehensive maintenance plan including:
1. Recommended maintenance schedule
2. Priority tasks
3. Estimated costs
4. Potential issues to watch for
5. Seasonal considerations

Maintenance Plan:`,
		[]string{"equipment_data", "usage_patterns", "environmental_factors", "service_history"},
	)
	maintenanceChain := chains.NewLLMChain(s.llm, maintenancePrompt)
	s.chains["maintenance_planning"] = maintenanceChain

	// Troubleshooting Chain
	troubleshootingPrompt := prompts.NewPromptTemplate(
		`You are an expert HVAC technician. Help troubleshoot this issue:

Problem Description: {{.problem_description}}
Equipment Type: {{.equipment_type}}
Symptoms: {{.symptoms}}
Environmental Conditions: {{.environmental_conditions}}

Provide step-by-step troubleshooting:
1. Most likely causes (ranked by probability)
2. Diagnostic steps to perform
3. Required tools and parts
4. Safety considerations
5. Estimated repair time and cost

Troubleshooting Guide:`,
		[]string{"problem_description", "equipment_type", "symptoms", "environmental_conditions"},
	)
	troubleshootingChain := chains.NewLLMChain(s.llm, troubleshootingPrompt)
	s.chains["troubleshooting"] = troubleshootingChain

	// Quote Generation Chain
	quotePrompt := prompts.NewPromptTemplate(
		`You are an HVAC sales specialist. Generate a professional quote based on:

Service Request: {{.service_request}}
Customer Requirements: {{.customer_requirements}}
Equipment Specifications: {{.equipment_specs}}
Labor Requirements: {{.labor_requirements}}
Market Rates: {{.market_rates}}

Generate a detailed quote including:
1. Itemized costs (equipment, labor, materials)
2. Timeline for completion
3. Warranty information
4. Payment terms
5. Alternative options

Professional Quote:`,
		[]string{"service_request", "customer_requirements", "equipment_specs", "labor_requirements", "market_rates"},
	)
	quoteChain := chains.NewLLMChain(s.llm, quotePrompt)
	s.chains["quote_generation"] = quoteChain

	// Email Triage Chain
	emailTriagePrompt := prompts.NewPromptTemplate(
		`You are an HVAC customer service specialist. Analyze this email and provide triage:

Email Subject: {{.subject}}
Email Content: {{.content}}
Sender Information: {{.sender_info}}
Previous Interactions: {{.previous_interactions}}

Provide triage analysis:
1. Priority level (Low/Medium/High/Critical)
2. Category (Service Request/Complaint/Inquiry/Emergency)
3. Required action
4. Suggested response
5. Escalation requirements

Email Triage:`,
		[]string{"subject", "content", "sender_info", "previous_interactions"},
	)
	emailTriageChain := chains.NewLLMChain(s.llm, emailTriagePrompt)
	s.chains["email_triage"] = emailTriageChain

	// Call Analysis Chain
	callAnalysisPrompt := prompts.NewPromptTemplate(
		`You are an HVAC call center specialist. Analyze this customer call:

Call Transcript: {{.transcript}}
Call Duration: {{.duration}}
Customer Information: {{.customer_info}}
Call Type: {{.call_type}}

Provide call analysis:
1. Customer sentiment
2. Issue summary
3. Resolution provided
4. Follow-up required
5. Service opportunities identified

Call Analysis:`,
		[]string{"transcript", "duration", "customer_info", "call_type"},
	)
	callAnalysisChain := chains.NewLLMChain(s.llm, callAnalysisPrompt)
	s.chains["call_analysis"] = callAnalysisChain

	s.log.Info("🔗 HVAC-specific chains initialized successfully")
	return nil
}

// ProcessHVACWorkflow executes a specific HVAC workflow
func (s *AdvancedLangChainService) ProcessHVACWorkflow(ctx context.Context, request *HVACWorkflowRequest) (*HVACWorkflowResponse, error) {
	startTime := time.Now()

	s.log.WithContext(ctx).Infof("🧠 Processing HVAC workflow: %s", request.Type)

	// Get the appropriate chain
	chainKey := string(request.Type)
	chain, exists := s.chains[chainKey]
	if !exists {
		return nil, fmt.Errorf("workflow type %s not supported", request.Type)
	}

	// Execute the chain
	result, err := chains.Run(ctx, chain, request.Input)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Workflow execution failed: %v", err)
		return nil, fmt.Errorf("workflow execution failed: %w", err)
	}

	// Process and structure the response
	response := &HVACWorkflowResponse{
		Result:      result,
		Confidence:  0.85, // TODO: Implement confidence scoring
		Reasoning:   "AI analysis completed successfully",
		ProcessTime: time.Since(startTime),
		Metadata:    request.Metadata,
	}

	// Add workflow-specific enhancements
	s.enhanceWorkflowResponse(request.Type, response)

	s.log.WithContext(ctx).Infof("✅ HVAC workflow completed in %v", response.ProcessTime)
	return response, nil
}

// enhanceWorkflowResponse adds workflow-specific enhancements
func (s *AdvancedLangChainService) enhanceWorkflowResponse(workflowType HVACWorkflowType, response *HVACWorkflowResponse) {
	switch workflowType {
	case WorkflowCustomerAnalysis:
		response.Actions = []string{"update_customer_profile", "schedule_follow_up", "create_service_reminder"}
		response.NextSteps = []string{"Review analysis with account manager", "Plan customer outreach strategy"}

	case WorkflowMaintenancePlanning:
		response.Actions = []string{"schedule_maintenance", "order_parts", "assign_technician"}
		response.NextSteps = []string{"Confirm schedule with customer", "Prepare maintenance checklist"}

	case WorkflowTroubleshooting:
		response.Actions = []string{"dispatch_technician", "order_replacement_parts", "update_service_ticket"}
		response.NextSteps = []string{"Perform on-site diagnosis", "Implement recommended solution"}

	case WorkflowQuoteGeneration:
		response.Actions = []string{"send_quote_to_customer", "schedule_follow_up_call", "create_calendar_reminder"}
		response.NextSteps = []string{"Wait for customer response", "Prepare for quote discussion"}

	case WorkflowEmailTriage:
		response.Actions = []string{"route_to_appropriate_team", "update_ticket_priority", "send_acknowledgment"}
		response.NextSteps = []string{"Process according to priority", "Monitor response time"}

	case WorkflowCallAnalysis:
		response.Actions = []string{"update_customer_record", "create_follow_up_task", "log_service_opportunity"}
		response.NextSteps = []string{"Review call quality", "Implement identified improvements"}
	}
}

// GetWorkflowCapabilities returns available workflow types and their descriptions
func (s *AdvancedLangChainService) GetWorkflowCapabilities() map[HVACWorkflowType]string {
	return map[HVACWorkflowType]string{
		WorkflowCustomerAnalysis:    "Analyze customer data and provide insights for relationship management",
		WorkflowMaintenancePlanning: "Create comprehensive maintenance plans based on equipment and usage data",
		WorkflowTroubleshooting:     "Provide step-by-step troubleshooting guidance for HVAC issues",
		WorkflowQuoteGeneration:     "Generate professional quotes for HVAC services and equipment",
		WorkflowEmailTriage:         "Analyze and triage incoming customer emails for appropriate routing",
		WorkflowCallAnalysis:        "Analyze customer service calls for quality and opportunities",
	}
}

// HealthCheck performs a health check on the LangChain service
func (s *AdvancedLangChainService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🧠 Performing LangChain service health check")

	// Test basic LLM functionality
	testRequest := &HVACWorkflowRequest{
		Type: WorkflowCustomerAnalysis,
		Input: map[string]interface{}{
			"customer_data":         "Test customer",
			"service_history":       "No previous service",
			"communication_history": "First contact",
		},
	}

	_, err := s.ProcessHVACWorkflow(ctx, testRequest)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ LangChain service health check failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("✅ LangChain service health check passed")
	return nil
}
