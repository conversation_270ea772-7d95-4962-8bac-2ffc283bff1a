package ai

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"gobackend-hvac-kratos/internal/conf"
	"image"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	// Added import
)

// 🤖 Gemma 3 Advanced AI Service for HVAC Email Intelligence
type Gemma3Service struct {
	log        *log.Helper
	config     *Gemma3Config
	httpClient *http.Client
}

// ⚙️ Gemma 3 Configuration
type Gemma3Config struct {
	OllamaURL       string        `yaml:"ollama_url"`
	ModelName       string        `yaml:"model_name"`       // gemma3:4b-instruct
	MaxTokens       int           `yaml:"max_tokens"`       // 8192
	Temperature     float64       `yaml:"temperature"`      // 0.7
	TopP            float64       `yaml:"top_p"`            // 0.9
	ContextWindow   int           `yaml:"context_window"`   // 128000
	Timeout         time.Duration `yaml:"timeout"`          // 60s
	RetryAttempts   int           `yaml:"retry_attempts"`   // 3
	EnableVision    bool          `yaml:"enable:"`          // true for multimodal
	ImageResolution int           `yaml:"image_resolution"` // 896
}

// 📧 HVAC Email Analysis Request
type HVACEmailAnalysisRequest struct {
	EmailContent string            `json:"email_content"`
	Subject      string            `json:"subject"`
	From         string            `json:"from"`
	To           []string          `json:"to"`
	Attachments  []*AttachmentData `json:"attachments,omitempty"`
	Images       []*ImageData      `json:"images,omitempty"`
	AnalysisType string            `json:"analysis_type"` // "comprehensive", "quick", "priority"
	HVACContext  *HVACContextData  `json:"hvac_context,omitempty"`
}

// 📎 Attachment Data
type AttachmentData struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Content     string `json:"content"` // Text content or base64 for binary
	Size        int64  `json:"size"`
}

// 🖼️ Image Data for Multimodal Analysis
type ImageData struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Base64Data  string `json:"base64_data"` // Base64 encoded image
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Description string `json:"description,omitempty"`
}

// 🏠 HVAC Context Data
type HVACContextData struct {
	CustomerHistory []string `json:"customer_history,omitempty"`
	ServiceType     string   `json:"service_type,omitempty"`
	Equipment       []string `json:"equipment,omitempty"`
	Location        string   `json:"location,omitempty"`
	SeasonalContext string   `json:"seasonal_context,omitempty"`
	BusinessHours   string   `json:"business_hours,omitempty"`
}

// 🎯 Comprehensive HVAC Analysis Response
type HVACAnalysisResponse struct {
	// Core Analysis
	Summary            string                 `json:"summary"`
	HVACRelevance      *HVACRelevanceAnalysis `json:"hvac_relevance"`
	SentimentAnalysis  *SentimentAnalysis     `json:"sentiment_analysis"`
	PriorityAssessment *PriorityAssessment    `json:"priority_assessment"`

	// Advanced Analysis
	TechnicalAnalysis *TechnicalAnalysis `json:"technical_analysis"`
	CustomerAnalysis  *CustomerAnalysis  `json:"customer_analysis"`
	ActionPlan        *ActionPlan        `json:"action_plan"`

	// Multimodal Analysis
	ImageAnalysis   []*ImageAnalysisResult `json:"image_analysis,omitempty"`
	DiagramAnalysis *DiagramAnalysis       `json:"diagram_analysis,omitempty"`

	// Business Intelligence
	BusinessInsights    *BusinessInsights `json:"business_insights"`
	RecommendedResponse string            `json:"recommended_response"`

	// Metadata
	ProcessingTime  time.Duration `json:"processing_time"`
	ModelUsed       string        `json:"model_used"`
	ConfidenceScore float64       `json:"confidence_score"`
	TokensUsed      int           `json:"tokens_used"`
}

// 🔧 HVAC Relevance Analysis
type HVACRelevanceAnalysis struct {
	IsHVACRelated      bool     `json:"is_hvac_related"`
	Confidence         float64  `json:"confidence"`
	HVACKeywords       []string `json:"hvac_keywords"`
	ServiceCategory    string   `json:"service_category"` // repair, maintenance, installation, emergency
	EquipmentMentioned []string `json:"equipment_mentioned"`
	SystemType         string   `json:"system_type"`   // heating, cooling, ventilation, combined
	UrgencyLevel       string   `json:"urgency_level"` // emergency, urgent, normal, low
	SeasonalRelevance  string   `json:"seasonal_relevance"`
}

// 💭 Advanced Sentiment Analysis
type SentimentAnalysis struct {
	OverallSentiment     string   `json:"overall_sentiment"`     // positive, negative, neutral
	SentimentScore       float64  `json:"sentiment_score"`       // -1.0 to 1.0
	EmotionalTone        []string `json:"emotional_tone"`        // frustrated, satisfied, concerned, etc.
	CustomerSatisfaction string   `json:"customer_satisfaction"` // very_satisfied, satisfied, neutral, dissatisfied, very_dissatisfied
	UrgencyIndicators    []string `json:"urgency_indicators"`
	PolitenessLevel      string   `json:"politeness_level"`
}

// ⚡ Priority Assessment
type PriorityAssessment struct {
	PriorityLevel      string   `json:"priority_level"`       // critical, high, medium, low
	PriorityScore      float64  `json:"priority_score"`       // 0-100
	ResponseTimeTarget string   `json:"response_time_target"` // immediate, 2h, 24h, 48h
	EscalationNeeded   bool     `json:"escalation_needed"`
	ReasonForPriority  []string `json:"reason_for_priority"`
	BusinessImpact     string   `json:"business_impact"`
}

// 🔬 Technical Analysis
type TechnicalAnalysis struct {
	TechnicalTerms      []string `json:"technical_terms"`
	DiagnosticClues     []string `json:"diagnostic_clues"`
	PossibleIssues      []string `json:"possible_issues"`
	RequiredExpertise   string   `json:"required_expertise"`   // basic, intermediate, advanced, specialist
	EstimatedComplexity string   `json:"estimated_complexity"` // simple, moderate, complex, very_complex
	PartsNeeded         []string `json:"parts_needed,omitempty"`
	ToolsRequired       []string `json:"tools_required,omitempty"`
}

// 👤 Customer Analysis
type CustomerAnalysis struct {
	CustomerType         string   `json:"customer_type"`       // residential, commercial, industrial
	CommunicationStyle   string   `json:"communication_style"` // direct, detailed, brief, technical
	KnowledgeLevel       string   `json:"knowledge_level"`     // novice, basic, intermediate, expert
	PreferredContact     string   `json:"preferred_contact"`   // email, phone, text, in_person
	CustomerConcerns     []string `json:"customer_concerns"`
	PreviousInteractions string   `json:"previous_interactions"`
}

// 📋 Action Plan
type ActionPlan struct {
	ImmediateActions   []string `json:"immediate_actions"`
	ScheduledActions   []string `json:"scheduled_actions"`
	FollowUpRequired   bool     `json:"follow_up_required"`
	FollowUpTimeframe  string   `json:"follow_up_timeframe"`
	AssignedTechnician string   `json:"assigned_technician,omitempty"`
	EstimatedDuration  string   `json:"estimated_duration"`
	EstimatedCost      string   `json:"estimated_cost,omitempty"`
	MaterialsNeeded    []string `json:"materials_needed,omitempty"`
}

// 🖼️ Image Analysis Result
type ImageAnalysisResult struct {
	ImageFilename    string   `json:"image_filename"`
	ImageType        string   `json:"image_type"` // diagram, photo, screenshot, document
	Description      string   `json:"description"`
	HVACElements     []string `json:"hvac_elements"` // equipment, components, systems
	TechnicalDetails []string `json:"technical_details"`
	IssuesIdentified []string `json:"issues_identified"`
	Recommendations  []string `json:"recommendations"`
	ConfidenceScore  float64  `json:"confidence_score"`
}

// 📊 Diagram Analysis
type DiagramAnalysis struct {
	DiagramType      string   `json:"diagram_type"` // schematic, floor_plan, system_layout
	SystemComponents []string `json:"system_components"`
	ConnectionPoints []string `json:"connection_points"`
	PotentialIssues  []string `json:"potential_issues"`
	ComplianceCheck  string   `json:"compliance_check"` // compliant, non_compliant, needs_review
	Recommendations  []string `json:"recommendations"`
}

// 💼 Business Insights
type BusinessInsights struct {
	RevenueOpportunity  string   `json:"revenue_opportunity"` // high, medium, low
	ServiceUpsell       []string `json:"service_upsell"`
	MaintenanceContract bool     `json:"maintenance_contract"`
	CustomerRetention   string   `json:"customer_retention"` // high_risk, medium_risk, low_risk
	CompetitiveFactors  []string `json:"competitive_factors"`
	SeasonalTrends      string   `json:"seasonal_trends"`
}

// NewGemma3Service creates a new Gemma 3 AI service
func NewGemma3Service(confModel *conf.AI_Model, logger log.Logger) *Gemma3Service {
	// Default values for Gemma3Config
	config := &Gemma3Config{
		OllamaURL:       confModel.GetEndpoint(),
		ModelName:       confModel.GetModelName(),
		MaxTokens:       int(confModel.GetMaxTokens()),
		Temperature:     0.7,
		TopP:            0.9,
		ContextWindow:   128000,
		Timeout:         60 * time.Second,
		RetryAttempts:   3,
		EnableVision:    true,
		ImageResolution: 896,
	}

	return &Gemma3Service{
		log:    log.NewHelper(logger),
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// 🎯 Comprehensive HVAC Email Analysis
func (s *Gemma3Service) AnalyzeHVACEmail(ctx context.Context, req *HVACEmailAnalysisRequest) (*HVACAnalysisResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("Starting comprehensive HVAC analysis for email: %s", req.Subject)

	// Build comprehensive prompt
	prompt := s.buildComprehensivePrompt(req)

	// Prepare Ollama request
	ollamaReq := &OllamaRequest{
		Model:  s.config.ModelName,
		Prompt: prompt,
		Options: map[string]interface{}{
			"temperature": s.config.Temperature,
			"top_p":       s.config.TopP,
			"num_predict": s.config.MaxTokens,
		},
		Stream: false,
	}

	// Add images for multimodal analysis
	if s.config.EnableVision && len(req.Images) > 0 {
		ollamaReq.Images = make([]string, len(req.Images))
		for i, img := range req.Images {
			ollamaReq.Images[i] = img.Base64Data
		}
	}

	// Make request to Ollama
	response, err := s.makeOllamaRequest(ctx, ollamaReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get Ollama response: %w", err)
	}

	// Parse structured response
	analysisResponse, err := s.parseAnalysisResponse(response.Response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	// Add metadata
	analysisResponse.ProcessingTime = time.Since(startTime)
	analysisResponse.ModelUsed = s.config.ModelName
	analysisResponse.TokensUsed = response.TotalDuration // Approximate

	s.log.WithContext(ctx).Infof("HVAC analysis completed in %v", analysisResponse.ProcessingTime)
	return analysisResponse, nil
}

// 🔨 Build comprehensive analysis prompt
func (s *Gemma3Service) buildComprehensivePrompt(req *HVACEmailAnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString("# HVAC Email Intelligence Analysis\n\n")
	prompt.WriteString("You are an expert HVAC (Heating, Ventilation, Air Conditioning) analyst with 20+ years of experience.\n")
	prompt.WriteString("Analyze this email comprehensively and provide structured insights.\n\n")

	// Email content
	prompt.WriteString(fmt.Sprintf("## Email Details:\n"))
	prompt.WriteString(fmt.Sprintf("**Subject:** %s\n", req.Subject))
	prompt.WriteString(fmt.Sprintf("**From:** %s\n", req.From))
	prompt.WriteString(fmt.Sprintf("**To:** %s\n", strings.Join(req.To, ", ")))
	prompt.WriteString(fmt.Sprintf("**Content:**\n%s\n\n", req.EmailContent))

	// Attachments
	if len(req.Attachments) > 0 {
		prompt.WriteString("## Attachments:\n")
		for _, att := range req.Attachments {
			prompt.WriteString(fmt.Sprintf("**%s** (%s):\n%s\n\n", att.Filename, att.ContentType, att.Content))
		}
	}

	// Images
	if len(req.Images) > 0 {
		prompt.WriteString("## Images for Analysis:\n")
		for _, img := range req.Images {
			prompt.WriteString(fmt.Sprintf("**%s** - %s\n", img.Filename, img.Description))
		}
		prompt.WriteString("\n")
	}

	// HVAC Context
	if req.HVACContext != nil {
		prompt.WriteString("## HVAC Context:\n")
		if req.HVACContext.ServiceType != "" {
			prompt.WriteString(fmt.Sprintf("**Service Type:** %s\n", req.HVACContext.ServiceType))
		}
		if len(req.HVACContext.Equipment) > 0 {
			prompt.WriteString(fmt.Sprintf("**Equipment:** %s\n", strings.Join(req.HVACContext.Equipment, ", ")))
		}
		if req.HVACContext.SeasonalContext != "" {
			prompt.WriteString(fmt.Sprintf("**Seasonal Context:** %s\n", req.HVACContext.SeasonalContext))
		}
		prompt.WriteString("\n")
	}

	// Analysis instructions
	prompt.WriteString("## Analysis Required:\n")
	prompt.WriteString("Provide a comprehensive JSON response with the following structure:\n\n")
	prompt.WriteString("```json\n")
	prompt.WriteString("{\n")
	prompt.WriteString("  \"summary\": \"Brief 2-3 sentence summary\",\n")
	prompt.WriteString("  \"hvac_relevance\": {\n")
	prompt.WriteString("    \"is_hvac_related\": true/false,\n")
	prompt.WriteString("    \"confidence\": 0.0-1.0,\n")
	prompt.WriteString("    \"hvac_keywords\": [\"keyword1\", \"keyword2\"],\n")
	prompt.WriteString("    \"service_category\": \"repair|maintenance|installation|emergency\",\n")
	prompt.WriteString("    \"equipment_mentioned\": [\"equipment1\", \"equipment2\"],\n")
	prompt.WriteString("    \"system_type\": \"heating|cooling|ventilation|combined\",\n")
	prompt.WriteString("    \"urgency_level\": \"emergency|urgent|normal|low\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"sentiment_analysis\": {\n")
	prompt.WriteString("    \"overall_sentiment\": \"positive|negative|neutral\",\n")
	prompt.WriteString("    \"sentiment_score\": -1.0 to 1.0,\n")
	prompt.WriteString("    \"emotional_tone\": [\"frustrated\", \"satisfied\"],\n")
	prompt.WriteString("    \"customer_satisfaction\": \"very_satisfied|satisfied|neutral|dissatisfied|very_dissatisfied\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"priority_assessment\": {\n")
	prompt.WriteString("    \"priority_level\": \"critical|high|medium|low\",\n")
	prompt.WriteString("    \"priority_score\": 0-100,\n")
	prompt.WriteString("    \"response_time_target\": \"immediate|2h|24h|48h\",\n")
	prompt.WriteString("    \"escalation_needed\": true/false\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"technical_analysis\": {\n")
	prompt.WriteString("    \"technical_terms\": [\"term1\", \"term2\"],\n")
	prompt.WriteString("    \"diagnostic_clues\": [\"clue1\", \"clue2\"],\n")
	prompt.WriteString("    \"possible_issues\": [\"issue1\", \"issue2\"],\n")
	prompt.WriteString("    \"required_expertise\": \"basic|intermediate|advanced|specialist\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"action_plan\": {\n")
	prompt.WriteString("    \"immediate_actions\": [\"action1\", \"action2\"],\n")
	prompt.WriteString("    \"scheduled_actions\": [\"action1\", \"action2\"],\n")
	prompt.WriteString("    \"follow_up_required\": true/false,\n")
	prompt.WriteString("    \"estimated_duration\": \"time estimate\"\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"business_insights\": {\n")
	prompt.WriteString("    \"revenue_opportunity\": \"high|medium|low\",\n")
	prompt.WriteString("    \"service_upsell\": [\"service1\", \"service2\"],\n")
	prompt.WriteString("    \"maintenance_contract\": true/false\n")
	prompt.WriteString("  },\n")
	prompt.WriteString("  \"recommended_response\": \"Professional response template\"\n")
	prompt.WriteString("}\n")
	prompt.WriteString("```\n\n")

	prompt.WriteString("Focus on HVAC-specific insights, technical accuracy, and actionable business intelligence.\n")
	prompt.WriteString("If images are provided, analyze them for HVAC equipment, diagrams, or technical details.\n")

	return prompt.String()
}

// 🌐 Ollama Request Structure
type OllamaRequest struct {
	Model   string                 `json:"model"`
	Prompt  string                 `json:"prompt"`
	Images  []string               `json:"images,omitempty"`
	Options map[string]interface{} `json:"options,omitempty"`
	Stream  bool                   `json:"stream"`
}

// 📥 Ollama Response Structure
type OllamaResponse struct {
	Response      string `json:"response"`
	TotalDuration int    `json:"total_duration"`
	LoadDuration  int    `json:"load_duration"`
	Done          bool   `json:"done"`
}

// 🌐 Make request to Ollama
func (s *Gemma3Service) makeOllamaRequest(ctx context.Context, req *OllamaRequest) (*OllamaResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.config.OllamaURL+"/api/generate", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("ollama request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var ollamaResp OllamaResponse
	if err := json.NewDecoder(resp.Body).Decode(&ollamaResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &ollamaResp, nil
}

// 📊 Parse structured analysis response
func (s *Gemma3Service) parseAnalysisResponse(response string) (*HVACAnalysisResponse, error) {
	// Extract JSON from response (handle markdown code blocks)
	jsonStart := strings.Index(response, "{")
	jsonEnd := strings.LastIndex(response, "}")

	if jsonStart == -1 || jsonEnd == -1 {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonStr := response[jsonStart : jsonEnd+1]

	var analysisResp HVACAnalysisResponse
	if err := json.Unmarshal([]byte(jsonStr), &analysisResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON response: %w", err)
	}

	return &analysisResp, nil
}

// 🖼️ Process image for multimodal analysis
func (s *Gemma3Service) ProcessImageForAnalysis(imageData []byte, filename string) (*ImageData, error) {
	// Decode image to get dimensions
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// Resize to 896x896 if needed (Gemma 3 requirement)
	if width != s.config.ImageResolution || height != s.config.ImageResolution {
		// TODO: Implement image resizing
		s.log.Warnf("Image %s has dimensions %dx%d, should be resized to %dx%d",
			filename, width, height, s.config.ImageResolution, s.config.ImageResolution)
	}

	// Encode to base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)

	return &ImageData{
		Filename:    filename,
		ContentType: "image/" + format,
		Base64Data:  base64Data,
		Width:       width,
		Height:      height,
	}, nil
}

// 🔧 Health check for Gemma 3 service
func (s *Gemma3Service) HealthCheck(ctx context.Context) error {
	req := &OllamaRequest{
		Model:  s.config.ModelName,
		Prompt: "Hello, are you working?",
		Stream: false,
	}

	_, err := s.makeOllamaRequest(ctx, req)
	return err
}
