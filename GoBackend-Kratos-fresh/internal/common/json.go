package common

import (
	"io"

	jsoniter "github.com/json-iterator/go"
)

// 🚀 Enhanced JSON processing with json-iterator
// 2-3x faster than standard encoding/json with full compatibility

var (
	// JSON provides a drop-in replacement for encoding/json
	// with significant performance improvements
	JSON = jsoniter.ConfigCompatibleWithStandardLibrary

	// FastJSON provides even faster JSON processing
	// but with less compatibility (use for performance-critical paths)
	FastJSON = jsoniter.ConfigFastest

	// SafeJSON provides the most compatible JSON processing
	// with additional safety checks
	SafeJSON = jsoniter.ConfigDefault
)

// Marshal is a drop-in replacement for json.Marshal with better performance
func Marshal(v interface{}) ([]byte, error) {
	return JSON.Marshal(v)
}

// Unmarshal is a drop-in replacement for json.Unmarshal with better performance
func Unmarshal(data []byte, v interface{}) error {
	return JSON.Unmarshal(data, v)
}

// MarshalToString marshals to string directly (more efficient)
func MarshalToString(v interface{}) (string, error) {
	return JSON.MarshalToString(v)
}

// UnmarshalFromString unmarshals from string directly (more efficient)
func UnmarshalFromString(str string, v interface{}) error {
	return JSON.UnmarshalFromString(str, v)
}

// FastMarshal uses the fastest configuration for performance-critical paths
func FastMarshal(v interface{}) ([]byte, error) {
	return FastJSON.Marshal(v)
}

// FastUnmarshal uses the fastest configuration for performance-critical paths
func FastUnmarshal(data []byte, v interface{}) error {
	return FastJSON.Unmarshal(data, v)
}

// SafeMarshal uses the safest configuration with additional checks
func SafeMarshal(v interface{}) ([]byte, error) {
	return SafeJSON.Marshal(v)
}

// SafeUnmarshal uses the safest configuration with additional checks
func SafeUnmarshal(data []byte, v interface{}) error {
	return SafeJSON.Unmarshal(data, v)
}

// JSONResponse represents a standard JSON API response
type JSONResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// NewSuccessResponse creates a successful JSON response
func NewSuccessResponse(data interface{}, message ...string) *JSONResponse {
	resp := &JSONResponse{
		Success: true,
		Data:    data,
	}
	if len(message) > 0 {
		resp.Message = message[0]
	}
	return resp
}

// NewErrorResponse creates an error JSON response
func NewErrorResponse(err string, message ...string) *JSONResponse {
	resp := &JSONResponse{
		Success: false,
		Error:   err,
	}
	if len(message) > 0 {
		resp.Message = message[0]
	}
	return resp
}

// ToJSON converts any struct to JSON string using fast marshaling
func ToJSON(v interface{}) string {
	str, err := MarshalToString(v)
	if err != nil {
		return "{\"error\":\"failed to marshal JSON\"}"
	}
	return str
}

// FromJSON converts JSON string to struct using fast unmarshaling
func FromJSON(jsonStr string, v interface{}) error {
	return UnmarshalFromString(jsonStr, v)
}

// PrettyJSON formats JSON with indentation for debugging
func PrettyJSON(v interface{}) (string, error) {
	data, err := JSON.MarshalIndent(v, "", "  ")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// CompactJSON removes unnecessary whitespace from JSON
func CompactJSON(jsonStr string) (string, error) {
	var v interface{}
	if err := UnmarshalFromString(jsonStr, &v); err != nil {
		return "", err
	}
	return MarshalToString(v)
}

// ValidateJSON checks if a string is valid JSON
func ValidateJSON(jsonStr string) bool {
	var v interface{}
	return UnmarshalFromString(jsonStr, &v) == nil
}

// JSONSize returns the size of JSON representation in bytes
func JSONSize(v interface{}) int {
	data, err := Marshal(v)
	if err != nil {
		return 0
	}
	return len(data)
}

// StreamEncoder provides streaming JSON encoding
type StreamEncoder struct {
	encoder *jsoniter.Encoder
}

// NewStreamEncoder creates a new streaming JSON encoder
func NewStreamEncoder(writer io.Writer) *StreamEncoder {
	return &StreamEncoder{
		encoder: JSON.NewEncoder(writer),
	}
}

// Encode encodes a value to the stream
func (e *StreamEncoder) Encode(v interface{}) error {
	return e.encoder.Encode(v)
}

// StreamDecoder provides streaming JSON decoding
type StreamDecoder struct {
	decoder *jsoniter.Decoder
}

// NewStreamDecoder creates a new streaming JSON decoder
func NewStreamDecoder(reader io.Reader) *StreamDecoder {
	return &StreamDecoder{
		decoder: JSON.NewDecoder(reader),
	}
}

// Decode decodes a value from the stream
func (d *StreamDecoder) Decode(v interface{}) error {
	return d.decoder.Decode(v)
}

// More returns true if there is more data to decode
func (d *StreamDecoder) More() bool {
	return d.decoder.More()
}

// Performance metrics for JSON operations
type JSONMetrics struct {
	MarshalCount   int64
	UnmarshalCount int64
	ErrorCount     int64
	TotalBytes     int64
}

var metrics JSONMetrics

// GetJSONMetrics returns current JSON processing metrics
func GetJSONMetrics() JSONMetrics {
	return metrics
}

// ResetJSONMetrics resets the JSON processing metrics
func ResetJSONMetrics() {
	metrics = JSONMetrics{}
}
