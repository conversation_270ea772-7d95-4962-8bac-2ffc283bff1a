package documents

import (
	"bytes"
	"fmt"
	"io"
	"strings"

	"github.com/disintegration/imaging"
	"github.com/h2non/filetype"
	"github.com/unidoc/unipdf/v3/common/license"
	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
	"go.uber.org/zap"
)

// PDFService handles PDF processing operations
type PDFService struct {
	logger *zap.Logger
	config *DocumentConfig
}

// DocumentConfig represents document processing configuration
type DocumentConfig struct {
	UniPDFLicense    string   `yaml:"unipdf_license"`
	MaxFileSize      int64    `yaml:"max_file_size"`      // in bytes
	AllowedTypes     []string `yaml:"allowed_types"`
	OCREnabled       bool     `yaml:"ocr_enabled"`
	ImageQuality     int      `yaml:"image_quality"`
	ThumbnailWidth   int      `yaml:"thumbnail_width"`
	ThumbnailHeight  int      `yaml:"thumbnail_height"`
}

// DefaultDocumentConfig returns default document configuration
func DefaultDocumentConfig() *DocumentConfig {
	return &DocumentConfig{
		UniPDFLicense:   "", // Set your UniPDF license key
		MaxFileSize:     50 * 1024 * 1024, // 50MB
		AllowedTypes:    []string{"pdf", "jpg", "jpeg", "png", "gif", "bmp", "tiff"},
		OCREnabled:      false, // Enable when OCR service is available
		ImageQuality:    85,
		ThumbnailWidth:  200,
		ThumbnailHeight: 200,
	}
}

// NewPDFService creates a new PDF service instance
func NewPDFService(config *DocumentConfig, logger *zap.Logger) (*PDFService, error) {
	if config == nil {
		config = DefaultDocumentConfig()
	}

	// Set UniPDF license if provided
	if config.UniPDFLicense != "" {
		err := license.SetMeteredKey(config.UniPDFLicense)
		if err != nil {
			logger.Warn("Failed to set UniPDF license", zap.Error(err))
		}
	}

	return &PDFService{
		logger: logger,
		config: config,
	}, nil
}

// ExtractTextFromPDF extracts text content from PDF
func (s *PDFService) ExtractTextFromPDF(reader io.Reader) (*PDFTextResult, error) {
	// Read PDF data
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF data: %w", err)
	}

	// Create PDF reader
	pdfReader, err := model.NewPdfReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create PDF reader: %w", err)
	}

	// Get number of pages
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return nil, fmt.Errorf("failed to get number of pages: %w", err)
	}

	var allText strings.Builder
	var pages []PageText

	// Extract text from each page
	for i := 1; i <= numPages; i++ {
		page, err := pdfReader.GetPage(i)
		if err != nil {
			s.logger.Warn("Failed to get page", zap.Int("page", i), zap.Error(err))
			continue
		}

		textExtractor, err := extractor.New(page)
		if err != nil {
			s.logger.Warn("Failed to create text extractor", zap.Int("page", i), zap.Error(err))
			continue
		}

		pageText, err := textExtractor.ExtractText()
		if err != nil {
			s.logger.Warn("Failed to extract text", zap.Int("page", i), zap.Error(err))
			continue
		}

		pages = append(pages, PageText{
			PageNumber: i,
			Text:       pageText,
		})

		allText.WriteString(pageText)
		allText.WriteString("\n")
	}

	result := &PDFTextResult{
		TotalPages: numPages,
		FullText:   allText.String(),
		Pages:      pages,
		FileSize:   int64(len(data)),
	}

	s.logger.Info("PDF text extraction completed",
		zap.Int("pages", numPages),
		zap.Int("text_length", len(result.FullText)),
	)

	return result, nil
}

// ProcessImage processes image files
func (s *PDFService) ProcessImage(reader io.Reader, filename string) (*ImageResult, error) {
	// Read image data
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	// Detect file type
	kind, err := filetype.Match(data)
	if err != nil {
		return nil, fmt.Errorf("failed to detect file type: %w", err)
	}

	if !filetype.IsImage(data) {
		return nil, fmt.Errorf("file is not an image")
	}

	// Decode image
	img, err := imaging.Decode(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Get image dimensions
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// Create thumbnail
	thumbnail := imaging.Thumbnail(img, s.config.ThumbnailWidth, s.config.ThumbnailHeight, imaging.Lanczos)

	// Encode thumbnail
	var thumbnailBuf bytes.Buffer
	err = imaging.Encode(&thumbnailBuf, thumbnail, imaging.JPEG, imaging.JPEGQuality(s.config.ImageQuality))
	if err != nil {
		return nil, fmt.Errorf("failed to encode thumbnail: %w", err)
	}

	result := &ImageResult{
		Filename:      filename,
		FileType:      kind.Extension,
		MimeType:      kind.MIME.Value,
		Width:         width,
		Height:        height,
		FileSize:      int64(len(data)),
		ThumbnailData: thumbnailBuf.Bytes(),
		OriginalData:  data,
	}

	s.logger.Info("Image processing completed",
		zap.String("filename", filename),
		zap.String("type", kind.Extension),
		zap.Int("width", width),
		zap.Int("height", height),
	)

	return result, nil
}

// ValidateFile validates file type and size
func (s *PDFService) ValidateFile(reader io.Reader, filename string) (*FileValidation, error) {
	// Read file data
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read file data: %w", err)
	}

	// Check file size
	if int64(len(data)) > s.config.MaxFileSize {
		return &FileValidation{
			Valid:  false,
			Reason: fmt.Sprintf("file size %d exceeds maximum allowed size %d", len(data), s.config.MaxFileSize),
		}, nil
	}

	// Detect file type
	kind, err := filetype.Match(data)
	if err != nil {
		return &FileValidation{
			Valid:  false,
			Reason: "failed to detect file type",
		}, nil
	}

	// Check if file type is allowed
	allowed := false
	for _, allowedType := range s.config.AllowedTypes {
		if kind.Extension == allowedType {
			allowed = true
			break
		}
	}

	if !allowed {
		return &FileValidation{
			Valid:  false,
			Reason: fmt.Sprintf("file type %s is not allowed", kind.Extension),
		}, nil
	}

	return &FileValidation{
		Valid:    true,
		FileType: kind.Extension,
		MimeType: kind.MIME.Value,
		FileSize: int64(len(data)),
	}, nil
}

// PDFTextResult represents PDF text extraction result
type PDFTextResult struct {
	TotalPages int        `json:"total_pages"`
	FullText   string     `json:"full_text"`
	Pages      []PageText `json:"pages"`
	FileSize   int64      `json:"file_size"`
}

// PageText represents text from a single page
type PageText struct {
	PageNumber int    `json:"page_number"`
	Text       string `json:"text"`
}

// ImageResult represents image processing result
type ImageResult struct {
	Filename      string `json:"filename"`
	FileType      string `json:"file_type"`
	MimeType      string `json:"mime_type"`
	Width         int    `json:"width"`
	Height        int    `json:"height"`
	FileSize      int64  `json:"file_size"`
	ThumbnailData []byte `json:"thumbnail_data"`
	OriginalData  []byte `json:"original_data"`
}

// FileValidation represents file validation result
type FileValidation struct {
	Valid    bool   `json:"valid"`
	Reason   string `json:"reason,omitempty"`
	FileType string `json:"file_type,omitempty"`
	MimeType string `json:"mime_type,omitempty"`
	FileSize int64  `json:"file_size,omitempty"`
}

// DocumentFoundation combines all document processing functionality
type DocumentFoundation struct {
	PDF    *PDFService
	Config *DocumentConfig
	Logger *zap.Logger
}

// NewDocumentFoundation creates a complete document foundation
func NewDocumentFoundation(config *DocumentConfig, logger *zap.Logger) (*DocumentFoundation, error) {
	if config == nil {
		config = DefaultDocumentConfig()
	}

	pdfService, err := NewPDFService(config, logger)
	if err != nil {
		return nil, err
	}

	return &DocumentFoundation{
		PDF:    pdfService,
		Config: config,
		Logger: logger,
	}, nil
}
