package tools

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	mcp_golang "github.com/metoro-io/mcp-golang"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/mcp/types"
)

// 📧 Email Intelligence Tools for MCP
// Comprehensive email processing and AI analysis for HVAC CRM
type EmailTools struct {
	emailUc   *biz.EmailUsecase
	aiUc      *biz.AIUsecase
	validator *validator.Validate
	logger    *log.Helper
}

// NewEmailTools creates a new email tools instance
func NewEmailTools(emailUc *biz.EmailUsecase, aiUc *biz.AIUsecase, validator *validator.Validate, logger log.Logger) *EmailTools {
	return &EmailTools{
		emailUc:   emailUc,
		aiUc:      aiUc,
		validator: validator,
		logger:    log.New<PERSON>elper(logger),
	}
}

// RegisterTools registers all email intelligence tools
func (t *EmailTools) RegisterTools(server *mcp_golang.Server, middleware func(func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error)) error {
	// Send Email Tool
	server.RegisterTool("send_email", "Send email with AI-powered content",
		middleware(t.sendEmail))

	// Analyze Email Tool
	server.RegisterTool("analyze_email", "Analyze email content using AI",
		middleware(t.analyzeEmail))

	// Process Transcription Tool
	server.RegisterTool("process_transcription", "Process audio transcription from emails",
		middleware(t.processTranscription))

	// Get Email Insights Tool
	server.RegisterTool("get_email_insights", "Get AI-powered insights from email analysis",
		middleware(t.getEmailInsights))

	// Extract Customer Info Tool
	server.RegisterTool("extract_customer_info", "Extract customer information from email content",
		middleware(t.extractCustomerInfo))

	// Classify Email Intent Tool
	server.RegisterTool("classify_email_intent", "Classify email intent using AI",
		middleware(t.classifyEmailIntent))

	// Generate Email Response Tool
	server.RegisterTool("generate_email_response", "Generate AI-powered email response",
		middleware(t.generateEmailResponse))

	// Analyze Sentiment Tool
	server.RegisterTool("analyze_sentiment", "Analyze sentiment of email content",
		middleware(t.analyzeSentiment))

	// Extract Entities Tool
	server.RegisterTool("extract_entities", "Extract named entities from email text",
		middleware(t.extractEntities))

	// Process HVAC Keywords Tool
	server.RegisterTool("process_hvac_keywords", "Extract HVAC-specific keywords and analysis",
		middleware(t.processHVACKeywords))

	t.logger.Info("Email intelligence tools registered successfully")
	return nil
}

// sendEmail sends an email with AI-powered content
func (t *EmailTools) sendEmail(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.SendEmailRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for send_email")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// TODO: Implement actual email sending logic
	// This would integrate with the email service

	t.logger.Infof("Email send requested: from=%s, to=%v, subject=%s", req.From, req.To, req.Subject)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📧 Email sent to %v with subject: %s", req.To, req.Subject)),
	), nil
}

// analyzeEmail analyzes email content using AI
func (t *EmailTools) analyzeEmail(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AnalyzeEmailRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for analyze_email")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Use AI service for analysis
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "email_analysis",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to analyze email")
		return nil, fmt.Errorf("failed to analyze email: %w", err)
	}

	t.logger.Infof("Email analyzed successfully: email_id=%d, confidence=%.2f", req.EmailID, result.Confidence)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🧠 Email Analysis:\n%s\nConfidence: %.2f",
			result.Analysis, result.Confidence)),
	), nil
}

// processTranscription processes audio transcription
func (t *EmailTools) processTranscription(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.ProcessTranscriptionRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for process_transcription")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// TODO: Implement actual transcription processing
	// This would integrate with NVIDIA NeMo/Whisper or ElevenLabs

	t.logger.Infof("Transcription processing requested: audio_file=%s, model=%s", req.AudioFile, req.Model)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🎤 Transcription processed for file: %s", req.AudioFile)),
	), nil
}

// getEmailInsights gets AI-powered insights from email analysis
func (t *EmailTools) getEmailInsights(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AnalyzeEmailRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_email_insights")
	}

	// TODO: Implement email insights retrieval
	// This would aggregate analysis results and provide actionable insights

	t.logger.Info("Email insights requested")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("💡 Email insights for ID %d (Feature coming soon)", req.EmailID)),
	), nil
}

// extractCustomerInfo extracts customer information from email content
func (t *EmailTools) extractCustomerInfo(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIAnalyzeRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for extract_customer_info")
	}

	// Use AI service for entity extraction
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "entity_extraction",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to extract customer info")
		return nil, fmt.Errorf("failed to extract customer info: %w", err)
	}

	t.logger.Info("Customer info extracted successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("👤 Customer Info Extracted:\n%s", result.Analysis)),
	), nil
}

// classifyEmailIntent classifies email intent
func (t *EmailTools) classifyEmailIntent(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIAnalyzeRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for classify_email_intent")
	}

	// Use AI service for intent classification
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "intent_classification",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to classify email intent")
		return nil, fmt.Errorf("failed to classify email intent: %w", err)
	}

	t.logger.Info("Email intent classified successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🎯 Email Intent: %s\nConfidence: %.2f",
			result.Analysis, result.Confidence)),
	), nil
}

// generateEmailResponse generates AI-powered email response
func (t *EmailTools) generateEmailResponse(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIChatRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for generate_email_response")
	}

	// Use AI service for response generation
	chatReq := &biz.ChatRequest{
		Message: fmt.Sprintf("Generate a professional email response for: %s", req.Message),
		Model:   req.Model,
		Context: req.Context,
	}

	result, err := t.aiUc.Chat(context.Background(), chatReq)
	if err != nil {
		t.logger.Error("Failed to generate email response")
		return nil, fmt.Errorf("failed to generate email response: %w", err)
	}

	t.logger.Info("Email response generated successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✉️ Generated Email Response:\n%s", result.Response)),
	), nil
}

// analyzeSentiment analyzes sentiment of content
func (t *EmailTools) analyzeSentiment(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIAnalyzeRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for analyze_sentiment")
	}

	// Use AI service for sentiment analysis
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "sentiment",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to analyze sentiment")
		return nil, fmt.Errorf("failed to analyze sentiment: %w", err)
	}

	t.logger.Info("Sentiment analyzed successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("😊 Sentiment Analysis: %s\nConfidence: %.2f",
			result.Analysis, result.Confidence)),
	), nil
}

// extractEntities extracts named entities from text
func (t *EmailTools) extractEntities(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIAnalyzeRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for extract_entities")
	}

	// Use AI service for entity extraction
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "entity_extraction",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to extract entities")
		return nil, fmt.Errorf("failed to extract entities: %w", err)
	}

	t.logger.Info("Entities extracted successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🏷️ Extracted Entities:\n%s", result.Analysis)),
	), nil
}

// processHVACKeywords extracts HVAC-specific keywords
func (t *EmailTools) processHVACKeywords(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AIAnalyzeRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for process_hvac_keywords")
	}

	// Use AI service for HVAC-specific analysis
	aiReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: "hvac_analysis",
		Model:        req.Model,
	}

	result, err := t.aiUc.Analyze(context.Background(), aiReq)
	if err != nil {
		t.logger.Error("Failed to process HVAC keywords")
		return nil, fmt.Errorf("failed to process HVAC keywords: %w", err)
	}

	t.logger.Info("HVAC keywords processed successfully")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🔧 HVAC Keywords & Analysis:\n%s", result.Analysis)),
	), nil
}
