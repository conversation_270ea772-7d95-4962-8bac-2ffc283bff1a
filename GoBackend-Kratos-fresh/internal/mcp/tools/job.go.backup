package tools

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	mcp_golang "github.com/metoro-io/mcp-golang"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/mcp/types"
)

// 🔧 Job Management Tools for MCP
// Comprehensive job lifecycle management for HVAC services
type JobTools struct {
	jobUc     *biz.JobUsecase
	validator *validator.Validate
	logger    *log.Helper
}

// NewJobTools creates a new job tools instance
func NewJobTools(jobUc *biz.JobUsecase, validator *validator.Validate, logger *log.Helper) *JobTools {
	return &JobTools{
		jobUc:     jobUc,
		validator: validator,
		logger:    logger,
	}
}

// RegisterTools registers all job management tools
func (t *JobTools) RegisterTools(server *mcp_golang.Server, middleware func(func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error)) error {
	// Create Job Tool
	server.RegisterTool("create_job", "Create a new HVAC service job",
		middleware(t.createJob))

	// Get Job Tool
	server.RegisterTool("get_job", "Retrieve job details by ID",
		middleware(t.getJob))

	// Update Job Tool
	server.RegisterTool("update_job", "Update existing job information",
		middleware(t.updateJob))

	// List Jobs Tool
	server.RegisterTool("list_jobs", "List jobs with filtering and pagination",
		middleware(t.listJobs))

	// Delete Job Tool
	server.RegisterTool("delete_job", "Delete a job (admin only)",
		middleware(t.deleteJob))

	// Update Job Status Tool
	server.RegisterTool("update_job_status", "Update job status (pending, scheduled, in_progress, completed, cancelled)",
		middleware(t.updateJobStatus))

	// Assign Technician Tool
	server.RegisterTool("assign_technician", "Assign a technician to a job",
		middleware(t.assignTechnician))

	// Schedule Job Tool
	server.RegisterTool("schedule_job", "Schedule a job for a specific date and time",
		middleware(t.scheduleJob))

	// Complete Job Tool
	server.RegisterTool("complete_job", "Mark a job as completed with details",
		middleware(t.completeJob))

	// Get Jobs by Customer Tool
	server.RegisterTool("get_jobs_by_customer", "Get all jobs for a specific customer",
		middleware(t.getJobsByCustomer))

	// Get Jobs by Status Tool
	server.RegisterTool("get_jobs_by_status", "Get all jobs with a specific status",
		middleware(t.getJobsByStatus))

	// Get Urgent Jobs Tool
	server.RegisterTool("get_urgent_jobs", "Get all urgent priority jobs",
		middleware(t.getUrgentJobs))

	t.logger.Info("Job management tools registered successfully")
	return nil
}

// createJob creates a new job
func (t *JobTools) createJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.CreateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for create_job")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Create job entity
	job := &biz.Job{
		CustomerID:  req.CustomerID,
		Title:       req.Title,
		Description: req.Description,
		Priority:    req.Priority,
		Status:      req.Status,
		ScheduledAt: req.ScheduledAt,
	}

	// Call business logic
	result, err := t.jobUc.CreateJob(context.Background(), job)
	if err != nil {
		t.logger.Errorf("Failed to create job"": %v", err)
		return nil, fmt.Errorf("failed to create job: %w", err)
	}

	t.logger.Infof("Job created successfully"":
		"job_id"=result.ID,
		"title"=result.Title,
		"customer_id"=result.CustomerID,
	)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Job created: %s (ID: %d) for Customer %d",
			result.Title, result.ID, result.CustomerID)),
	), nil
}

// getJob retrieves a job by ID
func (t *JobTools) getJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_job")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Call business logic
	job, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		t.logger.Error("Failed to get job", err, "job_id"=req.ID)
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	t.logger.Infof("Job retrieved successfully"": "job_id"=job.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🔧 Job: %s\n📋 Status: %s\n⚡ Priority: %s\n👤 Customer: %d\n📝 Description: %s",
			job.Title, job.Status, job.Priority, job.CustomerID, job.Description)),
	), nil
}

// updateJob updates an existing job
func (t *JobTools) updateJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for update_job")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Get existing job
	existing, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("job not found: %w", err)
	}

	// Update fields if provided
	if req.Title != "" {
		existing.Title = req.Title
	}
	if req.Description != "" {
		existing.Description = req.Description
	}
	if req.Priority != "" {
		existing.Priority = req.Priority
	}
	if req.Status != "" {
		existing.Status = req.Status
	}
	if !req.ScheduledAt.IsZero() {
		existing.ScheduledAt = req.ScheduledAt
	}

	// Call business logic
	result, err := t.jobUc.UpdateJob(context.Background(), existing)
	if err != nil {
		t.logger.Error("Failed to update job", err, "job_id"=req.ID)
		return nil, fmt.Errorf("failed to update job: %w", err)
	}

	t.logger.Infof("Job updated successfully"": "job_id"=result.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Job updated: %s (ID: %d)", result.Title, result.ID)),
	), nil
}

// listJobs lists jobs with filtering and pagination
func (t *JobTools) listJobs(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.ListJobsRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for list_jobs")
	}

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Call business logic
	jobs, total, err := t.jobUc.ListJobs(context.Background(), req.Page, req.PageSize, req.CustomerID, req.Status)
	if err != nil {
		t.logger.Errorf("Failed to list jobs"": %v", err)
		return nil, fmt.Errorf("failed to list jobs: %w", err)
	}

	t.logger.Infof("Jobs listed successfully"":
		"count"=len(jobs),
		"total"=total,
	)

	// Format response
	jobList := ""
	for i, job := range jobs {
		jobList += fmt.Sprintf("%d. %s (ID: %d) - Status: %s, Priority: %s\n",
			i+1, job.Title, job.ID, job.Status, job.Priority)
	}

	totalPages := (total + req.PageSize - 1) / req.PageSize

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🔧 Jobs (Page %d/%d, Total: %d):\n%s",
			req.Page, totalPages, total, jobList)),
	), nil
}

// deleteJob deletes a job
func (t *JobTools) deleteJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.DeleteJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for delete_job")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Get job title for logging
	job, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("job not found: %w", err)
	}

	// Call business logic
	err = t.jobUc.DeleteJob(context.Background(), req.ID)
	if err != nil {
		t.logger.Error("Failed to delete job", err, "job_id"=req.ID)
		return nil, fmt.Errorf("failed to delete job: %w", err)
	}

	t.logger.Infof("Job deleted successfully"":
		"job_id"=req.ID,
		"title"=job.Title,
	)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🗑️ Job '%s' (ID: %d) deleted successfully", job.Title, req.ID)),
	), nil
}

// updateJobStatus updates job status
func (t *JobTools) updateJobStatus(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for update_job_status")
	}

	// Get existing job
	existing, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("job not found: %w", err)
	}

	// Update only status
	existing.Status = req.Status

	// Call business logic
	result, err := t.jobUc.UpdateJob(context.Background(), existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update job status: %w", err)
	}

	t.logger.Infof("Job status updated"":
		"job_id"=result.ID,
		"new_status"=result.Status,
	)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Job status updated to '%s' for job: %s (ID: %d)",
			result.Status, result.Title, result.ID)),
	), nil
}

// assignTechnician assigns a technician to a job
func (t *JobTools) assignTechnician(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for assign_technician")
	}

	// TODO: Implement technician assignment logic
	// This would involve checking technician availability, skills, etc.

	t.logger.Infof("Technician assignment requested"":
		"job_id"=req.ID,
		"technician_id"=req.TechnicianID,
	)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("👨‍🔧 Technician assignment for job ID %d (Feature coming soon)", req.ID)),
	), nil
}

// scheduleJob schedules a job for a specific date and time
func (t *JobTools) scheduleJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for schedule_job")
	}

	// Get existing job
	existing, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("job not found: %w", err)
	}

	// Update schedule and status
	existing.ScheduledAt = req.ScheduledAt
	existing.Status = "scheduled"

	// Call business logic
	result, err := t.jobUc.UpdateJob(context.Background(), existing)
	if err != nil {
		return nil, fmt.Errorf("failed to schedule job: %w", err)
	}

	t.logger.Infof("Job scheduled"":
		"job_id"=result.ID,
		"scheduled_at"=result.ScheduledAt,
	)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📅 Job scheduled: %s (ID: %d) for %s",
			result.Title, result.ID, result.ScheduledAt.Format("2006-01-02 15:04"))),
	), nil
}

// completeJob marks a job as completed
func (t *JobTools) completeJob(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateJobRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for complete_job")
	}

	// Get existing job
	existing, err := t.jobUc.GetJob(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("job not found: %w", err)
	}

	// Update status to completed
	existing.Status = "completed"

	// Call business logic
	result, err := t.jobUc.UpdateJob(context.Background(), existing)
	if err != nil {
		return nil, fmt.Errorf("failed to complete job: %w", err)
	}

	t.logger.Infof("Job completed"": "job_id"=result.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Job completed: %s (ID: %d)", result.Title, result.ID)),
	), nil
}

// getJobsByCustomer gets all jobs for a specific customer
func (t *JobTools) getJobsByCustomer(args interface{}) (*mcp_golang.ToolResponse, error) {
	_, ok := args.(types.ListJobsRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_jobs_by_customer")
	}

	// Use the list jobs functionality with customer filter
	return t.listJobs(args)
}

// getJobsByStatus gets all jobs with a specific status
func (t *JobTools) getJobsByStatus(args interface{}) (*mcp_golang.ToolResponse, error) {
	_, ok := args.(types.ListJobsRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_jobs_by_status")
	}

	// Use the list jobs functionality with status filter
	return t.listJobs(args)
}

// getUrgentJobs gets all urgent priority jobs
func (t *JobTools) getUrgentJobs(args interface{}) (*mcp_golang.ToolResponse, error) {
	// Create request for urgent jobs
	req := types.ListJobsRequest{
		Page:     1,
		PageSize: 50,
		Priority: "urgent",
	}

	return t.listJobs(req)
}
