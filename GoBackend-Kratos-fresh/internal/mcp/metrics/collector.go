package metrics

import (
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// 📊 MCP Metrics Collector - Comprehensive monitoring for MCP server operations
type MCPMetrics struct {
	// Request metrics
	totalRequests       int64
	successfulRequests  int64
	failedRequests      int64
	rateLimitedRequests int64
	validationErrors    int64
	
	// Performance metrics
	totalExecutionTime  int64 // nanoseconds
	averageResponseTime time.Duration
	
	// Tool-specific metrics
	toolExecutions map[string]*ToolMetrics
	toolMutex      sync.RWMutex
	
	// Circuit breaker metrics
	circuitBreakerTrips int64
	circuitBreakerState string
	
	// System metrics
	activeConnections int64
	memoryUsage       int64
	cpuUsage          float64
	
	// Timestamps
	startTime time.Time
	lastReset time.Time
	
	// Logger
	logger *zap.Logger
	
	// Control
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// ToolMetrics tracks metrics for individual tools
type ToolMetrics struct {
	Name            string
	ExecutionCount  int64
	SuccessCount    int64
	FailureCount    int64
	TotalTime       int64 // nanoseconds
	AverageTime     time.Duration
	LastExecuted    time.Time
	ErrorRate       float64
	mu              sync.RWMutex
}

// NewMCPMetrics creates a new metrics collector
func NewMCPMetrics() *MCPMetrics {
	logger, _ := zap.NewProduction()
	
	return &MCPMetrics{
		toolExecutions:      make(map[string]*ToolMetrics),
		startTime:          time.Now(),
		lastReset:          time.Now(),
		circuitBreakerState: "closed",
		logger:             logger,
		stopChan:           make(chan struct{}),
	}
}

// StartCollection begins metrics collection
func (m *MCPMetrics) StartCollection() {
	m.wg.Add(1)
	go m.collectMetrics()
}

// Stop stops metrics collection
func (m *MCPMetrics) Stop() {
	close(m.stopChan)
	m.wg.Wait()
}

// collectMetrics runs the metrics collection loop
func (m *MCPMetrics) collectMetrics() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			m.updateAverageResponseTime()
			m.updateToolMetrics()
			m.logMetrics()
		case <-m.stopChan:
			return
		}
	}
}

// RecordToolExecution records metrics for a tool execution
func (m *MCPMetrics) RecordToolExecution(toolName string, duration time.Duration, success bool) {
	atomic.AddInt64(&m.totalRequests, 1)
	atomic.AddInt64(&m.totalExecutionTime, duration.Nanoseconds())
	
	if success {
		atomic.AddInt64(&m.successfulRequests, 1)
	} else {
		atomic.AddInt64(&m.failedRequests, 1)
	}
	
	// Update tool-specific metrics
	m.toolMutex.Lock()
	defer m.toolMutex.Unlock()
	
	tool, exists := m.toolExecutions[toolName]
	if !exists {
		tool = &ToolMetrics{
			Name: toolName,
		}
		m.toolExecutions[toolName] = tool
	}
	
	tool.mu.Lock()
	defer tool.mu.Unlock()
	
	tool.ExecutionCount++
	tool.TotalTime += duration.Nanoseconds()
	tool.LastExecuted = time.Now()
	
	if success {
		tool.SuccessCount++
	} else {
		tool.FailureCount++
	}
	
	// Calculate error rate
	if tool.ExecutionCount > 0 {
		tool.ErrorRate = float64(tool.FailureCount) / float64(tool.ExecutionCount) * 100
	}
	
	// Calculate average time
	if tool.ExecutionCount > 0 {
		tool.AverageTime = time.Duration(tool.TotalTime / tool.ExecutionCount)
	}
}

// IncrementRateLimited increments rate limited counter
func (m *MCPMetrics) IncrementRateLimited() {
	atomic.AddInt64(&m.rateLimitedRequests, 1)
}

// IncrementValidationErrors increments validation error counter
func (m *MCPMetrics) IncrementValidationErrors() {
	atomic.AddInt64(&m.validationErrors, 1)
}

// IncrementCircuitBreakerTrips increments circuit breaker trips
func (m *MCPMetrics) IncrementCircuitBreakerTrips() {
	atomic.AddInt64(&m.circuitBreakerTrips, 1)
}

// SetCircuitBreakerState sets the circuit breaker state
func (m *MCPMetrics) SetCircuitBreakerState(state string) {
	m.circuitBreakerState = state
}

// IncrementActiveConnections increments active connections
func (m *MCPMetrics) IncrementActiveConnections() {
	atomic.AddInt64(&m.activeConnections, 1)
}

// DecrementActiveConnections decrements active connections
func (m *MCPMetrics) DecrementActiveConnections() {
	atomic.AddInt64(&m.activeConnections, -1)
}

// updateAverageResponseTime calculates the average response time
func (m *MCPMetrics) updateAverageResponseTime() {
	totalRequests := atomic.LoadInt64(&m.totalRequests)
	totalTime := atomic.LoadInt64(&m.totalExecutionTime)
	
	if totalRequests > 0 {
		m.averageResponseTime = time.Duration(totalTime / totalRequests)
	}
}

// updateToolMetrics updates tool-specific metrics
func (m *MCPMetrics) updateToolMetrics() {
	m.toolMutex.RLock()
	defer m.toolMutex.RUnlock()
	
	for _, tool := range m.toolExecutions {
		tool.mu.Lock()
		if tool.ExecutionCount > 0 {
			tool.AverageTime = time.Duration(tool.TotalTime / tool.ExecutionCount)
			tool.ErrorRate = float64(tool.FailureCount) / float64(tool.ExecutionCount) * 100
		}
		tool.mu.Unlock()
	}
}

// logMetrics logs current metrics
func (m *MCPMetrics) logMetrics() {
	uptime := time.Since(m.startTime)
	
	m.logger.Info("MCP Server Metrics",
		zap.Int64("total_requests", atomic.LoadInt64(&m.totalRequests)),
		zap.Int64("successful_requests", atomic.LoadInt64(&m.successfulRequests)),
		zap.Int64("failed_requests", atomic.LoadInt64(&m.failedRequests)),
		zap.Int64("rate_limited_requests", atomic.LoadInt64(&m.rateLimitedRequests)),
		zap.Int64("validation_errors", atomic.LoadInt64(&m.validationErrors)),
		zap.Duration("average_response_time", m.averageResponseTime),
		zap.Int64("circuit_breaker_trips", atomic.LoadInt64(&m.circuitBreakerTrips)),
		zap.String("circuit_breaker_state", m.circuitBreakerState),
		zap.Int64("active_connections", atomic.LoadInt64(&m.activeConnections)),
		zap.Duration("uptime", uptime),
	)
}

// GetMetrics returns current metrics snapshot
func (m *MCPMetrics) GetMetrics() map[string]interface{} {
	uptime := time.Since(m.startTime)
	
	metrics := map[string]interface{}{
		"total_requests":         atomic.LoadInt64(&m.totalRequests),
		"successful_requests":    atomic.LoadInt64(&m.successfulRequests),
		"failed_requests":        atomic.LoadInt64(&m.failedRequests),
		"rate_limited_requests":  atomic.LoadInt64(&m.rateLimitedRequests),
		"validation_errors":      atomic.LoadInt64(&m.validationErrors),
		"average_response_time":  m.averageResponseTime.String(),
		"circuit_breaker_trips":  atomic.LoadInt64(&m.circuitBreakerTrips),
		"circuit_breaker_state":  m.circuitBreakerState,
		"active_connections":     atomic.LoadInt64(&m.activeConnections),
		"uptime":                uptime.String(),
		"start_time":            m.startTime,
		"last_reset":            m.lastReset,
	}
	
	// Add tool-specific metrics
	m.toolMutex.RLock()
	toolMetrics := make(map[string]interface{})
	for name, tool := range m.toolExecutions {
		tool.mu.RLock()
		toolMetrics[name] = map[string]interface{}{
			"execution_count": tool.ExecutionCount,
			"success_count":   tool.SuccessCount,
			"failure_count":   tool.FailureCount,
			"average_time":    tool.AverageTime.String(),
			"error_rate":      tool.ErrorRate,
			"last_executed":   tool.LastExecuted,
		}
		tool.mu.RUnlock()
	}
	m.toolMutex.RUnlock()
	
	metrics["tools"] = toolMetrics
	
	return metrics
}

// GetToolMetrics returns metrics for a specific tool
func (m *MCPMetrics) GetToolMetrics(toolName string) *ToolMetrics {
	m.toolMutex.RLock()
	defer m.toolMutex.RUnlock()
	
	if tool, exists := m.toolExecutions[toolName]; exists {
		// Return a copy to avoid race conditions
		tool.mu.RLock()
		defer tool.mu.RUnlock()
		
		return &ToolMetrics{
			Name:           tool.Name,
			ExecutionCount: tool.ExecutionCount,
			SuccessCount:   tool.SuccessCount,
			FailureCount:   tool.FailureCount,
			TotalTime:      tool.TotalTime,
			AverageTime:    tool.AverageTime,
			LastExecuted:   tool.LastExecuted,
			ErrorRate:      tool.ErrorRate,
		}
	}
	
	return nil
}

// ResetMetrics resets all metrics
func (m *MCPMetrics) ResetMetrics() {
	atomic.StoreInt64(&m.totalRequests, 0)
	atomic.StoreInt64(&m.successfulRequests, 0)
	atomic.StoreInt64(&m.failedRequests, 0)
	atomic.StoreInt64(&m.rateLimitedRequests, 0)
	atomic.StoreInt64(&m.validationErrors, 0)
	atomic.StoreInt64(&m.totalExecutionTime, 0)
	atomic.StoreInt64(&m.circuitBreakerTrips, 0)
	atomic.StoreInt64(&m.activeConnections, 0)
	
	m.averageResponseTime = 0
	m.lastReset = time.Now()
	
	// Reset tool metrics
	m.toolMutex.Lock()
	m.toolExecutions = make(map[string]*ToolMetrics)
	m.toolMutex.Unlock()
	
	m.logger.Info("MCP metrics reset")
}

// GetSuccessRate returns the overall success rate
func (m *MCPMetrics) GetSuccessRate() float64 {
	total := atomic.LoadInt64(&m.totalRequests)
	if total == 0 {
		return 0
	}
	
	successful := atomic.LoadInt64(&m.successfulRequests)
	return float64(successful) / float64(total) * 100
}

// GetRequestsPerSecond returns the current requests per second
func (m *MCPMetrics) GetRequestsPerSecond() float64 {
	uptime := time.Since(m.startTime)
	if uptime.Seconds() == 0 {
		return 0
	}
	
	total := atomic.LoadInt64(&m.totalRequests)
	return float64(total) / uptime.Seconds()
}
