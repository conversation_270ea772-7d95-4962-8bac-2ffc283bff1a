package types

import "time"

// 🔧 MCP Request/Response Types for HVAC CRM
// Comprehensive type definitions for all MCP tool operations

// Customer Management Types
type CreateCustomerRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"omitempty,min=10,max=20"`
	Address  string `json:"address" validate:"omitempty,max=500"`
	Company  string `json:"company" validate:"omitempty,max=100"`
	Notes    string `json:"notes" validate:"omitempty,max=1000"`
}

type UpdateCustomerRequest struct {
	ID       int64  `json:"id" validate:"required,gt=0"`
	Name     string `json:"name" validate:"omitempty,min=2,max=100"`
	Email    string `json:"email" validate:"omitempty,email"`
	Phone    string `json:"phone" validate:"omitempty,min=10,max=20"`
	Address  string `json:"address" validate:"omitempty,max=500"`
	Company  string `json:"company" validate:"omitempty,max=100"`
	Notes    string `json:"notes" validate:"omitempty,max=1000"`
}

type GetCustomerRequest struct {
	ID int64 `json:"id" validate:"required,gt=0"`
}

type ListCustomersRequest struct {
	Page     int32  `json:"page" validate:"omitempty,gt=0"`
	PageSize int32  `json:"page_size" validate:"omitempty,gt=0,lte=100"`
	Search   string `json:"search" validate:"omitempty,max=100"`
}

type DeleteCustomerRequest struct {
	ID int64 `json:"id" validate:"required,gt=0"`
}

// Job Management Types
type CreateJobRequest struct {
	CustomerID   int64     `json:"customer_id" validate:"required,gt=0"`
	Title        string    `json:"title" validate:"required,min=5,max=200"`
	Description  string    `json:"description" validate:"omitempty,max=2000"`
	Priority     string    `json:"priority" validate:"omitempty,oneof=low medium high urgent"`
	Status       string    `json:"status" validate:"omitempty,oneof=pending scheduled in_progress completed cancelled"`
	ScheduledAt  time.Time `json:"scheduled_at" validate:"omitempty"`
	TechnicianID int64     `json:"technician_id" validate:"omitempty,gt=0"`
	EstimatedDuration int  `json:"estimated_duration" validate:"omitempty,gt=0"`
}

type UpdateJobRequest struct {
	ID               int64     `json:"id" validate:"required,gt=0"`
	Title            string    `json:"title" validate:"omitempty,min=5,max=200"`
	Description      string    `json:"description" validate:"omitempty,max=2000"`
	Priority         string    `json:"priority" validate:"omitempty,oneof=low medium high urgent"`
	Status           string    `json:"status" validate:"omitempty,oneof=pending scheduled in_progress completed cancelled"`
	ScheduledAt      time.Time `json:"scheduled_at" validate:"omitempty"`
	TechnicianID     int64     `json:"technician_id" validate:"omitempty,gt=0"`
	EstimatedDuration int      `json:"estimated_duration" validate:"omitempty,gt=0"`
	ActualDuration   int       `json:"actual_duration" validate:"omitempty,gt=0"`
}

type GetJobRequest struct {
	ID int64 `json:"id" validate:"required,gt=0"`
}

type ListJobsRequest struct {
	Page       int32  `json:"page" validate:"omitempty,gt=0"`
	PageSize   int32  `json:"page_size" validate:"omitempty,gt=0,lte=100"`
	CustomerID int64  `json:"customer_id" validate:"omitempty,gt=0"`
	Status     string `json:"status" validate:"omitempty,oneof=pending scheduled in_progress completed cancelled"`
	Priority   string `json:"priority" validate:"omitempty,oneof=low medium high urgent"`
}

type DeleteJobRequest struct {
	ID int64 `json:"id" validate:"required,gt=0"`
}

// Email Processing Types
type SendEmailRequest struct {
	From     string   `json:"from" validate:"required,email"`
	To       []string `json:"to" validate:"required,dive,email"`
	CC       []string `json:"cc" validate:"omitempty,dive,email"`
	BCC      []string `json:"bcc" validate:"omitempty,dive,email"`
	Subject  string   `json:"subject" validate:"required,min=1,max=200"`
	Body     string   `json:"body" validate:"required,min=1"`
	HTMLBody string   `json:"html_body" validate:"omitempty"`
	Priority string   `json:"priority" validate:"omitempty,oneof=low normal high"`
}

type AnalyzeEmailRequest struct {
	EmailID int64  `json:"email_id" validate:"required,gt=0"`
	Content string `json:"content" validate:"omitempty,min=1"`
	Model   string `json:"model" validate:"omitempty,oneof=gemma-3-4b bielik-v3"`
}

type ProcessTranscriptionRequest struct {
	AudioFile string `json:"audio_file" validate:"required"`
	Language  string `json:"language" validate:"omitempty,len=2"`
	Model     string `json:"model" validate:"omitempty,oneof=nvidia-nemo whisper elevenlabs"`
}

// Analytics Types
type GetDashboardStatsRequest struct {
	DateFrom time.Time `json:"date_from" validate:"omitempty"`
	DateTo   time.Time `json:"date_to" validate:"omitempty"`
	Category string    `json:"category" validate:"omitempty,oneof=customers jobs revenue performance"`
}

type GenerateReportRequest struct {
	ReportType string    `json:"report_type" validate:"required,oneof=customer_analytics job_performance revenue_summary operational_metrics"`
	DateFrom   time.Time `json:"date_from" validate:"required"`
	DateTo     time.Time `json:"date_to" validate:"required"`
	Format     string    `json:"format" validate:"omitempty,oneof=json csv pdf"`
	Filters    map[string]interface{} `json:"filters" validate:"omitempty"`
}

// Workflow Types
type CreateWorkflowRequest struct {
	Name        string                 `json:"name" validate:"required,min=3,max=100"`
	Description string                 `json:"description" validate:"omitempty,max=500"`
	Trigger     string                 `json:"trigger" validate:"required,oneof=email_received job_created customer_updated"`
	Conditions  []WorkflowCondition    `json:"conditions" validate:"omitempty"`
	Actions     []WorkflowAction       `json:"actions" validate:"required,min=1"`
	IsActive    bool                   `json:"is_active" validate:"omitempty"`
}

type WorkflowCondition struct {
	Field    string      `json:"field" validate:"required"`
	Operator string      `json:"operator" validate:"required,oneof=equals contains greater_than less_than"`
	Value    interface{} `json:"value" validate:"required"`
}

type WorkflowAction struct {
	Type       string                 `json:"type" validate:"required,oneof=send_email create_job update_customer ai_analysis"`
	Parameters map[string]interface{} `json:"parameters" validate:"required"`
}

type ExecuteWorkflowRequest struct {
	WorkflowID int64                  `json:"workflow_id" validate:"required,gt=0"`
	EntityType string                 `json:"entity_type" validate:"required,oneof=customer job email"`
	EntityID   int64                  `json:"entity_id" validate:"required,gt=0"`
	Context    map[string]interface{} `json:"context" validate:"omitempty"`
}

// Authentication Types
type AuthenticateRequest struct {
	Token string `json:"token" validate:"required"`
}

type CreateUserRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required,min=2,max=50"`
	LastName  string `json:"last_name" validate:"required,min=2,max=50"`
	Role      string `json:"role" validate:"required,oneof=admin manager technician customer_service"`
}

type UpdateUserRoleRequest struct {
	UserID string `json:"user_id" validate:"required"`
	Role   string `json:"role" validate:"required,oneof=admin manager technician customer_service"`
}

// System Operations Types
type HealthCheckRequest struct {
	Component string `json:"component" validate:"omitempty,oneof=database redis minio ai_service"`
}

type BackupDataRequest struct {
	Tables    []string `json:"tables" validate:"omitempty"`
	Timestamp bool     `json:"timestamp" validate:"omitempty"`
}

type UploadFileRequest struct {
	FileName    string `json:"file_name" validate:"required"`
	ContentType string `json:"content_type" validate:"required"`
	BucketName  string `json:"bucket_name" validate:"required"`
	FileData    []byte `json:"file_data" validate:"required"`
}

type GetFileRequest struct {
	FileName   string `json:"file_name" validate:"required"`
	BucketName string `json:"bucket_name" validate:"required"`
}

// AI Processing Types
type AIAnalyzeRequest struct {
	Content      string `json:"content" validate:"required,min=1"`
	AnalysisType string `json:"analysis_type" validate:"required,oneof=sentiment entity_extraction intent_classification hvac_analysis"`
	Model        string `json:"model" validate:"omitempty,oneof=gemma-3-4b bielik-v3"`
	Language     string `json:"language" validate:"omitempty,len=2"`
}

type AIChatRequest struct {
	Message string   `json:"message" validate:"required,min=1"`
	Context []string `json:"context" validate:"omitempty"`
	Model   string   `json:"model" validate:"omitempty,oneof=gemma-3-4b bielik-v3"`
}

// Equipment Management Types
type RegisterEquipmentRequest struct {
	CustomerID   int64  `json:"customer_id" validate:"required,gt=0"`
	Type         string `json:"type" validate:"required,oneof=air_conditioner heat_pump furnace boiler"`
	Brand        string `json:"brand" validate:"required,min=2,max=50"`
	Model        string `json:"model" validate:"required,min=2,max=100"`
	SerialNumber string `json:"serial_number" validate:"required,min=5,max=50"`
	InstallDate  time.Time `json:"install_date" validate:"omitempty"`
	WarrantyEnd  time.Time `json:"warranty_end" validate:"omitempty"`
}

type ScheduleMaintenanceRequest struct {
	EquipmentID   int64     `json:"equipment_id" validate:"required,gt=0"`
	MaintenanceType string  `json:"maintenance_type" validate:"required,oneof=routine inspection repair emergency"`
	ScheduledDate time.Time `json:"scheduled_date" validate:"required"`
	TechnicianID  int64     `json:"technician_id" validate:"omitempty,gt=0"`
	Notes         string    `json:"notes" validate:"omitempty,max=1000"`
}

// Response Types
type MCPResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int32       `json:"page"`
	PageSize   int32       `json:"page_size"`
	Total      int32       `json:"total"`
	TotalPages int32       `json:"total_pages"`
}
