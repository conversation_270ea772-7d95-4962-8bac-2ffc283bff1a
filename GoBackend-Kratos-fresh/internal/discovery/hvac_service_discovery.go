package discovery

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Advanced HVAC Service Discovery for Kratos Framework Excellence
// Enterprise-grade service discovery with health monitoring and load balancing

// ==========================================
// HVAC SERVICE REGISTRY
// ==========================================

// HVACServiceRegistry provides advanced service discovery for HVAC microservices
type HVACServiceRegistry struct {
	services       map[string]*HVACServiceInfo
	healthCheckers map[string]*HealthChecker
	loadBalancer   *LoadBalancer
	eventBus       *ServiceEventBus
	config         *ServiceRegistryConfig
	log            *log.Helper
	logger         log.Logger
	mu             sync.RWMutex
	stopChan       chan struct{}
	running        bool
}

// HVACServiceInfo contains comprehensive service information
type HVACServiceInfo struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Version      string                 `json:"version"`
	Type         HVACServiceType        `json:"type"`
	Endpoints    []ServiceEndpoint      `json:"endpoints"`
	Metadata     map[string]interface{} `json:"metadata"`
	Health       HealthStatus           `json:"health"`
	LoadMetrics  *LoadMetrics           `json:"load_metrics"`
	Tags         []string               `json:"tags"`
	Region       string                 `json:"region"`
	Zone         string                 `json:"zone"`
	RegisteredAt time.Time              `json:"registered_at"`
	LastSeen     time.Time              `json:"last_seen"`
}

// HVACServiceType defines types of HVAC services
type HVACServiceType string

const (
	ServiceTypeCustomer     HVACServiceType = "customer"
	ServiceTypeTechnician   HVACServiceType = "technician"
	ServiceTypeJob          HVACServiceType = "job"
	ServiceTypeAI           HVACServiceType = "ai"
	ServiceTypeAnalytics    HVACServiceType = "analytics"
	ServiceTypeWorkflow     HVACServiceType = "workflow"
	ServiceTypeEmail        HVACServiceType = "email"
	ServiceTypeNotification HVACServiceType = "notification"
	ServiceTypeGateway      HVACServiceType = "gateway"
)

// ServiceEndpoint represents a service endpoint
type ServiceEndpoint struct {
	Protocol string `json:"protocol"` // http, grpc, websocket
	Address  string `json:"address"`
	Port     int    `json:"port"`
	Path     string `json:"path,omitempty"`
	Secure   bool   `json:"secure"`
}

// HealthStatus represents service health
type HealthStatus struct {
	Status       HealthStatusType `json:"status"`
	LastCheck    time.Time        `json:"last_check"`
	ResponseTime time.Duration    `json:"response_time"`
	ErrorCount   int              `json:"error_count"`
	Message      string           `json:"message"`
}

type HealthStatusType string

const (
	HealthStatusHealthy   HealthStatusType = "healthy"
	HealthStatusUnhealthy HealthStatusType = "unhealthy"
	HealthStatusDegraded  HealthStatusType = "degraded"
	HealthStatusUnknown   HealthStatusType = "unknown"
)

// LoadMetrics tracks service load
type LoadMetrics struct {
	CPU            float64 `json:"cpu"`
	Memory         float64 `json:"memory"`
	RequestsPerSec float64 `json:"requests_per_sec"`
	ActiveConns    int     `json:"active_connections"`
	QueueLength    int     `json:"queue_length"`
}

// ServiceRegistryConfig configures the service registry
type ServiceRegistryConfig struct {
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	ServiceTTL          time.Duration `json:"service_ttl"`
	EnableLoadBalancing bool          `json:"enable_load_balancing"`
	EnableEventBus      bool          `json:"enable_event_bus"`
	MaxRetries          int           `json:"max_retries"`
	RetryDelay          time.Duration `json:"retry_delay"`
}

// NewHVACServiceRegistry creates a new HVAC service registry
func NewHVACServiceRegistry(config *ServiceRegistryConfig, logger log.Logger) *HVACServiceRegistry {
	if config == nil {
		config = &ServiceRegistryConfig{
			HealthCheckInterval: 30 * time.Second,
			ServiceTTL:          5 * time.Minute,
			EnableLoadBalancing: true,
			EnableEventBus:      true,
			MaxRetries:          3,
			RetryDelay:          1 * time.Second,
		}
	}

	registry := &HVACServiceRegistry{
		services:       make(map[string]*HVACServiceInfo),
		healthCheckers: make(map[string]*HealthChecker),
		config:         config,
		log:            log.NewHelper(logger),
		logger:         logger,
		stopChan:       make(chan struct{}),
	}

	if config.EnableLoadBalancing {
		registry.loadBalancer = NewLoadBalancer(logger)
	}

	if config.EnableEventBus {
		registry.eventBus = NewServiceEventBus(logger)
	}

	return registry
}

// Start starts the service registry
func (r *HVACServiceRegistry) Start(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.running {
		return fmt.Errorf("service registry already running")
	}

	r.running = true

	// Start health checking
	go r.healthCheckLoop(ctx)

	// Start service cleanup
	go r.cleanupLoop(ctx)

	// Start event bus if enabled
	if r.eventBus != nil {
		r.eventBus.Start(ctx)
	}

	r.log.Info("HVAC Service Registry started")
	return nil
}

// Stop stops the service registry
func (r *HVACServiceRegistry) Stop() {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.running {
		return
	}

	r.running = false
	close(r.stopChan)

	// Stop event bus
	if r.eventBus != nil {
		r.eventBus.Stop()
	}

	r.log.Info("HVAC Service Registry stopped")
}

// RegisterService registers a new HVAC service
func (r *HVACServiceRegistry) RegisterService(ctx context.Context, service *HVACServiceInfo) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Validate service info
	if err := r.validateServiceInfo(service); err != nil {
		return fmt.Errorf("invalid service info: %w", err)
	}

	// Set registration time
	service.RegisteredAt = time.Now()
	service.LastSeen = time.Now()
	service.Health.Status = HealthStatusUnknown

	// Store service
	r.services[service.ID] = service

	// Create health checker
	if r.config.HealthCheckInterval > 0 {
		r.healthCheckers[service.ID] = NewHealthChecker(service, r.config.HealthCheckInterval, r.logger)
	}

	// Update load balancer
	if r.loadBalancer != nil {
		r.loadBalancer.AddService(service)
	}

	// Publish event
	if r.eventBus != nil {
		r.eventBus.PublishEvent(&ServiceEvent{
			Type:      EventTypeServiceRegistered,
			ServiceID: service.ID,
			Service:   service,
			Timestamp: time.Now(),
		})
	}

	r.log.Infof("Registered HVAC service: %s (%s)", service.Name, service.ID)
	return nil
}

// DeregisterService removes a service from the registry
func (r *HVACServiceRegistry) DeregisterService(ctx context.Context, serviceID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	service, exists := r.services[serviceID]
	if !exists {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	// Remove from registry
	delete(r.services, serviceID)

	// Stop health checker
	if checker, exists := r.healthCheckers[serviceID]; exists {
		checker.Stop()
		delete(r.healthCheckers, serviceID)
	}

	// Update load balancer
	if r.loadBalancer != nil {
		r.loadBalancer.RemoveService(serviceID)
	}

	// Publish event
	if r.eventBus != nil {
		r.eventBus.PublishEvent(&ServiceEvent{
			Type:      EventTypeServiceDeregistered,
			ServiceID: serviceID,
			Service:   service,
			Timestamp: time.Now(),
		})
	}

	r.log.Infof("Deregistered HVAC service: %s", serviceID)
	return nil
}

// DiscoverServices finds services by type and criteria
func (r *HVACServiceRegistry) DiscoverServices(ctx context.Context, serviceType HVACServiceType, criteria *DiscoveryCriteria) ([]*HVACServiceInfo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var matchingServices []*HVACServiceInfo

	for _, service := range r.services {
		if r.matchesDiscoveryCriteria(service, serviceType, criteria) {
			matchingServices = append(matchingServices, service)
		}
	}

	// Apply load balancing if enabled
	if r.loadBalancer != nil && len(matchingServices) > 1 {
		matchingServices = r.loadBalancer.SortByLoad(matchingServices)
	}

	return matchingServices, nil
}

// GetService retrieves a specific service by ID
func (r *HVACServiceRegistry) GetService(ctx context.Context, serviceID string) (*HVACServiceInfo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	service, exists := r.services[serviceID]
	if !exists {
		return nil, fmt.Errorf("service not found: %s", serviceID)
	}

	return service, nil
}

// GetHealthyServices returns only healthy services of a specific type
func (r *HVACServiceRegistry) GetHealthyServices(ctx context.Context, serviceType HVACServiceType) ([]*HVACServiceInfo, error) {
	criteria := &DiscoveryCriteria{
		HealthStatus: []HealthStatusType{HealthStatusHealthy},
	}
	return r.DiscoverServices(ctx, serviceType, criteria)
}

// ==========================================
// DISCOVERY CRITERIA
// ==========================================

// DiscoveryCriteria defines service discovery criteria
type DiscoveryCriteria struct {
	Tags         []string               `json:"tags"`
	Region       string                 `json:"region"`
	Zone         string                 `json:"zone"`
	Version      string                 `json:"version"`
	HealthStatus []HealthStatusType     `json:"health_status"`
	MaxLoad      *LoadMetrics           `json:"max_load"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ==========================================
// HEALTH CHECKER
// ==========================================

// HealthChecker monitors service health
type HealthChecker struct {
	service  *HVACServiceInfo
	interval time.Duration
	log      *log.Helper
	stopChan chan struct{}
	running  bool
}

// NewHealthChecker creates a new health checker
func NewHealthChecker(service *HVACServiceInfo, interval time.Duration, logger log.Logger) *HealthChecker {
	return &HealthChecker{
		service:  service,
		interval: interval,
		log:      log.NewHelper(logger),
		stopChan: make(chan struct{}),
	}
}

// Start starts health checking
func (h *HealthChecker) Start() {
	if h.running {
		return
	}
	h.running = true
	go h.healthCheckLoop()
}

// Stop stops health checking
func (h *HealthChecker) Stop() {
	if !h.running {
		return
	}
	h.running = false
	close(h.stopChan)
}

// healthCheckLoop runs the health check loop
func (h *HealthChecker) healthCheckLoop() {
	ticker := time.NewTicker(h.interval)
	defer ticker.Stop()

	for {
		select {
		case <-h.stopChan:
			return
		case <-ticker.C:
			h.performHealthCheck()
		}
	}
}

// performHealthCheck performs a health check on the service
func (h *HealthChecker) performHealthCheck() {
	start := time.Now()

	// Simplified health check - in production, make actual HTTP/gRPC calls
	healthy := true // Simulate health check result

	duration := time.Since(start)

	// Update health status
	if healthy {
		h.service.Health.Status = HealthStatusHealthy
		h.service.Health.ErrorCount = 0
	} else {
		h.service.Health.Status = HealthStatusUnhealthy
		h.service.Health.ErrorCount++
	}

	h.service.Health.LastCheck = time.Now()
	h.service.Health.ResponseTime = duration
	h.service.LastSeen = time.Now()
}

// ==========================================
// LOAD BALANCER
// ==========================================

// LoadBalancer provides intelligent load balancing
type LoadBalancer struct {
	log *log.Helper
}

// NewLoadBalancer creates a new load balancer
func NewLoadBalancer(logger log.Logger) *LoadBalancer {
	return &LoadBalancer{
		log: log.NewHelper(logger),
	}
}

// AddService adds a service to load balancing
func (lb *LoadBalancer) AddService(service *HVACServiceInfo) {
	lb.log.Infof("Added service to load balancer: %s", service.ID)
}

// RemoveService removes a service from load balancing
func (lb *LoadBalancer) RemoveService(serviceID string) {
	lb.log.Infof("Removed service from load balancer: %s", serviceID)
}

// SortByLoad sorts services by their current load
func (lb *LoadBalancer) SortByLoad(services []*HVACServiceInfo) []*HVACServiceInfo {
	// Simplified implementation - in production, implement proper load-based sorting
	return services
}

// ==========================================
// SERVICE EVENT BUS
// ==========================================

// ServiceEventBus handles service events
type ServiceEventBus struct {
	subscribers map[EventType][]EventHandler
	log         *log.Helper
	mu          sync.RWMutex
}

// EventType defines service event types
type EventType string

const (
	EventTypeServiceRegistered    EventType = "service_registered"
	EventTypeServiceDeregistered  EventType = "service_deregistered"
	EventTypeServiceHealthChanged EventType = "service_health_changed"
	EventTypeServiceLoadChanged   EventType = "service_load_changed"
)

// ServiceEvent represents a service event
type ServiceEvent struct {
	Type      EventType              `json:"type"`
	ServiceID string                 `json:"service_id"`
	Service   *HVACServiceInfo       `json:"service"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// EventHandler handles service events
type EventHandler func(*ServiceEvent)

// NewServiceEventBus creates a new service event bus
func NewServiceEventBus(logger log.Logger) *ServiceEventBus {
	return &ServiceEventBus{
		subscribers: make(map[EventType][]EventHandler),
		log:         log.NewHelper(logger),
	}
}

// Start starts the event bus
func (eb *ServiceEventBus) Start(ctx context.Context) {
	eb.log.Info("Service Event Bus started")
}

// Stop stops the event bus
func (eb *ServiceEventBus) Stop() {
	eb.log.Info("Service Event Bus stopped")
}

// Subscribe subscribes to service events
func (eb *ServiceEventBus) Subscribe(eventType EventType, handler EventHandler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	eb.subscribers[eventType] = append(eb.subscribers[eventType], handler)
}

// PublishEvent publishes a service event
func (eb *ServiceEventBus) PublishEvent(event *ServiceEvent) {
	eb.mu.RLock()
	handlers := eb.subscribers[event.Type]
	eb.mu.RUnlock()

	for _, handler := range handlers {
		go handler(event)
	}
}

// ==========================================
// HELPER METHODS
// ==========================================

func (r *HVACServiceRegistry) validateServiceInfo(service *HVACServiceInfo) error {
	if service.ID == "" {
		return fmt.Errorf("service ID is required")
	}
	if service.Name == "" {
		return fmt.Errorf("service name is required")
	}
	if len(service.Endpoints) == 0 {
		return fmt.Errorf("at least one endpoint is required")
	}
	return nil
}

func (r *HVACServiceRegistry) matchesDiscoveryCriteria(service *HVACServiceInfo, serviceType HVACServiceType, criteria *DiscoveryCriteria) bool {
	// Check service type
	if service.Type != serviceType {
		return false
	}

	// Check criteria if provided
	if criteria == nil {
		return true
	}

	// Check health status
	if len(criteria.HealthStatus) > 0 {
		found := false
		for _, status := range criteria.HealthStatus {
			if service.Health.Status == status {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check region
	if criteria.Region != "" && service.Region != criteria.Region {
		return false
	}

	// Check zone
	if criteria.Zone != "" && service.Zone != criteria.Zone {
		return false
	}

	// Check version
	if criteria.Version != "" && service.Version != criteria.Version {
		return false
	}

	// Check tags
	if len(criteria.Tags) > 0 {
		for _, requiredTag := range criteria.Tags {
			found := false
			for _, serviceTag := range service.Tags {
				if serviceTag == requiredTag {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	return true
}

func (r *HVACServiceRegistry) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(r.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-r.stopChan:
			return
		case <-ticker.C:
			r.performHealthChecks()
		}
	}
}

func (r *HVACServiceRegistry) performHealthChecks() {
	r.mu.RLock()
	checkers := make([]*HealthChecker, 0, len(r.healthCheckers))
	for _, checker := range r.healthCheckers {
		checkers = append(checkers, checker)
	}
	r.mu.RUnlock()

	for _, checker := range checkers {
		if !checker.running {
			checker.Start()
		}
	}
}

func (r *HVACServiceRegistry) cleanupLoop(ctx context.Context) {
	ticker := time.NewTicker(r.config.ServiceTTL)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-r.stopChan:
			return
		case <-ticker.C:
			r.cleanupStaleServices()
		}
	}
}

func (r *HVACServiceRegistry) cleanupStaleServices() {
	r.mu.Lock()
	defer r.mu.Unlock()

	now := time.Now()
	for serviceID, service := range r.services {
		if now.Sub(service.LastSeen) > r.config.ServiceTTL {
			r.log.Warnf("Removing stale service: %s", serviceID)
			delete(r.services, serviceID)

			if checker, exists := r.healthCheckers[serviceID]; exists {
				checker.Stop()
				delete(r.healthCheckers, serviceID)
			}
		}
	}
}
