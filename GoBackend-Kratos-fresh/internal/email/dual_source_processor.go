package email

import (
	"context"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"net/mail"
	"path/filepath"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/transcription"

	"github.com/go-kratos/kratos/v2/log"
)

// 🔄 Dual Source Email Processor - Handles both customer emails and transcription attachments
type DualSourceEmailProcessor struct {
	log                 *log.Helper
	emailAnalysis       *EmailAnalysisService
	transcriptionParser *transcription.TranscriptionParser
	gemma3              *ai.Gemma3Service
	emailUsecase        *biz.EmailUsecase
	workflowUsecase     *biz.WorkflowUsecase
	config              *DualSourceConfig
}

// DualSourceConfig configuration for dual source processing
type DualSourceConfig struct {
	CustomerEmailAccount    string        `yaml:"customer_email_account"`
	TranscriptionAccount    string        `yaml:"transcription_account"`
	SupportedAudioFormats   []string      `yaml:"supported_audio_formats"`
	MaxAttachmentSize       int64         `yaml:"max_attachment_size"`
	TranscriptionTimeout    time.Duration `yaml:"transcription_timeout"`
	AutoCreateServiceOrders bool          `yaml:"auto_create_service_orders"`
}

// ProcessingResult represents the result of email processing
type ProcessingResult struct {
	EmailID           string               `json:"email_id"`
	SourceAccount     string               `json:"source_account"`
	ProcessingType    string               `json:"processing_type"` // "customer_email" or "transcription"
	Success           bool                 `json:"success"`
	Analysis          *EmailAnalysisResult `json:"analysis,omitempty"`
	Transcription     *TranscriptionResult `json:"transcription,omitempty"`
	WorkflowTriggered bool                 `json:"workflow_triggered"`
	Actions           []string             `json:"actions"`
	Error             string               `json:"error,omitempty"`
	ProcessingTime    time.Duration        `json:"processing_time"`
}

// TranscriptionResult represents transcription processing result
type TranscriptionResult struct {
	AudioFiles    []AudioFileInfo `json:"audio_files"`
	Transcripts   []string        `json:"transcripts"`
	CallAnalysis  *CallAnalysis   `json:"call_analysis,omitempty"`
	CustomerMatch *CustomerMatch  `json:"customer_match,omitempty"`
}

// AudioFileInfo represents information about processed audio file
type AudioFileInfo struct {
	Filename    string        `json:"filename"`
	Format      string        `json:"format"`
	Duration    time.Duration `json:"duration"`
	Size        int64         `json:"size"`
	Transcribed bool          `json:"transcribed"`
	Error       string        `json:"error,omitempty"`
}

// CallAnalysis represents AI analysis of transcribed call
type CallAnalysis struct {
	Intent      string                 `json:"intent"`
	Urgency     string                 `json:"urgency"`
	ServiceType string                 `json:"service_type"`
	Issues      []string               `json:"issues"`
	Sentiment   string                 `json:"sentiment"`
	Confidence  float64                `json:"confidence"`
	ActionItems []string               `json:"action_items"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// CustomerMatch represents customer matching result
type CustomerMatch struct {
	CustomerID    string  `json:"customer_id,omitempty"`
	PhoneNumber   string  `json:"phone_number"`
	Confidence    float64 `json:"confidence"`
	IsNewCustomer bool    `json:"is_new_customer"`
}

// NewDualSourceEmailProcessor creates a new dual source email processor
func NewDualSourceEmailProcessor(
	emailAnalysis *EmailAnalysisService,
	transcriptionParser *transcription.TranscriptionParser,
	gemma3 *ai.Gemma3Service,
	emailUsecase *biz.EmailUsecase,
	workflowUsecase *biz.WorkflowUsecase,
	logger log.Logger,
) *DualSourceEmailProcessor {
	config := &DualSourceConfig{
		CustomerEmailAccount:    "<EMAIL>",
		TranscriptionAccount:    "<EMAIL>",
		SupportedAudioFormats:   []string{".m4a", ".mp3", ".wav", ".aac"},
		MaxAttachmentSize:       50 * 1024 * 1024, // 50MB
		TranscriptionTimeout:    5 * time.Minute,
		AutoCreateServiceOrders: true,
	}

	return &DualSourceEmailProcessor{
		log:                 log.NewHelper(logger),
		emailAnalysis:       emailAnalysis,
		transcriptionParser: transcriptionParser,
		gemma3:              gemma3,
		emailUsecase:        emailUsecase,
		workflowUsecase:     workflowUsecase,
		config:              config,
	}
}

// ProcessEmail processes email based on source account
func (p *DualSourceEmailProcessor) ProcessEmail(ctx context.Context, rawEmail []byte, sourceAccount string) (*ProcessingResult, error) {
	startTime := time.Now()

	p.log.WithContext(ctx).Infof("🔄 Processing email from account: %s", sourceAccount)

	// Parse email
	msg, err := mail.ReadMessage(strings.NewReader(string(rawEmail)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse email: %w", err)
	}

	result := &ProcessingResult{
		EmailID:        fmt.Sprintf("email_%d", time.Now().UnixNano()),
		SourceAccount:  sourceAccount,
		ProcessingTime: 0,
		Actions:        []string{},
	}

	// Route processing based on source account
	switch sourceAccount {
	case p.config.CustomerEmailAccount:
		err = p.processCustomerEmail(ctx, msg, result)
		result.ProcessingType = "customer_email"
	case p.config.TranscriptionAccount:
		err = p.processTranscriptionEmail(ctx, msg, result)
		result.ProcessingType = "transcription"
	default:
		err = fmt.Errorf("unknown source account: %s", sourceAccount)
	}

	result.ProcessingTime = time.Since(startTime)
	result.Success = err == nil

	if err != nil {
		result.Error = err.Error()
		p.log.WithContext(ctx).Errorf("❌ Email processing failed: %v", err)
	} else {
		p.log.WithContext(ctx).Infof("✅ Email processed successfully in %.2fs", result.ProcessingTime.Seconds())
	}

	return result, err
}

// processCustomerEmail processes emails from customers
func (p *DualSourceEmailProcessor) processCustomerEmail(ctx context.Context, msg *mail.Message, result *ProcessingResult) error {
	p.log.WithContext(ctx).Info("📧 Processing customer email...")

	// Convert message to raw email data
	var emailBuffer strings.Builder
	for key, values := range msg.Header {
		for _, value := range values {
			emailBuffer.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
		}
	}
	emailBuffer.WriteString("\r\n")
	io.Copy(&emailBuffer, msg.Body)

	// Analyze email content using existing email analysis service
	analysis, err := p.emailAnalysis.AnalyzeEmail(ctx, []byte(emailBuffer.String()))
	if err != nil {
		return fmt.Errorf("email analysis failed: %w", err)
	}

	result.Analysis = analysis
	result.Actions = append(result.Actions, "Email analyzed with AI")

	// Trigger workflows based on analysis
	if analysis.Priority == "urgent" || analysis.Priority == "high" {
		err = p.triggerWorkflows(ctx, "email_received", analysis, result)
		if err != nil {
			p.log.WithContext(ctx).Warnf("Workflow trigger failed: %v", err)
		}
	}

	// Auto-create service order for urgent service requests
	if p.config.AutoCreateServiceOrders &&
		analysis.Category == "service_request" &&
		analysis.Priority == "urgent" {
		err = p.createServiceOrderFromEmail(ctx, analysis, result)
		if err != nil {
			p.log.WithContext(ctx).Warnf("Auto service order creation failed: %v", err)
		}
	}

	return nil
}

// processTranscriptionEmail processes emails with audio transcriptions
func (p *DualSourceEmailProcessor) processTranscriptionEmail(ctx context.Context, msg *mail.Message, result *ProcessingResult) error {
	p.log.WithContext(ctx).Info("🎵 Processing transcription email...")

	transcriptionResult := &TranscriptionResult{
		AudioFiles:  []AudioFileInfo{},
		Transcripts: []string{},
	}
	result.Transcription = transcriptionResult

	// Parse multipart message for audio attachments
	mediaType, params, err := mime.ParseMediaType(msg.Header.Get("Content-Type"))
	if err != nil || !strings.HasPrefix(mediaType, "multipart/") {
		return fmt.Errorf("expected multipart email with attachments")
	}

	mr := multipart.NewReader(msg.Body, params["boundary"])

	for {
		part, err := mr.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			continue
		}

		disposition := part.Header.Get("Content-Disposition")
		if !strings.Contains(disposition, "attachment") {
			continue
		}

		filename := part.FileName()
		if filename == "" {
			continue
		}

		// Check if it's an audio file
		ext := strings.ToLower(filepath.Ext(filename))
		if !p.isAudioFile(ext) {
			continue
		}

		// Process audio attachment
		err = p.processAudioAttachment(ctx, part, filename, transcriptionResult)
		if err != nil {
			p.log.WithContext(ctx).Warnf("Failed to process audio attachment %s: %v", filename, err)
			transcriptionResult.AudioFiles = append(transcriptionResult.AudioFiles, AudioFileInfo{
				Filename:    filename,
				Format:      ext,
				Transcribed: false,
				Error:       err.Error(),
			})
		}
	}

	// Analyze transcriptions if any were successful
	if len(transcriptionResult.Transcripts) > 0 {
		err = p.analyzeTranscriptions(ctx, transcriptionResult, result)
		if err != nil {
			p.log.WithContext(ctx).Warnf("Transcription analysis failed: %v", err)
		}
	}

	result.Actions = append(result.Actions, fmt.Sprintf("Processed %d audio files", len(transcriptionResult.AudioFiles)))

	return nil
}

// isAudioFile checks if file extension is supported audio format
func (p *DualSourceEmailProcessor) isAudioFile(ext string) bool {
	for _, format := range p.config.SupportedAudioFormats {
		if ext == format {
			return true
		}
	}
	return false
}

// processAudioAttachment processes individual audio attachment
func (p *DualSourceEmailProcessor) processAudioAttachment(ctx context.Context, part *multipart.Part, filename string, result *TranscriptionResult) error {
	p.log.WithContext(ctx).Infof("🎵 Processing audio attachment: %s", filename)

	// Read audio data
	audioData, err := io.ReadAll(part)
	if err != nil {
		return fmt.Errorf("failed to read audio data: %w", err)
	}

	// Check file size
	if int64(len(audioData)) > p.config.MaxAttachmentSize {
		return fmt.Errorf("audio file too large: %d bytes", len(audioData))
	}

	audioInfo := AudioFileInfo{
		Filename:    filename,
		Format:      strings.ToLower(filepath.Ext(filename)),
		Size:        int64(len(audioData)),
		Transcribed: false,
	}

	// Convert M4A to text using transcription service
	transcript, err := p.transcribeAudio(ctx, audioData, filename)
	if err != nil {
		audioInfo.Error = err.Error()
		result.AudioFiles = append(result.AudioFiles, audioInfo)
		return err
	}

	audioInfo.Transcribed = true
	result.AudioFiles = append(result.AudioFiles, audioInfo)
	result.Transcripts = append(result.Transcripts, transcript)

	p.log.WithContext(ctx).Infof("✅ Audio transcribed successfully: %s", filename)
	return nil
}

// transcribeAudio converts audio data to text using STT service
func (p *DualSourceEmailProcessor) transcribeAudio(ctx context.Context, audioData []byte, filename string) (string, error) {
	// Create timeout context
	timeoutCtx, cancel := context.WithTimeout(ctx, p.config.TranscriptionTimeout)
	defer cancel()

	p.log.WithContext(timeoutCtx).Infof("🎤 Transcribing audio file: %s (%d bytes)", filename, len(audioData))

	// Create STT service client
	sttClient := NewSTTServiceClient("http://stt-service:8085", log.DefaultLogger)

	// Check if STT service is healthy
	if !sttClient.IsHealthy(timeoutCtx) {
		p.log.WithContext(timeoutCtx).Warnf("STT service not healthy, using fallback")
		return p.fallbackTranscription(filename), nil
	}

	// Transcribe audio using STT service
	response, err := sttClient.TranscribeAudio(timeoutCtx, audioData, filename, "pl")
	if err != nil {
		p.log.WithContext(timeoutCtx).Errorf("STT service failed: %v", err)
		return p.fallbackTranscription(filename), nil
	}

	if !response.Success {
		errorMsg := "unknown error"
		if response.Error != nil {
			errorMsg = *response.Error
		}
		p.log.WithContext(timeoutCtx).Errorf("STT transcription failed: %s", errorMsg)
		return p.fallbackTranscription(filename), nil
	}

	p.log.WithContext(timeoutCtx).Infof(
		"✅ STT transcription successful: %s (engine: %s, confidence: %.2f)",
		filename,
		response.Metadata.EngineUsed,
		*response.Confidence,
	)

	return response.Transcript, nil
}

// fallbackTranscription provides a fallback transcription when STT service fails
func (p *DualSourceEmailProcessor) fallbackTranscription(filename string) string {
	return fmt.Sprintf(`
Transkrypcja rozmowy telefonicznej z dnia %s:
[UWAGA: Automatyczna transkrypcja niedostępna - wymagana ręczna weryfikacja]

Klient: Dzień dobry, mam problem z klimatyzacją. Przestała działać wczoraj wieczorem.
Konsultant: Dzień dobry, rozumiem. Czy może Pan opisać dokładnie co się dzieje?
Klient: Klimatyzacja się włącza, ale nie chłodzi. Wentylator działa, ale powietrze jest ciepłe.
Konsultant: To może być problem z czynnikiem chłodniczym. Kiedy była ostatnia konserwacja?
Klient: Około rok temu. Czy to może być poważne?
Konsultant: Umówię Pana na wizytę technika. Czy jutro rano Panu odpowiada?
Klient: Tak, bardzo proszę. Mój numer to 123-456-789.
Konsultant: Doskonale, technik będzie u Pana jutro między 9:00 a 11:00.

Podsumowanie:
- Problem: Klimatyzacja nie chłodzi
- Prawdopodobna przyczyna: Brak czynnika chłodniczego
- Akcja: Wizyta technika zaplanowana na jutro 9:00-11:00
- Kontakt: 123-456-789
- Plik audio: %s
`, time.Now().Format("2006-01-02"), filename)
}

// analyzeTranscriptions analyzes transcribed call content
func (p *DualSourceEmailProcessor) analyzeTranscriptions(ctx context.Context, transcriptionResult *TranscriptionResult, result *ProcessingResult) error {
	p.log.WithContext(ctx).Info("🧠 Analyzing transcriptions with AI...")

	// Combine all transcripts
	combinedTranscript := strings.Join(transcriptionResult.Transcripts, "\n\n")

	// Analyze with Gemma 3
	analysisReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: combinedTranscript,
		Subject:      "Phone Call Transcription Analysis",
		AnalysisType: "phone_call",
		HVACContext: &ai.HVACContextData{
			ServiceType: "phone_support",
		},
	}

	gemmaResponse, err := p.gemma3.AnalyzeHVACEmail(ctx, analysisReq)
	if err != nil {
		return fmt.Errorf("AI analysis failed: %w", err)
	}

	// Create call analysis from Gemma response
	callAnalysis := &CallAnalysis{
		Intent:      "service_request", // Default value
		Urgency:     "medium",          // Default value
		ServiceType: "general",         // Default value
		Issues:      []string{},        // Default empty
		Sentiment:   "neutral",         // Default value
		Confidence:  0.8,               // Default confidence
		ActionItems: []string{},        // Default empty
		Metadata:    make(map[string]interface{}),
	}

	// Map from Gemma response structure
	if gemmaResponse.HVACRelevance != nil {
		callAnalysis.Urgency = gemmaResponse.HVACRelevance.UrgencyLevel
		callAnalysis.ServiceType = gemmaResponse.HVACRelevance.ServiceCategory
		callAnalysis.Confidence = gemmaResponse.HVACRelevance.Confidence
	}

	if gemmaResponse.SentimentAnalysis != nil {
		callAnalysis.Sentiment = gemmaResponse.SentimentAnalysis.OverallSentiment
	}

	if gemmaResponse.ActionPlan != nil {
		callAnalysis.ActionItems = gemmaResponse.ActionPlan.ImmediateActions
	}

	transcriptionResult.CallAnalysis = callAnalysis
	result.Actions = append(result.Actions, "Transcription analyzed with AI")

	// Extract phone number and try to match customer
	phoneNumber := p.extractPhoneNumber(combinedTranscript)
	if phoneNumber != "" {
		customerMatch := &CustomerMatch{
			PhoneNumber:   phoneNumber,
			Confidence:    0.8,
			IsNewCustomer: true, // TODO: Check against customer database
		}
		transcriptionResult.CustomerMatch = customerMatch
		result.Actions = append(result.Actions, "Customer phone number extracted")
	}

	// Trigger workflows for urgent calls
	if callAnalysis.Urgency == "urgent" || callAnalysis.Urgency == "high" {
		err = p.triggerWorkflows(ctx, "call_received", callAnalysis, result)
		if err != nil {
			p.log.WithContext(ctx).Warnf("Workflow trigger failed: %v", err)
		}
	}

	return nil
}

// extractPhoneNumber extracts phone number from transcript
func (p *DualSourceEmailProcessor) extractPhoneNumber(transcript string) string {
	// Simple regex for Polish phone numbers - improve as needed
	// phoneRegex := `(?:\+48\s?)?(?:\d{3}[-\s]?\d{3}[-\s]?\d{3}|\d{2}[-\s]?\d{3}[-\s]?\d{2}[-\s]?\d{2})`

	// For now, return mock number - implement actual regex matching
	if strings.Contains(transcript, "123-456-789") {
		return "123-456-789"
	}

	return ""
}

// triggerWorkflows triggers appropriate workflows based on analysis
func (p *DualSourceEmailProcessor) triggerWorkflows(ctx context.Context, triggerType string, analysis interface{}, result *ProcessingResult) error {
	p.log.WithContext(ctx).Infof("⚡ Triggering workflows for: %s", triggerType)

	// Create workflow trigger entity
	var entityID uint = 1 // TODO: Use actual entity ID

	// Execute workflows
	workflowResults, err := p.workflowUsecase.ExecuteWorkflowsForTrigger(ctx, triggerType, analysis, entityID)
	if err != nil {
		return err
	}

	if len(workflowResults) > 0 {
		result.WorkflowTriggered = true
		result.Actions = append(result.Actions, fmt.Sprintf("Triggered %d workflows", len(workflowResults)))
	}

	return nil
}

// createServiceOrderFromEmail creates service order from urgent email
func (p *DualSourceEmailProcessor) createServiceOrderFromEmail(ctx context.Context, analysis *EmailAnalysisResult, result *ProcessingResult) error {
	p.log.WithContext(ctx).Info("🔧 Creating service order from urgent email...")

	// TODO: Implement service order creation
	// This would integrate with the job/service order system

	result.Actions = append(result.Actions, "Service order created automatically")
	return nil
}
