package email

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🎤 STT Service Client
// Client for communicating with the containerized STT service

// STTTranscriptionRequest represents a request to the STT service
type STTTranscriptionRequest struct {
	Language         string `json:"language"`
	UsePrimaryOnly   bool   `json:"use_primary_only"`
	IncludeTimestamps bool   `json:"include_timestamps"`
	IncludeConfidence bool   `json:"include_confidence"`
}

// STTWordTimestamp represents word-level timestamp
type STTWordTimestamp struct {
	Word       string  `json:"word"`
	StartTime  float64 `json:"start_time"`
	EndTime    float64 `json:"end_time"`
	Confidence *float64 `json:"confidence,omitempty"`
}

// STTSpeakerSegment represents speaker diarization segment
type STTSpeakerSegment struct {
	SpeakerID  string   `json:"speaker_id"`
	StartTime  float64  `json:"start_time"`
	EndTime    float64  `json:"end_time"`
	Text       string   `json:"text"`
	Confidence *float64 `json:"confidence,omitempty"`
}

// STTTranscriptionMetadata represents transcription metadata
type STTTranscriptionMetadata struct {
	EngineUsed       string   `json:"engine_used"`
	ProcessingTime   float64  `json:"processing_time"`
	AudioDuration    *float64 `json:"audio_duration,omitempty"`
	SampleRate       *int     `json:"sample_rate,omitempty"`
	Channels         *int     `json:"channels,omitempty"`
	FileSize         *int     `json:"file_size,omitempty"`
	LanguageDetected *string  `json:"language_detected,omitempty"`
	QualityScore     *float64 `json:"quality_score,omitempty"`
}

// STTTranscriptionResponse represents response from STT service
type STTTranscriptionResponse struct {
	Success      bool                      `json:"success"`
	Transcript   string                    `json:"transcript"`
	Confidence   *float64                  `json:"confidence,omitempty"`
	Words        []STTWordTimestamp        `json:"words,omitempty"`
	Speakers     []STTSpeakerSegment       `json:"speakers,omitempty"`
	Metadata     STTTranscriptionMetadata  `json:"metadata"`
	Error        *string                   `json:"error,omitempty"`
	FallbackUsed bool                      `json:"fallback_used"`
	Timestamp    time.Time                 `json:"timestamp"`
}

// STTEngineStatus represents engine health status
type STTEngineStatus struct {
	Status                 string     `json:"status"`
	Available              bool       `json:"available"`
	LastUsed               *time.Time `json:"last_used,omitempty"`
	TotalRequests          int        `json:"total_requests"`
	SuccessfulRequests     int        `json:"successful_requests"`
	AverageProcessingTime  *float64   `json:"average_processing_time,omitempty"`
	ErrorRate              *float64   `json:"error_rate,omitempty"`
	LastError              *string    `json:"last_error,omitempty"`
}

// STTHealthResponse represents health check response
type STTHealthResponse struct {
	Status    string                     `json:"status"`
	Timestamp time.Time                  `json:"timestamp"`
	Engines   map[string]STTEngineStatus `json:"engines"`
	Version   string                     `json:"version"`
}

// STTServiceClient client for STT service
type STTServiceClient struct {
	baseURL    string
	httpClient *http.Client
	log        *log.Helper
}

// NewSTTServiceClient creates a new STT service client
func NewSTTServiceClient(baseURL string, logger log.Logger) *STTServiceClient {
	return &STTServiceClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 120 * time.Second, // Generous timeout for transcription
		},
		log: log.NewHelper(logger),
	}
}

// TranscribeAudio transcribes audio using the STT service
func (c *STTServiceClient) TranscribeAudio(
	ctx context.Context,
	audioData []byte,
	filename string,
	language string,
) (*STTTranscriptionResponse, error) {
	c.log.WithContext(ctx).Infof("🎤 Transcribing audio with STT service: %s", filename)

	// Create multipart form data
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add audio file
	part, err := writer.CreateFormFile("audio_file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(audioData); err != nil {
		return nil, fmt.Errorf("failed to write audio data: %w", err)
	}

	// Add form fields
	if err := writer.WriteField("language", language); err != nil {
		return nil, fmt.Errorf("failed to write language field: %w", err)
	}

	if err := writer.WriteField("include_timestamps", "true"); err != nil {
		return nil, fmt.Errorf("failed to write timestamps field: %w", err)
	}

	if err := writer.WriteField("include_confidence", "true"); err != nil {
		return nil, fmt.Errorf("failed to write confidence field: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/transcribe", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("STT service error (status %d): %s", resp.StatusCode, string(body))
	}

	// Parse response
	var response STTTranscriptionResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	c.log.WithContext(ctx).Infof(
		"✅ STT transcription completed: %s (engine: %s, time: %.2fs)",
		filename,
		response.Metadata.EngineUsed,
		response.Metadata.ProcessingTime,
	)

	return &response, nil
}

// GetHealth checks STT service health
func (c *STTServiceClient) GetHealth(ctx context.Context) (*STTHealthResponse, error) {
	url := fmt.Sprintf("%s/health", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("health check failed (status %d): %s", resp.StatusCode, string(body))
	}

	var health STTHealthResponse
	if err := json.Unmarshal(body, &health); err != nil {
		return nil, fmt.Errorf("failed to parse health response: %w", err)
	}

	return &health, nil
}

// GetEnginesStatus gets status of both STT engines
func (c *STTServiceClient) GetEnginesStatus(ctx context.Context) (map[string]STTEngineStatus, error) {
	url := fmt.Sprintf("%s/engines/status", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("engines status check failed (status %d): %s", resp.StatusCode, string(body))
	}

	var engines map[string]STTEngineStatus
	if err := json.Unmarshal(body, &engines); err != nil {
		return nil, fmt.Errorf("failed to parse engines response: %w", err)
	}

	return engines, nil
}

// IsHealthy checks if STT service is healthy
func (c *STTServiceClient) IsHealthy(ctx context.Context) bool {
	health, err := c.GetHealth(ctx)
	if err != nil {
		c.log.WithContext(ctx).Warnf("STT health check failed: %v", err)
		return false
	}

	return health.Status == "healthy"
}
