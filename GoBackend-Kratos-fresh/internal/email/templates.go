package email

import (
	"bytes"
	"fmt"
	"html/template"
	"time"
)

// HVACEmailTemplates provides HVAC-specific email templates
type HVACEmailTemplates struct {
	templates map[string]*EmailTemplate
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	Name        string
	Subject     string
	TextBody    string
	HTMLBody    string
	Variables   []string
	Description string
}

// TemplateData represents data for template rendering
type TemplateData struct {
	CustomerName    string
	CustomerEmail   string
	CustomerAddress string
	CustomerPhone   string
	CompanyName     string
	CompanyPhone    string
	CompanyEmail    string
	CompanyAddress  string
	ServiceType     string
	SystemType      string
	EstimatedCost   string
	Timeline        string
	LastServiceDate string
	NextServiceDate string
	TechnicianName  string
	AppointmentDate string
	AppointmentTime string
	JobDescription  string
	InvoiceNumber   string
	InvoiceAmount   string
	DueDate         string
	PaymentLink     string
	EmergencyPhone  string
	WebsiteURL      string
}

// NewHVACEmailTemplates creates a new HVAC email templates instance
func NewHVACEmailTemplates() *HVACEmailTemplates {
	templates := &HVACEmailTemplates{
		templates: make(map[string]*EmailTemplate),
	}

	templates.initializeTemplates()
	return templates
}

// initializeTemplates sets up all HVAC email templates
func (h *HVACEmailTemplates) initializeTemplates() {
	// Service Reminder Template
	h.templates["service_reminder"] = &EmailTemplate{
		Name:        "Service Reminder",
		Subject:     "HVAC Maintenance Reminder - {{.CustomerName}}",
		Description: "Reminds customers about upcoming HVAC maintenance",
		Variables:   []string{"CustomerName", "CustomerAddress", "SystemType", "LastServiceDate", "CompanyName", "CompanyPhone"},
		TextBody: `Dear {{.CustomerName}},

This is a friendly reminder that your HVAC system is due for maintenance.
Regular maintenance helps ensure optimal performance and extends equipment life.

Service Details:
- Customer: {{.CustomerName}}
- Address: {{.CustomerAddress}}
- System Type: {{.SystemType}}
- Last Service: {{.LastServiceDate}}

Please contact us at {{.CompanyPhone}} to schedule your appointment.

Best regards,
{{.CompanyName}} HVAC Services`,
		HTMLBody: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>HVAC Maintenance Reminder</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c5aa0;">HVAC Maintenance Reminder</h2>
        <p>Dear {{.CustomerName}},</p>
        <p>This is a friendly reminder that your HVAC system is due for maintenance.</p>
        <p>Regular maintenance helps ensure optimal performance and extends equipment life.</p>

        <div style="background-color: #f4f4f4; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #2c5aa0;">Service Details:</h3>
            <ul style="list-style-type: none; padding: 0;">
                <li><strong>Customer:</strong> {{.CustomerName}}</li>
                <li><strong>Address:</strong> {{.CustomerAddress}}</li>
                <li><strong>System Type:</strong> {{.SystemType}}</li>
                <li><strong>Last Service:</strong> {{.LastServiceDate}}</li>
            </ul>
        </div>

        <p>Please contact us at <strong>{{.CompanyPhone}}</strong> to schedule your appointment.</p>

        <p>Best regards,<br>
        <strong>{{.CompanyName}} HVAC Services</strong></p>
    </div>
</body>
</html>`,
	}

	// Quote Follow-up Template
	h.templates["quote_follow_up"] = &EmailTemplate{
		Name:        "Quote Follow-up",
		Subject:     "Your HVAC Service Quote - {{.CustomerName}}",
		Description: "Sends service quotes to customers",
		Variables:   []string{"CustomerName", "ServiceType", "EstimatedCost", "Timeline", "CompanyName", "CompanyPhone"},
		TextBody: `Dear {{.CustomerName}},

Thank you for your interest in our HVAC services. Please find your quote details below.

Quote Summary:
- Service: {{.ServiceType}}
- Estimated Cost: {{.EstimatedCost}}
- Timeline: {{.Timeline}}

This quote is valid for 30 days. Please don't hesitate to contact us at {{.CompanyPhone}} with any questions.

Best regards,
{{.CompanyName}}`,
		HTMLBody: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Your HVAC Service Quote</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c5aa0;">Your HVAC Service Quote</h2>
        <p>Dear {{.CustomerName}},</p>
        <p>Thank you for your interest in our HVAC services. Please find your quote details below.</p>

        <div style="background-color: #f4f4f4; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #2c5aa0;">Quote Summary:</h3>
            <ul style="list-style-type: none; padding: 0;">
                <li><strong>Service:</strong> {{.ServiceType}}</li>
                <li><strong>Estimated Cost:</strong> {{.EstimatedCost}}</li>
                <li><strong>Timeline:</strong> {{.Timeline}}</li>
            </ul>
        </div>

        <p style="background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107;">
            <em>This quote is valid for 30 days.</em>
        </p>

        <p>Please don't hesitate to contact us at <strong>{{.CompanyPhone}}</strong> with any questions.</p>

        <p>Best regards,<br>
        <strong>{{.CompanyName}}</strong></p>
    </div>
</body>
</html>`,
	}

	// Appointment Confirmation Template
	h.templates["appointment_confirmation"] = &EmailTemplate{
		Name:        "Appointment Confirmation",
		Subject:     "HVAC Service Appointment Confirmed - {{.AppointmentDate}}",
		Description: "Confirms scheduled HVAC service appointments",
		Variables:   []string{"CustomerName", "AppointmentDate", "AppointmentTime", "TechnicianName", "ServiceType", "CustomerAddress", "CompanyName", "CompanyPhone", "EmergencyPhone"},
		TextBody: `Dear {{.CustomerName}},

Your HVAC service appointment has been confirmed!

Appointment Details:
- Date: {{.AppointmentDate}}
- Time: {{.AppointmentTime}}
- Service: {{.ServiceType}}
- Technician: {{.TechnicianName}}
- Location: {{.CustomerAddress}}

Our technician will arrive within the scheduled time window. Please ensure someone is available to provide access to your HVAC system.

If you need to reschedule, please call us at {{.CompanyPhone}}.
For emergencies, call {{.EmergencyPhone}}.

Best regards,
{{.CompanyName}} HVAC Services`,
		HTMLBody: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>HVAC Service Appointment Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #28a745;">✅ HVAC Service Appointment Confirmed</h2>
        <p>Dear {{.CustomerName}},</p>
        <p>Your HVAC service appointment has been confirmed!</p>

        <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #155724;">Appointment Details:</h3>
            <ul style="list-style-type: none; padding: 0;">
                <li><strong>📅 Date:</strong> {{.AppointmentDate}}</li>
                <li><strong>🕐 Time:</strong> {{.AppointmentTime}}</li>
                <li><strong>🔧 Service:</strong> {{.ServiceType}}</li>
                <li><strong>👨‍🔧 Technician:</strong> {{.TechnicianName}}</li>
                <li><strong>📍 Location:</strong> {{.CustomerAddress}}</li>
            </ul>
        </div>

        <p>Our technician will arrive within the scheduled time window. Please ensure someone is available to provide access to your HVAC system.</p>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>📞 Need to reschedule?</strong> Call us at {{.CompanyPhone}}</p>
            <p><strong>🚨 Emergency service:</strong> Call {{.EmergencyPhone}}</p>
        </div>

        <p>Best regards,<br>
        <strong>{{.CompanyName}} HVAC Services</strong></p>
    </div>
</body>
</html>`,
	}

	// Invoice Notification Template
	h.templates["invoice_notification"] = &EmailTemplate{
		Name:        "Invoice Notification",
		Subject:     "HVAC Service Invoice #{{.InvoiceNumber}} - {{.CustomerName}}",
		Description: "Sends invoices to customers after service completion",
		Variables:   []string{"CustomerName", "InvoiceNumber", "InvoiceAmount", "ServiceType", "DueDate", "PaymentLink", "CompanyName", "CompanyPhone"},
		TextBody: `Dear {{.CustomerName}},

Thank you for choosing our HVAC services. Please find your invoice details below.

Invoice Details:
- Invoice Number: {{.InvoiceNumber}}
- Service: {{.ServiceType}}
- Amount: {{.InvoiceAmount}}
- Due Date: {{.DueDate}}

Payment Options:
- Online: {{.PaymentLink}}
- Phone: Call {{.CompanyPhone}}

We appreciate your business!

Best regards,
{{.CompanyName}} HVAC Services`,
		HTMLBody: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>HVAC Service Invoice</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c5aa0;">💰 HVAC Service Invoice</h2>
        <p>Dear {{.CustomerName}},</p>
        <p>Thank you for choosing our HVAC services. Please find your invoice details below.</p>

        <div style="background-color: #f4f4f4; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #2c5aa0;">Invoice Details:</h3>
            <ul style="list-style-type: none; padding: 0;">
                <li><strong>📄 Invoice Number:</strong> {{.InvoiceNumber}}</li>
                <li><strong>🔧 Service:</strong> {{.ServiceType}}</li>
                <li><strong>💵 Amount:</strong> {{.InvoiceAmount}}</li>
                <li><strong>📅 Due Date:</strong> {{.DueDate}}</li>
            </ul>
        </div>

        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #17a2b8;">
            <h3 style="margin-top: 0; color: #0c5460;">Payment Options:</h3>
            <p><strong>💻 Online:</strong> <a href="{{.PaymentLink}}" style="color: #007bff;">Pay Now</a></p>
            <p><strong>📞 Phone:</strong> Call {{.CompanyPhone}}</p>
        </div>

        <p>We appreciate your business!</p>

        <p>Best regards,<br>
        <strong>{{.CompanyName}} HVAC Services</strong></p>
    </div>
</body>
</html>`,
	}
}

// GetTemplate returns a template by name
func (h *HVACEmailTemplates) GetTemplate(name string) (*EmailTemplate, error) {
	template, exists := h.templates[name]
	if !exists {
		return nil, fmt.Errorf("template '%s' not found", name)
	}
	return template, nil
}

// RenderTemplate renders a template with the provided data
func (h *HVACEmailTemplates) RenderTemplate(templateName string, data *TemplateData) (*EmailMessage, error) {
	tmpl, err := h.GetTemplate(templateName)
	if err != nil {
		return nil, err
	}

	// Render subject
	subject, err := h.renderText(tmpl.Subject, data)
	if err != nil {
		return nil, fmt.Errorf("failed to render subject: %w", err)
	}

	// Render text body
	textBody, err := h.renderText(tmpl.TextBody, data)
	if err != nil {
		return nil, fmt.Errorf("failed to render text body: %w", err)
	}

	// Render HTML body
	htmlBody, err := h.renderText(tmpl.HTMLBody, data)
	if err != nil {
		return nil, fmt.Errorf("failed to render HTML body: %w", err)
	}

	return &EmailMessage{
		Subject:   subject,
		Body:      textBody,
		HTMLBody:  htmlBody,
		Timestamp: time.Now(),
	}, nil
}

// renderText renders a text template with data
func (h *HVACEmailTemplates) renderText(templateText string, data *TemplateData) (string, error) {
	tmpl, err := template.New("email").Parse(templateText)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

// ListTemplates returns all available template names
func (h *HVACEmailTemplates) ListTemplates() []string {
	var names []string
	for name := range h.templates {
		names = append(names, name)
	}
	return names
}

// GetTemplateVariables returns the variables used by a template
func (h *HVACEmailTemplates) GetTemplateVariables(templateName string) ([]string, error) {
	tmpl, err := h.GetTemplate(templateName)
	if err != nil {
		return nil, err
	}
	return tmpl.Variables, nil
}
