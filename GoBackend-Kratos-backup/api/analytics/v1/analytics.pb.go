// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: analytics/v1/analytics.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Executive Dashboard
type GetExecutiveDashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetExecutiveDashboardRequest) Reset() {
	*x = GetExecutiveDashboardRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExecutiveDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExecutiveDashboardRequest) ProtoMessage() {}

func (x *GetExecutiveDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExecutiveDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetExecutiveDashboardRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{0}
}

type GetExecutiveDashboardResponse struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	DashboardType string                     `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp     `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          *ExecutiveDashboardSummary `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget         `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetExecutiveDashboardResponse) Reset() {
	*x = GetExecutiveDashboardResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExecutiveDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExecutiveDashboardResponse) ProtoMessage() {}

func (x *GetExecutiveDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExecutiveDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetExecutiveDashboardResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{1}
}

func (x *GetExecutiveDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetExecutiveDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetExecutiveDashboardResponse) GetData() *ExecutiveDashboardSummary {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetExecutiveDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type ExecutiveDashboardSummary struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Period            string                 `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`
	TodayRevenue      float64                `protobuf:"fixed64,2,opt,name=today_revenue,json=todayRevenue,proto3" json:"today_revenue,omitempty"`
	TodayJobs         int32                  `protobuf:"varint,3,opt,name=today_jobs,json=todayJobs,proto3" json:"today_jobs,omitempty"`
	TodaySatisfaction float64                `protobuf:"fixed64,4,opt,name=today_satisfaction,json=todaySatisfaction,proto3" json:"today_satisfaction,omitempty"`
	TodayEfficiency   float64                `protobuf:"fixed64,5,opt,name=today_efficiency,json=todayEfficiency,proto3" json:"today_efficiency,omitempty"`
	WeekRevenue       float64                `protobuf:"fixed64,6,opt,name=week_revenue,json=weekRevenue,proto3" json:"week_revenue,omitempty"`
	WeekJobs          int32                  `protobuf:"varint,7,opt,name=week_jobs,json=weekJobs,proto3" json:"week_jobs,omitempty"`
	MonthRevenue      float64                `protobuf:"fixed64,8,opt,name=month_revenue,json=monthRevenue,proto3" json:"month_revenue,omitempty"`
	MonthJobs         int32                  `protobuf:"varint,9,opt,name=month_jobs,json=monthJobs,proto3" json:"month_jobs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ExecutiveDashboardSummary) Reset() {
	*x = ExecutiveDashboardSummary{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecutiveDashboardSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutiveDashboardSummary) ProtoMessage() {}

func (x *ExecutiveDashboardSummary) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutiveDashboardSummary.ProtoReflect.Descriptor instead.
func (*ExecutiveDashboardSummary) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{2}
}

func (x *ExecutiveDashboardSummary) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

func (x *ExecutiveDashboardSummary) GetTodayRevenue() float64 {
	if x != nil {
		return x.TodayRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodayJobs() int32 {
	if x != nil {
		return x.TodayJobs
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodaySatisfaction() float64 {
	if x != nil {
		return x.TodaySatisfaction
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodayEfficiency() float64 {
	if x != nil {
		return x.TodayEfficiency
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetWeekRevenue() float64 {
	if x != nil {
		return x.WeekRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetWeekJobs() int32 {
	if x != nil {
		return x.WeekJobs
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetMonthRevenue() float64 {
	if x != nil {
		return x.MonthRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetMonthJobs() int32 {
	if x != nil {
		return x.MonthJobs
	}
	return 0
}

// Customer Insights Dashboard
type GetCustomerInsightsDashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerInsightsDashboardRequest) Reset() {
	*x = GetCustomerInsightsDashboardRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerInsightsDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerInsightsDashboardRequest) ProtoMessage() {}

func (x *GetCustomerInsightsDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerInsightsDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerInsightsDashboardRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{3}
}

type GetCustomerInsightsDashboardResponse struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	DashboardType string                       `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp       `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          []*CustomerInsightsDashboard `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget           `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerInsightsDashboardResponse) Reset() {
	*x = GetCustomerInsightsDashboardResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerInsightsDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerInsightsDashboardResponse) ProtoMessage() {}

func (x *GetCustomerInsightsDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerInsightsDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerInsightsDashboardResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomerInsightsDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetCustomerInsightsDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetCustomerInsightsDashboardResponse) GetData() []*CustomerInsightsDashboard {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetCustomerInsightsDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type CustomerInsightsDashboard struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	LoyaltyTier      string                 `protobuf:"bytes,1,opt,name=loyalty_tier,json=loyaltyTier,proto3" json:"loyalty_tier,omitempty"`
	CustomerCount    int32                  `protobuf:"varint,2,opt,name=customer_count,json=customerCount,proto3" json:"customer_count,omitempty"`
	AvgLifetimeValue float64                `protobuf:"fixed64,3,opt,name=avg_lifetime_value,json=avgLifetimeValue,proto3" json:"avg_lifetime_value,omitempty"`
	AvgSatisfaction  float64                `protobuf:"fixed64,4,opt,name=avg_satisfaction,json=avgSatisfaction,proto3" json:"avg_satisfaction,omitempty"`
	AvgChurnRisk     float64                `protobuf:"fixed64,5,opt,name=avg_churn_risk,json=avgChurnRisk,proto3" json:"avg_churn_risk,omitempty"`
	TierTotalRevenue float64                `protobuf:"fixed64,6,opt,name=tier_total_revenue,json=tierTotalRevenue,proto3" json:"tier_total_revenue,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CustomerInsightsDashboard) Reset() {
	*x = CustomerInsightsDashboard{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerInsightsDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerInsightsDashboard) ProtoMessage() {}

func (x *CustomerInsightsDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerInsightsDashboard.ProtoReflect.Descriptor instead.
func (*CustomerInsightsDashboard) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerInsightsDashboard) GetLoyaltyTier() string {
	if x != nil {
		return x.LoyaltyTier
	}
	return ""
}

func (x *CustomerInsightsDashboard) GetCustomerCount() int32 {
	if x != nil {
		return x.CustomerCount
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgLifetimeValue() float64 {
	if x != nil {
		return x.AvgLifetimeValue
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgSatisfaction() float64 {
	if x != nil {
		return x.AvgSatisfaction
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgChurnRisk() float64 {
	if x != nil {
		return x.AvgChurnRisk
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetTierTotalRevenue() float64 {
	if x != nil {
		return x.TierTotalRevenue
	}
	return 0
}

// Operational Dashboard
type GetOperationalDashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOperationalDashboardRequest) Reset() {
	*x = GetOperationalDashboardRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOperationalDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOperationalDashboardRequest) ProtoMessage() {}

func (x *GetOperationalDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOperationalDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetOperationalDashboardRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{6}
}

type GetOperationalDashboardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DashboardType string                 `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          *OperationalAnalytics  `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget     `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOperationalDashboardResponse) Reset() {
	*x = GetOperationalDashboardResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOperationalDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOperationalDashboardResponse) ProtoMessage() {}

func (x *GetOperationalDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOperationalDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetOperationalDashboardResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{7}
}

func (x *GetOperationalDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetOperationalDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetOperationalDashboardResponse) GetData() *OperationalAnalytics {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetOperationalDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type OperationalAnalytics struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Id                      uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AnalysisDate            *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=analysis_date,json=analysisDate,proto3" json:"analysis_date,omitempty"`
	TotalActiveJobs         int32                  `protobuf:"varint,3,opt,name=total_active_jobs,json=totalActiveJobs,proto3" json:"total_active_jobs,omitempty"`
	CompletedJobs           int32                  `protobuf:"varint,4,opt,name=completed_jobs,json=completedJobs,proto3" json:"completed_jobs,omitempty"`
	CancelledJobs           int32                  `protobuf:"varint,5,opt,name=cancelled_jobs,json=cancelledJobs,proto3" json:"cancelled_jobs,omitempty"`
	EmergencyJobs           int32                  `protobuf:"varint,6,opt,name=emergency_jobs,json=emergencyJobs,proto3" json:"emergency_jobs,omitempty"`
	AverageResponseTimeMs   int64                  `protobuf:"varint,7,opt,name=average_response_time_ms,json=averageResponseTimeMs,proto3" json:"average_response_time_ms,omitempty"`
	AverageCompletionTimeMs int64                  `protobuf:"varint,8,opt,name=average_completion_time_ms,json=averageCompletionTimeMs,proto3" json:"average_completion_time_ms,omitempty"`
	TechnicianEfficiency    float64                `protobuf:"fixed64,9,opt,name=technician_efficiency,json=technicianEfficiency,proto3" json:"technician_efficiency,omitempty"`
	EquipmentUtilization    float64                `protobuf:"fixed64,10,opt,name=equipment_utilization,json=equipmentUtilization,proto3" json:"equipment_utilization,omitempty"`
	CustomerSatisfaction    float64                `protobuf:"fixed64,11,opt,name=customer_satisfaction,json=customerSatisfaction,proto3" json:"customer_satisfaction,omitempty"`
	FirstTimeFixRate        float64                `protobuf:"fixed64,12,opt,name=first_time_fix_rate,json=firstTimeFixRate,proto3" json:"first_time_fix_rate,omitempty"`
	CallbackRate            float64                `protobuf:"fixed64,13,opt,name=callback_rate,json=callbackRate,proto3" json:"callback_rate,omitempty"`
	PartsAvailability       float64                `protobuf:"fixed64,14,opt,name=parts_availability,json=partsAvailability,proto3" json:"parts_availability,omitempty"`
	FuelCosts               float64                `protobuf:"fixed64,15,opt,name=fuel_costs,json=fuelCosts,proto3" json:"fuel_costs,omitempty"`
	OvertimeHours           float64                `protobuf:"fixed64,16,opt,name=overtime_hours,json=overtimeHours,proto3" json:"overtime_hours,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *OperationalAnalytics) Reset() {
	*x = OperationalAnalytics{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OperationalAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalAnalytics) ProtoMessage() {}

func (x *OperationalAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalAnalytics.ProtoReflect.Descriptor instead.
func (*OperationalAnalytics) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{8}
}

func (x *OperationalAnalytics) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OperationalAnalytics) GetAnalysisDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AnalysisDate
	}
	return nil
}

func (x *OperationalAnalytics) GetTotalActiveJobs() int32 {
	if x != nil {
		return x.TotalActiveJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetCompletedJobs() int32 {
	if x != nil {
		return x.CompletedJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetCancelledJobs() int32 {
	if x != nil {
		return x.CancelledJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetEmergencyJobs() int32 {
	if x != nil {
		return x.EmergencyJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetAverageResponseTimeMs() int64 {
	if x != nil {
		return x.AverageResponseTimeMs
	}
	return 0
}

func (x *OperationalAnalytics) GetAverageCompletionTimeMs() int64 {
	if x != nil {
		return x.AverageCompletionTimeMs
	}
	return 0
}

func (x *OperationalAnalytics) GetTechnicianEfficiency() float64 {
	if x != nil {
		return x.TechnicianEfficiency
	}
	return 0
}

func (x *OperationalAnalytics) GetEquipmentUtilization() float64 {
	if x != nil {
		return x.EquipmentUtilization
	}
	return 0
}

func (x *OperationalAnalytics) GetCustomerSatisfaction() float64 {
	if x != nil {
		return x.CustomerSatisfaction
	}
	return 0
}

func (x *OperationalAnalytics) GetFirstTimeFixRate() float64 {
	if x != nil {
		return x.FirstTimeFixRate
	}
	return 0
}

func (x *OperationalAnalytics) GetCallbackRate() float64 {
	if x != nil {
		return x.CallbackRate
	}
	return 0
}

func (x *OperationalAnalytics) GetPartsAvailability() float64 {
	if x != nil {
		return x.PartsAvailability
	}
	return 0
}

func (x *OperationalAnalytics) GetFuelCosts() float64 {
	if x != nil {
		return x.FuelCosts
	}
	return 0
}

func (x *OperationalAnalytics) GetOvertimeHours() float64 {
	if x != nil {
		return x.OvertimeHours
	}
	return 0
}

// Performance Trends
type GetPerformanceTrendsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Weeks         int32                  `protobuf:"varint,1,opt,name=weeks,proto3" json:"weeks,omitempty"` // Number of weeks to fetch
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPerformanceTrendsRequest) Reset() {
	*x = GetPerformanceTrendsRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPerformanceTrendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPerformanceTrendsRequest) ProtoMessage() {}

func (x *GetPerformanceTrendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPerformanceTrendsRequest.ProtoReflect.Descriptor instead.
func (*GetPerformanceTrendsRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{9}
}

func (x *GetPerformanceTrendsRequest) GetWeeks() int32 {
	if x != nil {
		return x.Weeks
	}
	return 0
}

type GetPerformanceTrendsResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Trends        []*PerformanceTrendsDashboard `protobuf:"bytes,1,rep,name=trends,proto3" json:"trends,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPerformanceTrendsResponse) Reset() {
	*x = GetPerformanceTrendsResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPerformanceTrendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPerformanceTrendsResponse) ProtoMessage() {}

func (x *GetPerformanceTrendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPerformanceTrendsResponse.ProtoReflect.Descriptor instead.
func (*GetPerformanceTrendsResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{10}
}

func (x *GetPerformanceTrendsResponse) GetTrends() []*PerformanceTrendsDashboard {
	if x != nil {
		return x.Trends
	}
	return nil
}

type PerformanceTrendsDashboard struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	WeekStart        *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=week_start,json=weekStart,proto3" json:"week_start,omitempty"`
	AvgEfficiency    float64                `protobuf:"fixed64,2,opt,name=avg_efficiency,json=avgEfficiency,proto3" json:"avg_efficiency,omitempty"`
	AvgSatisfaction  float64                `protobuf:"fixed64,3,opt,name=avg_satisfaction,json=avgSatisfaction,proto3" json:"avg_satisfaction,omitempty"`
	AvgFirstTimeFix  float64                `protobuf:"fixed64,4,opt,name=avg_first_time_fix,json=avgFirstTimeFix,proto3" json:"avg_first_time_fix,omitempty"`
	TotalJobs        int32                  `protobuf:"varint,5,opt,name=total_jobs,json=totalJobs,proto3" json:"total_jobs,omitempty"`
	AvgResponseHours float64                `protobuf:"fixed64,6,opt,name=avg_response_hours,json=avgResponseHours,proto3" json:"avg_response_hours,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PerformanceTrendsDashboard) Reset() {
	*x = PerformanceTrendsDashboard{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceTrendsDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceTrendsDashboard) ProtoMessage() {}

func (x *PerformanceTrendsDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceTrendsDashboard.ProtoReflect.Descriptor instead.
func (*PerformanceTrendsDashboard) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{11}
}

func (x *PerformanceTrendsDashboard) GetWeekStart() *timestamppb.Timestamp {
	if x != nil {
		return x.WeekStart
	}
	return nil
}

func (x *PerformanceTrendsDashboard) GetAvgEfficiency() float64 {
	if x != nil {
		return x.AvgEfficiency
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgSatisfaction() float64 {
	if x != nil {
		return x.AvgSatisfaction
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgFirstTimeFix() float64 {
	if x != nil {
		return x.AvgFirstTimeFix
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetTotalJobs() int32 {
	if x != nil {
		return x.TotalJobs
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgResponseHours() float64 {
	if x != nil {
		return x.AvgResponseHours
	}
	return 0
}

// KPIs
type GetKPIsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      string                 `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"` // Optional filter by category
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKPIsRequest) Reset() {
	*x = GetKPIsRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKPIsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKPIsRequest) ProtoMessage() {}

func (x *GetKPIsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKPIsRequest.ProtoReflect.Descriptor instead.
func (*GetKPIsRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{12}
}

func (x *GetKPIsRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type GetKPIsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kpis          []*KPITracking         `protobuf:"bytes,1,rep,name=kpis,proto3" json:"kpis,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKPIsResponse) Reset() {
	*x = GetKPIsResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKPIsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKPIsResponse) ProtoMessage() {}

func (x *GetKPIsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKPIsResponse.ProtoReflect.Descriptor instead.
func (*GetKPIsResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{13}
}

func (x *GetKPIsResponse) GetKpis() []*KPITracking {
	if x != nil {
		return x.Kpis
	}
	return nil
}

type UpdateKPIRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KpiName       string                 `protobuf:"bytes,1,opt,name=kpi_name,json=kpiName,proto3" json:"kpi_name,omitempty"`
	Category      string                 `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	Value         float64                `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
	Target        *float64               `protobuf:"fixed64,4,opt,name=target,proto3,oneof" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateKPIRequest) Reset() {
	*x = UpdateKPIRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKPIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKPIRequest) ProtoMessage() {}

func (x *UpdateKPIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKPIRequest.ProtoReflect.Descriptor instead.
func (*UpdateKPIRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateKPIRequest) GetKpiName() string {
	if x != nil {
		return x.KpiName
	}
	return ""
}

func (x *UpdateKPIRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *UpdateKPIRequest) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *UpdateKPIRequest) GetTarget() float64 {
	if x != nil && x.Target != nil {
		return *x.Target
	}
	return 0
}

type UpdateKPIResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateKPIResponse) Reset() {
	*x = UpdateKPIResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKPIResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKPIResponse) ProtoMessage() {}

func (x *UpdateKPIResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKPIResponse.ProtoReflect.Descriptor instead.
func (*UpdateKPIResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateKPIResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateKPIResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type KPITracking struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	KpiName           string                 `protobuf:"bytes,2,opt,name=kpi_name,json=kpiName,proto3" json:"kpi_name,omitempty"`
	KpiCategory       string                 `protobuf:"bytes,3,opt,name=kpi_category,json=kpiCategory,proto3" json:"kpi_category,omitempty"`
	CurrentValue      float64                `protobuf:"fixed64,4,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	TargetValue       *float64               `protobuf:"fixed64,5,opt,name=target_value,json=targetValue,proto3,oneof" json:"target_value,omitempty"`
	PreviousValue     *float64               `protobuf:"fixed64,6,opt,name=previous_value,json=previousValue,proto3,oneof" json:"previous_value,omitempty"`
	TrendDirection    string                 `protobuf:"bytes,7,opt,name=trend_direction,json=trendDirection,proto3" json:"trend_direction,omitempty"`
	TrendPercentage   *float64               `protobuf:"fixed64,8,opt,name=trend_percentage,json=trendPercentage,proto3,oneof" json:"trend_percentage,omitempty"`
	MeasurementDate   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=measurement_date,json=measurementDate,proto3" json:"measurement_date,omitempty"`
	MeasurementTime   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=measurement_time,json=measurementTime,proto3" json:"measurement_time,omitempty"`
	AlertThresholdMin *float64               `protobuf:"fixed64,11,opt,name=alert_threshold_min,json=alertThresholdMin,proto3,oneof" json:"alert_threshold_min,omitempty"`
	AlertThresholdMax *float64               `protobuf:"fixed64,12,opt,name=alert_threshold_max,json=alertThresholdMax,proto3,oneof" json:"alert_threshold_max,omitempty"`
	IsAlertTriggered  bool                   `protobuf:"varint,13,opt,name=is_alert_triggered,json=isAlertTriggered,proto3" json:"is_alert_triggered,omitempty"`
	Notes             string                 `protobuf:"bytes,14,opt,name=notes,proto3" json:"notes,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *KPITracking) Reset() {
	*x = KPITracking{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KPITracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KPITracking) ProtoMessage() {}

func (x *KPITracking) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KPITracking.ProtoReflect.Descriptor instead.
func (*KPITracking) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{16}
}

func (x *KPITracking) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KPITracking) GetKpiName() string {
	if x != nil {
		return x.KpiName
	}
	return ""
}

func (x *KPITracking) GetKpiCategory() string {
	if x != nil {
		return x.KpiCategory
	}
	return ""
}

func (x *KPITracking) GetCurrentValue() float64 {
	if x != nil {
		return x.CurrentValue
	}
	return 0
}

func (x *KPITracking) GetTargetValue() float64 {
	if x != nil && x.TargetValue != nil {
		return *x.TargetValue
	}
	return 0
}

func (x *KPITracking) GetPreviousValue() float64 {
	if x != nil && x.PreviousValue != nil {
		return *x.PreviousValue
	}
	return 0
}

func (x *KPITracking) GetTrendDirection() string {
	if x != nil {
		return x.TrendDirection
	}
	return ""
}

func (x *KPITracking) GetTrendPercentage() float64 {
	if x != nil && x.TrendPercentage != nil {
		return *x.TrendPercentage
	}
	return 0
}

func (x *KPITracking) GetMeasurementDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MeasurementDate
	}
	return nil
}

func (x *KPITracking) GetMeasurementTime() *timestamppb.Timestamp {
	if x != nil {
		return x.MeasurementTime
	}
	return nil
}

func (x *KPITracking) GetAlertThresholdMin() float64 {
	if x != nil && x.AlertThresholdMin != nil {
		return *x.AlertThresholdMin
	}
	return 0
}

func (x *KPITracking) GetAlertThresholdMax() float64 {
	if x != nil && x.AlertThresholdMax != nil {
		return *x.AlertThresholdMax
	}
	return 0
}

func (x *KPITracking) GetIsAlertTriggered() bool {
	if x != nil {
		return x.IsAlertTriggered
	}
	return false
}

func (x *KPITracking) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

// Real-time Metrics
type GetRealTimeMetricsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRealTimeMetricsRequest) Reset() {
	*x = GetRealTimeMetricsRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRealTimeMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRealTimeMetricsRequest) ProtoMessage() {}

func (x *GetRealTimeMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRealTimeMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetRealTimeMetricsRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{17}
}

type GetRealTimeMetricsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metrics       *structpb.Struct       `protobuf:"bytes,1,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRealTimeMetricsResponse) Reset() {
	*x = GetRealTimeMetricsResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRealTimeMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRealTimeMetricsResponse) ProtoMessage() {}

func (x *GetRealTimeMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRealTimeMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetRealTimeMetricsResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{18}
}

func (x *GetRealTimeMetricsResponse) GetMetrics() *structpb.Struct {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// Dashboard Widgets
type GetDashboardWidgetsRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	DashboardCategory string                 `protobuf:"bytes,1,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetDashboardWidgetsRequest) Reset() {
	*x = GetDashboardWidgetsRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDashboardWidgetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDashboardWidgetsRequest) ProtoMessage() {}

func (x *GetDashboardWidgetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDashboardWidgetsRequest.ProtoReflect.Descriptor instead.
func (*GetDashboardWidgetsRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{19}
}

func (x *GetDashboardWidgetsRequest) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

type GetDashboardWidgetsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Widgets       []*DashboardWidget     `protobuf:"bytes,1,rep,name=widgets,proto3" json:"widgets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDashboardWidgetsResponse) Reset() {
	*x = GetDashboardWidgetsResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDashboardWidgetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDashboardWidgetsResponse) ProtoMessage() {}

func (x *GetDashboardWidgetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDashboardWidgetsResponse.ProtoReflect.Descriptor instead.
func (*GetDashboardWidgetsResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{20}
}

func (x *GetDashboardWidgetsResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type CreateDashboardWidgetRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	WidgetName        string                 `protobuf:"bytes,1,opt,name=widget_name,json=widgetName,proto3" json:"widget_name,omitempty"`
	WidgetType        string                 `protobuf:"bytes,2,opt,name=widget_type,json=widgetType,proto3" json:"widget_type,omitempty"`
	DashboardCategory string                 `protobuf:"bytes,3,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
	DataSource        string                 `protobuf:"bytes,4,opt,name=data_source,json=dataSource,proto3" json:"data_source,omitempty"`
	RefreshInterval   int32                  `protobuf:"varint,5,opt,name=refresh_interval,json=refreshInterval,proto3" json:"refresh_interval,omitempty"`
	WidgetConfig      *structpb.Struct       `protobuf:"bytes,6,opt,name=widget_config,json=widgetConfig,proto3" json:"widget_config,omitempty"`
	IsActive          bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateDashboardWidgetRequest) Reset() {
	*x = CreateDashboardWidgetRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDashboardWidgetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDashboardWidgetRequest) ProtoMessage() {}

func (x *CreateDashboardWidgetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDashboardWidgetRequest.ProtoReflect.Descriptor instead.
func (*CreateDashboardWidgetRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{21}
}

func (x *CreateDashboardWidgetRequest) GetWidgetName() string {
	if x != nil {
		return x.WidgetName
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetWidgetType() string {
	if x != nil {
		return x.WidgetType
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetRefreshInterval() int32 {
	if x != nil {
		return x.RefreshInterval
	}
	return 0
}

func (x *CreateDashboardWidgetRequest) GetWidgetConfig() *structpb.Struct {
	if x != nil {
		return x.WidgetConfig
	}
	return nil
}

func (x *CreateDashboardWidgetRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type CreateDashboardWidgetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Widget        *DashboardWidget       `protobuf:"bytes,3,opt,name=widget,proto3" json:"widget,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDashboardWidgetResponse) Reset() {
	*x = CreateDashboardWidgetResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDashboardWidgetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDashboardWidgetResponse) ProtoMessage() {}

func (x *CreateDashboardWidgetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDashboardWidgetResponse.ProtoReflect.Descriptor instead.
func (*CreateDashboardWidgetResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{22}
}

func (x *CreateDashboardWidgetResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateDashboardWidgetResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateDashboardWidgetResponse) GetWidget() *DashboardWidget {
	if x != nil {
		return x.Widget
	}
	return nil
}

type DashboardWidget struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WidgetName        string                 `protobuf:"bytes,2,opt,name=widget_name,json=widgetName,proto3" json:"widget_name,omitempty"`
	WidgetType        string                 `protobuf:"bytes,3,opt,name=widget_type,json=widgetType,proto3" json:"widget_type,omitempty"`
	DashboardCategory string                 `protobuf:"bytes,4,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
	DataSource        string                 `protobuf:"bytes,5,opt,name=data_source,json=dataSource,proto3" json:"data_source,omitempty"`
	RefreshInterval   int32                  `protobuf:"varint,6,opt,name=refresh_interval,json=refreshInterval,proto3" json:"refresh_interval,omitempty"`
	WidgetConfig      *structpb.Struct       `protobuf:"bytes,7,opt,name=widget_config,json=widgetConfig,proto3" json:"widget_config,omitempty"`
	IsActive          bool                   `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DashboardWidget) Reset() {
	*x = DashboardWidget{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DashboardWidget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardWidget) ProtoMessage() {}

func (x *DashboardWidget) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardWidget.ProtoReflect.Descriptor instead.
func (*DashboardWidget) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{23}
}

func (x *DashboardWidget) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DashboardWidget) GetWidgetName() string {
	if x != nil {
		return x.WidgetName
	}
	return ""
}

func (x *DashboardWidget) GetWidgetType() string {
	if x != nil {
		return x.WidgetType
	}
	return ""
}

func (x *DashboardWidget) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

func (x *DashboardWidget) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *DashboardWidget) GetRefreshInterval() int32 {
	if x != nil {
		return x.RefreshInterval
	}
	return 0
}

func (x *DashboardWidget) GetWidgetConfig() *structpb.Struct {
	if x != nil {
		return x.WidgetConfig
	}
	return nil
}

func (x *DashboardWidget) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *DashboardWidget) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DashboardWidget) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Health Check
type HealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{24}
}

type HealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Healthy       bool                   `protobuf:"varint,1,opt,name=healthy,proto3" json:"healthy,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	mi := &file_analytics_v1_analytics_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_proto_rawDescGZIP(), []int{25}
}

func (x *HealthCheckResponse) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *HealthCheckResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HealthCheckResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

var File_analytics_v1_analytics_proto protoreflect.FileDescriptor

const file_analytics_v1_analytics_proto_rawDesc = "" +
	"\n" +
	"\x1canalytics/v1/analytics.proto\x12\x10api.analytics.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x1e\n" +
	"\x1cGetExecutiveDashboardRequest\"\x83\x02\n" +
	"\x1dGetExecutiveDashboardResponse\x12%\n" +
	"\x0edashboard_type\x18\x01 \x01(\tR\rdashboardType\x12=\n" +
	"\flast_updated\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\x12?\n" +
	"\x04data\x18\x03 \x01(\v2+.api.analytics.v1.ExecutiveDashboardSummaryR\x04data\x12;\n" +
	"\awidgets\x18\x04 \x03(\v2!.api.analytics.v1.DashboardWidgetR\awidgets\"\xd5\x02\n" +
	"\x19ExecutiveDashboardSummary\x12\x16\n" +
	"\x06period\x18\x01 \x01(\tR\x06period\x12#\n" +
	"\rtoday_revenue\x18\x02 \x01(\x01R\ftodayRevenue\x12\x1d\n" +
	"\n" +
	"today_jobs\x18\x03 \x01(\x05R\ttodayJobs\x12-\n" +
	"\x12today_satisfaction\x18\x04 \x01(\x01R\x11todaySatisfaction\x12)\n" +
	"\x10today_efficiency\x18\x05 \x01(\x01R\x0ftodayEfficiency\x12!\n" +
	"\fweek_revenue\x18\x06 \x01(\x01R\vweekRevenue\x12\x1b\n" +
	"\tweek_jobs\x18\a \x01(\x05R\bweekJobs\x12#\n" +
	"\rmonth_revenue\x18\b \x01(\x01R\fmonthRevenue\x12\x1d\n" +
	"\n" +
	"month_jobs\x18\t \x01(\x05R\tmonthJobs\"%\n" +
	"#GetCustomerInsightsDashboardRequest\"\x8a\x02\n" +
	"$GetCustomerInsightsDashboardResponse\x12%\n" +
	"\x0edashboard_type\x18\x01 \x01(\tR\rdashboardType\x12=\n" +
	"\flast_updated\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\x12?\n" +
	"\x04data\x18\x03 \x03(\v2+.api.analytics.v1.CustomerInsightsDashboardR\x04data\x12;\n" +
	"\awidgets\x18\x04 \x03(\v2!.api.analytics.v1.DashboardWidgetR\awidgets\"\x92\x02\n" +
	"\x19CustomerInsightsDashboard\x12!\n" +
	"\floyalty_tier\x18\x01 \x01(\tR\vloyaltyTier\x12%\n" +
	"\x0ecustomer_count\x18\x02 \x01(\x05R\rcustomerCount\x12,\n" +
	"\x12avg_lifetime_value\x18\x03 \x01(\x01R\x10avgLifetimeValue\x12)\n" +
	"\x10avg_satisfaction\x18\x04 \x01(\x01R\x0favgSatisfaction\x12$\n" +
	"\x0eavg_churn_risk\x18\x05 \x01(\x01R\favgChurnRisk\x12,\n" +
	"\x12tier_total_revenue\x18\x06 \x01(\x01R\x10tierTotalRevenue\" \n" +
	"\x1eGetOperationalDashboardRequest\"\x80\x02\n" +
	"\x1fGetOperationalDashboardResponse\x12%\n" +
	"\x0edashboard_type\x18\x01 \x01(\tR\rdashboardType\x12=\n" +
	"\flast_updated\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\x12:\n" +
	"\x04data\x18\x03 \x01(\v2&.api.analytics.v1.OperationalAnalyticsR\x04data\x12;\n" +
	"\awidgets\x18\x04 \x03(\v2!.api.analytics.v1.DashboardWidgetR\awidgets\"\xe6\x05\n" +
	"\x14OperationalAnalytics\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12?\n" +
	"\ranalysis_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\fanalysisDate\x12*\n" +
	"\x11total_active_jobs\x18\x03 \x01(\x05R\x0ftotalActiveJobs\x12%\n" +
	"\x0ecompleted_jobs\x18\x04 \x01(\x05R\rcompletedJobs\x12%\n" +
	"\x0ecancelled_jobs\x18\x05 \x01(\x05R\rcancelledJobs\x12%\n" +
	"\x0eemergency_jobs\x18\x06 \x01(\x05R\remergencyJobs\x127\n" +
	"\x18average_response_time_ms\x18\a \x01(\x03R\x15averageResponseTimeMs\x12;\n" +
	"\x1aaverage_completion_time_ms\x18\b \x01(\x03R\x17averageCompletionTimeMs\x123\n" +
	"\x15technician_efficiency\x18\t \x01(\x01R\x14technicianEfficiency\x123\n" +
	"\x15equipment_utilization\x18\n" +
	" \x01(\x01R\x14equipmentUtilization\x123\n" +
	"\x15customer_satisfaction\x18\v \x01(\x01R\x14customerSatisfaction\x12-\n" +
	"\x13first_time_fix_rate\x18\f \x01(\x01R\x10firstTimeFixRate\x12#\n" +
	"\rcallback_rate\x18\r \x01(\x01R\fcallbackRate\x12-\n" +
	"\x12parts_availability\x18\x0e \x01(\x01R\x11partsAvailability\x12\x1d\n" +
	"\n" +
	"fuel_costs\x18\x0f \x01(\x01R\tfuelCosts\x12%\n" +
	"\x0eovertime_hours\x18\x10 \x01(\x01R\rovertimeHours\"3\n" +
	"\x1bGetPerformanceTrendsRequest\x12\x14\n" +
	"\x05weeks\x18\x01 \x01(\x05R\x05weeks\"d\n" +
	"\x1cGetPerformanceTrendsResponse\x12D\n" +
	"\x06trends\x18\x01 \x03(\v2,.api.analytics.v1.PerformanceTrendsDashboardR\x06trends\"\xa3\x02\n" +
	"\x1aPerformanceTrendsDashboard\x129\n" +
	"\n" +
	"week_start\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tweekStart\x12%\n" +
	"\x0eavg_efficiency\x18\x02 \x01(\x01R\ravgEfficiency\x12)\n" +
	"\x10avg_satisfaction\x18\x03 \x01(\x01R\x0favgSatisfaction\x12+\n" +
	"\x12avg_first_time_fix\x18\x04 \x01(\x01R\x0favgFirstTimeFix\x12\x1d\n" +
	"\n" +
	"total_jobs\x18\x05 \x01(\x05R\ttotalJobs\x12,\n" +
	"\x12avg_response_hours\x18\x06 \x01(\x01R\x10avgResponseHours\",\n" +
	"\x0eGetKPIsRequest\x12\x1a\n" +
	"\bcategory\x18\x01 \x01(\tR\bcategory\"D\n" +
	"\x0fGetKPIsResponse\x121\n" +
	"\x04kpis\x18\x01 \x03(\v2\x1d.api.analytics.v1.KPITrackingR\x04kpis\"\x87\x01\n" +
	"\x10UpdateKPIRequest\x12\x19\n" +
	"\bkpi_name\x18\x01 \x01(\tR\akpiName\x12\x1a\n" +
	"\bcategory\x18\x02 \x01(\tR\bcategory\x12\x14\n" +
	"\x05value\x18\x03 \x01(\x01R\x05value\x12\x1b\n" +
	"\x06target\x18\x04 \x01(\x01H\x00R\x06target\x88\x01\x01B\t\n" +
	"\a_target\"G\n" +
	"\x11UpdateKPIResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xd2\x05\n" +
	"\vKPITracking\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x19\n" +
	"\bkpi_name\x18\x02 \x01(\tR\akpiName\x12!\n" +
	"\fkpi_category\x18\x03 \x01(\tR\vkpiCategory\x12#\n" +
	"\rcurrent_value\x18\x04 \x01(\x01R\fcurrentValue\x12&\n" +
	"\ftarget_value\x18\x05 \x01(\x01H\x00R\vtargetValue\x88\x01\x01\x12*\n" +
	"\x0eprevious_value\x18\x06 \x01(\x01H\x01R\rpreviousValue\x88\x01\x01\x12'\n" +
	"\x0ftrend_direction\x18\a \x01(\tR\x0etrendDirection\x12.\n" +
	"\x10trend_percentage\x18\b \x01(\x01H\x02R\x0ftrendPercentage\x88\x01\x01\x12E\n" +
	"\x10measurement_date\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\x0fmeasurementDate\x12E\n" +
	"\x10measurement_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\x0fmeasurementTime\x123\n" +
	"\x13alert_threshold_min\x18\v \x01(\x01H\x03R\x11alertThresholdMin\x88\x01\x01\x123\n" +
	"\x13alert_threshold_max\x18\f \x01(\x01H\x04R\x11alertThresholdMax\x88\x01\x01\x12,\n" +
	"\x12is_alert_triggered\x18\r \x01(\bR\x10isAlertTriggered\x12\x14\n" +
	"\x05notes\x18\x0e \x01(\tR\x05notesB\x0f\n" +
	"\r_target_valueB\x11\n" +
	"\x0f_previous_valueB\x13\n" +
	"\x11_trend_percentageB\x16\n" +
	"\x14_alert_threshold_minB\x16\n" +
	"\x14_alert_threshold_max\"\x1b\n" +
	"\x19GetRealTimeMetricsRequest\"O\n" +
	"\x1aGetRealTimeMetricsResponse\x121\n" +
	"\ametrics\x18\x01 \x01(\v2\x17.google.protobuf.StructR\ametrics\"K\n" +
	"\x1aGetDashboardWidgetsRequest\x12-\n" +
	"\x12dashboard_category\x18\x01 \x01(\tR\x11dashboardCategory\"Z\n" +
	"\x1bGetDashboardWidgetsResponse\x12;\n" +
	"\awidgets\x18\x01 \x03(\v2!.api.analytics.v1.DashboardWidgetR\awidgets\"\xb6\x02\n" +
	"\x1cCreateDashboardWidgetRequest\x12\x1f\n" +
	"\vwidget_name\x18\x01 \x01(\tR\n" +
	"widgetName\x12\x1f\n" +
	"\vwidget_type\x18\x02 \x01(\tR\n" +
	"widgetType\x12-\n" +
	"\x12dashboard_category\x18\x03 \x01(\tR\x11dashboardCategory\x12\x1f\n" +
	"\vdata_source\x18\x04 \x01(\tR\n" +
	"dataSource\x12)\n" +
	"\x10refresh_interval\x18\x05 \x01(\x05R\x0frefreshInterval\x12<\n" +
	"\rwidget_config\x18\x06 \x01(\v2\x17.google.protobuf.StructR\fwidgetConfig\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\"\x8e\x01\n" +
	"\x1dCreateDashboardWidgetResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x129\n" +
	"\x06widget\x18\x03 \x01(\v2!.api.analytics.v1.DashboardWidgetR\x06widget\"\xaf\x03\n" +
	"\x0fDashboardWidget\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1f\n" +
	"\vwidget_name\x18\x02 \x01(\tR\n" +
	"widgetName\x12\x1f\n" +
	"\vwidget_type\x18\x03 \x01(\tR\n" +
	"widgetType\x12-\n" +
	"\x12dashboard_category\x18\x04 \x01(\tR\x11dashboardCategory\x12\x1f\n" +
	"\vdata_source\x18\x05 \x01(\tR\n" +
	"dataSource\x12)\n" +
	"\x10refresh_interval\x18\x06 \x01(\x05R\x0frefreshInterval\x12<\n" +
	"\rwidget_config\x18\a \x01(\v2\x17.google.protobuf.StructR\fwidgetConfig\x12\x1b\n" +
	"\tis_active\x18\b \x01(\bR\bisActive\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x14\n" +
	"\x12HealthCheckRequest\"\x83\x01\n" +
	"\x13HealthCheckResponse\x12\x18\n" +
	"\ahealthy\x18\x01 \x01(\bR\ahealthy\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp2\x8f\f\n" +
	"\x10AnalyticsService\x12\xa7\x01\n" +
	"\x15GetExecutiveDashboard\x12..api.analytics.v1.GetExecutiveDashboardRequest\x1a/.api.analytics.v1.GetExecutiveDashboardResponse\"-\x82\xd3\xe4\x93\x02'\x12%/api/v1/analytics/dashboard/executive\x12\xbb\x01\n" +
	"\x1cGetCustomerInsightsDashboard\x125.api.analytics.v1.GetCustomerInsightsDashboardRequest\x1a6.api.analytics.v1.GetCustomerInsightsDashboardResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v1/analytics/dashboard/customer\x12\xae\x01\n" +
	"\x17GetOperationalDashboard\x120.api.analytics.v1.GetOperationalDashboardRequest\x1a1.api.analytics.v1.GetOperationalDashboardResponse\".\x82\xd3\xe4\x93\x02(\x12&/api/v1/analytics/dashboard/operations\x12\xa3\x01\n" +
	"\x14GetPerformanceTrends\x12-.api.analytics.v1.GetPerformanceTrendsRequest\x1a..api.analytics.v1.GetPerformanceTrendsResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v1/analytics/trends/performance\x12n\n" +
	"\aGetKPIs\x12 .api.analytics.v1.GetKPIsRequest\x1a!.api.analytics.v1.GetKPIsResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/analytics/kpis\x12w\n" +
	"\tUpdateKPI\x12\".api.analytics.v1.UpdateKPIRequest\x1a#.api.analytics.v1.UpdateKPIResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/analytics/kpis\x12\x9b\x01\n" +
	"\x12GetRealTimeMetrics\x12+.api.analytics.v1.GetRealTimeMetricsRequest\x1a,.api.analytics.v1.GetRealTimeMetricsResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v1/analytics/metrics/realtime\x12\x95\x01\n" +
	"\x13GetDashboardWidgets\x12,.api.analytics.v1.GetDashboardWidgetsRequest\x1a-.api.analytics.v1.GetDashboardWidgetsResponse\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/api/v1/analytics/widgets\x12\x9e\x01\n" +
	"\x15CreateDashboardWidget\x12..api.analytics.v1.CreateDashboardWidgetRequest\x1a/.api.analytics.v1.CreateDashboardWidgetResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/api/v1/analytics/widgets\x12|\n" +
	"\vHealthCheck\x12$.api.analytics.v1.HealthCheckRequest\x1a%.api.analytics.v1.HealthCheckResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v1/analytics/healthB+Z)gobackend-hvac-kratos/api/analytics/v1;v1b\x06proto3"

var (
	file_analytics_v1_analytics_proto_rawDescOnce sync.Once
	file_analytics_v1_analytics_proto_rawDescData []byte
)

func file_analytics_v1_analytics_proto_rawDescGZIP() []byte {
	file_analytics_v1_analytics_proto_rawDescOnce.Do(func() {
		file_analytics_v1_analytics_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_analytics_v1_analytics_proto_rawDesc), len(file_analytics_v1_analytics_proto_rawDesc)))
	})
	return file_analytics_v1_analytics_proto_rawDescData
}

var file_analytics_v1_analytics_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_analytics_v1_analytics_proto_goTypes = []any{
	(*GetExecutiveDashboardRequest)(nil),         // 0: api.analytics.v1.GetExecutiveDashboardRequest
	(*GetExecutiveDashboardResponse)(nil),        // 1: api.analytics.v1.GetExecutiveDashboardResponse
	(*ExecutiveDashboardSummary)(nil),            // 2: api.analytics.v1.ExecutiveDashboardSummary
	(*GetCustomerInsightsDashboardRequest)(nil),  // 3: api.analytics.v1.GetCustomerInsightsDashboardRequest
	(*GetCustomerInsightsDashboardResponse)(nil), // 4: api.analytics.v1.GetCustomerInsightsDashboardResponse
	(*CustomerInsightsDashboard)(nil),            // 5: api.analytics.v1.CustomerInsightsDashboard
	(*GetOperationalDashboardRequest)(nil),       // 6: api.analytics.v1.GetOperationalDashboardRequest
	(*GetOperationalDashboardResponse)(nil),      // 7: api.analytics.v1.GetOperationalDashboardResponse
	(*OperationalAnalytics)(nil),                 // 8: api.analytics.v1.OperationalAnalytics
	(*GetPerformanceTrendsRequest)(nil),          // 9: api.analytics.v1.GetPerformanceTrendsRequest
	(*GetPerformanceTrendsResponse)(nil),         // 10: api.analytics.v1.GetPerformanceTrendsResponse
	(*PerformanceTrendsDashboard)(nil),           // 11: api.analytics.v1.PerformanceTrendsDashboard
	(*GetKPIsRequest)(nil),                       // 12: api.analytics.v1.GetKPIsRequest
	(*GetKPIsResponse)(nil),                      // 13: api.analytics.v1.GetKPIsResponse
	(*UpdateKPIRequest)(nil),                     // 14: api.analytics.v1.UpdateKPIRequest
	(*UpdateKPIResponse)(nil),                    // 15: api.analytics.v1.UpdateKPIResponse
	(*KPITracking)(nil),                          // 16: api.analytics.v1.KPITracking
	(*GetRealTimeMetricsRequest)(nil),            // 17: api.analytics.v1.GetRealTimeMetricsRequest
	(*GetRealTimeMetricsResponse)(nil),           // 18: api.analytics.v1.GetRealTimeMetricsResponse
	(*GetDashboardWidgetsRequest)(nil),           // 19: api.analytics.v1.GetDashboardWidgetsRequest
	(*GetDashboardWidgetsResponse)(nil),          // 20: api.analytics.v1.GetDashboardWidgetsResponse
	(*CreateDashboardWidgetRequest)(nil),         // 21: api.analytics.v1.CreateDashboardWidgetRequest
	(*CreateDashboardWidgetResponse)(nil),        // 22: api.analytics.v1.CreateDashboardWidgetResponse
	(*DashboardWidget)(nil),                      // 23: api.analytics.v1.DashboardWidget
	(*HealthCheckRequest)(nil),                   // 24: api.analytics.v1.HealthCheckRequest
	(*HealthCheckResponse)(nil),                  // 25: api.analytics.v1.HealthCheckResponse
	(*timestamppb.Timestamp)(nil),                // 26: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                      // 27: google.protobuf.Struct
}
var file_analytics_v1_analytics_proto_depIdxs = []int32{
	26, // 0: api.analytics.v1.GetExecutiveDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	2,  // 1: api.analytics.v1.GetExecutiveDashboardResponse.data:type_name -> api.analytics.v1.ExecutiveDashboardSummary
	23, // 2: api.analytics.v1.GetExecutiveDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 3: api.analytics.v1.GetCustomerInsightsDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	5,  // 4: api.analytics.v1.GetCustomerInsightsDashboardResponse.data:type_name -> api.analytics.v1.CustomerInsightsDashboard
	23, // 5: api.analytics.v1.GetCustomerInsightsDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 6: api.analytics.v1.GetOperationalDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	8,  // 7: api.analytics.v1.GetOperationalDashboardResponse.data:type_name -> api.analytics.v1.OperationalAnalytics
	23, // 8: api.analytics.v1.GetOperationalDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 9: api.analytics.v1.OperationalAnalytics.analysis_date:type_name -> google.protobuf.Timestamp
	11, // 10: api.analytics.v1.GetPerformanceTrendsResponse.trends:type_name -> api.analytics.v1.PerformanceTrendsDashboard
	26, // 11: api.analytics.v1.PerformanceTrendsDashboard.week_start:type_name -> google.protobuf.Timestamp
	16, // 12: api.analytics.v1.GetKPIsResponse.kpis:type_name -> api.analytics.v1.KPITracking
	26, // 13: api.analytics.v1.KPITracking.measurement_date:type_name -> google.protobuf.Timestamp
	26, // 14: api.analytics.v1.KPITracking.measurement_time:type_name -> google.protobuf.Timestamp
	27, // 15: api.analytics.v1.GetRealTimeMetricsResponse.metrics:type_name -> google.protobuf.Struct
	23, // 16: api.analytics.v1.GetDashboardWidgetsResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	27, // 17: api.analytics.v1.CreateDashboardWidgetRequest.widget_config:type_name -> google.protobuf.Struct
	23, // 18: api.analytics.v1.CreateDashboardWidgetResponse.widget:type_name -> api.analytics.v1.DashboardWidget
	27, // 19: api.analytics.v1.DashboardWidget.widget_config:type_name -> google.protobuf.Struct
	26, // 20: api.analytics.v1.DashboardWidget.created_at:type_name -> google.protobuf.Timestamp
	26, // 21: api.analytics.v1.DashboardWidget.updated_at:type_name -> google.protobuf.Timestamp
	26, // 22: api.analytics.v1.HealthCheckResponse.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 23: api.analytics.v1.AnalyticsService.GetExecutiveDashboard:input_type -> api.analytics.v1.GetExecutiveDashboardRequest
	3,  // 24: api.analytics.v1.AnalyticsService.GetCustomerInsightsDashboard:input_type -> api.analytics.v1.GetCustomerInsightsDashboardRequest
	6,  // 25: api.analytics.v1.AnalyticsService.GetOperationalDashboard:input_type -> api.analytics.v1.GetOperationalDashboardRequest
	9,  // 26: api.analytics.v1.AnalyticsService.GetPerformanceTrends:input_type -> api.analytics.v1.GetPerformanceTrendsRequest
	12, // 27: api.analytics.v1.AnalyticsService.GetKPIs:input_type -> api.analytics.v1.GetKPIsRequest
	14, // 28: api.analytics.v1.AnalyticsService.UpdateKPI:input_type -> api.analytics.v1.UpdateKPIRequest
	17, // 29: api.analytics.v1.AnalyticsService.GetRealTimeMetrics:input_type -> api.analytics.v1.GetRealTimeMetricsRequest
	19, // 30: api.analytics.v1.AnalyticsService.GetDashboardWidgets:input_type -> api.analytics.v1.GetDashboardWidgetsRequest
	21, // 31: api.analytics.v1.AnalyticsService.CreateDashboardWidget:input_type -> api.analytics.v1.CreateDashboardWidgetRequest
	24, // 32: api.analytics.v1.AnalyticsService.HealthCheck:input_type -> api.analytics.v1.HealthCheckRequest
	1,  // 33: api.analytics.v1.AnalyticsService.GetExecutiveDashboard:output_type -> api.analytics.v1.GetExecutiveDashboardResponse
	4,  // 34: api.analytics.v1.AnalyticsService.GetCustomerInsightsDashboard:output_type -> api.analytics.v1.GetCustomerInsightsDashboardResponse
	7,  // 35: api.analytics.v1.AnalyticsService.GetOperationalDashboard:output_type -> api.analytics.v1.GetOperationalDashboardResponse
	10, // 36: api.analytics.v1.AnalyticsService.GetPerformanceTrends:output_type -> api.analytics.v1.GetPerformanceTrendsResponse
	13, // 37: api.analytics.v1.AnalyticsService.GetKPIs:output_type -> api.analytics.v1.GetKPIsResponse
	15, // 38: api.analytics.v1.AnalyticsService.UpdateKPI:output_type -> api.analytics.v1.UpdateKPIResponse
	18, // 39: api.analytics.v1.AnalyticsService.GetRealTimeMetrics:output_type -> api.analytics.v1.GetRealTimeMetricsResponse
	20, // 40: api.analytics.v1.AnalyticsService.GetDashboardWidgets:output_type -> api.analytics.v1.GetDashboardWidgetsResponse
	22, // 41: api.analytics.v1.AnalyticsService.CreateDashboardWidget:output_type -> api.analytics.v1.CreateDashboardWidgetResponse
	25, // 42: api.analytics.v1.AnalyticsService.HealthCheck:output_type -> api.analytics.v1.HealthCheckResponse
	33, // [33:43] is the sub-list for method output_type
	23, // [23:33] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_analytics_v1_analytics_proto_init() }
func file_analytics_v1_analytics_proto_init() {
	if File_analytics_v1_analytics_proto != nil {
		return
	}
	file_analytics_v1_analytics_proto_msgTypes[14].OneofWrappers = []any{}
	file_analytics_v1_analytics_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_analytics_v1_analytics_proto_rawDesc), len(file_analytics_v1_analytics_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_analytics_v1_analytics_proto_goTypes,
		DependencyIndexes: file_analytics_v1_analytics_proto_depIdxs,
		MessageInfos:      file_analytics_v1_analytics_proto_msgTypes,
	}.Build()
	File_analytics_v1_analytics_proto = out.File
	file_analytics_v1_analytics_proto_goTypes = nil
	file_analytics_v1_analytics_proto_depIdxs = nil
}
