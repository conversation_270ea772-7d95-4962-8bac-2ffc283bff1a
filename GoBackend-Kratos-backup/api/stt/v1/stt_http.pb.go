// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: stt/v1/stt.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSTTServiceAnalyzeHVACCall = "/api.stt.v1.STTService/AnalyzeHVACCall"
const OperationSTTServiceConfigureModel = "/api.stt.v1.STTService/ConfigureModel"
const OperationSTTServiceGetModelStatus = "/api.stt.v1.STTService/GetModelStatus"
const OperationSTTServiceTranscribeAudio = "/api.stt.v1.STTService/TranscribeAudio"
const OperationSTTServiceTranscribePhoneCall = "/api.stt.v1.STTService/TranscribePhoneCall"

type STTServiceHTTPServer interface {
	// AnalyzeHVACCall 🔧 Analiza techniczna rozmów HVAC
	AnalyzeHVACCall(context.Context, *HVACCallAnalysisRequest) (*HVACCallAnalysisResponse, error)
	// ConfigureModel ⚙️ Konfiguracja modelu
	ConfigureModel(context.Context, *ModelConfigRequest) (*ModelConfigResponse, error)
	// GetModelStatus 📊 Status modelu i metryki
	GetModelStatus(context.Context, *ModelStatusRequest) (*ModelStatusResponse, error)
	// TranscribeAudio 🎯 Transkrypcja audio na tekst (główna funkcja)
	TranscribeAudio(context.Context, *TranscribeRequest) (*TranscribeResponse, error)
	// TranscribePhoneCall 📞 Transkrypcja rozmów telefonicznych HVAC
	TranscribePhoneCall(context.Context, *PhoneCallRequest) (*PhoneCallResponse, error)
}

func RegisterSTTServiceHTTPServer(s *http.Server, srv STTServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/stt/transcribe", _STTService_TranscribeAudio0_HTTP_Handler(srv))
	r.POST("/api/v1/stt/phone-call", _STTService_TranscribePhoneCall0_HTTP_Handler(srv))
	r.POST("/api/v1/stt/analyze-hvac-call", _STTService_AnalyzeHVACCall0_HTTP_Handler(srv))
	r.GET("/api/v1/stt/model/status", _STTService_GetModelStatus0_HTTP_Handler(srv))
	r.POST("/api/v1/stt/model/configure", _STTService_ConfigureModel0_HTTP_Handler(srv))
}

func _STTService_TranscribeAudio0_HTTP_Handler(srv STTServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TranscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSTTServiceTranscribeAudio)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TranscribeAudio(ctx, req.(*TranscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TranscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _STTService_TranscribePhoneCall0_HTTP_Handler(srv STTServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PhoneCallRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSTTServiceTranscribePhoneCall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TranscribePhoneCall(ctx, req.(*PhoneCallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PhoneCallResponse)
		return ctx.Result(200, reply)
	}
}

func _STTService_AnalyzeHVACCall0_HTTP_Handler(srv STTServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HVACCallAnalysisRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSTTServiceAnalyzeHVACCall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AnalyzeHVACCall(ctx, req.(*HVACCallAnalysisRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HVACCallAnalysisResponse)
		return ctx.Result(200, reply)
	}
}

func _STTService_GetModelStatus0_HTTP_Handler(srv STTServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModelStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSTTServiceGetModelStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModelStatus(ctx, req.(*ModelStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModelStatusResponse)
		return ctx.Result(200, reply)
	}
}

func _STTService_ConfigureModel0_HTTP_Handler(srv STTServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModelConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSTTServiceConfigureModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConfigureModel(ctx, req.(*ModelConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModelConfigResponse)
		return ctx.Result(200, reply)
	}
}

type STTServiceHTTPClient interface {
	AnalyzeHVACCall(ctx context.Context, req *HVACCallAnalysisRequest, opts ...http.CallOption) (rsp *HVACCallAnalysisResponse, err error)
	ConfigureModel(ctx context.Context, req *ModelConfigRequest, opts ...http.CallOption) (rsp *ModelConfigResponse, err error)
	GetModelStatus(ctx context.Context, req *ModelStatusRequest, opts ...http.CallOption) (rsp *ModelStatusResponse, err error)
	TranscribeAudio(ctx context.Context, req *TranscribeRequest, opts ...http.CallOption) (rsp *TranscribeResponse, err error)
	TranscribePhoneCall(ctx context.Context, req *PhoneCallRequest, opts ...http.CallOption) (rsp *PhoneCallResponse, err error)
}

type STTServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewSTTServiceHTTPClient(client *http.Client) STTServiceHTTPClient {
	return &STTServiceHTTPClientImpl{client}
}

func (c *STTServiceHTTPClientImpl) AnalyzeHVACCall(ctx context.Context, in *HVACCallAnalysisRequest, opts ...http.CallOption) (*HVACCallAnalysisResponse, error) {
	var out HVACCallAnalysisResponse
	pattern := "/api/v1/stt/analyze-hvac-call"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSTTServiceAnalyzeHVACCall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *STTServiceHTTPClientImpl) ConfigureModel(ctx context.Context, in *ModelConfigRequest, opts ...http.CallOption) (*ModelConfigResponse, error) {
	var out ModelConfigResponse
	pattern := "/api/v1/stt/model/configure"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSTTServiceConfigureModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *STTServiceHTTPClientImpl) GetModelStatus(ctx context.Context, in *ModelStatusRequest, opts ...http.CallOption) (*ModelStatusResponse, error) {
	var out ModelStatusResponse
	pattern := "/api/v1/stt/model/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSTTServiceGetModelStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *STTServiceHTTPClientImpl) TranscribeAudio(ctx context.Context, in *TranscribeRequest, opts ...http.CallOption) (*TranscribeResponse, error) {
	var out TranscribeResponse
	pattern := "/api/v1/stt/transcribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSTTServiceTranscribeAudio))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *STTServiceHTTPClientImpl) TranscribePhoneCall(ctx context.Context, in *PhoneCallRequest, opts ...http.CallOption) (*PhoneCallResponse, error) {
	var out PhoneCallResponse
	pattern := "/api/v1/stt/phone-call"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSTTServiceTranscribePhoneCall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
