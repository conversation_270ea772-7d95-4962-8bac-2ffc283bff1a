// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: stt/v1/stt.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 🎯 Enums
type TranscriptionMode int32

const (
	TranscriptionMode_TRANSCRIPTION_MODE_UNSPECIFIED    TranscriptionMode = 0
	TranscriptionMode_TRANSCRIPTION_MODE_STANDARD       TranscriptionMode = 1
	TranscriptionMode_TRANSCRIPTION_MODE_HVAC_OPTIMIZED TranscriptionMode = 2
	TranscriptionMode_TRANSCRIPTION_MODE_PHONE_CALL     TranscriptionMode = 3
	TranscriptionMode_TRANSCRIPTION_MODE_TECHNICAL      TranscriptionMode = 4
)

// Enum value maps for TranscriptionMode.
var (
	TranscriptionMode_name = map[int32]string{
		0: "TRANSCRIPTION_MODE_UNSPECIFIED",
		1: "TRANSCRIPTION_MODE_STANDARD",
		2: "TRANSCRIPTION_MODE_HVAC_OPTIMIZED",
		3: "TRANSCRIPTION_MODE_PHONE_CALL",
		4: "TRANSCRIPTION_MODE_TECHNICAL",
	}
	TranscriptionMode_value = map[string]int32{
		"TRANSCRIPTION_MODE_UNSPECIFIED":    0,
		"TRANSCRIPTION_MODE_STANDARD":       1,
		"TRANSCRIPTION_MODE_HVAC_OPTIMIZED": 2,
		"TRANSCRIPTION_MODE_PHONE_CALL":     3,
		"TRANSCRIPTION_MODE_TECHNICAL":      4,
	}
)

func (x TranscriptionMode) Enum() *TranscriptionMode {
	p := new(TranscriptionMode)
	*p = x
	return p
}

func (x TranscriptionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TranscriptionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_stt_v1_stt_proto_enumTypes[0].Descriptor()
}

func (TranscriptionMode) Type() protoreflect.EnumType {
	return &file_stt_v1_stt_proto_enumTypes[0]
}

func (x TranscriptionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TranscriptionMode.Descriptor instead.
func (TranscriptionMode) EnumDescriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{0}
}

type Priority int32

const (
	Priority_PRIORITY_UNSPECIFIED Priority = 0
	Priority_PRIORITY_LOW         Priority = 1
	Priority_PRIORITY_MEDIUM      Priority = 2
	Priority_PRIORITY_HIGH        Priority = 3
	Priority_PRIORITY_URGENT      Priority = 4
	Priority_PRIORITY_EMERGENCY   Priority = 5
)

// Enum value maps for Priority.
var (
	Priority_name = map[int32]string{
		0: "PRIORITY_UNSPECIFIED",
		1: "PRIORITY_LOW",
		2: "PRIORITY_MEDIUM",
		3: "PRIORITY_HIGH",
		4: "PRIORITY_URGENT",
		5: "PRIORITY_EMERGENCY",
	}
	Priority_value = map[string]int32{
		"PRIORITY_UNSPECIFIED": 0,
		"PRIORITY_LOW":         1,
		"PRIORITY_MEDIUM":      2,
		"PRIORITY_HIGH":        3,
		"PRIORITY_URGENT":      4,
		"PRIORITY_EMERGENCY":   5,
	}
)

func (x Priority) Enum() *Priority {
	p := new(Priority)
	*p = x
	return p
}

func (x Priority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Priority) Descriptor() protoreflect.EnumDescriptor {
	return file_stt_v1_stt_proto_enumTypes[1].Descriptor()
}

func (Priority) Type() protoreflect.EnumType {
	return &file_stt_v1_stt_proto_enumTypes[1]
}

func (x Priority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Priority.Descriptor instead.
func (Priority) EnumDescriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{1}
}

type ServiceType int32

const (
	ServiceType_SERVICE_TYPE_UNSPECIFIED  ServiceType = 0
	ServiceType_SERVICE_TYPE_MAINTENANCE  ServiceType = 1
	ServiceType_SERVICE_TYPE_REPAIR       ServiceType = 2
	ServiceType_SERVICE_TYPE_INSTALLATION ServiceType = 3
	ServiceType_SERVICE_TYPE_INSPECTION   ServiceType = 4
	ServiceType_SERVICE_TYPE_EMERGENCY    ServiceType = 5
	ServiceType_SERVICE_TYPE_CONSULTATION ServiceType = 6
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_MAINTENANCE",
		2: "SERVICE_TYPE_REPAIR",
		3: "SERVICE_TYPE_INSTALLATION",
		4: "SERVICE_TYPE_INSPECTION",
		5: "SERVICE_TYPE_EMERGENCY",
		6: "SERVICE_TYPE_CONSULTATION",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED":  0,
		"SERVICE_TYPE_MAINTENANCE":  1,
		"SERVICE_TYPE_REPAIR":       2,
		"SERVICE_TYPE_INSTALLATION": 3,
		"SERVICE_TYPE_INSPECTION":   4,
		"SERVICE_TYPE_EMERGENCY":    5,
		"SERVICE_TYPE_CONSULTATION": 6,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_stt_v1_stt_proto_enumTypes[2].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_stt_v1_stt_proto_enumTypes[2]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{2}
}

type AnalysisType int32

const (
	AnalysisType_ANALYSIS_TYPE_UNSPECIFIED AnalysisType = 0
	AnalysisType_ANALYSIS_TYPE_TECHNICAL   AnalysisType = 1
	AnalysisType_ANALYSIS_TYPE_SENTIMENT   AnalysisType = 2
	AnalysisType_ANALYSIS_TYPE_URGENCY     AnalysisType = 3
	AnalysisType_ANALYSIS_TYPE_COST        AnalysisType = 4
	AnalysisType_ANALYSIS_TYPE_QUALITY     AnalysisType = 5
)

// Enum value maps for AnalysisType.
var (
	AnalysisType_name = map[int32]string{
		0: "ANALYSIS_TYPE_UNSPECIFIED",
		1: "ANALYSIS_TYPE_TECHNICAL",
		2: "ANALYSIS_TYPE_SENTIMENT",
		3: "ANALYSIS_TYPE_URGENCY",
		4: "ANALYSIS_TYPE_COST",
		5: "ANALYSIS_TYPE_QUALITY",
	}
	AnalysisType_value = map[string]int32{
		"ANALYSIS_TYPE_UNSPECIFIED": 0,
		"ANALYSIS_TYPE_TECHNICAL":   1,
		"ANALYSIS_TYPE_SENTIMENT":   2,
		"ANALYSIS_TYPE_URGENCY":     3,
		"ANALYSIS_TYPE_COST":        4,
		"ANALYSIS_TYPE_QUALITY":     5,
	}
)

func (x AnalysisType) Enum() *AnalysisType {
	p := new(AnalysisType)
	*p = x
	return p
}

func (x AnalysisType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisType) Descriptor() protoreflect.EnumDescriptor {
	return file_stt_v1_stt_proto_enumTypes[3].Descriptor()
}

func (AnalysisType) Type() protoreflect.EnumType {
	return &file_stt_v1_stt_proto_enumTypes[3]
}

func (x AnalysisType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisType.Descriptor instead.
func (AnalysisType) EnumDescriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{3}
}

type ModelStatus int32

const (
	ModelStatus_MODEL_STATUS_UNSPECIFIED ModelStatus = 0
	ModelStatus_MODEL_STATUS_LOADING     ModelStatus = 1
	ModelStatus_MODEL_STATUS_READY       ModelStatus = 2
	ModelStatus_MODEL_STATUS_BUSY        ModelStatus = 3
	ModelStatus_MODEL_STATUS_ERROR       ModelStatus = 4
	ModelStatus_MODEL_STATUS_MAINTENANCE ModelStatus = 5
)

// Enum value maps for ModelStatus.
var (
	ModelStatus_name = map[int32]string{
		0: "MODEL_STATUS_UNSPECIFIED",
		1: "MODEL_STATUS_LOADING",
		2: "MODEL_STATUS_READY",
		3: "MODEL_STATUS_BUSY",
		4: "MODEL_STATUS_ERROR",
		5: "MODEL_STATUS_MAINTENANCE",
	}
	ModelStatus_value = map[string]int32{
		"MODEL_STATUS_UNSPECIFIED": 0,
		"MODEL_STATUS_LOADING":     1,
		"MODEL_STATUS_READY":       2,
		"MODEL_STATUS_BUSY":        3,
		"MODEL_STATUS_ERROR":       4,
		"MODEL_STATUS_MAINTENANCE": 5,
	}
)

func (x ModelStatus) Enum() *ModelStatus {
	p := new(ModelStatus)
	*p = x
	return p
}

func (x ModelStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_stt_v1_stt_proto_enumTypes[4].Descriptor()
}

func (ModelStatus) Type() protoreflect.EnumType {
	return &file_stt_v1_stt_proto_enumTypes[4]
}

func (x ModelStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelStatus.Descriptor instead.
func (ModelStatus) EnumDescriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{4}
}

// 🎤 Podstawowe żądanie transkrypcji
type TranscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AudioData     []byte                 `protobuf:"bytes,1,opt,name=audio_data,json=audioData,proto3" json:"audio_data,omitempty"`
	AudioFormat   string                 `protobuf:"bytes,2,opt,name=audio_format,json=audioFormat,proto3" json:"audio_format,omitempty"`
	SampleRate    int32                  `protobuf:"varint,3,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	Mode          TranscriptionMode      `protobuf:"varint,5,opt,name=mode,proto3,enum=api.stt.v1.TranscriptionMode" json:"mode,omitempty"`
	HvacContext   *HVACContext           `protobuf:"bytes,6,opt,name=hvac_context,json=hvacContext,proto3" json:"hvac_context,omitempty"`
	Options       *TranscriptionOptions  `protobuf:"bytes,7,opt,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscribeRequest) Reset() {
	*x = TranscribeRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscribeRequest) ProtoMessage() {}

func (x *TranscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscribeRequest.ProtoReflect.Descriptor instead.
func (*TranscribeRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{0}
}

func (x *TranscribeRequest) GetAudioData() []byte {
	if x != nil {
		return x.AudioData
	}
	return nil
}

func (x *TranscribeRequest) GetAudioFormat() string {
	if x != nil {
		return x.AudioFormat
	}
	return ""
}

func (x *TranscribeRequest) GetSampleRate() int32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *TranscribeRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *TranscribeRequest) GetMode() TranscriptionMode {
	if x != nil {
		return x.Mode
	}
	return TranscriptionMode_TRANSCRIPTION_MODE_UNSPECIFIED
}

func (x *TranscribeRequest) GetHvacContext() *HVACContext {
	if x != nil {
		return x.HvacContext
	}
	return nil
}

func (x *TranscribeRequest) GetOptions() *TranscriptionOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

// 📝 Odpowiedź transkrypcji
type TranscribeResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Transcript      string                 `protobuf:"bytes,1,opt,name=transcript,proto3" json:"transcript,omitempty"`
	Confidence      float32                `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Segments        []*TranscriptSegment   `protobuf:"bytes,3,rep,name=segments,proto3" json:"segments,omitempty"`
	HvacKeywords    []string               `protobuf:"bytes,4,rep,name=hvac_keywords,json=hvacKeywords,proto3" json:"hvac_keywords,omitempty"`
	Sentiment       *SentimentAnalysis     `protobuf:"bytes,5,opt,name=sentiment,proto3" json:"sentiment,omitempty"`
	Metadata        *ProcessingMetadata    `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	TechnicalIssues []*TechnicalIssue      `protobuf:"bytes,7,rep,name=technical_issues,json=technicalIssues,proto3" json:"technical_issues,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TranscribeResponse) Reset() {
	*x = TranscribeResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscribeResponse) ProtoMessage() {}

func (x *TranscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscribeResponse.ProtoReflect.Descriptor instead.
func (*TranscribeResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{1}
}

func (x *TranscribeResponse) GetTranscript() string {
	if x != nil {
		return x.Transcript
	}
	return ""
}

func (x *TranscribeResponse) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *TranscribeResponse) GetSegments() []*TranscriptSegment {
	if x != nil {
		return x.Segments
	}
	return nil
}

func (x *TranscribeResponse) GetHvacKeywords() []string {
	if x != nil {
		return x.HvacKeywords
	}
	return nil
}

func (x *TranscribeResponse) GetSentiment() *SentimentAnalysis {
	if x != nil {
		return x.Sentiment
	}
	return nil
}

func (x *TranscribeResponse) GetMetadata() *ProcessingMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *TranscribeResponse) GetTechnicalIssues() []*TechnicalIssue {
	if x != nil {
		return x.TechnicalIssues
	}
	return nil
}

// 🌊 Streaming transkrypcja - żądanie
type StreamTranscribeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to RequestType:
	//
	//	*StreamTranscribeRequest_Config
	//	*StreamTranscribeRequest_AudioChunk
	RequestType   isStreamTranscribeRequest_RequestType `protobuf_oneof:"request_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamTranscribeRequest) Reset() {
	*x = StreamTranscribeRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamTranscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamTranscribeRequest) ProtoMessage() {}

func (x *StreamTranscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamTranscribeRequest.ProtoReflect.Descriptor instead.
func (*StreamTranscribeRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{2}
}

func (x *StreamTranscribeRequest) GetRequestType() isStreamTranscribeRequest_RequestType {
	if x != nil {
		return x.RequestType
	}
	return nil
}

func (x *StreamTranscribeRequest) GetConfig() *StreamConfig {
	if x != nil {
		if x, ok := x.RequestType.(*StreamTranscribeRequest_Config); ok {
			return x.Config
		}
	}
	return nil
}

func (x *StreamTranscribeRequest) GetAudioChunk() []byte {
	if x != nil {
		if x, ok := x.RequestType.(*StreamTranscribeRequest_AudioChunk); ok {
			return x.AudioChunk
		}
	}
	return nil
}

type isStreamTranscribeRequest_RequestType interface {
	isStreamTranscribeRequest_RequestType()
}

type StreamTranscribeRequest_Config struct {
	Config *StreamConfig `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

type StreamTranscribeRequest_AudioChunk struct {
	AudioChunk []byte `protobuf:"bytes,2,opt,name=audio_chunk,json=audioChunk,proto3,oneof"`
}

func (*StreamTranscribeRequest_Config) isStreamTranscribeRequest_RequestType() {}

func (*StreamTranscribeRequest_AudioChunk) isStreamTranscribeRequest_RequestType() {}

// 🌊 Streaming transkrypcja - odpowiedź
type StreamTranscribeResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PartialTranscript string                 `protobuf:"bytes,1,opt,name=partial_transcript,json=partialTranscript,proto3" json:"partial_transcript,omitempty"`
	FinalTranscript   string                 `protobuf:"bytes,2,opt,name=final_transcript,json=finalTranscript,proto3" json:"final_transcript,omitempty"`
	IsFinal           bool                   `protobuf:"varint,3,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	Confidence        float32                `protobuf:"fixed32,4,opt,name=confidence,proto3" json:"confidence,omitempty"`
	TimestampMs       int64                  `protobuf:"varint,5,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Keywords          []string               `protobuf:"bytes,6,rep,name=keywords,proto3" json:"keywords,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StreamTranscribeResponse) Reset() {
	*x = StreamTranscribeResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamTranscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamTranscribeResponse) ProtoMessage() {}

func (x *StreamTranscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamTranscribeResponse.ProtoReflect.Descriptor instead.
func (*StreamTranscribeResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{3}
}

func (x *StreamTranscribeResponse) GetPartialTranscript() string {
	if x != nil {
		return x.PartialTranscript
	}
	return ""
}

func (x *StreamTranscribeResponse) GetFinalTranscript() string {
	if x != nil {
		return x.FinalTranscript
	}
	return ""
}

func (x *StreamTranscribeResponse) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *StreamTranscribeResponse) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *StreamTranscribeResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *StreamTranscribeResponse) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

// 📞 Żądanie transkrypcji rozmowy telefonicznej
type PhoneCallRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CallAudio       []byte                 `protobuf:"bytes,1,opt,name=call_audio,json=callAudio,proto3" json:"call_audio,omitempty"`
	CallMetadata    *CallMetadata          `protobuf:"bytes,2,opt,name=call_metadata,json=callMetadata,proto3" json:"call_metadata,omitempty"`
	CustomerContext *CustomerContext       `protobuf:"bytes,3,opt,name=customer_context,json=customerContext,proto3" json:"customer_context,omitempty"`
	AnalysisOptions *CallAnalysisOptions   `protobuf:"bytes,4,opt,name=analysis_options,json=analysisOptions,proto3" json:"analysis_options,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PhoneCallRequest) Reset() {
	*x = PhoneCallRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneCallRequest) ProtoMessage() {}

func (x *PhoneCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneCallRequest.ProtoReflect.Descriptor instead.
func (*PhoneCallRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{4}
}

func (x *PhoneCallRequest) GetCallAudio() []byte {
	if x != nil {
		return x.CallAudio
	}
	return nil
}

func (x *PhoneCallRequest) GetCallMetadata() *CallMetadata {
	if x != nil {
		return x.CallMetadata
	}
	return nil
}

func (x *PhoneCallRequest) GetCustomerContext() *CustomerContext {
	if x != nil {
		return x.CustomerContext
	}
	return nil
}

func (x *PhoneCallRequest) GetAnalysisOptions() *CallAnalysisOptions {
	if x != nil {
		return x.AnalysisOptions
	}
	return nil
}

// 📞 Odpowiedź transkrypcji rozmowy
type PhoneCallResponse struct {
	state              protoimpl.MessageState  `protogen:"open.v1"`
	FullTranscript     string                  `protobuf:"bytes,1,opt,name=full_transcript,json=fullTranscript,proto3" json:"full_transcript,omitempty"`
	SpeakerSegments    []*SpeakerSegment       `protobuf:"bytes,2,rep,name=speaker_segments,json=speakerSegments,proto3" json:"speaker_segments,omitempty"`
	CallSummary        *CallSummary            `protobuf:"bytes,3,opt,name=call_summary,json=callSummary,proto3" json:"call_summary,omitempty"`
	CustomerNeeds      []*CustomerNeed         `protobuf:"bytes,4,rep,name=customer_needs,json=customerNeeds,proto3" json:"customer_needs,omitempty"`
	RecommendedActions []*RecommendedAction    `protobuf:"bytes,5,rep,name=recommended_actions,json=recommendedActions,proto3" json:"recommended_actions,omitempty"`
	ServiceQuality     *ServiceQualityAnalysis `protobuf:"bytes,6,opt,name=service_quality,json=serviceQuality,proto3" json:"service_quality,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PhoneCallResponse) Reset() {
	*x = PhoneCallResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneCallResponse) ProtoMessage() {}

func (x *PhoneCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneCallResponse.ProtoReflect.Descriptor instead.
func (*PhoneCallResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{5}
}

func (x *PhoneCallResponse) GetFullTranscript() string {
	if x != nil {
		return x.FullTranscript
	}
	return ""
}

func (x *PhoneCallResponse) GetSpeakerSegments() []*SpeakerSegment {
	if x != nil {
		return x.SpeakerSegments
	}
	return nil
}

func (x *PhoneCallResponse) GetCallSummary() *CallSummary {
	if x != nil {
		return x.CallSummary
	}
	return nil
}

func (x *PhoneCallResponse) GetCustomerNeeds() []*CustomerNeed {
	if x != nil {
		return x.CustomerNeeds
	}
	return nil
}

func (x *PhoneCallResponse) GetRecommendedActions() []*RecommendedAction {
	if x != nil {
		return x.RecommendedActions
	}
	return nil
}

func (x *PhoneCallResponse) GetServiceQuality() *ServiceQualityAnalysis {
	if x != nil {
		return x.ServiceQuality
	}
	return nil
}

// 🔧 Żądanie analizy rozmowy HVAC
type HVACCallAnalysisRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Transcript      string                 `protobuf:"bytes,1,opt,name=transcript,proto3" json:"transcript,omitempty"`
	CallMetadata    *CallMetadata          `protobuf:"bytes,2,opt,name=call_metadata,json=callMetadata,proto3" json:"call_metadata,omitempty"`
	CustomerHistory *CustomerHistory       `protobuf:"bytes,3,opt,name=customer_history,json=customerHistory,proto3" json:"customer_history,omitempty"`
	AnalysisTypes   []AnalysisType         `protobuf:"varint,4,rep,packed,name=analysis_types,json=analysisTypes,proto3,enum=api.stt.v1.AnalysisType" json:"analysis_types,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *HVACCallAnalysisRequest) Reset() {
	*x = HVACCallAnalysisRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HVACCallAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HVACCallAnalysisRequest) ProtoMessage() {}

func (x *HVACCallAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HVACCallAnalysisRequest.ProtoReflect.Descriptor instead.
func (*HVACCallAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{6}
}

func (x *HVACCallAnalysisRequest) GetTranscript() string {
	if x != nil {
		return x.Transcript
	}
	return ""
}

func (x *HVACCallAnalysisRequest) GetCallMetadata() *CallMetadata {
	if x != nil {
		return x.CallMetadata
	}
	return nil
}

func (x *HVACCallAnalysisRequest) GetCustomerHistory() *CustomerHistory {
	if x != nil {
		return x.CustomerHistory
	}
	return nil
}

func (x *HVACCallAnalysisRequest) GetAnalysisTypes() []AnalysisType {
	if x != nil {
		return x.AnalysisTypes
	}
	return nil
}

// 🔧 Odpowiedź analizy HVAC
type HVACCallAnalysisResponse struct {
	state                     protoimpl.MessageState      `protogen:"open.v1"`
	TechnicalIssues           []*TechnicalIssue           `protobuf:"bytes,1,rep,name=technical_issues,json=technicalIssues,proto3" json:"technical_issues,omitempty"`
	RequiredService           ServiceType                 `protobuf:"varint,2,opt,name=required_service,json=requiredService,proto3,enum=api.stt.v1.ServiceType" json:"required_service,omitempty"`
	Priority                  Priority                    `protobuf:"varint,3,opt,name=priority,proto3,enum=api.stt.v1.Priority" json:"priority,omitempty"`
	EstimatedTime             *EstimatedRepairTime        `protobuf:"bytes,4,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	RequiredParts             []*RequiredPart             `protobuf:"bytes,5,rep,name=required_parts,json=requiredParts,proto3" json:"required_parts,omitempty"`
	CostAnalysis              *CostAnalysis               `protobuf:"bytes,6,opt,name=cost_analysis,json=costAnalysis,proto3" json:"cost_analysis,omitempty"`
	TechnicianRecommendations []*TechnicianRecommendation `protobuf:"bytes,7,rep,name=technician_recommendations,json=technicianRecommendations,proto3" json:"technician_recommendations,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *HVACCallAnalysisResponse) Reset() {
	*x = HVACCallAnalysisResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HVACCallAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HVACCallAnalysisResponse) ProtoMessage() {}

func (x *HVACCallAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HVACCallAnalysisResponse.ProtoReflect.Descriptor instead.
func (*HVACCallAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{7}
}

func (x *HVACCallAnalysisResponse) GetTechnicalIssues() []*TechnicalIssue {
	if x != nil {
		return x.TechnicalIssues
	}
	return nil
}

func (x *HVACCallAnalysisResponse) GetRequiredService() ServiceType {
	if x != nil {
		return x.RequiredService
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *HVACCallAnalysisResponse) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *HVACCallAnalysisResponse) GetEstimatedTime() *EstimatedRepairTime {
	if x != nil {
		return x.EstimatedTime
	}
	return nil
}

func (x *HVACCallAnalysisResponse) GetRequiredParts() []*RequiredPart {
	if x != nil {
		return x.RequiredParts
	}
	return nil
}

func (x *HVACCallAnalysisResponse) GetCostAnalysis() *CostAnalysis {
	if x != nil {
		return x.CostAnalysis
	}
	return nil
}

func (x *HVACCallAnalysisResponse) GetTechnicianRecommendations() []*TechnicianRecommendation {
	if x != nil {
		return x.TechnicianRecommendations
	}
	return nil
}

// 📊 Status modelu
type ModelStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelStatusRequest) Reset() {
	*x = ModelStatusRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStatusRequest) ProtoMessage() {}

func (x *ModelStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStatusRequest.ProtoReflect.Descriptor instead.
func (*ModelStatusRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{8}
}

type ModelStatusResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Status             ModelStatus            `protobuf:"varint,1,opt,name=status,proto3,enum=api.stt.v1.ModelStatus" json:"status,omitempty"`
	ModelVersion       string                 `protobuf:"bytes,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	Performance        *PerformanceMetrics    `protobuf:"bytes,3,opt,name=performance,proto3" json:"performance,omitempty"`
	SupportedLanguages []string               `protobuf:"bytes,4,rep,name=supported_languages,json=supportedLanguages,proto3" json:"supported_languages,omitempty"`
	ResourceUsage      *ResourceUsage         `protobuf:"bytes,5,opt,name=resource_usage,json=resourceUsage,proto3" json:"resource_usage,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ModelStatusResponse) Reset() {
	*x = ModelStatusResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStatusResponse) ProtoMessage() {}

func (x *ModelStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStatusResponse.ProtoReflect.Descriptor instead.
func (*ModelStatusResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{9}
}

func (x *ModelStatusResponse) GetStatus() ModelStatus {
	if x != nil {
		return x.Status
	}
	return ModelStatus_MODEL_STATUS_UNSPECIFIED
}

func (x *ModelStatusResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *ModelStatusResponse) GetPerformance() *PerformanceMetrics {
	if x != nil {
		return x.Performance
	}
	return nil
}

func (x *ModelStatusResponse) GetSupportedLanguages() []string {
	if x != nil {
		return x.SupportedLanguages
	}
	return nil
}

func (x *ModelStatusResponse) GetResourceUsage() *ResourceUsage {
	if x != nil {
		return x.ResourceUsage
	}
	return nil
}

// ⚙️ Konfiguracja modelu
type ModelConfigRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Parameters      *ModelParameters       `protobuf:"bytes,1,opt,name=parameters,proto3" json:"parameters,omitempty"`
	HvacDictionary  *HVACDictionary        `protobuf:"bytes,2,opt,name=hvac_dictionary,json=hvacDictionary,proto3" json:"hvac_dictionary,omitempty"`
	QualitySettings *QualitySettings       `protobuf:"bytes,3,opt,name=quality_settings,json=qualitySettings,proto3" json:"quality_settings,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ModelConfigRequest) Reset() {
	*x = ModelConfigRequest{}
	mi := &file_stt_v1_stt_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfigRequest) ProtoMessage() {}

func (x *ModelConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfigRequest.ProtoReflect.Descriptor instead.
func (*ModelConfigRequest) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{10}
}

func (x *ModelConfigRequest) GetParameters() *ModelParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelConfigRequest) GetHvacDictionary() *HVACDictionary {
	if x != nil {
		return x.HvacDictionary
	}
	return nil
}

func (x *ModelConfigRequest) GetQualitySettings() *QualitySettings {
	if x != nil {
		return x.QualitySettings
	}
	return nil
}

type ModelConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	CurrentConfig *ModelConfiguration    `protobuf:"bytes,3,opt,name=current_config,json=currentConfig,proto3" json:"current_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelConfigResponse) Reset() {
	*x = ModelConfigResponse{}
	mi := &file_stt_v1_stt_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfigResponse) ProtoMessage() {}

func (x *ModelConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfigResponse.ProtoReflect.Descriptor instead.
func (*ModelConfigResponse) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{11}
}

func (x *ModelConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ModelConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ModelConfigResponse) GetCurrentConfig() *ModelConfiguration {
	if x != nil {
		return x.CurrentConfig
	}
	return nil
}

// 🏗️ Struktury danych
type HVACContext struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	SystemType           string                 `protobuf:"bytes,1,opt,name=system_type,json=systemType,proto3" json:"system_type,omitempty"`
	EquipmentBrand       string                 `protobuf:"bytes,2,opt,name=equipment_brand,json=equipmentBrand,proto3" json:"equipment_brand,omitempty"`
	EquipmentModel       string                 `protobuf:"bytes,3,opt,name=equipment_model,json=equipmentModel,proto3" json:"equipment_model,omitempty"`
	InstallationAgeYears int32                  `protobuf:"varint,4,opt,name=installation_age_years,json=installationAgeYears,proto3" json:"installation_age_years,omitempty"`
	ServiceHistory       []string               `protobuf:"bytes,5,rep,name=service_history,json=serviceHistory,proto3" json:"service_history,omitempty"`
	Season               string                 `protobuf:"bytes,6,opt,name=season,proto3" json:"season,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *HVACContext) Reset() {
	*x = HVACContext{}
	mi := &file_stt_v1_stt_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HVACContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HVACContext) ProtoMessage() {}

func (x *HVACContext) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HVACContext.ProtoReflect.Descriptor instead.
func (*HVACContext) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{12}
}

func (x *HVACContext) GetSystemType() string {
	if x != nil {
		return x.SystemType
	}
	return ""
}

func (x *HVACContext) GetEquipmentBrand() string {
	if x != nil {
		return x.EquipmentBrand
	}
	return ""
}

func (x *HVACContext) GetEquipmentModel() string {
	if x != nil {
		return x.EquipmentModel
	}
	return ""
}

func (x *HVACContext) GetInstallationAgeYears() int32 {
	if x != nil {
		return x.InstallationAgeYears
	}
	return 0
}

func (x *HVACContext) GetServiceHistory() []string {
	if x != nil {
		return x.ServiceHistory
	}
	return nil
}

func (x *HVACContext) GetSeason() string {
	if x != nil {
		return x.Season
	}
	return ""
}

type TranscriptionOptions struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	EnableEmotionDetection    bool                   `protobuf:"varint,1,opt,name=enable_emotion_detection,json=enableEmotionDetection,proto3" json:"enable_emotion_detection,omitempty"`
	EnableKeywordDetection    bool                   `protobuf:"varint,2,opt,name=enable_keyword_detection,json=enableKeywordDetection,proto3" json:"enable_keyword_detection,omitempty"`
	EnableTechnicalAnalysis   bool                   `protobuf:"varint,3,opt,name=enable_technical_analysis,json=enableTechnicalAnalysis,proto3" json:"enable_technical_analysis,omitempty"`
	TimestampGranularityMs    int32                  `protobuf:"varint,4,opt,name=timestamp_granularity_ms,json=timestampGranularityMs,proto3" json:"timestamp_granularity_ms,omitempty"`
	EnableNoiseReduction      bool                   `protobuf:"varint,5,opt,name=enable_noise_reduction,json=enableNoiseReduction,proto3" json:"enable_noise_reduction,omitempty"`
	EnableVolumeNormalization bool                   `protobuf:"varint,6,opt,name=enable_volume_normalization,json=enableVolumeNormalization,proto3" json:"enable_volume_normalization,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *TranscriptionOptions) Reset() {
	*x = TranscriptionOptions{}
	mi := &file_stt_v1_stt_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptionOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptionOptions) ProtoMessage() {}

func (x *TranscriptionOptions) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptionOptions.ProtoReflect.Descriptor instead.
func (*TranscriptionOptions) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{13}
}

func (x *TranscriptionOptions) GetEnableEmotionDetection() bool {
	if x != nil {
		return x.EnableEmotionDetection
	}
	return false
}

func (x *TranscriptionOptions) GetEnableKeywordDetection() bool {
	if x != nil {
		return x.EnableKeywordDetection
	}
	return false
}

func (x *TranscriptionOptions) GetEnableTechnicalAnalysis() bool {
	if x != nil {
		return x.EnableTechnicalAnalysis
	}
	return false
}

func (x *TranscriptionOptions) GetTimestampGranularityMs() int32 {
	if x != nil {
		return x.TimestampGranularityMs
	}
	return 0
}

func (x *TranscriptionOptions) GetEnableNoiseReduction() bool {
	if x != nil {
		return x.EnableNoiseReduction
	}
	return false
}

func (x *TranscriptionOptions) GetEnableVolumeNormalization() bool {
	if x != nil {
		return x.EnableVolumeNormalization
	}
	return false
}

type TranscriptSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	StartTimeMs   int64                  `protobuf:"varint,2,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	EndTimeMs     int64                  `protobuf:"varint,3,opt,name=end_time_ms,json=endTimeMs,proto3" json:"end_time_ms,omitempty"`
	Confidence    float32                `protobuf:"fixed32,4,opt,name=confidence,proto3" json:"confidence,omitempty"`
	SpeakerId     string                 `protobuf:"bytes,5,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id,omitempty"`
	Emotions      []string               `protobuf:"bytes,6,rep,name=emotions,proto3" json:"emotions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscriptSegment) Reset() {
	*x = TranscriptSegment{}
	mi := &file_stt_v1_stt_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptSegment) ProtoMessage() {}

func (x *TranscriptSegment) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptSegment.ProtoReflect.Descriptor instead.
func (*TranscriptSegment) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{14}
}

func (x *TranscriptSegment) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TranscriptSegment) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *TranscriptSegment) GetEndTimeMs() int64 {
	if x != nil {
		return x.EndTimeMs
	}
	return 0
}

func (x *TranscriptSegment) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *TranscriptSegment) GetSpeakerId() string {
	if x != nil {
		return x.SpeakerId
	}
	return ""
}

func (x *TranscriptSegment) GetEmotions() []string {
	if x != nil {
		return x.Emotions
	}
	return nil
}

type SentimentAnalysis struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	OverallSentiment  float32                `protobuf:"fixed32,1,opt,name=overall_sentiment,json=overallSentiment,proto3" json:"overall_sentiment,omitempty"`
	FrustrationLevel  float32                `protobuf:"fixed32,2,opt,name=frustration_level,json=frustrationLevel,proto3" json:"frustration_level,omitempty"`
	SatisfactionLevel float32                `protobuf:"fixed32,3,opt,name=satisfaction_level,json=satisfactionLevel,proto3" json:"satisfaction_level,omitempty"`
	Emotions          []*EmotionScore        `protobuf:"bytes,4,rep,name=emotions,proto3" json:"emotions,omitempty"`
	ConversationTone  string                 `protobuf:"bytes,5,opt,name=conversation_tone,json=conversationTone,proto3" json:"conversation_tone,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SentimentAnalysis) Reset() {
	*x = SentimentAnalysis{}
	mi := &file_stt_v1_stt_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SentimentAnalysis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SentimentAnalysis) ProtoMessage() {}

func (x *SentimentAnalysis) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SentimentAnalysis.ProtoReflect.Descriptor instead.
func (*SentimentAnalysis) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{15}
}

func (x *SentimentAnalysis) GetOverallSentiment() float32 {
	if x != nil {
		return x.OverallSentiment
	}
	return 0
}

func (x *SentimentAnalysis) GetFrustrationLevel() float32 {
	if x != nil {
		return x.FrustrationLevel
	}
	return 0
}

func (x *SentimentAnalysis) GetSatisfactionLevel() float32 {
	if x != nil {
		return x.SatisfactionLevel
	}
	return 0
}

func (x *SentimentAnalysis) GetEmotions() []*EmotionScore {
	if x != nil {
		return x.Emotions
	}
	return nil
}

func (x *SentimentAnalysis) GetConversationTone() string {
	if x != nil {
		return x.ConversationTone
	}
	return ""
}

type EmotionScore struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emotion       string                 `protobuf:"bytes,1,opt,name=emotion,proto3" json:"emotion,omitempty"`
	Score         float32                `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmotionScore) Reset() {
	*x = EmotionScore{}
	mi := &file_stt_v1_stt_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmotionScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmotionScore) ProtoMessage() {}

func (x *EmotionScore) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmotionScore.ProtoReflect.Descriptor instead.
func (*EmotionScore) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{16}
}

func (x *EmotionScore) GetEmotion() string {
	if x != nil {
		return x.Emotion
	}
	return ""
}

func (x *EmotionScore) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type ProcessingMetadata struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProcessingTimeMs int64                  `protobuf:"varint,1,opt,name=processing_time_ms,json=processingTimeMs,proto3" json:"processing_time_ms,omitempty"`
	ModelVersion     string                 `protobuf:"bytes,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	AudioQuality     *AudioQuality          `protobuf:"bytes,3,opt,name=audio_quality,json=audioQuality,proto3" json:"audio_quality,omitempty"`
	Stats            *ProcessingStats       `protobuf:"bytes,4,opt,name=stats,proto3" json:"stats,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ProcessingMetadata) Reset() {
	*x = ProcessingMetadata{}
	mi := &file_stt_v1_stt_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessingMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingMetadata) ProtoMessage() {}

func (x *ProcessingMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingMetadata.ProtoReflect.Descriptor instead.
func (*ProcessingMetadata) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{17}
}

func (x *ProcessingMetadata) GetProcessingTimeMs() int64 {
	if x != nil {
		return x.ProcessingTimeMs
	}
	return 0
}

func (x *ProcessingMetadata) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *ProcessingMetadata) GetAudioQuality() *AudioQuality {
	if x != nil {
		return x.AudioQuality
	}
	return nil
}

func (x *ProcessingMetadata) GetStats() *ProcessingStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

type AudioQuality struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NoiseLevel    float32                `protobuf:"fixed32,1,opt,name=noise_level,json=noiseLevel,proto3" json:"noise_level,omitempty"`
	SignalQuality float32                `protobuf:"fixed32,2,opt,name=signal_quality,json=signalQuality,proto3" json:"signal_quality,omitempty"`
	QualityIssues []string               `protobuf:"bytes,3,rep,name=quality_issues,json=qualityIssues,proto3" json:"quality_issues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioQuality) Reset() {
	*x = AudioQuality{}
	mi := &file_stt_v1_stt_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioQuality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioQuality) ProtoMessage() {}

func (x *AudioQuality) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioQuality.ProtoReflect.Descriptor instead.
func (*AudioQuality) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{18}
}

func (x *AudioQuality) GetNoiseLevel() float32 {
	if x != nil {
		return x.NoiseLevel
	}
	return 0
}

func (x *AudioQuality) GetSignalQuality() float32 {
	if x != nil {
		return x.SignalQuality
	}
	return 0
}

func (x *AudioQuality) GetQualityIssues() []string {
	if x != nil {
		return x.QualityIssues
	}
	return nil
}

type ProcessingStats struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	WordCount            int32                  `protobuf:"varint,1,opt,name=word_count,json=wordCount,proto3" json:"word_count,omitempty"`
	AudioDurationSeconds float32                `protobuf:"fixed32,2,opt,name=audio_duration_seconds,json=audioDurationSeconds,proto3" json:"audio_duration_seconds,omitempty"`
	ProcessingSpeedRatio float32                `protobuf:"fixed32,3,opt,name=processing_speed_ratio,json=processingSpeedRatio,proto3" json:"processing_speed_ratio,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ProcessingStats) Reset() {
	*x = ProcessingStats{}
	mi := &file_stt_v1_stt_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessingStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingStats) ProtoMessage() {}

func (x *ProcessingStats) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingStats.ProtoReflect.Descriptor instead.
func (*ProcessingStats) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{19}
}

func (x *ProcessingStats) GetWordCount() int32 {
	if x != nil {
		return x.WordCount
	}
	return 0
}

func (x *ProcessingStats) GetAudioDurationSeconds() float32 {
	if x != nil {
		return x.AudioDurationSeconds
	}
	return 0
}

func (x *ProcessingStats) GetProcessingSpeedRatio() float32 {
	if x != nil {
		return x.ProcessingSpeedRatio
	}
	return 0
}

type TechnicalIssue struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IssueType            string                 `protobuf:"bytes,1,opt,name=issue_type,json=issueType,proto3" json:"issue_type,omitempty"`
	Description          string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Confidence           float32                `protobuf:"fixed32,3,opt,name=confidence,proto3" json:"confidence,omitempty"`
	AffectedComponent    string                 `protobuf:"bytes,4,opt,name=affected_component,json=affectedComponent,proto3" json:"affected_component,omitempty"`
	PossibleCauses       []string               `protobuf:"bytes,5,rep,name=possible_causes,json=possibleCauses,proto3" json:"possible_causes,omitempty"`
	RecommendedSolutions []string               `protobuf:"bytes,6,rep,name=recommended_solutions,json=recommendedSolutions,proto3" json:"recommended_solutions,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TechnicalIssue) Reset() {
	*x = TechnicalIssue{}
	mi := &file_stt_v1_stt_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TechnicalIssue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TechnicalIssue) ProtoMessage() {}

func (x *TechnicalIssue) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TechnicalIssue.ProtoReflect.Descriptor instead.
func (*TechnicalIssue) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{20}
}

func (x *TechnicalIssue) GetIssueType() string {
	if x != nil {
		return x.IssueType
	}
	return ""
}

func (x *TechnicalIssue) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TechnicalIssue) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *TechnicalIssue) GetAffectedComponent() string {
	if x != nil {
		return x.AffectedComponent
	}
	return ""
}

func (x *TechnicalIssue) GetPossibleCauses() []string {
	if x != nil {
		return x.PossibleCauses
	}
	return nil
}

func (x *TechnicalIssue) GetRecommendedSolutions() []string {
	if x != nil {
		return x.RecommendedSolutions
	}
	return nil
}

type StreamConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SampleRate    int32                  `protobuf:"varint,1,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	AudioFormat   string                 `protobuf:"bytes,2,opt,name=audio_format,json=audioFormat,proto3" json:"audio_format,omitempty"`
	HvacContext   *HVACContext           `protobuf:"bytes,3,opt,name=hvac_context,json=hvacContext,proto3" json:"hvac_context,omitempty"`
	Options       *TranscriptionOptions  `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamConfig) Reset() {
	*x = StreamConfig{}
	mi := &file_stt_v1_stt_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamConfig) ProtoMessage() {}

func (x *StreamConfig) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamConfig.ProtoReflect.Descriptor instead.
func (*StreamConfig) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{21}
}

func (x *StreamConfig) GetSampleRate() int32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *StreamConfig) GetAudioFormat() string {
	if x != nil {
		return x.AudioFormat
	}
	return ""
}

func (x *StreamConfig) GetHvacContext() *HVACContext {
	if x != nil {
		return x.HvacContext
	}
	return nil
}

func (x *StreamConfig) GetOptions() *TranscriptionOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type CallMetadata struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CallId          string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	StartTime       int64                  `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	DurationSeconds int64                  `protobuf:"varint,3,opt,name=duration_seconds,json=durationSeconds,proto3" json:"duration_seconds,omitempty"`
	CustomerPhone   string                 `protobuf:"bytes,4,opt,name=customer_phone,json=customerPhone,proto3" json:"customer_phone,omitempty"`
	OperatorId      string                 `protobuf:"bytes,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	CallType        string                 `protobuf:"bytes,6,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CallMetadata) Reset() {
	*x = CallMetadata{}
	mi := &file_stt_v1_stt_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallMetadata) ProtoMessage() {}

func (x *CallMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallMetadata.ProtoReflect.Descriptor instead.
func (*CallMetadata) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{22}
}

func (x *CallMetadata) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *CallMetadata) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CallMetadata) GetDurationSeconds() int64 {
	if x != nil {
		return x.DurationSeconds
	}
	return 0
}

func (x *CallMetadata) GetCustomerPhone() string {
	if x != nil {
		return x.CustomerPhone
	}
	return ""
}

func (x *CallMetadata) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *CallMetadata) GetCallType() string {
	if x != nil {
		return x.CallType
	}
	return ""
}

type CustomerContext struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CustomerId     string                 `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Address        string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	ServiceHistory []string               `protobuf:"bytes,4,rep,name=service_history,json=serviceHistory,proto3" json:"service_history,omitempty"`
	Preferences    *CustomerPreferences   `protobuf:"bytes,5,opt,name=preferences,proto3" json:"preferences,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerContext) Reset() {
	*x = CustomerContext{}
	mi := &file_stt_v1_stt_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerContext) ProtoMessage() {}

func (x *CustomerContext) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerContext.ProtoReflect.Descriptor instead.
func (*CustomerContext) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{23}
}

func (x *CustomerContext) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *CustomerContext) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerContext) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CustomerContext) GetServiceHistory() []string {
	if x != nil {
		return x.ServiceHistory
	}
	return nil
}

func (x *CustomerContext) GetPreferences() *CustomerPreferences {
	if x != nil {
		return x.Preferences
	}
	return nil
}

type CustomerPreferences struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	PreferredContactTime   string                 `protobuf:"bytes,1,opt,name=preferred_contact_time,json=preferredContactTime,proto3" json:"preferred_contact_time,omitempty"`
	PreferredCommunication string                 `protobuf:"bytes,2,opt,name=preferred_communication,json=preferredCommunication,proto3" json:"preferred_communication,omitempty"`
	SpecialNotes           []string               `protobuf:"bytes,3,rep,name=special_notes,json=specialNotes,proto3" json:"special_notes,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CustomerPreferences) Reset() {
	*x = CustomerPreferences{}
	mi := &file_stt_v1_stt_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPreferences) ProtoMessage() {}

func (x *CustomerPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPreferences.ProtoReflect.Descriptor instead.
func (*CustomerPreferences) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{24}
}

func (x *CustomerPreferences) GetPreferredContactTime() string {
	if x != nil {
		return x.PreferredContactTime
	}
	return ""
}

func (x *CustomerPreferences) GetPreferredCommunication() string {
	if x != nil {
		return x.PreferredCommunication
	}
	return ""
}

func (x *CustomerPreferences) GetSpecialNotes() []string {
	if x != nil {
		return x.SpecialNotes
	}
	return nil
}

type CallAnalysisOptions struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	AnalyzeSentiment      bool                   `protobuf:"varint,1,opt,name=analyze_sentiment,json=analyzeSentiment,proto3" json:"analyze_sentiment,omitempty"`
	DetectTechnicalIssues bool                   `protobuf:"varint,2,opt,name=detect_technical_issues,json=detectTechnicalIssues,proto3" json:"detect_technical_issues,omitempty"`
	AnalyzeServiceQuality bool                   `protobuf:"varint,3,opt,name=analyze_service_quality,json=analyzeServiceQuality,proto3" json:"analyze_service_quality,omitempty"`
	GenerateSummary       bool                   `protobuf:"varint,4,opt,name=generate_summary,json=generateSummary,proto3" json:"generate_summary,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CallAnalysisOptions) Reset() {
	*x = CallAnalysisOptions{}
	mi := &file_stt_v1_stt_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallAnalysisOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAnalysisOptions) ProtoMessage() {}

func (x *CallAnalysisOptions) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAnalysisOptions.ProtoReflect.Descriptor instead.
func (*CallAnalysisOptions) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{25}
}

func (x *CallAnalysisOptions) GetAnalyzeSentiment() bool {
	if x != nil {
		return x.AnalyzeSentiment
	}
	return false
}

func (x *CallAnalysisOptions) GetDetectTechnicalIssues() bool {
	if x != nil {
		return x.DetectTechnicalIssues
	}
	return false
}

func (x *CallAnalysisOptions) GetAnalyzeServiceQuality() bool {
	if x != nil {
		return x.AnalyzeServiceQuality
	}
	return false
}

func (x *CallAnalysisOptions) GetGenerateSummary() bool {
	if x != nil {
		return x.GenerateSummary
	}
	return false
}

type SpeakerSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SpeakerId     string                 `protobuf:"bytes,1,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	StartTimeMs   int64                  `protobuf:"varint,3,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	EndTimeMs     int64                  `protobuf:"varint,4,opt,name=end_time_ms,json=endTimeMs,proto3" json:"end_time_ms,omitempty"`
	Sentiment     *SentimentAnalysis     `protobuf:"bytes,5,opt,name=sentiment,proto3" json:"sentiment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpeakerSegment) Reset() {
	*x = SpeakerSegment{}
	mi := &file_stt_v1_stt_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpeakerSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeakerSegment) ProtoMessage() {}

func (x *SpeakerSegment) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeakerSegment.ProtoReflect.Descriptor instead.
func (*SpeakerSegment) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{26}
}

func (x *SpeakerSegment) GetSpeakerId() string {
	if x != nil {
		return x.SpeakerId
	}
	return ""
}

func (x *SpeakerSegment) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SpeakerSegment) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *SpeakerSegment) GetEndTimeMs() int64 {
	if x != nil {
		return x.EndTimeMs
	}
	return 0
}

func (x *SpeakerSegment) GetSentiment() *SentimentAnalysis {
	if x != nil {
		return x.Sentiment
	}
	return nil
}

type CallSummary struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Summary       string                 `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	KeyPoints     []string               `protobuf:"bytes,2,rep,name=key_points,json=keyPoints,proto3" json:"key_points,omitempty"`
	DecisionsMade []string               `protobuf:"bytes,3,rep,name=decisions_made,json=decisionsMade,proto3" json:"decisions_made,omitempty"`
	NextSteps     []string               `protobuf:"bytes,4,rep,name=next_steps,json=nextSteps,proto3" json:"next_steps,omitempty"`
	OverallRating float32                `protobuf:"fixed32,5,opt,name=overall_rating,json=overallRating,proto3" json:"overall_rating,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallSummary) Reset() {
	*x = CallSummary{}
	mi := &file_stt_v1_stt_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallSummary) ProtoMessage() {}

func (x *CallSummary) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallSummary.ProtoReflect.Descriptor instead.
func (*CallSummary) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{27}
}

func (x *CallSummary) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *CallSummary) GetKeyPoints() []string {
	if x != nil {
		return x.KeyPoints
	}
	return nil
}

func (x *CallSummary) GetDecisionsMade() []string {
	if x != nil {
		return x.DecisionsMade
	}
	return nil
}

func (x *CallSummary) GetNextSteps() []string {
	if x != nil {
		return x.NextSteps
	}
	return nil
}

func (x *CallSummary) GetOverallRating() float32 {
	if x != nil {
		return x.OverallRating
	}
	return 0
}

type CustomerNeed struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NeedType      string                 `protobuf:"bytes,1,opt,name=need_type,json=needType,proto3" json:"need_type,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Priority      Priority               `protobuf:"varint,3,opt,name=priority,proto3,enum=api.stt.v1.Priority" json:"priority,omitempty"`
	CostEstimate  *CostEstimate          `protobuf:"bytes,4,opt,name=cost_estimate,json=costEstimate,proto3" json:"cost_estimate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerNeed) Reset() {
	*x = CustomerNeed{}
	mi := &file_stt_v1_stt_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerNeed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerNeed) ProtoMessage() {}

func (x *CustomerNeed) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerNeed.ProtoReflect.Descriptor instead.
func (*CustomerNeed) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{28}
}

func (x *CustomerNeed) GetNeedType() string {
	if x != nil {
		return x.NeedType
	}
	return ""
}

func (x *CustomerNeed) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CustomerNeed) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *CustomerNeed) GetCostEstimate() *CostEstimate {
	if x != nil {
		return x.CostEstimate
	}
	return nil
}

type CostEstimate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MinCost       float32                `protobuf:"fixed32,1,opt,name=min_cost,json=minCost,proto3" json:"min_cost,omitempty"`
	MaxCost       float32                `protobuf:"fixed32,2,opt,name=max_cost,json=maxCost,proto3" json:"max_cost,omitempty"`
	Currency      string                 `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	CostFactors   []string               `protobuf:"bytes,4,rep,name=cost_factors,json=costFactors,proto3" json:"cost_factors,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CostEstimate) Reset() {
	*x = CostEstimate{}
	mi := &file_stt_v1_stt_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CostEstimate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CostEstimate) ProtoMessage() {}

func (x *CostEstimate) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CostEstimate.ProtoReflect.Descriptor instead.
func (*CostEstimate) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{29}
}

func (x *CostEstimate) GetMinCost() float32 {
	if x != nil {
		return x.MinCost
	}
	return 0
}

func (x *CostEstimate) GetMaxCost() float32 {
	if x != nil {
		return x.MaxCost
	}
	return 0
}

func (x *CostEstimate) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CostEstimate) GetCostFactors() []string {
	if x != nil {
		return x.CostFactors
	}
	return nil
}

type RecommendedAction struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	ActionType               string                 `protobuf:"bytes,1,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	Description              string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Priority                 Priority               `protobuf:"varint,3,opt,name=priority,proto3,enum=api.stt.v1.Priority" json:"priority,omitempty"`
	EstimatedDurationMinutes int32                  `protobuf:"varint,4,opt,name=estimated_duration_minutes,json=estimatedDurationMinutes,proto3" json:"estimated_duration_minutes,omitempty"`
	RequiredResources        []string               `protobuf:"bytes,5,rep,name=required_resources,json=requiredResources,proto3" json:"required_resources,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *RecommendedAction) Reset() {
	*x = RecommendedAction{}
	mi := &file_stt_v1_stt_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedAction) ProtoMessage() {}

func (x *RecommendedAction) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedAction.ProtoReflect.Descriptor instead.
func (*RecommendedAction) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{30}
}

func (x *RecommendedAction) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *RecommendedAction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RecommendedAction) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *RecommendedAction) GetEstimatedDurationMinutes() int32 {
	if x != nil {
		return x.EstimatedDurationMinutes
	}
	return 0
}

func (x *RecommendedAction) GetRequiredResources() []string {
	if x != nil {
		return x.RequiredResources
	}
	return nil
}

type ServiceQualityAnalysis struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	OverallQualityScore       float32                `protobuf:"fixed32,1,opt,name=overall_quality_score,json=overallQualityScore,proto3" json:"overall_quality_score,omitempty"`
	ProfessionalismScore      float32                `protobuf:"fixed32,2,opt,name=professionalism_score,json=professionalismScore,proto3" json:"professionalism_score,omitempty"`
	ResolutionSpeedScore      float32                `protobuf:"fixed32,3,opt,name=resolution_speed_score,json=resolutionSpeedScore,proto3" json:"resolution_speed_score,omitempty"`
	CustomerSatisfactionScore float32                `protobuf:"fixed32,4,opt,name=customer_satisfaction_score,json=customerSatisfactionScore,proto3" json:"customer_satisfaction_score,omitempty"`
	ImprovementAreas          []string               `protobuf:"bytes,5,rep,name=improvement_areas,json=improvementAreas,proto3" json:"improvement_areas,omitempty"`
	PositiveAspects           []string               `protobuf:"bytes,6,rep,name=positive_aspects,json=positiveAspects,proto3" json:"positive_aspects,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *ServiceQualityAnalysis) Reset() {
	*x = ServiceQualityAnalysis{}
	mi := &file_stt_v1_stt_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceQualityAnalysis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceQualityAnalysis) ProtoMessage() {}

func (x *ServiceQualityAnalysis) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceQualityAnalysis.ProtoReflect.Descriptor instead.
func (*ServiceQualityAnalysis) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{31}
}

func (x *ServiceQualityAnalysis) GetOverallQualityScore() float32 {
	if x != nil {
		return x.OverallQualityScore
	}
	return 0
}

func (x *ServiceQualityAnalysis) GetProfessionalismScore() float32 {
	if x != nil {
		return x.ProfessionalismScore
	}
	return 0
}

func (x *ServiceQualityAnalysis) GetResolutionSpeedScore() float32 {
	if x != nil {
		return x.ResolutionSpeedScore
	}
	return 0
}

func (x *ServiceQualityAnalysis) GetCustomerSatisfactionScore() float32 {
	if x != nil {
		return x.CustomerSatisfactionScore
	}
	return 0
}

func (x *ServiceQualityAnalysis) GetImprovementAreas() []string {
	if x != nil {
		return x.ImprovementAreas
	}
	return nil
}

func (x *ServiceQualityAnalysis) GetPositiveAspects() []string {
	if x != nil {
		return x.PositiveAspects
	}
	return nil
}

type CustomerHistory struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PreviousTickets    []string               `protobuf:"bytes,1,rep,name=previous_tickets,json=previousTickets,proto3" json:"previous_tickets,omitempty"`
	PaymentHistory     *PaymentHistory        `protobuf:"bytes,2,opt,name=payment_history,json=paymentHistory,proto3" json:"payment_history,omitempty"`
	ServicePreferences *ServicePreferences    `protobuf:"bytes,3,opt,name=service_preferences,json=servicePreferences,proto3" json:"service_preferences,omitempty"`
	PreviousRatings    []*ServiceRating       `protobuf:"bytes,4,rep,name=previous_ratings,json=previousRatings,proto3" json:"previous_ratings,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustomerHistory) Reset() {
	*x = CustomerHistory{}
	mi := &file_stt_v1_stt_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerHistory) ProtoMessage() {}

func (x *CustomerHistory) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerHistory.ProtoReflect.Descriptor instead.
func (*CustomerHistory) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{32}
}

func (x *CustomerHistory) GetPreviousTickets() []string {
	if x != nil {
		return x.PreviousTickets
	}
	return nil
}

func (x *CustomerHistory) GetPaymentHistory() *PaymentHistory {
	if x != nil {
		return x.PaymentHistory
	}
	return nil
}

func (x *CustomerHistory) GetServicePreferences() *ServicePreferences {
	if x != nil {
		return x.ServicePreferences
	}
	return nil
}

func (x *CustomerHistory) GetPreviousRatings() []*ServiceRating {
	if x != nil {
		return x.PreviousRatings
	}
	return nil
}

type PaymentHistory struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	PaymentStatus          string                 `protobuf:"bytes,1,opt,name=payment_status,json=paymentStatus,proto3" json:"payment_status,omitempty"`
	PaymentDelays          []string               `protobuf:"bytes,2,rep,name=payment_delays,json=paymentDelays,proto3" json:"payment_delays,omitempty"`
	PreferredPaymentMethod string                 `protobuf:"bytes,3,opt,name=preferred_payment_method,json=preferredPaymentMethod,proto3" json:"preferred_payment_method,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PaymentHistory) Reset() {
	*x = PaymentHistory{}
	mi := &file_stt_v1_stt_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentHistory) ProtoMessage() {}

func (x *PaymentHistory) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentHistory.ProtoReflect.Descriptor instead.
func (*PaymentHistory) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{33}
}

func (x *PaymentHistory) GetPaymentStatus() string {
	if x != nil {
		return x.PaymentStatus
	}
	return ""
}

func (x *PaymentHistory) GetPaymentDelays() []string {
	if x != nil {
		return x.PaymentDelays
	}
	return nil
}

func (x *PaymentHistory) GetPreferredPaymentMethod() string {
	if x != nil {
		return x.PreferredPaymentMethod
	}
	return ""
}

type ServicePreferences struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	PreferredTechnician   string                 `protobuf:"bytes,1,opt,name=preferred_technician,json=preferredTechnician,proto3" json:"preferred_technician,omitempty"`
	PreferredServiceHours []string               `protobuf:"bytes,2,rep,name=preferred_service_hours,json=preferredServiceHours,proto3" json:"preferred_service_hours,omitempty"`
	SpecialRequirements   []string               `protobuf:"bytes,3,rep,name=special_requirements,json=specialRequirements,proto3" json:"special_requirements,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ServicePreferences) Reset() {
	*x = ServicePreferences{}
	mi := &file_stt_v1_stt_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServicePreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServicePreferences) ProtoMessage() {}

func (x *ServicePreferences) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServicePreferences.ProtoReflect.Descriptor instead.
func (*ServicePreferences) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{34}
}

func (x *ServicePreferences) GetPreferredTechnician() string {
	if x != nil {
		return x.PreferredTechnician
	}
	return ""
}

func (x *ServicePreferences) GetPreferredServiceHours() []string {
	if x != nil {
		return x.PreferredServiceHours
	}
	return nil
}

func (x *ServicePreferences) GetSpecialRequirements() []string {
	if x != nil {
		return x.SpecialRequirements
	}
	return nil
}

type ServiceRating struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceDate   string                 `protobuf:"bytes,1,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	Rating        int32                  `protobuf:"varint,2,opt,name=rating,proto3" json:"rating,omitempty"`
	Comment       string                 `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	ServiceType   string                 `protobuf:"bytes,4,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceRating) Reset() {
	*x = ServiceRating{}
	mi := &file_stt_v1_stt_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceRating) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRating) ProtoMessage() {}

func (x *ServiceRating) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRating.ProtoReflect.Descriptor instead.
func (*ServiceRating) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{35}
}

func (x *ServiceRating) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *ServiceRating) GetRating() int32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *ServiceRating) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ServiceRating) GetServiceType() string {
	if x != nil {
		return x.ServiceType
	}
	return ""
}

type EstimatedRepairTime struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	MinHours               int32                  `protobuf:"varint,1,opt,name=min_hours,json=minHours,proto3" json:"min_hours,omitempty"`
	MaxHours               int32                  `protobuf:"varint,2,opt,name=max_hours,json=maxHours,proto3" json:"max_hours,omitempty"`
	TimeFactors            []string               `protobuf:"bytes,3,rep,name=time_factors,json=timeFactors,proto3" json:"time_factors,omitempty"`
	TechnicianAvailability string                 `protobuf:"bytes,4,opt,name=technician_availability,json=technicianAvailability,proto3" json:"technician_availability,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *EstimatedRepairTime) Reset() {
	*x = EstimatedRepairTime{}
	mi := &file_stt_v1_stt_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EstimatedRepairTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EstimatedRepairTime) ProtoMessage() {}

func (x *EstimatedRepairTime) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EstimatedRepairTime.ProtoReflect.Descriptor instead.
func (*EstimatedRepairTime) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{36}
}

func (x *EstimatedRepairTime) GetMinHours() int32 {
	if x != nil {
		return x.MinHours
	}
	return 0
}

func (x *EstimatedRepairTime) GetMaxHours() int32 {
	if x != nil {
		return x.MaxHours
	}
	return 0
}

func (x *EstimatedRepairTime) GetTimeFactors() []string {
	if x != nil {
		return x.TimeFactors
	}
	return nil
}

func (x *EstimatedRepairTime) GetTechnicianAvailability() string {
	if x != nil {
		return x.TechnicianAvailability
	}
	return ""
}

type RequiredPart struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PartName         string                 `protobuf:"bytes,1,opt,name=part_name,json=partName,proto3" json:"part_name,omitempty"`
	PartCode         string                 `protobuf:"bytes,2,opt,name=part_code,json=partCode,proto3" json:"part_code,omitempty"`
	Quantity         int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	EstimatedPrice   float32                `protobuf:"fixed32,4,opt,name=estimated_price,json=estimatedPrice,proto3" json:"estimated_price,omitempty"`
	Availability     string                 `protobuf:"bytes,5,opt,name=availability,proto3" json:"availability,omitempty"`
	AlternativeParts []string               `protobuf:"bytes,6,rep,name=alternative_parts,json=alternativeParts,proto3" json:"alternative_parts,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RequiredPart) Reset() {
	*x = RequiredPart{}
	mi := &file_stt_v1_stt_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequiredPart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredPart) ProtoMessage() {}

func (x *RequiredPart) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredPart.ProtoReflect.Descriptor instead.
func (*RequiredPart) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{37}
}

func (x *RequiredPart) GetPartName() string {
	if x != nil {
		return x.PartName
	}
	return ""
}

func (x *RequiredPart) GetPartCode() string {
	if x != nil {
		return x.PartCode
	}
	return ""
}

func (x *RequiredPart) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *RequiredPart) GetEstimatedPrice() float32 {
	if x != nil {
		return x.EstimatedPrice
	}
	return 0
}

func (x *RequiredPart) GetAvailability() string {
	if x != nil {
		return x.Availability
	}
	return ""
}

func (x *RequiredPart) GetAlternativeParts() []string {
	if x != nil {
		return x.AlternativeParts
	}
	return nil
}

type CostAnalysis struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PartsCost       float32                `protobuf:"fixed32,1,opt,name=parts_cost,json=partsCost,proto3" json:"parts_cost,omitempty"`
	LaborCost       float32                `protobuf:"fixed32,2,opt,name=labor_cost,json=laborCost,proto3" json:"labor_cost,omitempty"`
	AdditionalCosts float32                `protobuf:"fixed32,3,opt,name=additional_costs,json=additionalCosts,proto3" json:"additional_costs,omitempty"`
	TotalCost       float32                `protobuf:"fixed32,4,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	Currency        string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	CostBreakdown   []*CostBreakdown       `protobuf:"bytes,6,rep,name=cost_breakdown,json=costBreakdown,proto3" json:"cost_breakdown,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CostAnalysis) Reset() {
	*x = CostAnalysis{}
	mi := &file_stt_v1_stt_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CostAnalysis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CostAnalysis) ProtoMessage() {}

func (x *CostAnalysis) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CostAnalysis.ProtoReflect.Descriptor instead.
func (*CostAnalysis) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{38}
}

func (x *CostAnalysis) GetPartsCost() float32 {
	if x != nil {
		return x.PartsCost
	}
	return 0
}

func (x *CostAnalysis) GetLaborCost() float32 {
	if x != nil {
		return x.LaborCost
	}
	return 0
}

func (x *CostAnalysis) GetAdditionalCosts() float32 {
	if x != nil {
		return x.AdditionalCosts
	}
	return 0
}

func (x *CostAnalysis) GetTotalCost() float32 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *CostAnalysis) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CostAnalysis) GetCostBreakdown() []*CostBreakdown {
	if x != nil {
		return x.CostBreakdown
	}
	return nil
}

type CostBreakdown struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      string                 `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Amount        float32                `protobuf:"fixed32,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CostBreakdown) Reset() {
	*x = CostBreakdown{}
	mi := &file_stt_v1_stt_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CostBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CostBreakdown) ProtoMessage() {}

func (x *CostBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CostBreakdown.ProtoReflect.Descriptor instead.
func (*CostBreakdown) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{39}
}

func (x *CostBreakdown) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CostBreakdown) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CostBreakdown) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type TechnicianRecommendation struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	RecommendationType string                 `protobuf:"bytes,1,opt,name=recommendation_type,json=recommendationType,proto3" json:"recommendation_type,omitempty"`
	Description        string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Priority           Priority               `protobuf:"varint,3,opt,name=priority,proto3,enum=api.stt.v1.Priority" json:"priority,omitempty"`
	RequiredTools      []string               `protobuf:"bytes,4,rep,name=required_tools,json=requiredTools,proto3" json:"required_tools,omitempty"`
	SafetyMeasures     []string               `protobuf:"bytes,5,rep,name=safety_measures,json=safetyMeasures,proto3" json:"safety_measures,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TechnicianRecommendation) Reset() {
	*x = TechnicianRecommendation{}
	mi := &file_stt_v1_stt_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TechnicianRecommendation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TechnicianRecommendation) ProtoMessage() {}

func (x *TechnicianRecommendation) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TechnicianRecommendation.ProtoReflect.Descriptor instead.
func (*TechnicianRecommendation) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{40}
}

func (x *TechnicianRecommendation) GetRecommendationType() string {
	if x != nil {
		return x.RecommendationType
	}
	return ""
}

func (x *TechnicianRecommendation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TechnicianRecommendation) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *TechnicianRecommendation) GetRequiredTools() []string {
	if x != nil {
		return x.RequiredTools
	}
	return nil
}

func (x *TechnicianRecommendation) GetSafetyMeasures() []string {
	if x != nil {
		return x.SafetyMeasures
	}
	return nil
}

type PerformanceMetrics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	AverageResponseTimeMs float32                `protobuf:"fixed32,1,opt,name=average_response_time_ms,json=averageResponseTimeMs,proto3" json:"average_response_time_ms,omitempty"`
	TranscriptionAccuracy float32                `protobuf:"fixed32,2,opt,name=transcription_accuracy,json=transcriptionAccuracy,proto3" json:"transcription_accuracy,omitempty"`
	ProcessedRequests     int64                  `protobuf:"varint,3,opt,name=processed_requests,json=processedRequests,proto3" json:"processed_requests,omitempty"`
	ErrorCount            int64                  `protobuf:"varint,4,opt,name=error_count,json=errorCount,proto3" json:"error_count,omitempty"`
	CpuUsage              float32                `protobuf:"fixed32,5,opt,name=cpu_usage,json=cpuUsage,proto3" json:"cpu_usage,omitempty"`
	MemoryUsage           float32                `protobuf:"fixed32,6,opt,name=memory_usage,json=memoryUsage,proto3" json:"memory_usage,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *PerformanceMetrics) Reset() {
	*x = PerformanceMetrics{}
	mi := &file_stt_v1_stt_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetrics) ProtoMessage() {}

func (x *PerformanceMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetrics.ProtoReflect.Descriptor instead.
func (*PerformanceMetrics) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{41}
}

func (x *PerformanceMetrics) GetAverageResponseTimeMs() float32 {
	if x != nil {
		return x.AverageResponseTimeMs
	}
	return 0
}

func (x *PerformanceMetrics) GetTranscriptionAccuracy() float32 {
	if x != nil {
		return x.TranscriptionAccuracy
	}
	return 0
}

func (x *PerformanceMetrics) GetProcessedRequests() int64 {
	if x != nil {
		return x.ProcessedRequests
	}
	return 0
}

func (x *PerformanceMetrics) GetErrorCount() int64 {
	if x != nil {
		return x.ErrorCount
	}
	return 0
}

func (x *PerformanceMetrics) GetCpuUsage() float32 {
	if x != nil {
		return x.CpuUsage
	}
	return 0
}

func (x *PerformanceMetrics) GetMemoryUsage() float32 {
	if x != nil {
		return x.MemoryUsage
	}
	return 0
}

type ResourceUsage struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	GpuUsage       float32                `protobuf:"fixed32,1,opt,name=gpu_usage,json=gpuUsage,proto3" json:"gpu_usage,omitempty"`
	GpuMemoryMb    float32                `protobuf:"fixed32,2,opt,name=gpu_memory_mb,json=gpuMemoryMb,proto3" json:"gpu_memory_mb,omitempty"`
	CpuUsage       float32                `protobuf:"fixed32,3,opt,name=cpu_usage,json=cpuUsage,proto3" json:"cpu_usage,omitempty"`
	RamUsageMb     float32                `protobuf:"fixed32,4,opt,name=ram_usage_mb,json=ramUsageMb,proto3" json:"ram_usage_mb,omitempty"`
	ActiveSessions int32                  `protobuf:"varint,5,opt,name=active_sessions,json=activeSessions,proto3" json:"active_sessions,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ResourceUsage) Reset() {
	*x = ResourceUsage{}
	mi := &file_stt_v1_stt_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUsage) ProtoMessage() {}

func (x *ResourceUsage) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUsage.ProtoReflect.Descriptor instead.
func (*ResourceUsage) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{42}
}

func (x *ResourceUsage) GetGpuUsage() float32 {
	if x != nil {
		return x.GpuUsage
	}
	return 0
}

func (x *ResourceUsage) GetGpuMemoryMb() float32 {
	if x != nil {
		return x.GpuMemoryMb
	}
	return 0
}

func (x *ResourceUsage) GetCpuUsage() float32 {
	if x != nil {
		return x.CpuUsage
	}
	return 0
}

func (x *ResourceUsage) GetRamUsageMb() float32 {
	if x != nil {
		return x.RamUsageMb
	}
	return 0
}

func (x *ResourceUsage) GetActiveSessions() int32 {
	if x != nil {
		return x.ActiveSessions
	}
	return 0
}

type ModelParameters struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	BeamSize             int32                  `protobuf:"varint,1,opt,name=beam_size,json=beamSize,proto3" json:"beam_size,omitempty"`
	Temperature          float32                `protobuf:"fixed32,2,opt,name=temperature,proto3" json:"temperature,omitempty"`
	ConfidenceThreshold  float32                `protobuf:"fixed32,3,opt,name=confidence_threshold,json=confidenceThreshold,proto3" json:"confidence_threshold,omitempty"`
	MaxSegmentLength     int32                  `protobuf:"varint,4,opt,name=max_segment_length,json=maxSegmentLength,proto3" json:"max_segment_length,omitempty"`
	EnablePostProcessing bool                   `protobuf:"varint,5,opt,name=enable_post_processing,json=enablePostProcessing,proto3" json:"enable_post_processing,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ModelParameters) Reset() {
	*x = ModelParameters{}
	mi := &file_stt_v1_stt_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelParameters) ProtoMessage() {}

func (x *ModelParameters) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelParameters.ProtoReflect.Descriptor instead.
func (*ModelParameters) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{43}
}

func (x *ModelParameters) GetBeamSize() int32 {
	if x != nil {
		return x.BeamSize
	}
	return 0
}

func (x *ModelParameters) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *ModelParameters) GetConfidenceThreshold() float32 {
	if x != nil {
		return x.ConfidenceThreshold
	}
	return 0
}

func (x *ModelParameters) GetMaxSegmentLength() int32 {
	if x != nil {
		return x.MaxSegmentLength
	}
	return 0
}

func (x *ModelParameters) GetEnablePostProcessing() bool {
	if x != nil {
		return x.EnablePostProcessing
	}
	return false
}

type HVACDictionary struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HvacKeywords   []string               `protobuf:"bytes,1,rep,name=hvac_keywords,json=hvacKeywords,proto3" json:"hvac_keywords,omitempty"`
	BrandNames     []string               `protobuf:"bytes,2,rep,name=brand_names,json=brandNames,proto3" json:"brand_names,omitempty"`
	ModelNames     []string               `protobuf:"bytes,3,rep,name=model_names,json=modelNames,proto3" json:"model_names,omitempty"`
	TechnicalTerms []string               `protobuf:"bytes,4,rep,name=technical_terms,json=technicalTerms,proto3" json:"technical_terms,omitempty"`
	ErrorCodes     []string               `protobuf:"bytes,5,rep,name=error_codes,json=errorCodes,proto3" json:"error_codes,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HVACDictionary) Reset() {
	*x = HVACDictionary{}
	mi := &file_stt_v1_stt_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HVACDictionary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HVACDictionary) ProtoMessage() {}

func (x *HVACDictionary) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HVACDictionary.ProtoReflect.Descriptor instead.
func (*HVACDictionary) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{44}
}

func (x *HVACDictionary) GetHvacKeywords() []string {
	if x != nil {
		return x.HvacKeywords
	}
	return nil
}

func (x *HVACDictionary) GetBrandNames() []string {
	if x != nil {
		return x.BrandNames
	}
	return nil
}

func (x *HVACDictionary) GetModelNames() []string {
	if x != nil {
		return x.ModelNames
	}
	return nil
}

func (x *HVACDictionary) GetTechnicalTerms() []string {
	if x != nil {
		return x.TechnicalTerms
	}
	return nil
}

func (x *HVACDictionary) GetErrorCodes() []string {
	if x != nil {
		return x.ErrorCodes
	}
	return nil
}

type QualitySettings struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	MinAudioQuality         float32                `protobuf:"fixed32,1,opt,name=min_audio_quality,json=minAudioQuality,proto3" json:"min_audio_quality,omitempty"`
	MaxNoiseLevel           float32                `protobuf:"fixed32,2,opt,name=max_noise_level,json=maxNoiseLevel,proto3" json:"max_noise_level,omitempty"`
	EnableAutoEnhancement   bool                   `protobuf:"varint,3,opt,name=enable_auto_enhancement,json=enableAutoEnhancement,proto3" json:"enable_auto_enhancement,omitempty"`
	FilteringAggressiveness int32                  `protobuf:"varint,4,opt,name=filtering_aggressiveness,json=filteringAggressiveness,proto3" json:"filtering_aggressiveness,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *QualitySettings) Reset() {
	*x = QualitySettings{}
	mi := &file_stt_v1_stt_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QualitySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QualitySettings) ProtoMessage() {}

func (x *QualitySettings) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QualitySettings.ProtoReflect.Descriptor instead.
func (*QualitySettings) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{45}
}

func (x *QualitySettings) GetMinAudioQuality() float32 {
	if x != nil {
		return x.MinAudioQuality
	}
	return 0
}

func (x *QualitySettings) GetMaxNoiseLevel() float32 {
	if x != nil {
		return x.MaxNoiseLevel
	}
	return 0
}

func (x *QualitySettings) GetEnableAutoEnhancement() bool {
	if x != nil {
		return x.EnableAutoEnhancement
	}
	return false
}

func (x *QualitySettings) GetFilteringAggressiveness() int32 {
	if x != nil {
		return x.FilteringAggressiveness
	}
	return 0
}

type ModelConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Parameters    *ModelParameters       `protobuf:"bytes,1,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Dictionary    *HVACDictionary        `protobuf:"bytes,2,opt,name=dictionary,proto3" json:"dictionary,omitempty"`
	Quality       *QualitySettings       `protobuf:"bytes,3,opt,name=quality,proto3" json:"quality,omitempty"`
	LastUpdated   int64                  `protobuf:"varint,4,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelConfiguration) Reset() {
	*x = ModelConfiguration{}
	mi := &file_stt_v1_stt_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfiguration) ProtoMessage() {}

func (x *ModelConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_stt_v1_stt_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfiguration.ProtoReflect.Descriptor instead.
func (*ModelConfiguration) Descriptor() ([]byte, []int) {
	return file_stt_v1_stt_proto_rawDescGZIP(), []int{46}
}

func (x *ModelConfiguration) GetParameters() *ModelParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelConfiguration) GetDictionary() *HVACDictionary {
	if x != nil {
		return x.Dictionary
	}
	return nil
}

func (x *ModelConfiguration) GetQuality() *QualitySettings {
	if x != nil {
		return x.Quality
	}
	return nil
}

func (x *ModelConfiguration) GetLastUpdated() int64 {
	if x != nil {
		return x.LastUpdated
	}
	return 0
}

var File_stt_v1_stt_proto protoreflect.FileDescriptor

const file_stt_v1_stt_proto_rawDesc = "" +
	"\n" +
	"\x10stt/v1/stt.proto\x12\n" +
	"api.stt.v1\x1a\x1cgoogle/api/annotations.proto\"\xbd\x02\n" +
	"\x11TranscribeRequest\x12\x1d\n" +
	"\n" +
	"audio_data\x18\x01 \x01(\fR\taudioData\x12!\n" +
	"\faudio_format\x18\x02 \x01(\tR\vaudioFormat\x12\x1f\n" +
	"\vsample_rate\x18\x03 \x01(\x05R\n" +
	"sampleRate\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x121\n" +
	"\x04mode\x18\x05 \x01(\x0e2\x1d.api.stt.v1.TranscriptionModeR\x04mode\x12:\n" +
	"\fhvac_context\x18\x06 \x01(\v2\x17.api.stt.v1.HVACContextR\vhvacContext\x12:\n" +
	"\aoptions\x18\a \x01(\v2 .api.stt.v1.TranscriptionOptionsR\aoptions\"\xf4\x02\n" +
	"\x12TranscribeResponse\x12\x1e\n" +
	"\n" +
	"transcript\x18\x01 \x01(\tR\n" +
	"transcript\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x02R\n" +
	"confidence\x129\n" +
	"\bsegments\x18\x03 \x03(\v2\x1d.api.stt.v1.TranscriptSegmentR\bsegments\x12#\n" +
	"\rhvac_keywords\x18\x04 \x03(\tR\fhvacKeywords\x12;\n" +
	"\tsentiment\x18\x05 \x01(\v2\x1d.api.stt.v1.SentimentAnalysisR\tsentiment\x12:\n" +
	"\bmetadata\x18\x06 \x01(\v2\x1e.api.stt.v1.ProcessingMetadataR\bmetadata\x12E\n" +
	"\x10technical_issues\x18\a \x03(\v2\x1a.api.stt.v1.TechnicalIssueR\x0ftechnicalIssues\"\x80\x01\n" +
	"\x17StreamTranscribeRequest\x122\n" +
	"\x06config\x18\x01 \x01(\v2\x18.api.stt.v1.StreamConfigH\x00R\x06config\x12!\n" +
	"\vaudio_chunk\x18\x02 \x01(\fH\x00R\n" +
	"audioChunkB\x0e\n" +
	"\frequest_type\"\xee\x01\n" +
	"\x18StreamTranscribeResponse\x12-\n" +
	"\x12partial_transcript\x18\x01 \x01(\tR\x11partialTranscript\x12)\n" +
	"\x10final_transcript\x18\x02 \x01(\tR\x0ffinalTranscript\x12\x19\n" +
	"\bis_final\x18\x03 \x01(\bR\aisFinal\x12\x1e\n" +
	"\n" +
	"confidence\x18\x04 \x01(\x02R\n" +
	"confidence\x12!\n" +
	"\ftimestamp_ms\x18\x05 \x01(\x03R\vtimestampMs\x12\x1a\n" +
	"\bkeywords\x18\x06 \x03(\tR\bkeywords\"\x84\x02\n" +
	"\x10PhoneCallRequest\x12\x1d\n" +
	"\n" +
	"call_audio\x18\x01 \x01(\fR\tcallAudio\x12=\n" +
	"\rcall_metadata\x18\x02 \x01(\v2\x18.api.stt.v1.CallMetadataR\fcallMetadata\x12F\n" +
	"\x10customer_context\x18\x03 \x01(\v2\x1b.api.stt.v1.CustomerContextR\x0fcustomerContext\x12J\n" +
	"\x10analysis_options\x18\x04 \x01(\v2\x1f.api.stt.v1.CallAnalysisOptionsR\x0fanalysisOptions\"\x9d\x03\n" +
	"\x11PhoneCallResponse\x12'\n" +
	"\x0ffull_transcript\x18\x01 \x01(\tR\x0efullTranscript\x12E\n" +
	"\x10speaker_segments\x18\x02 \x03(\v2\x1a.api.stt.v1.SpeakerSegmentR\x0fspeakerSegments\x12:\n" +
	"\fcall_summary\x18\x03 \x01(\v2\x17.api.stt.v1.CallSummaryR\vcallSummary\x12?\n" +
	"\x0ecustomer_needs\x18\x04 \x03(\v2\x18.api.stt.v1.CustomerNeedR\rcustomerNeeds\x12N\n" +
	"\x13recommended_actions\x18\x05 \x03(\v2\x1d.api.stt.v1.RecommendedActionR\x12recommendedActions\x12K\n" +
	"\x0fservice_quality\x18\x06 \x01(\v2\".api.stt.v1.ServiceQualityAnalysisR\x0eserviceQuality\"\x81\x02\n" +
	"\x17HVACCallAnalysisRequest\x12\x1e\n" +
	"\n" +
	"transcript\x18\x01 \x01(\tR\n" +
	"transcript\x12=\n" +
	"\rcall_metadata\x18\x02 \x01(\v2\x18.api.stt.v1.CallMetadataR\fcallMetadata\x12F\n" +
	"\x10customer_history\x18\x03 \x01(\v2\x1b.api.stt.v1.CustomerHistoryR\x0fcustomerHistory\x12?\n" +
	"\x0eanalysis_types\x18\x04 \x03(\x0e2\x18.api.stt.v1.AnalysisTypeR\ranalysisTypes\"\x84\x04\n" +
	"\x18HVACCallAnalysisResponse\x12E\n" +
	"\x10technical_issues\x18\x01 \x03(\v2\x1a.api.stt.v1.TechnicalIssueR\x0ftechnicalIssues\x12B\n" +
	"\x10required_service\x18\x02 \x01(\x0e2\x17.api.stt.v1.ServiceTypeR\x0frequiredService\x120\n" +
	"\bpriority\x18\x03 \x01(\x0e2\x14.api.stt.v1.PriorityR\bpriority\x12F\n" +
	"\x0eestimated_time\x18\x04 \x01(\v2\x1f.api.stt.v1.EstimatedRepairTimeR\restimatedTime\x12?\n" +
	"\x0erequired_parts\x18\x05 \x03(\v2\x18.api.stt.v1.RequiredPartR\rrequiredParts\x12=\n" +
	"\rcost_analysis\x18\x06 \x01(\v2\x18.api.stt.v1.CostAnalysisR\fcostAnalysis\x12c\n" +
	"\x1atechnician_recommendations\x18\a \x03(\v2$.api.stt.v1.TechnicianRecommendationR\x19technicianRecommendations\"\x14\n" +
	"\x12ModelStatusRequest\"\xa0\x02\n" +
	"\x13ModelStatusResponse\x12/\n" +
	"\x06status\x18\x01 \x01(\x0e2\x17.api.stt.v1.ModelStatusR\x06status\x12#\n" +
	"\rmodel_version\x18\x02 \x01(\tR\fmodelVersion\x12@\n" +
	"\vperformance\x18\x03 \x01(\v2\x1e.api.stt.v1.PerformanceMetricsR\vperformance\x12/\n" +
	"\x13supported_languages\x18\x04 \x03(\tR\x12supportedLanguages\x12@\n" +
	"\x0eresource_usage\x18\x05 \x01(\v2\x19.api.stt.v1.ResourceUsageR\rresourceUsage\"\xde\x01\n" +
	"\x12ModelConfigRequest\x12;\n" +
	"\n" +
	"parameters\x18\x01 \x01(\v2\x1b.api.stt.v1.ModelParametersR\n" +
	"parameters\x12C\n" +
	"\x0fhvac_dictionary\x18\x02 \x01(\v2\x1a.api.stt.v1.HVACDictionaryR\x0ehvacDictionary\x12F\n" +
	"\x10quality_settings\x18\x03 \x01(\v2\x1b.api.stt.v1.QualitySettingsR\x0fqualitySettings\"\x90\x01\n" +
	"\x13ModelConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12E\n" +
	"\x0ecurrent_config\x18\x03 \x01(\v2\x1e.api.stt.v1.ModelConfigurationR\rcurrentConfig\"\xf7\x01\n" +
	"\vHVACContext\x12\x1f\n" +
	"\vsystem_type\x18\x01 \x01(\tR\n" +
	"systemType\x12'\n" +
	"\x0fequipment_brand\x18\x02 \x01(\tR\x0eequipmentBrand\x12'\n" +
	"\x0fequipment_model\x18\x03 \x01(\tR\x0eequipmentModel\x124\n" +
	"\x16installation_age_years\x18\x04 \x01(\x05R\x14installationAgeYears\x12'\n" +
	"\x0fservice_history\x18\x05 \x03(\tR\x0eserviceHistory\x12\x16\n" +
	"\x06season\x18\x06 \x01(\tR\x06season\"\xf6\x02\n" +
	"\x14TranscriptionOptions\x128\n" +
	"\x18enable_emotion_detection\x18\x01 \x01(\bR\x16enableEmotionDetection\x128\n" +
	"\x18enable_keyword_detection\x18\x02 \x01(\bR\x16enableKeywordDetection\x12:\n" +
	"\x19enable_technical_analysis\x18\x03 \x01(\bR\x17enableTechnicalAnalysis\x128\n" +
	"\x18timestamp_granularity_ms\x18\x04 \x01(\x05R\x16timestampGranularityMs\x124\n" +
	"\x16enable_noise_reduction\x18\x05 \x01(\bR\x14enableNoiseReduction\x12>\n" +
	"\x1benable_volume_normalization\x18\x06 \x01(\bR\x19enableVolumeNormalization\"\xc6\x01\n" +
	"\x11TranscriptSegment\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\"\n" +
	"\rstart_time_ms\x18\x02 \x01(\x03R\vstartTimeMs\x12\x1e\n" +
	"\vend_time_ms\x18\x03 \x01(\x03R\tendTimeMs\x12\x1e\n" +
	"\n" +
	"confidence\x18\x04 \x01(\x02R\n" +
	"confidence\x12\x1d\n" +
	"\n" +
	"speaker_id\x18\x05 \x01(\tR\tspeakerId\x12\x1a\n" +
	"\bemotions\x18\x06 \x03(\tR\bemotions\"\xff\x01\n" +
	"\x11SentimentAnalysis\x12+\n" +
	"\x11overall_sentiment\x18\x01 \x01(\x02R\x10overallSentiment\x12+\n" +
	"\x11frustration_level\x18\x02 \x01(\x02R\x10frustrationLevel\x12-\n" +
	"\x12satisfaction_level\x18\x03 \x01(\x02R\x11satisfactionLevel\x124\n" +
	"\bemotions\x18\x04 \x03(\v2\x18.api.stt.v1.EmotionScoreR\bemotions\x12+\n" +
	"\x11conversation_tone\x18\x05 \x01(\tR\x10conversationTone\">\n" +
	"\fEmotionScore\x12\x18\n" +
	"\aemotion\x18\x01 \x01(\tR\aemotion\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x02R\x05score\"\xd9\x01\n" +
	"\x12ProcessingMetadata\x12,\n" +
	"\x12processing_time_ms\x18\x01 \x01(\x03R\x10processingTimeMs\x12#\n" +
	"\rmodel_version\x18\x02 \x01(\tR\fmodelVersion\x12=\n" +
	"\raudio_quality\x18\x03 \x01(\v2\x18.api.stt.v1.AudioQualityR\faudioQuality\x121\n" +
	"\x05stats\x18\x04 \x01(\v2\x1b.api.stt.v1.ProcessingStatsR\x05stats\"}\n" +
	"\fAudioQuality\x12\x1f\n" +
	"\vnoise_level\x18\x01 \x01(\x02R\n" +
	"noiseLevel\x12%\n" +
	"\x0esignal_quality\x18\x02 \x01(\x02R\rsignalQuality\x12%\n" +
	"\x0equality_issues\x18\x03 \x03(\tR\rqualityIssues\"\x9c\x01\n" +
	"\x0fProcessingStats\x12\x1d\n" +
	"\n" +
	"word_count\x18\x01 \x01(\x05R\twordCount\x124\n" +
	"\x16audio_duration_seconds\x18\x02 \x01(\x02R\x14audioDurationSeconds\x124\n" +
	"\x16processing_speed_ratio\x18\x03 \x01(\x02R\x14processingSpeedRatio\"\xfe\x01\n" +
	"\x0eTechnicalIssue\x12\x1d\n" +
	"\n" +
	"issue_type\x18\x01 \x01(\tR\tissueType\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x1e\n" +
	"\n" +
	"confidence\x18\x03 \x01(\x02R\n" +
	"confidence\x12-\n" +
	"\x12affected_component\x18\x04 \x01(\tR\x11affectedComponent\x12'\n" +
	"\x0fpossible_causes\x18\x05 \x03(\tR\x0epossibleCauses\x123\n" +
	"\x15recommended_solutions\x18\x06 \x03(\tR\x14recommendedSolutions\"\xca\x01\n" +
	"\fStreamConfig\x12\x1f\n" +
	"\vsample_rate\x18\x01 \x01(\x05R\n" +
	"sampleRate\x12!\n" +
	"\faudio_format\x18\x02 \x01(\tR\vaudioFormat\x12:\n" +
	"\fhvac_context\x18\x03 \x01(\v2\x17.api.stt.v1.HVACContextR\vhvacContext\x12:\n" +
	"\aoptions\x18\x04 \x01(\v2 .api.stt.v1.TranscriptionOptionsR\aoptions\"\xd6\x01\n" +
	"\fCallMetadata\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\x12\x1d\n" +
	"\n" +
	"start_time\x18\x02 \x01(\x03R\tstartTime\x12)\n" +
	"\x10duration_seconds\x18\x03 \x01(\x03R\x0fdurationSeconds\x12%\n" +
	"\x0ecustomer_phone\x18\x04 \x01(\tR\rcustomerPhone\x12\x1f\n" +
	"\voperator_id\x18\x05 \x01(\tR\n" +
	"operatorId\x12\x1b\n" +
	"\tcall_type\x18\x06 \x01(\tR\bcallType\"\xcc\x01\n" +
	"\x0fCustomerContext\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\tR\n" +
	"customerId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\tR\aaddress\x12'\n" +
	"\x0fservice_history\x18\x04 \x03(\tR\x0eserviceHistory\x12A\n" +
	"\vpreferences\x18\x05 \x01(\v2\x1f.api.stt.v1.CustomerPreferencesR\vpreferences\"\xa9\x01\n" +
	"\x13CustomerPreferences\x124\n" +
	"\x16preferred_contact_time\x18\x01 \x01(\tR\x14preferredContactTime\x127\n" +
	"\x17preferred_communication\x18\x02 \x01(\tR\x16preferredCommunication\x12#\n" +
	"\rspecial_notes\x18\x03 \x03(\tR\fspecialNotes\"\xdd\x01\n" +
	"\x13CallAnalysisOptions\x12+\n" +
	"\x11analyze_sentiment\x18\x01 \x01(\bR\x10analyzeSentiment\x126\n" +
	"\x17detect_technical_issues\x18\x02 \x01(\bR\x15detectTechnicalIssues\x126\n" +
	"\x17analyze_service_quality\x18\x03 \x01(\bR\x15analyzeServiceQuality\x12)\n" +
	"\x10generate_summary\x18\x04 \x01(\bR\x0fgenerateSummary\"\xc4\x01\n" +
	"\x0eSpeakerSegment\x12\x1d\n" +
	"\n" +
	"speaker_id\x18\x01 \x01(\tR\tspeakerId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\"\n" +
	"\rstart_time_ms\x18\x03 \x01(\x03R\vstartTimeMs\x12\x1e\n" +
	"\vend_time_ms\x18\x04 \x01(\x03R\tendTimeMs\x12;\n" +
	"\tsentiment\x18\x05 \x01(\v2\x1d.api.stt.v1.SentimentAnalysisR\tsentiment\"\xb3\x01\n" +
	"\vCallSummary\x12\x18\n" +
	"\asummary\x18\x01 \x01(\tR\asummary\x12\x1d\n" +
	"\n" +
	"key_points\x18\x02 \x03(\tR\tkeyPoints\x12%\n" +
	"\x0edecisions_made\x18\x03 \x03(\tR\rdecisionsMade\x12\x1d\n" +
	"\n" +
	"next_steps\x18\x04 \x03(\tR\tnextSteps\x12%\n" +
	"\x0eoverall_rating\x18\x05 \x01(\x02R\roverallRating\"\xbe\x01\n" +
	"\fCustomerNeed\x12\x1b\n" +
	"\tneed_type\x18\x01 \x01(\tR\bneedType\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x120\n" +
	"\bpriority\x18\x03 \x01(\x0e2\x14.api.stt.v1.PriorityR\bpriority\x12=\n" +
	"\rcost_estimate\x18\x04 \x01(\v2\x18.api.stt.v1.CostEstimateR\fcostEstimate\"\x83\x01\n" +
	"\fCostEstimate\x12\x19\n" +
	"\bmin_cost\x18\x01 \x01(\x02R\aminCost\x12\x19\n" +
	"\bmax_cost\x18\x02 \x01(\x02R\amaxCost\x12\x1a\n" +
	"\bcurrency\x18\x03 \x01(\tR\bcurrency\x12!\n" +
	"\fcost_factors\x18\x04 \x03(\tR\vcostFactors\"\xf5\x01\n" +
	"\x11RecommendedAction\x12\x1f\n" +
	"\vaction_type\x18\x01 \x01(\tR\n" +
	"actionType\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x120\n" +
	"\bpriority\x18\x03 \x01(\x0e2\x14.api.stt.v1.PriorityR\bpriority\x12<\n" +
	"\x1aestimated_duration_minutes\x18\x04 \x01(\x05R\x18estimatedDurationMinutes\x12-\n" +
	"\x12required_resources\x18\x05 \x03(\tR\x11requiredResources\"\xcf\x02\n" +
	"\x16ServiceQualityAnalysis\x122\n" +
	"\x15overall_quality_score\x18\x01 \x01(\x02R\x13overallQualityScore\x123\n" +
	"\x15professionalism_score\x18\x02 \x01(\x02R\x14professionalismScore\x124\n" +
	"\x16resolution_speed_score\x18\x03 \x01(\x02R\x14resolutionSpeedScore\x12>\n" +
	"\x1bcustomer_satisfaction_score\x18\x04 \x01(\x02R\x19customerSatisfactionScore\x12+\n" +
	"\x11improvement_areas\x18\x05 \x03(\tR\x10improvementAreas\x12)\n" +
	"\x10positive_aspects\x18\x06 \x03(\tR\x0fpositiveAspects\"\x98\x02\n" +
	"\x0fCustomerHistory\x12)\n" +
	"\x10previous_tickets\x18\x01 \x03(\tR\x0fpreviousTickets\x12C\n" +
	"\x0fpayment_history\x18\x02 \x01(\v2\x1a.api.stt.v1.PaymentHistoryR\x0epaymentHistory\x12O\n" +
	"\x13service_preferences\x18\x03 \x01(\v2\x1e.api.stt.v1.ServicePreferencesR\x12servicePreferences\x12D\n" +
	"\x10previous_ratings\x18\x04 \x03(\v2\x19.api.stt.v1.ServiceRatingR\x0fpreviousRatings\"\x98\x01\n" +
	"\x0ePaymentHistory\x12%\n" +
	"\x0epayment_status\x18\x01 \x01(\tR\rpaymentStatus\x12%\n" +
	"\x0epayment_delays\x18\x02 \x03(\tR\rpaymentDelays\x128\n" +
	"\x18preferred_payment_method\x18\x03 \x01(\tR\x16preferredPaymentMethod\"\xb2\x01\n" +
	"\x12ServicePreferences\x121\n" +
	"\x14preferred_technician\x18\x01 \x01(\tR\x13preferredTechnician\x126\n" +
	"\x17preferred_service_hours\x18\x02 \x03(\tR\x15preferredServiceHours\x121\n" +
	"\x14special_requirements\x18\x03 \x03(\tR\x13specialRequirements\"\x87\x01\n" +
	"\rServiceRating\x12!\n" +
	"\fservice_date\x18\x01 \x01(\tR\vserviceDate\x12\x16\n" +
	"\x06rating\x18\x02 \x01(\x05R\x06rating\x12\x18\n" +
	"\acomment\x18\x03 \x01(\tR\acomment\x12!\n" +
	"\fservice_type\x18\x04 \x01(\tR\vserviceType\"\xab\x01\n" +
	"\x13EstimatedRepairTime\x12\x1b\n" +
	"\tmin_hours\x18\x01 \x01(\x05R\bminHours\x12\x1b\n" +
	"\tmax_hours\x18\x02 \x01(\x05R\bmaxHours\x12!\n" +
	"\ftime_factors\x18\x03 \x03(\tR\vtimeFactors\x127\n" +
	"\x17technician_availability\x18\x04 \x01(\tR\x16technicianAvailability\"\xde\x01\n" +
	"\fRequiredPart\x12\x1b\n" +
	"\tpart_name\x18\x01 \x01(\tR\bpartName\x12\x1b\n" +
	"\tpart_code\x18\x02 \x01(\tR\bpartCode\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12'\n" +
	"\x0festimated_price\x18\x04 \x01(\x02R\x0eestimatedPrice\x12\"\n" +
	"\favailability\x18\x05 \x01(\tR\favailability\x12+\n" +
	"\x11alternative_parts\x18\x06 \x03(\tR\x10alternativeParts\"\xf4\x01\n" +
	"\fCostAnalysis\x12\x1d\n" +
	"\n" +
	"parts_cost\x18\x01 \x01(\x02R\tpartsCost\x12\x1d\n" +
	"\n" +
	"labor_cost\x18\x02 \x01(\x02R\tlaborCost\x12)\n" +
	"\x10additional_costs\x18\x03 \x01(\x02R\x0fadditionalCosts\x12\x1d\n" +
	"\n" +
	"total_cost\x18\x04 \x01(\x02R\ttotalCost\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12@\n" +
	"\x0ecost_breakdown\x18\x06 \x03(\v2\x19.api.stt.v1.CostBreakdownR\rcostBreakdown\"e\n" +
	"\rCostBreakdown\x12\x1a\n" +
	"\bcategory\x18\x01 \x01(\tR\bcategory\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x02R\x06amount\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"\xef\x01\n" +
	"\x18TechnicianRecommendation\x12/\n" +
	"\x13recommendation_type\x18\x01 \x01(\tR\x12recommendationType\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x120\n" +
	"\bpriority\x18\x03 \x01(\x0e2\x14.api.stt.v1.PriorityR\bpriority\x12%\n" +
	"\x0erequired_tools\x18\x04 \x03(\tR\rrequiredTools\x12'\n" +
	"\x0fsafety_measures\x18\x05 \x03(\tR\x0esafetyMeasures\"\x94\x02\n" +
	"\x12PerformanceMetrics\x127\n" +
	"\x18average_response_time_ms\x18\x01 \x01(\x02R\x15averageResponseTimeMs\x125\n" +
	"\x16transcription_accuracy\x18\x02 \x01(\x02R\x15transcriptionAccuracy\x12-\n" +
	"\x12processed_requests\x18\x03 \x01(\x03R\x11processedRequests\x12\x1f\n" +
	"\verror_count\x18\x04 \x01(\x03R\n" +
	"errorCount\x12\x1b\n" +
	"\tcpu_usage\x18\x05 \x01(\x02R\bcpuUsage\x12!\n" +
	"\fmemory_usage\x18\x06 \x01(\x02R\vmemoryUsage\"\xb8\x01\n" +
	"\rResourceUsage\x12\x1b\n" +
	"\tgpu_usage\x18\x01 \x01(\x02R\bgpuUsage\x12\"\n" +
	"\rgpu_memory_mb\x18\x02 \x01(\x02R\vgpuMemoryMb\x12\x1b\n" +
	"\tcpu_usage\x18\x03 \x01(\x02R\bcpuUsage\x12 \n" +
	"\fram_usage_mb\x18\x04 \x01(\x02R\n" +
	"ramUsageMb\x12'\n" +
	"\x0factive_sessions\x18\x05 \x01(\x05R\x0eactiveSessions\"\xe7\x01\n" +
	"\x0fModelParameters\x12\x1b\n" +
	"\tbeam_size\x18\x01 \x01(\x05R\bbeamSize\x12 \n" +
	"\vtemperature\x18\x02 \x01(\x02R\vtemperature\x121\n" +
	"\x14confidence_threshold\x18\x03 \x01(\x02R\x13confidenceThreshold\x12,\n" +
	"\x12max_segment_length\x18\x04 \x01(\x05R\x10maxSegmentLength\x124\n" +
	"\x16enable_post_processing\x18\x05 \x01(\bR\x14enablePostProcessing\"\xc1\x01\n" +
	"\x0eHVACDictionary\x12#\n" +
	"\rhvac_keywords\x18\x01 \x03(\tR\fhvacKeywords\x12\x1f\n" +
	"\vbrand_names\x18\x02 \x03(\tR\n" +
	"brandNames\x12\x1f\n" +
	"\vmodel_names\x18\x03 \x03(\tR\n" +
	"modelNames\x12'\n" +
	"\x0ftechnical_terms\x18\x04 \x03(\tR\x0etechnicalTerms\x12\x1f\n" +
	"\verror_codes\x18\x05 \x03(\tR\n" +
	"errorCodes\"\xd8\x01\n" +
	"\x0fQualitySettings\x12*\n" +
	"\x11min_audio_quality\x18\x01 \x01(\x02R\x0fminAudioQuality\x12&\n" +
	"\x0fmax_noise_level\x18\x02 \x01(\x02R\rmaxNoiseLevel\x126\n" +
	"\x17enable_auto_enhancement\x18\x03 \x01(\bR\x15enableAutoEnhancement\x129\n" +
	"\x18filtering_aggressiveness\x18\x04 \x01(\x05R\x17filteringAggressiveness\"\xe7\x01\n" +
	"\x12ModelConfiguration\x12;\n" +
	"\n" +
	"parameters\x18\x01 \x01(\v2\x1b.api.stt.v1.ModelParametersR\n" +
	"parameters\x12:\n" +
	"\n" +
	"dictionary\x18\x02 \x01(\v2\x1a.api.stt.v1.HVACDictionaryR\n" +
	"dictionary\x125\n" +
	"\aquality\x18\x03 \x01(\v2\x1b.api.stt.v1.QualitySettingsR\aquality\x12!\n" +
	"\flast_updated\x18\x04 \x01(\x03R\vlastUpdated*\xc4\x01\n" +
	"\x11TranscriptionMode\x12\"\n" +
	"\x1eTRANSCRIPTION_MODE_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bTRANSCRIPTION_MODE_STANDARD\x10\x01\x12%\n" +
	"!TRANSCRIPTION_MODE_HVAC_OPTIMIZED\x10\x02\x12!\n" +
	"\x1dTRANSCRIPTION_MODE_PHONE_CALL\x10\x03\x12 \n" +
	"\x1cTRANSCRIPTION_MODE_TECHNICAL\x10\x04*\x8b\x01\n" +
	"\bPriority\x12\x18\n" +
	"\x14PRIORITY_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fPRIORITY_LOW\x10\x01\x12\x13\n" +
	"\x0fPRIORITY_MEDIUM\x10\x02\x12\x11\n" +
	"\rPRIORITY_HIGH\x10\x03\x12\x13\n" +
	"\x0fPRIORITY_URGENT\x10\x04\x12\x16\n" +
	"\x12PRIORITY_EMERGENCY\x10\x05*\xd9\x01\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18SERVICE_TYPE_MAINTENANCE\x10\x01\x12\x17\n" +
	"\x13SERVICE_TYPE_REPAIR\x10\x02\x12\x1d\n" +
	"\x19SERVICE_TYPE_INSTALLATION\x10\x03\x12\x1b\n" +
	"\x17SERVICE_TYPE_INSPECTION\x10\x04\x12\x1a\n" +
	"\x16SERVICE_TYPE_EMERGENCY\x10\x05\x12\x1d\n" +
	"\x19SERVICE_TYPE_CONSULTATION\x10\x06*\xb5\x01\n" +
	"\fAnalysisType\x12\x1d\n" +
	"\x19ANALYSIS_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17ANALYSIS_TYPE_TECHNICAL\x10\x01\x12\x1b\n" +
	"\x17ANALYSIS_TYPE_SENTIMENT\x10\x02\x12\x19\n" +
	"\x15ANALYSIS_TYPE_URGENCY\x10\x03\x12\x16\n" +
	"\x12ANALYSIS_TYPE_COST\x10\x04\x12\x19\n" +
	"\x15ANALYSIS_TYPE_QUALITY\x10\x05*\xaa\x01\n" +
	"\vModelStatus\x12\x1c\n" +
	"\x18MODEL_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14MODEL_STATUS_LOADING\x10\x01\x12\x16\n" +
	"\x12MODEL_STATUS_READY\x10\x02\x12\x15\n" +
	"\x11MODEL_STATUS_BUSY\x10\x03\x12\x16\n" +
	"\x12MODEL_STATUS_ERROR\x10\x04\x12\x1c\n" +
	"\x18MODEL_STATUS_MAINTENANCE\x10\x052\xd4\x05\n" +
	"\n" +
	"STTService\x12s\n" +
	"\x0fTranscribeAudio\x12\x1d.api.stt.v1.TranscribeRequest\x1a\x1e.api.stt.v1.TranscribeResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/stt/transcribe\x12a\n" +
	"\x10TranscribeStream\x12#.api.stt.v1.StreamTranscribeRequest\x1a$.api.stt.v1.StreamTranscribeResponse(\x010\x01\x12u\n" +
	"\x13TranscribePhoneCall\x12\x1c.api.stt.v1.PhoneCallRequest\x1a\x1d.api.stt.v1.PhoneCallResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/stt/phone-call\x12\x86\x01\n" +
	"\x0fAnalyzeHVACCall\x12#.api.stt.v1.HVACCallAnalysisRequest\x1a$.api.stt.v1.HVACCallAnalysisResponse\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/api/v1/stt/analyze-hvac-call\x12s\n" +
	"\x0eGetModelStatus\x12\x1e.api.stt.v1.ModelStatusRequest\x1a\x1f.api.stt.v1.ModelStatusResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v1/stt/model/status\x12y\n" +
	"\x0eConfigureModel\x12\x1e.api.stt.v1.ModelConfigRequest\x1a\x1f.api.stt.v1.ModelConfigResponse\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/api/v1/stt/model/configureB%Z#gobackend-hvac-kratos/api/stt/v1;v1b\x06proto3"

var (
	file_stt_v1_stt_proto_rawDescOnce sync.Once
	file_stt_v1_stt_proto_rawDescData []byte
)

func file_stt_v1_stt_proto_rawDescGZIP() []byte {
	file_stt_v1_stt_proto_rawDescOnce.Do(func() {
		file_stt_v1_stt_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_stt_v1_stt_proto_rawDesc), len(file_stt_v1_stt_proto_rawDesc)))
	})
	return file_stt_v1_stt_proto_rawDescData
}

var file_stt_v1_stt_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_stt_v1_stt_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_stt_v1_stt_proto_goTypes = []any{
	(TranscriptionMode)(0),           // 0: api.stt.v1.TranscriptionMode
	(Priority)(0),                    // 1: api.stt.v1.Priority
	(ServiceType)(0),                 // 2: api.stt.v1.ServiceType
	(AnalysisType)(0),                // 3: api.stt.v1.AnalysisType
	(ModelStatus)(0),                 // 4: api.stt.v1.ModelStatus
	(*TranscribeRequest)(nil),        // 5: api.stt.v1.TranscribeRequest
	(*TranscribeResponse)(nil),       // 6: api.stt.v1.TranscribeResponse
	(*StreamTranscribeRequest)(nil),  // 7: api.stt.v1.StreamTranscribeRequest
	(*StreamTranscribeResponse)(nil), // 8: api.stt.v1.StreamTranscribeResponse
	(*PhoneCallRequest)(nil),         // 9: api.stt.v1.PhoneCallRequest
	(*PhoneCallResponse)(nil),        // 10: api.stt.v1.PhoneCallResponse
	(*HVACCallAnalysisRequest)(nil),  // 11: api.stt.v1.HVACCallAnalysisRequest
	(*HVACCallAnalysisResponse)(nil), // 12: api.stt.v1.HVACCallAnalysisResponse
	(*ModelStatusRequest)(nil),       // 13: api.stt.v1.ModelStatusRequest
	(*ModelStatusResponse)(nil),      // 14: api.stt.v1.ModelStatusResponse
	(*ModelConfigRequest)(nil),       // 15: api.stt.v1.ModelConfigRequest
	(*ModelConfigResponse)(nil),      // 16: api.stt.v1.ModelConfigResponse
	(*HVACContext)(nil),              // 17: api.stt.v1.HVACContext
	(*TranscriptionOptions)(nil),     // 18: api.stt.v1.TranscriptionOptions
	(*TranscriptSegment)(nil),        // 19: api.stt.v1.TranscriptSegment
	(*SentimentAnalysis)(nil),        // 20: api.stt.v1.SentimentAnalysis
	(*EmotionScore)(nil),             // 21: api.stt.v1.EmotionScore
	(*ProcessingMetadata)(nil),       // 22: api.stt.v1.ProcessingMetadata
	(*AudioQuality)(nil),             // 23: api.stt.v1.AudioQuality
	(*ProcessingStats)(nil),          // 24: api.stt.v1.ProcessingStats
	(*TechnicalIssue)(nil),           // 25: api.stt.v1.TechnicalIssue
	(*StreamConfig)(nil),             // 26: api.stt.v1.StreamConfig
	(*CallMetadata)(nil),             // 27: api.stt.v1.CallMetadata
	(*CustomerContext)(nil),          // 28: api.stt.v1.CustomerContext
	(*CustomerPreferences)(nil),      // 29: api.stt.v1.CustomerPreferences
	(*CallAnalysisOptions)(nil),      // 30: api.stt.v1.CallAnalysisOptions
	(*SpeakerSegment)(nil),           // 31: api.stt.v1.SpeakerSegment
	(*CallSummary)(nil),              // 32: api.stt.v1.CallSummary
	(*CustomerNeed)(nil),             // 33: api.stt.v1.CustomerNeed
	(*CostEstimate)(nil),             // 34: api.stt.v1.CostEstimate
	(*RecommendedAction)(nil),        // 35: api.stt.v1.RecommendedAction
	(*ServiceQualityAnalysis)(nil),   // 36: api.stt.v1.ServiceQualityAnalysis
	(*CustomerHistory)(nil),          // 37: api.stt.v1.CustomerHistory
	(*PaymentHistory)(nil),           // 38: api.stt.v1.PaymentHistory
	(*ServicePreferences)(nil),       // 39: api.stt.v1.ServicePreferences
	(*ServiceRating)(nil),            // 40: api.stt.v1.ServiceRating
	(*EstimatedRepairTime)(nil),      // 41: api.stt.v1.EstimatedRepairTime
	(*RequiredPart)(nil),             // 42: api.stt.v1.RequiredPart
	(*CostAnalysis)(nil),             // 43: api.stt.v1.CostAnalysis
	(*CostBreakdown)(nil),            // 44: api.stt.v1.CostBreakdown
	(*TechnicianRecommendation)(nil), // 45: api.stt.v1.TechnicianRecommendation
	(*PerformanceMetrics)(nil),       // 46: api.stt.v1.PerformanceMetrics
	(*ResourceUsage)(nil),            // 47: api.stt.v1.ResourceUsage
	(*ModelParameters)(nil),          // 48: api.stt.v1.ModelParameters
	(*HVACDictionary)(nil),           // 49: api.stt.v1.HVACDictionary
	(*QualitySettings)(nil),          // 50: api.stt.v1.QualitySettings
	(*ModelConfiguration)(nil),       // 51: api.stt.v1.ModelConfiguration
}
var file_stt_v1_stt_proto_depIdxs = []int32{
	0,  // 0: api.stt.v1.TranscribeRequest.mode:type_name -> api.stt.v1.TranscriptionMode
	17, // 1: api.stt.v1.TranscribeRequest.hvac_context:type_name -> api.stt.v1.HVACContext
	18, // 2: api.stt.v1.TranscribeRequest.options:type_name -> api.stt.v1.TranscriptionOptions
	19, // 3: api.stt.v1.TranscribeResponse.segments:type_name -> api.stt.v1.TranscriptSegment
	20, // 4: api.stt.v1.TranscribeResponse.sentiment:type_name -> api.stt.v1.SentimentAnalysis
	22, // 5: api.stt.v1.TranscribeResponse.metadata:type_name -> api.stt.v1.ProcessingMetadata
	25, // 6: api.stt.v1.TranscribeResponse.technical_issues:type_name -> api.stt.v1.TechnicalIssue
	26, // 7: api.stt.v1.StreamTranscribeRequest.config:type_name -> api.stt.v1.StreamConfig
	27, // 8: api.stt.v1.PhoneCallRequest.call_metadata:type_name -> api.stt.v1.CallMetadata
	28, // 9: api.stt.v1.PhoneCallRequest.customer_context:type_name -> api.stt.v1.CustomerContext
	30, // 10: api.stt.v1.PhoneCallRequest.analysis_options:type_name -> api.stt.v1.CallAnalysisOptions
	31, // 11: api.stt.v1.PhoneCallResponse.speaker_segments:type_name -> api.stt.v1.SpeakerSegment
	32, // 12: api.stt.v1.PhoneCallResponse.call_summary:type_name -> api.stt.v1.CallSummary
	33, // 13: api.stt.v1.PhoneCallResponse.customer_needs:type_name -> api.stt.v1.CustomerNeed
	35, // 14: api.stt.v1.PhoneCallResponse.recommended_actions:type_name -> api.stt.v1.RecommendedAction
	36, // 15: api.stt.v1.PhoneCallResponse.service_quality:type_name -> api.stt.v1.ServiceQualityAnalysis
	27, // 16: api.stt.v1.HVACCallAnalysisRequest.call_metadata:type_name -> api.stt.v1.CallMetadata
	37, // 17: api.stt.v1.HVACCallAnalysisRequest.customer_history:type_name -> api.stt.v1.CustomerHistory
	3,  // 18: api.stt.v1.HVACCallAnalysisRequest.analysis_types:type_name -> api.stt.v1.AnalysisType
	25, // 19: api.stt.v1.HVACCallAnalysisResponse.technical_issues:type_name -> api.stt.v1.TechnicalIssue
	2,  // 20: api.stt.v1.HVACCallAnalysisResponse.required_service:type_name -> api.stt.v1.ServiceType
	1,  // 21: api.stt.v1.HVACCallAnalysisResponse.priority:type_name -> api.stt.v1.Priority
	41, // 22: api.stt.v1.HVACCallAnalysisResponse.estimated_time:type_name -> api.stt.v1.EstimatedRepairTime
	42, // 23: api.stt.v1.HVACCallAnalysisResponse.required_parts:type_name -> api.stt.v1.RequiredPart
	43, // 24: api.stt.v1.HVACCallAnalysisResponse.cost_analysis:type_name -> api.stt.v1.CostAnalysis
	45, // 25: api.stt.v1.HVACCallAnalysisResponse.technician_recommendations:type_name -> api.stt.v1.TechnicianRecommendation
	4,  // 26: api.stt.v1.ModelStatusResponse.status:type_name -> api.stt.v1.ModelStatus
	46, // 27: api.stt.v1.ModelStatusResponse.performance:type_name -> api.stt.v1.PerformanceMetrics
	47, // 28: api.stt.v1.ModelStatusResponse.resource_usage:type_name -> api.stt.v1.ResourceUsage
	48, // 29: api.stt.v1.ModelConfigRequest.parameters:type_name -> api.stt.v1.ModelParameters
	49, // 30: api.stt.v1.ModelConfigRequest.hvac_dictionary:type_name -> api.stt.v1.HVACDictionary
	50, // 31: api.stt.v1.ModelConfigRequest.quality_settings:type_name -> api.stt.v1.QualitySettings
	51, // 32: api.stt.v1.ModelConfigResponse.current_config:type_name -> api.stt.v1.ModelConfiguration
	21, // 33: api.stt.v1.SentimentAnalysis.emotions:type_name -> api.stt.v1.EmotionScore
	23, // 34: api.stt.v1.ProcessingMetadata.audio_quality:type_name -> api.stt.v1.AudioQuality
	24, // 35: api.stt.v1.ProcessingMetadata.stats:type_name -> api.stt.v1.ProcessingStats
	17, // 36: api.stt.v1.StreamConfig.hvac_context:type_name -> api.stt.v1.HVACContext
	18, // 37: api.stt.v1.StreamConfig.options:type_name -> api.stt.v1.TranscriptionOptions
	29, // 38: api.stt.v1.CustomerContext.preferences:type_name -> api.stt.v1.CustomerPreferences
	20, // 39: api.stt.v1.SpeakerSegment.sentiment:type_name -> api.stt.v1.SentimentAnalysis
	1,  // 40: api.stt.v1.CustomerNeed.priority:type_name -> api.stt.v1.Priority
	34, // 41: api.stt.v1.CustomerNeed.cost_estimate:type_name -> api.stt.v1.CostEstimate
	1,  // 42: api.stt.v1.RecommendedAction.priority:type_name -> api.stt.v1.Priority
	38, // 43: api.stt.v1.CustomerHistory.payment_history:type_name -> api.stt.v1.PaymentHistory
	39, // 44: api.stt.v1.CustomerHistory.service_preferences:type_name -> api.stt.v1.ServicePreferences
	40, // 45: api.stt.v1.CustomerHistory.previous_ratings:type_name -> api.stt.v1.ServiceRating
	44, // 46: api.stt.v1.CostAnalysis.cost_breakdown:type_name -> api.stt.v1.CostBreakdown
	1,  // 47: api.stt.v1.TechnicianRecommendation.priority:type_name -> api.stt.v1.Priority
	48, // 48: api.stt.v1.ModelConfiguration.parameters:type_name -> api.stt.v1.ModelParameters
	49, // 49: api.stt.v1.ModelConfiguration.dictionary:type_name -> api.stt.v1.HVACDictionary
	50, // 50: api.stt.v1.ModelConfiguration.quality:type_name -> api.stt.v1.QualitySettings
	5,  // 51: api.stt.v1.STTService.TranscribeAudio:input_type -> api.stt.v1.TranscribeRequest
	7,  // 52: api.stt.v1.STTService.TranscribeStream:input_type -> api.stt.v1.StreamTranscribeRequest
	9,  // 53: api.stt.v1.STTService.TranscribePhoneCall:input_type -> api.stt.v1.PhoneCallRequest
	11, // 54: api.stt.v1.STTService.AnalyzeHVACCall:input_type -> api.stt.v1.HVACCallAnalysisRequest
	13, // 55: api.stt.v1.STTService.GetModelStatus:input_type -> api.stt.v1.ModelStatusRequest
	15, // 56: api.stt.v1.STTService.ConfigureModel:input_type -> api.stt.v1.ModelConfigRequest
	6,  // 57: api.stt.v1.STTService.TranscribeAudio:output_type -> api.stt.v1.TranscribeResponse
	8,  // 58: api.stt.v1.STTService.TranscribeStream:output_type -> api.stt.v1.StreamTranscribeResponse
	10, // 59: api.stt.v1.STTService.TranscribePhoneCall:output_type -> api.stt.v1.PhoneCallResponse
	12, // 60: api.stt.v1.STTService.AnalyzeHVACCall:output_type -> api.stt.v1.HVACCallAnalysisResponse
	14, // 61: api.stt.v1.STTService.GetModelStatus:output_type -> api.stt.v1.ModelStatusResponse
	16, // 62: api.stt.v1.STTService.ConfigureModel:output_type -> api.stt.v1.ModelConfigResponse
	57, // [57:63] is the sub-list for method output_type
	51, // [51:57] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_stt_v1_stt_proto_init() }
func file_stt_v1_stt_proto_init() {
	if File_stt_v1_stt_proto != nil {
		return
	}
	file_stt_v1_stt_proto_msgTypes[2].OneofWrappers = []any{
		(*StreamTranscribeRequest_Config)(nil),
		(*StreamTranscribeRequest_AudioChunk)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_stt_v1_stt_proto_rawDesc), len(file_stt_v1_stt_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_stt_v1_stt_proto_goTypes,
		DependencyIndexes: file_stt_v1_stt_proto_depIdxs,
		EnumInfos:         file_stt_v1_stt_proto_enumTypes,
		MessageInfos:      file_stt_v1_stt_proto_msgTypes,
	}.Build()
	File_stt_v1_stt_proto = out.File
	file_stt_v1_stt_proto_goTypes = nil
	file_stt_v1_stt_proto_depIdxs = nil
}
