// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: hvac/v1/hvac.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationHVACServiceCreateCustomer = "/api.hvac.v1.HVACService/CreateCustomer"
const OperationHVACServiceCreateJob = "/api.hvac.v1.HVACService/CreateJob"
const OperationHVACServiceCreateLead = "/api.hvac.v1.HVACService/CreateLead"
const OperationHVACServiceDeleteLead = "/api.hvac.v1.HVACService/DeleteLead"
const OperationHVACServiceDetectDuplicateLeads = "/api.hvac.v1.HVACService/DetectDuplicateLeads"
const OperationHVACServiceExportLeads = "/api.hvac.v1.HVACService/ExportLeads"
const OperationHVACServiceGetCampaignROI = "/api.hvac.v1.HVACService/GetCampaignROI"
const OperationHVACServiceGetCustomer = "/api.hvac.v1.HVACService/GetCustomer"
const OperationHVACServiceGetJob = "/api.hvac.v1.HVACService/GetJob"
const OperationHVACServiceGetLead = "/api.hvac.v1.HVACService/GetLead"
const OperationHVACServiceImportLeads = "/api.hvac.v1.HVACService/ImportLeads"
const OperationHVACServiceListCustomers = "/api.hvac.v1.HVACService/ListCustomers"
const OperationHVACServiceListJobs = "/api.hvac.v1.HVACService/ListJobs"
const OperationHVACServiceListLeads = "/api.hvac.v1.HVACService/ListLeads"
const OperationHVACServiceMergeLeads = "/api.hvac.v1.HVACService/MergeLeads"
const OperationHVACServiceTrackCampaign = "/api.hvac.v1.HVACService/TrackCampaign"
const OperationHVACServiceUpdateLead = "/api.hvac.v1.HVACService/UpdateLead"

type HVACServiceHTTPServer interface {
	// CreateCustomer Customer Management
	CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
	// CreateJob Job Management
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	// CreateLead Lead Management
	CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error)
	DeleteLead(context.Context, *DeleteLeadRequest) (*DeleteLeadResponse, error)
	// DetectDuplicateLeads Advanced Lead Deduplication
	DetectDuplicateLeads(context.Context, *DetectDuplicateLeadsRequest) (*DetectDuplicateLeadsResponse, error)
	ExportLeads(context.Context, *ExportLeadsRequest) (*ExportLeadsResponse, error)
	GetCampaignROI(context.Context, *GetCampaignROIRequest) (*GetCampaignROIResponse, error)
	GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
	GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error)
	GetLead(context.Context, *GetLeadRequest) (*GetLeadResponse, error)
	// ImportLeads Lead Import/Export
	ImportLeads(context.Context, *ImportLeadsRequest) (*ImportLeadsResponse, error)
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
	ListLeads(context.Context, *ListLeadsRequest) (*ListLeadsResponse, error)
	MergeLeads(context.Context, *MergeLeadsRequest) (*MergeLeadsResponse, error)
	// TrackCampaign Campaign Attribution
	TrackCampaign(context.Context, *TrackCampaignRequest) (*TrackCampaignResponse, error)
	UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error)
}

func RegisterHVACServiceHTTPServer(s *http.Server, srv HVACServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/customers", _HVACService_CreateCustomer0_HTTP_Handler(srv))
	r.GET("/api/v1/customers/{id}", _HVACService_GetCustomer0_HTTP_Handler(srv))
	r.GET("/api/v1/customers", _HVACService_ListCustomers0_HTTP_Handler(srv))
	r.POST("/api/v1/jobs", _HVACService_CreateJob0_HTTP_Handler(srv))
	r.GET("/api/v1/jobs/{id}", _HVACService_GetJob0_HTTP_Handler(srv))
	r.GET("/api/v1/jobs", _HVACService_ListJobs0_HTTP_Handler(srv))
	r.POST("/api/v1/leads", _HVACService_CreateLead0_HTTP_Handler(srv))
	r.GET("/api/v1/leads/{id}", _HVACService_GetLead0_HTTP_Handler(srv))
	r.GET("/api/v1/leads", _HVACService_ListLeads0_HTTP_Handler(srv))
	r.PUT("/api/v1/leads/{id}", _HVACService_UpdateLead0_HTTP_Handler(srv))
	r.DELETE("/api/v1/leads/{id}", _HVACService_DeleteLead0_HTTP_Handler(srv))
	r.POST("/api/v1/leads/import", _HVACService_ImportLeads0_HTTP_Handler(srv))
	r.GET("/api/v1/leads/export", _HVACService_ExportLeads0_HTTP_Handler(srv))
	r.POST("/api/v1/leads/deduplicate/detect", _HVACService_DetectDuplicateLeads0_HTTP_Handler(srv))
	r.POST("/api/v1/leads/deduplicate/merge", _HVACService_MergeLeads0_HTTP_Handler(srv))
	r.POST("/api/v1/campaigns/track", _HVACService_TrackCampaign0_HTTP_Handler(srv))
	r.GET("/api/v1/campaigns/roi", _HVACService_GetCampaignROI0_HTTP_Handler(srv))
}

func _HVACService_CreateCustomer0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCustomerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceCreateCustomer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCustomer(ctx, req.(*CreateCustomerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCustomerResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetCustomer0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCustomerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetCustomer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCustomer(ctx, req.(*GetCustomerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCustomerResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ListCustomers0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCustomersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceListCustomers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCustomers(ctx, req.(*ListCustomersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCustomersResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_CreateJob0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceCreateJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateJob(ctx, req.(*CreateJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateJobResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetJob0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJob(ctx, req.(*GetJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetJobResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ListJobs0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceListJobs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJobs(ctx, req.(*ListJobsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_CreateLead0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateLeadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceCreateLead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateLead(ctx, req.(*CreateLeadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateLeadResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetLead0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLeadRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetLead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLead(ctx, req.(*GetLeadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLeadResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ListLeads0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLeadsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceListLeads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLeads(ctx, req.(*ListLeadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLeadsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_UpdateLead0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateLeadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceUpdateLead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLead(ctx, req.(*UpdateLeadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateLeadResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_DeleteLead0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteLeadRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceDeleteLead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteLead(ctx, req.(*DeleteLeadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteLeadResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ImportLeads0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ImportLeadsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceImportLeads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ImportLeads(ctx, req.(*ImportLeadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ImportLeadsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ExportLeads0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportLeadsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceExportLeads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportLeads(ctx, req.(*ExportLeadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExportLeadsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_DetectDuplicateLeads0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DetectDuplicateLeadsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceDetectDuplicateLeads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DetectDuplicateLeads(ctx, req.(*DetectDuplicateLeadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DetectDuplicateLeadsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_MergeLeads0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MergeLeadsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceMergeLeads)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MergeLeads(ctx, req.(*MergeLeadsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MergeLeadsResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_TrackCampaign0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TrackCampaignRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceTrackCampaign)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TrackCampaign(ctx, req.(*TrackCampaignRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TrackCampaignResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetCampaignROI0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCampaignROIRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetCampaignROI)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCampaignROI(ctx, req.(*GetCampaignROIRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCampaignROIResponse)
		return ctx.Result(200, reply)
	}
}

type HVACServiceHTTPClient interface {
	CreateCustomer(ctx context.Context, req *CreateCustomerRequest, opts ...http.CallOption) (rsp *CreateCustomerResponse, err error)
	CreateJob(ctx context.Context, req *CreateJobRequest, opts ...http.CallOption) (rsp *CreateJobResponse, err error)
	CreateLead(ctx context.Context, req *CreateLeadRequest, opts ...http.CallOption) (rsp *CreateLeadResponse, err error)
	DeleteLead(ctx context.Context, req *DeleteLeadRequest, opts ...http.CallOption) (rsp *DeleteLeadResponse, err error)
	DetectDuplicateLeads(ctx context.Context, req *DetectDuplicateLeadsRequest, opts ...http.CallOption) (rsp *DetectDuplicateLeadsResponse, err error)
	ExportLeads(ctx context.Context, req *ExportLeadsRequest, opts ...http.CallOption) (rsp *ExportLeadsResponse, err error)
	GetCampaignROI(ctx context.Context, req *GetCampaignROIRequest, opts ...http.CallOption) (rsp *GetCampaignROIResponse, err error)
	GetCustomer(ctx context.Context, req *GetCustomerRequest, opts ...http.CallOption) (rsp *GetCustomerResponse, err error)
	GetJob(ctx context.Context, req *GetJobRequest, opts ...http.CallOption) (rsp *GetJobResponse, err error)
	GetLead(ctx context.Context, req *GetLeadRequest, opts ...http.CallOption) (rsp *GetLeadResponse, err error)
	ImportLeads(ctx context.Context, req *ImportLeadsRequest, opts ...http.CallOption) (rsp *ImportLeadsResponse, err error)
	ListCustomers(ctx context.Context, req *ListCustomersRequest, opts ...http.CallOption) (rsp *ListCustomersResponse, err error)
	ListJobs(ctx context.Context, req *ListJobsRequest, opts ...http.CallOption) (rsp *ListJobsResponse, err error)
	ListLeads(ctx context.Context, req *ListLeadsRequest, opts ...http.CallOption) (rsp *ListLeadsResponse, err error)
	MergeLeads(ctx context.Context, req *MergeLeadsRequest, opts ...http.CallOption) (rsp *MergeLeadsResponse, err error)
	TrackCampaign(ctx context.Context, req *TrackCampaignRequest, opts ...http.CallOption) (rsp *TrackCampaignResponse, err error)
	UpdateLead(ctx context.Context, req *UpdateLeadRequest, opts ...http.CallOption) (rsp *UpdateLeadResponse, err error)
}

type HVACServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewHVACServiceHTTPClient(client *http.Client) HVACServiceHTTPClient {
	return &HVACServiceHTTPClientImpl{client}
}

func (c *HVACServiceHTTPClientImpl) CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...http.CallOption) (*CreateCustomerResponse, error) {
	var out CreateCustomerResponse
	pattern := "/api/v1/customers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceCreateCustomer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...http.CallOption) (*CreateJobResponse, error) {
	var out CreateJobResponse
	pattern := "/api/v1/jobs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceCreateJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...http.CallOption) (*CreateLeadResponse, error) {
	var out CreateLeadResponse
	pattern := "/api/v1/leads"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceCreateLead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) DeleteLead(ctx context.Context, in *DeleteLeadRequest, opts ...http.CallOption) (*DeleteLeadResponse, error) {
	var out DeleteLeadResponse
	pattern := "/api/v1/leads/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceDeleteLead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) DetectDuplicateLeads(ctx context.Context, in *DetectDuplicateLeadsRequest, opts ...http.CallOption) (*DetectDuplicateLeadsResponse, error) {
	var out DetectDuplicateLeadsResponse
	pattern := "/api/v1/leads/deduplicate/detect"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceDetectDuplicateLeads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ExportLeads(ctx context.Context, in *ExportLeadsRequest, opts ...http.CallOption) (*ExportLeadsResponse, error) {
	var out ExportLeadsResponse
	pattern := "/api/v1/leads/export"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceExportLeads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetCampaignROI(ctx context.Context, in *GetCampaignROIRequest, opts ...http.CallOption) (*GetCampaignROIResponse, error) {
	var out GetCampaignROIResponse
	pattern := "/api/v1/campaigns/roi"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetCampaignROI))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...http.CallOption) (*GetCustomerResponse, error) {
	var out GetCustomerResponse
	pattern := "/api/v1/customers/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetCustomer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetJob(ctx context.Context, in *GetJobRequest, opts ...http.CallOption) (*GetJobResponse, error) {
	var out GetJobResponse
	pattern := "/api/v1/jobs/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetLead(ctx context.Context, in *GetLeadRequest, opts ...http.CallOption) (*GetLeadResponse, error) {
	var out GetLeadResponse
	pattern := "/api/v1/leads/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetLead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ImportLeads(ctx context.Context, in *ImportLeadsRequest, opts ...http.CallOption) (*ImportLeadsResponse, error) {
	var out ImportLeadsResponse
	pattern := "/api/v1/leads/import"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceImportLeads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...http.CallOption) (*ListCustomersResponse, error) {
	var out ListCustomersResponse
	pattern := "/api/v1/customers"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceListCustomers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ListJobs(ctx context.Context, in *ListJobsRequest, opts ...http.CallOption) (*ListJobsResponse, error) {
	var out ListJobsResponse
	pattern := "/api/v1/jobs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceListJobs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ListLeads(ctx context.Context, in *ListLeadsRequest, opts ...http.CallOption) (*ListLeadsResponse, error) {
	var out ListLeadsResponse
	pattern := "/api/v1/leads"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceListLeads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) MergeLeads(ctx context.Context, in *MergeLeadsRequest, opts ...http.CallOption) (*MergeLeadsResponse, error) {
	var out MergeLeadsResponse
	pattern := "/api/v1/leads/deduplicate/merge"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceMergeLeads))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) TrackCampaign(ctx context.Context, in *TrackCampaignRequest, opts ...http.CallOption) (*TrackCampaignResponse, error) {
	var out TrackCampaignResponse
	pattern := "/api/v1/campaigns/track"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceTrackCampaign))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...http.CallOption) (*UpdateLeadResponse, error) {
	var out UpdateLeadResponse
	pattern := "/api/v1/leads/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceUpdateLead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
