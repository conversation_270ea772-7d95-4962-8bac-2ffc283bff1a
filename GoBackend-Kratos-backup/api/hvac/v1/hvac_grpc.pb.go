// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: hvac/v1/hvac.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	HVACService_CreateCustomer_FullMethodName       = "/api.hvac.v1.HVACService/CreateCustomer"
	HVACService_GetCustomer_FullMethodName          = "/api.hvac.v1.HVACService/GetCustomer"
	HVACService_ListCustomers_FullMethodName        = "/api.hvac.v1.HVACService/ListCustomers"
	HVACService_CreateJob_FullMethodName            = "/api.hvac.v1.HVACService/CreateJob"
	HVACService_GetJob_FullMethodName               = "/api.hvac.v1.HVACService/GetJob"
	HVACService_ListJobs_FullMethodName             = "/api.hvac.v1.HVACService/ListJobs"
	HVACService_CreateLead_FullMethodName           = "/api.hvac.v1.HVACService/CreateLead"
	HVACService_GetLead_FullMethodName              = "/api.hvac.v1.HVACService/GetLead"
	HVACService_ListLeads_FullMethodName            = "/api.hvac.v1.HVACService/ListLeads"
	HVACService_UpdateLead_FullMethodName           = "/api.hvac.v1.HVACService/UpdateLead"
	HVACService_DeleteLead_FullMethodName           = "/api.hvac.v1.HVACService/DeleteLead"
	HVACService_ImportLeads_FullMethodName          = "/api.hvac.v1.HVACService/ImportLeads"
	HVACService_ExportLeads_FullMethodName          = "/api.hvac.v1.HVACService/ExportLeads"
	HVACService_DetectDuplicateLeads_FullMethodName = "/api.hvac.v1.HVACService/DetectDuplicateLeads"
	HVACService_MergeLeads_FullMethodName           = "/api.hvac.v1.HVACService/MergeLeads"
	HVACService_TrackCampaign_FullMethodName        = "/api.hvac.v1.HVACService/TrackCampaign"
	HVACService_GetCampaignROI_FullMethodName       = "/api.hvac.v1.HVACService/GetCampaignROI"
)

// HVACServiceClient is the client API for HVACService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// HVAC Service Definition
type HVACServiceClient interface {
	// Customer Management
	CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error)
	GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error)
	ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error)
	// Job Management
	CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error)
	GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*GetJobResponse, error)
	ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error)
	// Lead Management
	CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error)
	GetLead(ctx context.Context, in *GetLeadRequest, opts ...grpc.CallOption) (*GetLeadResponse, error)
	ListLeads(ctx context.Context, in *ListLeadsRequest, opts ...grpc.CallOption) (*ListLeadsResponse, error)
	UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error)
	DeleteLead(ctx context.Context, in *DeleteLeadRequest, opts ...grpc.CallOption) (*DeleteLeadResponse, error)
	// Lead Import/Export
	ImportLeads(ctx context.Context, in *ImportLeadsRequest, opts ...grpc.CallOption) (*ImportLeadsResponse, error)
	ExportLeads(ctx context.Context, in *ExportLeadsRequest, opts ...grpc.CallOption) (*ExportLeadsResponse, error)
	// Advanced Lead Deduplication
	DetectDuplicateLeads(ctx context.Context, in *DetectDuplicateLeadsRequest, opts ...grpc.CallOption) (*DetectDuplicateLeadsResponse, error)
	MergeLeads(ctx context.Context, in *MergeLeadsRequest, opts ...grpc.CallOption) (*MergeLeadsResponse, error)
	// Campaign Attribution
	TrackCampaign(ctx context.Context, in *TrackCampaignRequest, opts ...grpc.CallOption) (*TrackCampaignResponse, error)
	GetCampaignROI(ctx context.Context, in *GetCampaignROIRequest, opts ...grpc.CallOption) (*GetCampaignROIResponse, error)
}

type hVACServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHVACServiceClient(cc grpc.ClientConnInterface) HVACServiceClient {
	return &hVACServiceClient{cc}
}

func (c *hVACServiceClient) CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerResponse)
	err := c.cc.Invoke(ctx, HVACService_CreateCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomerResponse)
	err := c.cc.Invoke(ctx, HVACService_GetCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomersResponse)
	err := c.cc.Invoke(ctx, HVACService_ListCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateJobResponse)
	err := c.cc.Invoke(ctx, HVACService_CreateJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*GetJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobResponse)
	err := c.cc.Invoke(ctx, HVACService_GetJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListJobsResponse)
	err := c.cc.Invoke(ctx, HVACService_ListJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateLeadResponse)
	err := c.cc.Invoke(ctx, HVACService_CreateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) GetLead(ctx context.Context, in *GetLeadRequest, opts ...grpc.CallOption) (*GetLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeadResponse)
	err := c.cc.Invoke(ctx, HVACService_GetLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) ListLeads(ctx context.Context, in *ListLeadsRequest, opts ...grpc.CallOption) (*ListLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListLeadsResponse)
	err := c.cc.Invoke(ctx, HVACService_ListLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLeadResponse)
	err := c.cc.Invoke(ctx, HVACService_UpdateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) DeleteLead(ctx context.Context, in *DeleteLeadRequest, opts ...grpc.CallOption) (*DeleteLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteLeadResponse)
	err := c.cc.Invoke(ctx, HVACService_DeleteLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) ImportLeads(ctx context.Context, in *ImportLeadsRequest, opts ...grpc.CallOption) (*ImportLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportLeadsResponse)
	err := c.cc.Invoke(ctx, HVACService_ImportLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) ExportLeads(ctx context.Context, in *ExportLeadsRequest, opts ...grpc.CallOption) (*ExportLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExportLeadsResponse)
	err := c.cc.Invoke(ctx, HVACService_ExportLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) DetectDuplicateLeads(ctx context.Context, in *DetectDuplicateLeadsRequest, opts ...grpc.CallOption) (*DetectDuplicateLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectDuplicateLeadsResponse)
	err := c.cc.Invoke(ctx, HVACService_DetectDuplicateLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) MergeLeads(ctx context.Context, in *MergeLeadsRequest, opts ...grpc.CallOption) (*MergeLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MergeLeadsResponse)
	err := c.cc.Invoke(ctx, HVACService_MergeLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) TrackCampaign(ctx context.Context, in *TrackCampaignRequest, opts ...grpc.CallOption) (*TrackCampaignResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TrackCampaignResponse)
	err := c.cc.Invoke(ctx, HVACService_TrackCampaign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hVACServiceClient) GetCampaignROI(ctx context.Context, in *GetCampaignROIRequest, opts ...grpc.CallOption) (*GetCampaignROIResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCampaignROIResponse)
	err := c.cc.Invoke(ctx, HVACService_GetCampaignROI_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HVACServiceServer is the server API for HVACService service.
// All implementations must embed UnimplementedHVACServiceServer
// for forward compatibility.
//
// HVAC Service Definition
type HVACServiceServer interface {
	// Customer Management
	CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
	GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	// Job Management
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error)
	ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
	// Lead Management
	CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error)
	GetLead(context.Context, *GetLeadRequest) (*GetLeadResponse, error)
	ListLeads(context.Context, *ListLeadsRequest) (*ListLeadsResponse, error)
	UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error)
	DeleteLead(context.Context, *DeleteLeadRequest) (*DeleteLeadResponse, error)
	// Lead Import/Export
	ImportLeads(context.Context, *ImportLeadsRequest) (*ImportLeadsResponse, error)
	ExportLeads(context.Context, *ExportLeadsRequest) (*ExportLeadsResponse, error)
	// Advanced Lead Deduplication
	DetectDuplicateLeads(context.Context, *DetectDuplicateLeadsRequest) (*DetectDuplicateLeadsResponse, error)
	MergeLeads(context.Context, *MergeLeadsRequest) (*MergeLeadsResponse, error)
	// Campaign Attribution
	TrackCampaign(context.Context, *TrackCampaignRequest) (*TrackCampaignResponse, error)
	GetCampaignROI(context.Context, *GetCampaignROIRequest) (*GetCampaignROIResponse, error)
	mustEmbedUnimplementedHVACServiceServer()
}

// UnimplementedHVACServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedHVACServiceServer struct{}

func (UnimplementedHVACServiceServer) CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomer not implemented")
}
func (UnimplementedHVACServiceServer) GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomer not implemented")
}
func (UnimplementedHVACServiceServer) ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomers not implemented")
}
func (UnimplementedHVACServiceServer) CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (UnimplementedHVACServiceServer) GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJob not implemented")
}
func (UnimplementedHVACServiceServer) ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobs not implemented")
}
func (UnimplementedHVACServiceServer) CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLead not implemented")
}
func (UnimplementedHVACServiceServer) GetLead(context.Context, *GetLeadRequest) (*GetLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLead not implemented")
}
func (UnimplementedHVACServiceServer) ListLeads(context.Context, *ListLeadsRequest) (*ListLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeads not implemented")
}
func (UnimplementedHVACServiceServer) UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLead not implemented")
}
func (UnimplementedHVACServiceServer) DeleteLead(context.Context, *DeleteLeadRequest) (*DeleteLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLead not implemented")
}
func (UnimplementedHVACServiceServer) ImportLeads(context.Context, *ImportLeadsRequest) (*ImportLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportLeads not implemented")
}
func (UnimplementedHVACServiceServer) ExportLeads(context.Context, *ExportLeadsRequest) (*ExportLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportLeads not implemented")
}
func (UnimplementedHVACServiceServer) DetectDuplicateLeads(context.Context, *DetectDuplicateLeadsRequest) (*DetectDuplicateLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectDuplicateLeads not implemented")
}
func (UnimplementedHVACServiceServer) MergeLeads(context.Context, *MergeLeadsRequest) (*MergeLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeLeads not implemented")
}
func (UnimplementedHVACServiceServer) TrackCampaign(context.Context, *TrackCampaignRequest) (*TrackCampaignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackCampaign not implemented")
}
func (UnimplementedHVACServiceServer) GetCampaignROI(context.Context, *GetCampaignROIRequest) (*GetCampaignROIResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCampaignROI not implemented")
}
func (UnimplementedHVACServiceServer) mustEmbedUnimplementedHVACServiceServer() {}
func (UnimplementedHVACServiceServer) testEmbeddedByValue()                     {}

// UnsafeHVACServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HVACServiceServer will
// result in compilation errors.
type UnsafeHVACServiceServer interface {
	mustEmbedUnimplementedHVACServiceServer()
}

func RegisterHVACServiceServer(s grpc.ServiceRegistrar, srv HVACServiceServer) {
	// If the following call pancis, it indicates UnimplementedHVACServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&HVACService_ServiceDesc, srv)
}

func _HVACService_CreateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).CreateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_CreateCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).CreateCustomer(ctx, req.(*CreateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_GetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).GetCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_GetCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).GetCustomer(ctx, req.(*GetCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_ListCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).ListCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_ListCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).ListCustomers(ctx, req.(*ListCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).CreateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_CreateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).CreateJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_GetJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).GetJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_GetJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).GetJob(ctx, req.(*GetJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_ListJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).ListJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_ListJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).ListJobs(ctx, req.(*ListJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_CreateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).CreateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_CreateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).CreateLead(ctx, req.(*CreateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_GetLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).GetLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_GetLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).GetLead(ctx, req.(*GetLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_ListLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).ListLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_ListLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).ListLeads(ctx, req.(*ListLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_UpdateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).UpdateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_UpdateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).UpdateLead(ctx, req.(*UpdateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_DeleteLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).DeleteLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_DeleteLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).DeleteLead(ctx, req.(*DeleteLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_ImportLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).ImportLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_ImportLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).ImportLeads(ctx, req.(*ImportLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_ExportLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).ExportLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_ExportLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).ExportLeads(ctx, req.(*ExportLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_DetectDuplicateLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectDuplicateLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).DetectDuplicateLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_DetectDuplicateLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).DetectDuplicateLeads(ctx, req.(*DetectDuplicateLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_MergeLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).MergeLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_MergeLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).MergeLeads(ctx, req.(*MergeLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_TrackCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrackCampaignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).TrackCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_TrackCampaign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).TrackCampaign(ctx, req.(*TrackCampaignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HVACService_GetCampaignROI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCampaignROIRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HVACServiceServer).GetCampaignROI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HVACService_GetCampaignROI_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HVACServiceServer).GetCampaignROI(ctx, req.(*GetCampaignROIRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HVACService_ServiceDesc is the grpc.ServiceDesc for HVACService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HVACService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.hvac.v1.HVACService",
	HandlerType: (*HVACServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomer",
			Handler:    _HVACService_CreateCustomer_Handler,
		},
		{
			MethodName: "GetCustomer",
			Handler:    _HVACService_GetCustomer_Handler,
		},
		{
			MethodName: "ListCustomers",
			Handler:    _HVACService_ListCustomers_Handler,
		},
		{
			MethodName: "CreateJob",
			Handler:    _HVACService_CreateJob_Handler,
		},
		{
			MethodName: "GetJob",
			Handler:    _HVACService_GetJob_Handler,
		},
		{
			MethodName: "ListJobs",
			Handler:    _HVACService_ListJobs_Handler,
		},
		{
			MethodName: "CreateLead",
			Handler:    _HVACService_CreateLead_Handler,
		},
		{
			MethodName: "GetLead",
			Handler:    _HVACService_GetLead_Handler,
		},
		{
			MethodName: "ListLeads",
			Handler:    _HVACService_ListLeads_Handler,
		},
		{
			MethodName: "UpdateLead",
			Handler:    _HVACService_UpdateLead_Handler,
		},
		{
			MethodName: "DeleteLead",
			Handler:    _HVACService_DeleteLead_Handler,
		},
		{
			MethodName: "ImportLeads",
			Handler:    _HVACService_ImportLeads_Handler,
		},
		{
			MethodName: "ExportLeads",
			Handler:    _HVACService_ExportLeads_Handler,
		},
		{
			MethodName: "DetectDuplicateLeads",
			Handler:    _HVACService_DetectDuplicateLeads_Handler,
		},
		{
			MethodName: "MergeLeads",
			Handler:    _HVACService_MergeLeads_Handler,
		},
		{
			MethodName: "TrackCampaign",
			Handler:    _HVACService_TrackCampaign_Handler,
		},
		{
			MethodName: "GetCampaignROI",
			Handler:    _HVACService_GetCampaignROI_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hvac/v1/hvac.proto",
}
