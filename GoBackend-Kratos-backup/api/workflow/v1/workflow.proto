syntax = "proto3";

package api.workflow.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/duration.proto";

option go_package = "gobackend-hvac-kratos/api/workflow/v1;v1";

// ⚡ Workflow Service - Advanced Process Automation API
service WorkflowService {
  // Create workflow rule
  rpc CreateWorkflowRule(CreateWorkflowRuleRequest) returns (CreateWorkflowRuleResponse) {
    option (google.api.http) = {
      post: "/api/v1/workflow/rules"
      body: "*"
    };
  }

  // Get workflow rules
  rpc GetWorkflowRules(GetWorkflowRulesRequest) returns (GetWorkflowRulesResponse) {
    option (google.api.http) = {
      get: "/api/v1/workflow/rules"
    };
  }

  // Execute workflows for trigger
  rpc ExecuteWorkflowsForTrigger(ExecuteWorkflowsForTriggerRequest) returns (ExecuteWorkflowsForTriggerResponse) {
    option (google.api.http) = {
      post: "/api/v1/workflow/execute"
      body: "*"
    };
  }

  // Get workflow executions
  rpc GetWorkflowExecutions(GetWorkflowExecutionsRequest) returns (GetWorkflowExecutionsResponse) {
    option (google.api.http) = {
      get: "/api/v1/workflow/executions"
    };
  }

  // Get workflow templates
  rpc GetWorkflowTemplates(GetWorkflowTemplatesRequest) returns (GetWorkflowTemplatesResponse) {
    option (google.api.http) = {
      get: "/api/v1/workflow/templates"
    };
  }

  // Create workflow from template
  rpc CreateWorkflowFromTemplate(CreateWorkflowFromTemplateRequest) returns (CreateWorkflowFromTemplateResponse) {
    option (google.api.http) = {
      post: "/api/v1/workflow/templates/{template_id}/create"
      body: "*"
    };
  }

  // Update workflow rule
  rpc UpdateWorkflowRule(UpdateWorkflowRuleRequest) returns (UpdateWorkflowRuleResponse) {
    option (google.api.http) = {
      put: "/api/v1/workflow/rules/{rule_id}"
      body: "*"
    };
  }

  // Delete workflow rule
  rpc DeleteWorkflowRule(DeleteWorkflowRuleRequest) returns (DeleteWorkflowRuleResponse) {
    option (google.api.http) = {
      delete: "/api/v1/workflow/rules/{rule_id}"
    };
  }

  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse) {
    option (google.api.http) = {
      get: "/api/v1/workflow/health"
    };
  }
}

// ============================================================================
// ⚡ REQUEST/RESPONSE MESSAGES
// ============================================================================

// Create Workflow Rule
message CreateWorkflowRuleRequest {
  string rule_name = 1;
  string description = 2;
  string trigger_type = 3;
  google.protobuf.Struct trigger_conditions = 4;
  google.protobuf.Struct actions = 5;
  int32 priority = 6;
  bool is_active = 7;
  string created_by = 8;
}

message CreateWorkflowRuleResponse {
  bool success = 1;
  string message = 2;
  WorkflowRule rule = 3;
}

// Get Workflow Rules
message GetWorkflowRulesRequest {
  string trigger_type = 1; // Optional filter
  optional bool is_active = 2; // Optional filter
  int32 page = 3;
  int32 page_size = 4;
}

message GetWorkflowRulesResponse {
  repeated WorkflowRule rules = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Execute Workflows
message ExecuteWorkflowsForTriggerRequest {
  string trigger_type = 1;
  uint32 entity_id = 2;
  google.protobuf.Struct entity_data = 3;
}

message ExecuteWorkflowsForTriggerResponse {
  bool success = 1;
  string message = 2;
  repeated WorkflowResult results = 3;
}

// Get Workflow Executions
message GetWorkflowExecutionsRequest {
  optional uint32 workflow_rule_id = 1;
  string status = 2; // Optional filter
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
  int32 page = 5;
  int32 page_size = 6;
}

message GetWorkflowExecutionsResponse {
  repeated WorkflowExecution executions = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Get Workflow Templates
message GetWorkflowTemplatesRequest {
  string category = 1; // Optional filter
}

message GetWorkflowTemplatesResponse {
  repeated WorkflowTemplate templates = 1;
}

// Create from Template
message CreateWorkflowFromTemplateRequest {
  uint32 template_id = 1;
  string rule_name = 2;
  google.protobuf.Struct customizations = 3;
}

message CreateWorkflowFromTemplateResponse {
  bool success = 1;
  string message = 2;
  WorkflowRule rule = 3;
}

// Update Workflow Rule
message UpdateWorkflowRuleRequest {
  uint32 rule_id = 1;
  string rule_name = 2;
  string description = 3;
  google.protobuf.Struct trigger_conditions = 4;
  google.protobuf.Struct actions = 5;
  int32 priority = 6;
  bool is_active = 7;
}

message UpdateWorkflowRuleResponse {
  bool success = 1;
  string message = 2;
  WorkflowRule rule = 3;
}

// Delete Workflow Rule
message DeleteWorkflowRuleRequest {
  uint32 rule_id = 1;
}

message DeleteWorkflowRuleResponse {
  bool success = 1;
  string message = 2;
}

// ============================================================================
// ⚡ DATA MODELS
// ============================================================================

message WorkflowRule {
  uint32 id = 1;
  string rule_name = 2;
  string description = 3;
  string trigger_type = 4;
  google.protobuf.Struct trigger_conditions = 5;
  google.protobuf.Struct actions = 6;
  int32 priority = 7;
  bool is_active = 8;
  int32 execution_count = 9;
  int32 success_count = 10;
  google.protobuf.Timestamp last_executed = 11;
  string created_by = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
}

message WorkflowExecution {
  uint32 id = 1;
  uint32 workflow_rule_id = 2;
  WorkflowRule workflow_rule = 3;
  uint32 trigger_entity_id = 4;
  string trigger_entity_type = 5;
  string execution_status = 6;
  google.protobuf.Timestamp started_at = 7;
  google.protobuf.Timestamp completed_at = 8;
  google.protobuf.Duration execution_time = 9;
  google.protobuf.Struct actions_executed = 10;
  google.protobuf.Struct results = 11;
  string error_message = 12;
  google.protobuf.Struct metadata = 13;
}

message WorkflowTemplate {
  uint32 id = 1;
  string template_name = 2;
  string category = 3;
  string description = 4;
  google.protobuf.Struct template = 5;
  bool is_public = 6;
  int32 usage_count = 7;
  string created_by = 8;
  google.protobuf.Timestamp created_at = 9;
}

message WorkflowResult {
  bool success = 1;
  repeated WorkflowActionResult actions_executed = 2;
  string error_message = 3;
  google.protobuf.Duration execution_time = 4;
  google.protobuf.Struct metadata = 5;
}

message WorkflowActionResult {
  WorkflowAction action = 1;
  bool success = 2;
  google.protobuf.Struct result = 3;
  string error = 4;
  google.protobuf.Duration duration = 5;
}

message WorkflowAction {
  string type = 1;
  string target = 2;
  google.protobuf.Struct parameters = 3;
  google.protobuf.Duration delay = 4;
}

// Health Check
message HealthCheckRequest {}

message HealthCheckResponse {
  bool healthy = 1;
  string message = 2;
  google.protobuf.Timestamp timestamp = 3;
}