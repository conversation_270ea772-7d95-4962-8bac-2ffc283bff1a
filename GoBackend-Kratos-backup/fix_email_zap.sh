#!/bin/bash

# Fix email.go zap usage
echo "🔧 Fixing email.go zap usage..."

# Replace zap.String calls with Infof format
sed -i 's/t\.logger\.Info("Email send requested", /t.logger.Infof("Email send requested: from=%s, to=%v, subject=%s", req.From, req.To, req.Subject)/g' internal/mcp/tools/email.go
sed -i 's/zap\.String("from", req\.From),//g' internal/mcp/tools/email.go
sed -i 's/zap\.Strings("to", req\.To),//g' internal/mcp/tools/email.go
sed -i 's/zap\.String("subject", req\.Subject),//g' internal/mcp/tools/email.go
sed -i 's/)$/)/g' internal/mcp/tools/email.go

# Replace other zap calls
sed -i 's/t\.logger\.Info("Email analyzed successfully", /t.logger.Infof("Email analyzed successfully: email_id=%d, confidence=%.2f", req.EmailID, result.Confidence)/g' internal/mcp/tools/email.go
sed -i 's/zap\.Int64("email_id", req\.EmailID),//g' internal/mcp/tools/email.go
sed -i 's/zap\.Float32("confidence", result\.Confidence),//g' internal/mcp/tools/email.go

sed -i 's/t\.logger\.Info("Transcription processing requested", /t.logger.Infof("Transcription processing requested: audio_file=%s, model=%s", req.AudioFile, req.Model)/g' internal/mcp/tools/email.go
sed -i 's/zap\.String("audio_file", req\.AudioFile),//g' internal/mcp/tools/email.go
sed -i 's/zap\.String("model", req\.Model),//g' internal/mcp/tools/email.go

echo "✅ email.go zap usage fixed!"
