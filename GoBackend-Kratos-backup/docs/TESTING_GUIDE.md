# 🧪 GoBackend-Kratos Testing Guide

## Overview

This guide covers the comprehensive testing strategy for GoBackend-Kratos, with special focus on HVAC-Remix frontend compatibility.

## 📋 Test Categories

### 1. Unit Tests (`/tests/unit/`)
- **Purpose**: Test individual components in isolation
- **Coverage**: All core modules (LangGraph, Executive, AI, Email)
- **Location**: `./tests/unit/`
- **Run Command**: `go test ./tests/unit/...`

### 2. Integration Tests (`/tests/integration/`)
- **Purpose**: Test component interactions and API endpoints
- **Coverage**: Database operations, external service integrations
- **Location**: `./tests/integration/`
- **Run Command**: `go test -tags=integration ./tests/integration/...`

### 3. HVAC-Remix Compatibility Tests (`/tests/compatibility/`)
- **Purpose**: Ensure full compatibility with hvac-remix frontend
- **Coverage**: API contracts, data types, real-time communication
- **Location**: `./tests/compatibility/`
- **Run Command**: `go test -tags=compatibility ./tests/compatibility/...`

### 4. Performance Tests (`/tests/performance/`)
- **Purpose**: Validate performance requirements and benchmarks
- **Coverage**: Response times, throughput, memory usage
- **Location**: `./tests/performance/`
- **Run Command**: `go test -bench=. ./tests/performance/...`

## 🚀 Quick Start

### Prerequisites
```bash
# Install required tools
go install github.com/stretchr/testify@latest
go install github.com/golang/mock/mockgen@latest
docker --version
docker-compose --version
```

### Run All Tests
```bash
# Make script executable
chmod +x scripts/run_tests.sh

# Run complete test suite
./scripts/run_tests.sh

# Run with load testing
./scripts/run_tests.sh --load-test
```

### Run Specific Test Categories
```bash
# Unit tests only
go test -v ./tests/unit/...

# Integration tests only
go test -v -tags=integration ./tests/integration/...

# HVAC-Remix compatibility only
go test -v -tags=compatibility ./tests/compatibility/...

# Performance benchmarks only
go test -bench=. -benchmem ./tests/performance/...
```

## 🔗 HVAC-Remix Compatibility

### API Endpoint Compatibility

The compatibility tests verify that GoBackend-Kratos provides all API endpoints expected by hvac-remix:

#### Customer Management
- `GET /api/v1/customers` - List customers
- `GET /api/v1/customers/{id}` - Get customer details
- `POST /api/v1/customers` - Create customer
- `PUT /api/v1/customers/{id}` - Update customer
- `DELETE /api/v1/customers/{id}` - Delete customer

#### Job Management
- `GET /api/v1/jobs` - List jobs
- `GET /api/v1/jobs/{id}` - Get job details
- `POST /api/v1/jobs` - Create job
- `PUT /api/v1/jobs/{id}` - Update job
- `PATCH /api/v1/jobs/{id}/status` - Update job status

#### AI Services
- `POST /api/v1/ai/analyze` - Analyze content
- `POST /api/v1/ai/chat` - AI chat interaction
- `GET /api/v1/ai/models` - List available models

#### Email Processing
- `GET /api/v1/emails` - List emails
- `POST /api/v1/emails/process` - Process email
- `GET /api/v1/emails/{id}/analysis` - Get email analysis

#### Real-time Communication
- `WebSocket /ws/realtime` - Real-time updates

### Data Type Compatibility

All data structures are compatible with hvac-remix TypeScript definitions:

```typescript
// Customer type compatibility
interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  type: 'residential' | 'commercial' | 'industrial';
}

// Job type compatibility
interface Job {
  id: string;
  customerId: string;
  title: string;
  description: string;
  status: 'pending' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  type: 'installation' | 'maintenance' | 'repair' | 'inspection' | 'emergency';
  scheduledDate?: Date;
}

// AI Analysis Response compatibility
interface AIAnalysisResponse {
  analysis: string;
  confidence: number;
  category: string;
  urgency: string;
  recommendations: string[];
  estimatedCost: {
    min: number;
    max: number;
    currency: string;
  };
  requiredParts: string[];
  estimatedDuration: string;
}
```

### Real-time Communication

WebSocket compatibility ensures real-time updates work seamlessly:

```javascript
// hvac-remix WebSocket usage
const ws = new WebSocket('ws://localhost:8080/ws/realtime');

// Subscribe to job updates
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'job_updates'
}));

// Receive real-time updates
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  // Handle job status changes, new emails, etc.
};
```

## 📊 Performance Requirements

### Response Time Targets
- **API Endpoints**: < 500ms for list operations, < 200ms for single item retrieval
- **AI Analysis**: < 3 seconds for email analysis
- **Workflow Execution**: < 5 seconds for standard workflows
- **Real-time Updates**: < 100ms latency

### Throughput Targets
- **Concurrent Users**: Support 100+ concurrent users
- **API Requests**: Handle 1000+ requests per minute
- **Email Processing**: Process 500+ emails per hour
- **WebSocket Connections**: Support 200+ concurrent connections

### Memory Usage
- **Base Memory**: < 512MB at startup
- **Peak Memory**: < 2GB under load
- **Memory Leaks**: Zero tolerance for memory leaks

## 🔧 Test Environment Setup

### Local Development
```bash
# Start test dependencies
docker-compose -f docker-compose.test.yml up -d

# Wait for services to be ready
sleep 10

# Run tests
go test ./...

# Cleanup
docker-compose -f docker-compose.test.yml down
```

### CI/CD Pipeline
```yaml
# GitHub Actions example
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      
      - name: Start test environment
        run: docker-compose -f docker-compose.test.yml up -d
      
      - name: Run tests
        run: ./scripts/run_tests.sh
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.out
```

## 📈 Coverage Requirements

### Minimum Coverage Targets
- **Overall Coverage**: 80%
- **Critical Modules**: 90%
  - LangGraph Workflow Service
  - Executive AI Assistant
  - Email Processing
  - API Handlers

### Coverage Exclusions
- Generated code (protobuf, mocks)
- Test files
- Main functions
- Configuration structs

## 🐛 Debugging Tests

### Common Issues

#### Database Connection Errors
```bash
# Check if PostgreSQL is running
docker-compose -f docker-compose.test.yml ps postgres-test

# Check logs
docker-compose -f docker-compose.test.yml logs postgres-test
```

#### Redis Connection Errors
```bash
# Test Redis connection
docker exec hvac-redis-test redis-cli -a testpass ping
```

#### AI Service Errors
```bash
# Check LM Studio mock server
curl http://localhost:1235/health
```

### Verbose Testing
```bash
# Run with verbose output
go test -v -timeout=30m ./...

# Run specific test with debugging
go test -v -run TestSpecificFunction ./tests/unit/

# Run with race detection
go test -race ./...
```

## 📝 Writing New Tests

### Unit Test Template
```go
func TestNewFeature(t *testing.T) {
    // Setup
    service := setupTestService(t)
    
    t.Run("Valid_Input", func(t *testing.T) {
        // Arrange
        input := &ValidInput{...}
        
        // Act
        result, err := service.NewFeature(context.Background(), input)
        
        // Assert
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, expected, result.Field)
    })
    
    t.Run("Invalid_Input", func(t *testing.T) {
        // Test error cases
    })
}
```

### Integration Test Template
```go
//go:build integration

func TestNewFeatureIntegration(t *testing.T) {
    // Setup real dependencies
    db := setupTestDB(t)
    defer db.Close()
    
    // Test with real database
    service := NewService(db, ...)
    
    // Test integration scenarios
}
```

### Compatibility Test Template
```go
//go:build compatibility

func TestHVACRemixCompatibility_NewFeature(t *testing.T) {
    // Setup test server
    server := setupCompatibilityTestServer(t)
    defer server.Close()
    
    // Test API compatibility
    resp, err := http.Get(server.URL + "/api/v1/new-feature")
    require.NoError(t, err)
    
    // Verify response structure matches hvac-remix expectations
    var response NewFeatureResponse
    err = json.NewDecoder(resp.Body).Decode(&response)
    require.NoError(t, err)
    
    // Assert compatibility
    assert.NotEmpty(t, response.RequiredField)
}
```

## 🎯 Best Practices

### Test Organization
- Group related tests in the same file
- Use descriptive test names with underscores
- Follow AAA pattern (Arrange, Act, Assert)
- Use table-driven tests for multiple scenarios

### Mock Usage
- Mock external dependencies
- Use interfaces for better testability
- Generate mocks with `mockgen`
- Verify mock expectations

### Test Data
- Use factories for test data creation
- Keep test data minimal and focused
- Use constants for expected values
- Clean up test data after tests

### Performance Testing
- Use benchmarks for performance-critical code
- Test with realistic data sizes
- Monitor memory allocations
- Set appropriate timeouts

## 📚 Additional Resources

- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [Testify Documentation](https://github.com/stretchr/testify)
- [Docker Compose Testing](https://docs.docker.com/compose/startup-order/)
- [HVAC-Remix API Documentation](../hvac-remix/docs/api.md)

## 🤝 Contributing

When adding new features:

1. **Write tests first** (TDD approach)
2. **Ensure compatibility** with hvac-remix
3. **Add performance benchmarks** for critical paths
4. **Update documentation** as needed
5. **Verify coverage** meets requirements

## 📞 Support

For testing issues:
- Check existing test documentation
- Review test logs and error messages
- Consult team members for complex scenarios
- Update this guide with new patterns and solutions
