# 📧 HVAC Email Intelligence System

## 🚀 Comprehensive Email Analysis & Retrieval for HVAC CRM

Zaawansowany system analizy emaili zintegrowany z GoBackend-Kratos HVAC CRM, wykorzystujący biblioteki z `wizja_gov3.md` do kompleksowego przetwarzania emaili z załącznikami.

---

## 🎯 **Główne Funkcjonalności**

### 📮 **Multi-Mailbox Email Retrieval**
- ✅ Pobieranie emaili z 2-3 skrzynek jednocześnie
- ✅ Obsługa Gmail, Outlook, Business Email
- ✅ Automatyczne pobieranie załączników
- ✅ Konfigurowalny harmonogram pobierania

### 🤖 **AI-Powered Email Analysis**
- ✅ Analiza sentymentu za pomocą Ollama/Gemma
- ✅ Kategoryzacja HVAC-specific
- ✅ Wykrywanie priorytetów i pilności
- ✅ Ekstrakcja action items
- ✅ Analiza relevance dla branży HVAC

### 📎 **Advanced Attachment Processing**
- ✅ **Excel files** (.xlsx, .xls) - pełna analiza danych
- ✅ **Word documents** (.docx) - ekstrakcja tekstu
- ✅ **PDF files** - przygotowane do implementacji
- ✅ **Text files** (.txt, .csv) - analiza zawartości

### 🔍 **Vector Database & Semantic Search**
- ✅ ChromemDB dla przechowywania embeddings
- ✅ Semantyczne wyszukiwanie podobnych emaili
- ✅ Kontekstowe rekomendacje
- ✅ Analiza trendów w komunikacji

### 📊 **Comprehensive Dashboard**
- ✅ Real-time analytics
- ✅ Sentiment breakdown
- ✅ Category statistics
- ✅ Processing metrics
- ✅ Top keywords analysis

---

## 🏗️ **Architektura Systemu**

```
📧 Email Intelligence System
├── 📥 Email Retrieval Service
│   ├── IMAP Client (go-imap)
│   ├── Multi-mailbox support
│   └── Attachment extraction
├── 🔍 Email Analysis Service  
│   ├── AI Analysis (LangChain + Ollama)
│   ├── Attachment Processing (Excelize)
│   ├── Vector Storage (ChromemDB)
│   └── HVAC-specific logic
├── 📊 Dashboard Service
│   ├── Real-time statistics
│   ├── Search & filtering
│   └── RESTful API
└── 🌐 HTTP Server
    ├── API endpoints
    ├── CORS support
    └── Middleware stack
```

---

## 🚀 **Quick Start**

### 1. **Konfiguracja Environment Variables**

```bash
# Gmail Configuration
export GMAIL_USERNAME="<EMAIL>"
export GMAIL_PASSWORD="your-app-specific-password"

# Outlook Configuration  
export OUTLOOK_USERNAME="<EMAIL>"
export OUTLOOK_PASSWORD="your-outlook-password"

# Business Email Configuration
export BUSINESS_USERNAME="<EMAIL>"
export BUSINESS_PASSWORD="your-business-email-password"

# AI Services
export OLLAMA_URL="http://localhost:11434"
export VECTOR_DB_PATH="./data/email_vectors"
```

### 2. **Uruchomienie Systemu**

```bash
# Build the application
cd GoBackend-Kratos
docker run --rm -v $(pwd):/workspace -w /workspace golang:1.24 go build -o email-intelligence ./cmd/email-intelligence

# Run the service
./email-intelligence -conf configs/email-intelligence.yaml
```

### 3. **Weryfikacja Działania**

```bash
# Check service status
curl http://localhost:8082/status

# Start email retrieval
curl -X POST http://localhost:8082/api/v1/retrieval/start

# View dashboard stats
curl http://localhost:8082/api/v1/email-analysis/dashboard/stats
```

---

## 📡 **API Endpoints**

### 📊 **Dashboard & Analytics**
```bash
GET  /api/v1/email-analysis/dashboard/stats     # Dashboard statistics
POST /api/v1/email-analysis/search              # Search emails
GET  /api/v1/email-analysis/emails/{id}         # Get email by ID
POST /api/v1/email-analysis/analyze             # Analyze new email
GET  /api/v1/email-analysis/similar             # Find similar emails
```

### 📮 **Email Retrieval**
```bash
POST /api/v1/retrieval/start                    # Start email retrieval
POST /api/v1/retrieval/stop                     # Stop email retrieval
GET  /api/v1/retrieval/status                   # Retrieval status
GET  /api/v1/retrieval/mailboxes                # List mailboxes
POST /api/v1/retrieval/mailboxes                # Add mailbox
PUT  /api/v1/retrieval/mailboxes/{name}         # Update mailbox
DELETE /api/v1/retrieval/mailboxes/{name}       # Remove mailbox
POST /api/v1/retrieval/mailboxes/{name}/test    # Test mailbox connection
```

### 🔍 **Analysis & Processing**
```bash
POST /api/v1/analysis/analyze                   # Analyze single email
POST /api/v1/analysis/batch-analyze             # Batch analysis
POST /api/v1/analysis/reanalyze/{id}            # Reanalyze email
```

---

## 📊 **Przykłady Użycia**

### 🔍 **Wyszukiwanie Emaili HVAC**

```bash
curl -X POST http://localhost:8082/api/v1/email-analysis/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "air conditioning repair",
    "has_hvac": true,
    "sentiment": "negative",
    "priority": "high",
    "limit": 10
  }'
```

### 📊 **Dashboard Statistics**

```bash
curl http://localhost:8082/api/v1/email-analysis/dashboard/stats | jq '.'
```

Response:
```json
{
  "total_emails": 1250,
  "today_emails": 45,
  "hvac_emails": 890,
  "high_priority_emails": 23,
  "attachments_count": 156,
  "sentiment_breakdown": {
    "positive": 650,
    "negative": 200,
    "neutral": 400
  },
  "category_breakdown": {
    "hvac_service": 890,
    "general": 200,
    "support": 100,
    "sales": 60
  }
}
```

### 🔍 **Semantic Search**

```bash
curl "http://localhost:8082/api/v1/email-analysis/similar?query=heating%20system%20broken&limit=5"
```

---

## 🔧 **Integracja z GoBackend-Kratos CRM**

### 📡 **API Integration**

```go
// Example: Fetch analyzed emails for CRM
func (s *HVACService) GetAnalyzedEmails(ctx context.Context) ([]*EmailAnalysis, error) {
    resp, err := http.Get("http://localhost:8082/api/v1/email-analysis/search")
    if err != nil {
        return nil, err
    }
    
    var emails []*EmailAnalysis
    json.NewDecoder(resp.Body).Decode(&emails)
    return emails, nil
}
```

### 🔔 **Webhook Integration**

```go
// Example: Handle high-priority HVAC emails
func (s *HVACService) HandlePriorityEmail(email *EmailAnalysis) {
    if email.Priority == "high" && email.HVACRelevance.IsHVACRelated {
        // Create urgent service ticket
        s.CreateUrgentTicket(email)
        
        // Send notification to technicians
        s.NotifyTechnicians(email)
    }
}
```

---

## 📈 **Monitoring & Metrics**

### 📊 **Key Metrics**
- **Processing Rate**: Emails analyzed per minute
- **Success Rate**: Percentage of successful analyses
- **Response Time**: Average analysis time
- **HVAC Relevance**: Percentage of HVAC-related emails
- **Sentiment Distribution**: Positive/Negative/Neutral breakdown

### 🔍 **Health Checks**

```bash
# Service health
curl http://localhost:8082/health

# Detailed status
curl http://localhost:8082/status
```

---

## 🔮 **Przyszłe Rozszerzenia**

### 🎤 **System Transkrypcji**
- Integracja z AssemblyAI dla nagrań głosowych
- Automatyczna transkrypcja voicemail
- Analiza rozmów telefonicznych z klientami

### 📱 **Mobile Integration**
- Push notifications dla pilnych emaili
- Mobile dashboard dla techników
- Offline access do kluczowych informacji

### 🤖 **Advanced AI Features**
- Automatyczne generowanie odpowiedzi
- Predykcyjne modelowanie potrzeb klientów
- Inteligentne routing emaili do odpowiednich techników

---

## 🛠️ **Wykorzystane Biblioteki z wizja_gov3.md**

### ✅ **Zaimplementowane**
- `github.com/xuri/excelize/v2` - Excel files processing
- `github.com/emersion/go-imap` - IMAP client library
- `github.com/philippgille/chromem-go` - Vector database
- `github.com/tmc/langchaingo` - LLM integration
- `github.com/jackc/pgx/v5` - PostgreSQL driver
- `github.com/pressly/goose/v3` - Database migrations

### 🔄 **Planowane do Implementacji**
- `github.com/gomutex/godocx` - Word documents processing
- `github.com/danieldk/go2vec` - Word embeddings
- `github.com/capillariesio/capillaries` - Data processing framework
- `github.com/vdobler/chart` - Chart generation
- `github.com/presbrey/ollamafarm` - Ollama load balancing

---

## 🎉 **Podsumowanie**

Email Intelligence System to kompletne rozwiązanie do analizy emaili dla firm HVAC, które:

✅ **Automatyzuje** pobieranie i analizę emaili z wielu skrzynek  
✅ **Wykorzystuje AI** do inteligentnej kategoryzacji i priorytetyzacji  
✅ **Przetwarza załączniki** (Excel, Word, PDF) z pełną analizą  
✅ **Zapewnia dashboard** z real-time analytics  
✅ **Integruje się** seamlessly z GoBackend-Kratos CRM  
✅ **Skaluje się** dla rosnących potrzeb biznesowych  

**Rezultat**: Znacznie zwiększona efektywność obsługi klientów i lepsze zarządzanie komunikacją w firmie HVAC! 🚀
