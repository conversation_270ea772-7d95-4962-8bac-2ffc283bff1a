GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)
INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
API_PROTO_FILES=$(shell find api -name *.proto)

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/wire/cmd/wire@latest

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)

.PHONY: api
# generate api proto
api:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       api/email/v1/email.proto api/ai/v1/ai.proto api/hvac/v1/hvac.proto api/stt/v1/stt.proto api/analytics/v1/analytics.proto api/workflow/v1/workflow.proto

.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go mod tidy
	go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: wire
# generate wire
wire:
	cd cmd/server && wire

.PHONY: all
# generate all
all:
	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

# 📧 Email Intelligence Service targets

.PHONY: build-email
# build email intelligence service
build-email:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/email-intelligence ./cmd/email-intelligence

.PHONY: run-email
# run email intelligence service
run-email:
	./bin/email-intelligence -conf configs/email-intelligence.yaml

.PHONY: email-intelligence
# build and run email intelligence service
email-intelligence: build-email run-email

.PHONY: docker-build-email
# build email intelligence with docker
docker-build-email:
	docker run --rm -v $(PWD):/workspace -w /workspace golang:1.24 go build -o email-intelligence ./cmd/email-intelligence

.PHONY: docker-run-email
# run email intelligence with docker
docker-run-email:
	docker run --rm -v $(PWD):/workspace -w /workspace -p 8082:8082 golang:1.24 ./email-intelligence -conf configs/email-intelligence.yaml

.PHONY: test-email
# test email intelligence components
test-email:
	go test -v ./internal/email/... -cover

.PHONY: setup-email-env
# setup email intelligence environment
setup-email-env:
	@echo "Setting up Email Intelligence environment..."
	mkdir -p data/email_vectors
	mkdir -p data/attachments
	mkdir -p logs
	@echo "Environment setup complete!"

.PHONY: start-ollama
# start ollama for AI analysis
start-ollama:
	@echo "Starting Ollama service..."
	docker run -d --name ollama -p 11434:11434 ollama/ollama
	@echo "Ollama started on http://localhost:11434"

.PHONY: pull-gemma
# pull gemma model for ollama
pull-gemma:
	@echo "Pulling Gemma model..."
	docker exec ollama ollama pull gemma:7b
	@echo "Gemma model ready!"

.PHONY: pull-gemma3
# pull gemma 3 4b instruct model for advanced analysis
pull-gemma3:
	@echo "Pulling Gemma 3 4B Instruct model..."
	docker exec ollama ollama pull gemma3:4b-instruct
	@echo "Gemma 3 4B Instruct model ready!"

.PHONY: setup-gemma3-complete
# complete gemma 3 setup with 4b instruct model
setup-gemma3-complete: setup-email-env start-ollama pull-gemma3
	@echo "🎉 Gemma 3 Email Intelligence setup complete!"
	@echo "🤖 Ready to analyze emails with Gemma 3 4B Instruct!"
	@echo "📊 Features: 128K context, 8192 output tokens, multimodal ready"

.PHONY: setup-email-complete
# complete email intelligence setup
setup-email-complete: setup-email-env start-ollama pull-gemma
	@echo "🎉 Email Intelligence setup complete!"
	@echo "📧 Ready to analyze emails with AI!"

.PHONY: demo-email
# quick demo of email intelligence
demo-email:
	@echo "🚀 Starting Email Intelligence Demo..."
	@echo "📧 Building service..."
	make build-email
	@echo "🌐 Starting service on http://localhost:8082..."
	@echo "📊 Dashboard: http://localhost:8082/api/v1/email-analysis/dashboard/stats"
	@echo "🔍 Health: http://localhost:8082/health"
	./bin/email-intelligence -conf configs/email-intelligence.yaml

.PHONY: dev-email
# email intelligence development mode
dev-email:
	@echo "🔧 Starting Email Intelligence in development mode..."
	go run ./cmd/email-intelligence -conf configs/email-intelligence.yaml

# 🚀 OPTIMIZATION TARGETS

.PHONY: optimize
# run all optimizations
optimize: optimize-deps optimize-build optimize-docker
	@echo "🎉 All optimizations completed!"

.PHONY: optimize-deps
# optimize dependencies
optimize-deps:
	@echo "🔧 Optimizing dependencies..."
	go mod tidy
	go mod download
	@echo "✅ Dependencies optimized!"

.PHONY: optimize-build
# optimize build process
optimize-build:
	@echo "🔧 Optimizing build..."
	go build -ldflags="-s -w" -o ./bin/server-optimized ./cmd/server
	@echo "✅ Build optimized!"

.PHONY: optimize-docker
# optimize docker images
optimize-docker:
	@echo "🔧 Optimizing Docker images..."
	docker build --target builder -t gobackend-hvac-builder .
	docker build -t gobackend-hvac-optimized .
	@echo "✅ Docker images optimized!"

.PHONY: benchmark
# run performance benchmarks
benchmark:
	@echo "📊 Running performance benchmarks..."
	go test -bench=. -benchmem ./...
	@echo "✅ Benchmarks completed!"

.PHONY: profile
# run CPU and memory profiling
profile:
	@echo "🔍 Running profiling..."
	go test -cpuprofile=cpu.prof -memprofile=mem.prof -bench=. ./...
	@echo "✅ Profiling completed! Check cpu.prof and mem.prof"

.PHONY: clean-cache
# clean build cache
clean-cache:
	@echo "🧹 Cleaning build cache..."
	go clean -cache -modcache -testcache
	@echo "✅ Cache cleaned!"

.PHONY: security-scan
# run security vulnerability scan
security-scan:
	@echo "🔒 Running security scan..."
	go list -json -m all | nancy sleuth
	@echo "✅ Security scan completed!"

# 🐙 HUGO-ENHANCED OCTOPUS INTERFACE TARGETS

.PHONY: build-hugo
# build hugo-enhanced octopus interface
build-hugo:
	@echo "🐙 Building Hugo-Enhanced Octopus Interface..."
	./scripts/build-hugo-octopus.sh

.PHONY: build-octopus
# build octopus interface service
build-octopus:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/octopus ./cmd/octopus

.PHONY: run-octopus
# run octopus interface service
run-octopus:
	./bin/octopus -conf configs/octopus.yaml

.PHONY: octopus-complete
# build hugo site and run octopus interface
octopus-complete: build-hugo build-octopus run-octopus

.PHONY: dev-octopus
# octopus interface development mode with hugo
dev-octopus: build-hugo
	@echo "🐙 Starting Octopus Interface in development mode..."
	go run ./cmd/octopus -conf configs/octopus.yaml

.PHONY: hugo-dev
# hugo development server with live reload
hugo-dev:
	@echo "🐙 Starting Hugo development server..."
	cd web/hugo-octopus && ../../hugo server --bind 0.0.0.0 --port 1313 --buildDrafts --watch

.PHONY: docker-octopus
# build and run octopus interface with docker
docker-octopus: build-hugo
	@echo "🐙 Building Octopus Interface Docker image..."
	docker build -f Dockerfile.octopus -t gobackend-octopus .
	@echo "🚀 Running Octopus Interface container..."
	docker run -p 8083:8083 --name octopus-interface gobackend-octopus

.PHONY: clean-hugo
# clean hugo build artifacts
clean-hugo:
	@echo "🧹 Cleaning Hugo build artifacts..."
	rm -rf web/octopus/static/*
	@echo "✅ Hugo artifacts cleaned!"