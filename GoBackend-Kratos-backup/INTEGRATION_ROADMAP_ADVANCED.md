# 🚀 ADVANCED INTEGRATION ROADMAP
## GoBackend-<PERSON><PERSON><PERSON> HVAC CRM - Complete Customer Intelligence System

---

## 🎯 **VISION: COMPREHENSIVE CUSTOMER INTELLIGENCE**

Stworzenie **kompletnego systemu inteligencji klienta** który automatycznie:
- 📧 **Analizuje wszystkie emaile** z głównych skrzynek
- 📞 **Przetwarza transkrypcje rozmów** z dedykowanej skrzynki
- 👤 **Buduje profile klientów** automatycznie
- 🔗 **Łączy wszystkie interakcje** w jednej bazie danych
- 🧠 **Dostarcza insights** dla lepszej obsługi

---

## 📋 **PHASE 1: CUSTOMER PROFILE SYSTEM**
### 🎯 **Priority: HIGH** | ⏱️ **Timeline: 2-3 tygodnie**

### **1.1 Customer Database Schema**
```sql
-- 👤 Customer Profiles
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(50),
    name VARCHAR(255),
    company VARCHAR(255),
    customer_type VARCHAR(50), -- residential, commercial, industrial
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    -- Profile Data
    communication_preference VARCHAR(50), -- email, phone, text
    knowledge_level VARCHAR(50), -- novice, basic, intermediate, expert
    satisfaction_score DECIMAL(3,2), -- 0.00-5.00
    lifetime_value DECIMAL(10,2),

    -- Contact Info
    address JSONB,
    emergency_contact JSONB,
    preferred_technician_id UUID,

    -- Metadata
    tags TEXT[],
    notes TEXT,
    status VARCHAR(50) DEFAULT 'active'
);

-- 📞 Phone Numbers (multiple per customer)
CREATE TABLE customer_phones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id),
    phone_number VARCHAR(50),
    phone_type VARCHAR(50), -- mobile, home, work, emergency
    is_primary BOOLEAN DEFAULT false,
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 📧 Email Addresses (multiple per customer)
CREATE TABLE customer_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id),
    email_address VARCHAR(255),
    email_type VARCHAR(50), -- primary, work, billing, emergency
    is_primary BOOLEAN DEFAULT false,
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **1.2 Interaction Tracking System**
```sql
-- 🔗 All Customer Interactions
CREATE TABLE customer_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id),
    interaction_type VARCHAR(50), -- email, phone_call, service_visit, quote
    direction VARCHAR(20), -- inbound, outbound
    timestamp TIMESTAMP DEFAULT NOW(),

    -- Content
    subject VARCHAR(500),
    content TEXT,
    summary TEXT,

    -- Metadata
    source_system VARCHAR(50), -- email_intelligence, phone_transcription, crm
    source_id VARCHAR(255), -- original email/call ID
    channel VARCHAR(50), -- gmail, outlook, phone, in_person

    -- Analysis Results
    sentiment VARCHAR(50),
    sentiment_score DECIMAL(3,2),
    priority_level VARCHAR(50),
    hvac_relevance BOOLEAN,
    action_items TEXT[],

    -- Business Data
    estimated_value DECIMAL(10,2),
    follow_up_required BOOLEAN,
    follow_up_date TIMESTAMP,

    created_at TIMESTAMP DEFAULT NOW()
);

-- 📞 Phone Call Details
CREATE TABLE phone_calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interaction_id UUID REFERENCES customer_interactions(id),
    phone_number VARCHAR(50),
    duration_seconds INTEGER,
    call_direction VARCHAR(20), -- inbound, outbound
    call_status VARCHAR(50), -- completed, missed, voicemail

    -- Transcription Data
    transcription_text TEXT,
    transcription_confidence DECIMAL(3,2),
    transcription_language VARCHAR(10),

    -- Analysis
    call_purpose VARCHAR(100), -- emergency, quote_request, follow_up, complaint
    urgency_level VARCHAR(50),
    technical_complexity VARCHAR(50),

    created_at TIMESTAMP DEFAULT NOW()
);
```

### **1.3 Customer Profile Service**
```go
// internal/customer/profile_service.go
type CustomerProfileService struct {
    log        *log.Helper
    db         *gorm.DB
    emailSvc   *email.EmailAnalysisService
    gemma3     *ai.Gemma3Service
}

type Customer struct {
    ID                     uuid.UUID `gorm:"type:uuid;primary_key"`
    Email                  string    `gorm:"unique"`
    Phone                  string
    Name                   string
    Company                string
    CustomerType           string
    CommunicationPreference string
    KnowledgeLevel         string
    SatisfactionScore      float64
    LifetimeValue          float64

    // Relations
    Phones       []CustomerPhone    `gorm:"foreignKey:CustomerID"`
    Emails       []CustomerEmail    `gorm:"foreignKey:CustomerID"`
    Interactions []CustomerInteraction `gorm:"foreignKey:CustomerID"`

    CreatedAt time.Time
    UpdatedAt time.Time
}

// 🔍 Auto-detect or create customer from email/phone
func (s *CustomerProfileService) FindOrCreateCustomer(
    ctx context.Context,
    email string,
    phone string,
) (*Customer, error)

// 📊 Build comprehensive customer profile
func (s *CustomerProfileService) BuildCustomerProfile(
    ctx context.Context,
    customerID uuid.UUID,
) (*CustomerProfile, error)

// 🔗 Link interaction to customer
func (s *CustomerProfileService) LinkInteraction(
    ctx context.Context,
    customerID uuid.UUID,
    interaction *CustomerInteraction,
) error
```

---

## 📋 **PHASE 2: TRANSCRIPTION MAILBOX INTEGRATION**
### 🎯 **Priority: HIGH** | ⏱️ **Timeline: 1-2 tygodnie**

### **2.1 Transcription Email Parser**
```go
// internal/transcription/parser.go
type TranscriptionParser struct {
    log    *log.Helper
    gemma3 *ai.Gemma3Service
}

type TranscriptionEmail struct {
    PhoneNumber    string
    CallDirection  string // inbound/outbound
    CallDuration   time.Duration
    CallTimestamp  time.Time
    Transcription  string
    Confidence     float64
    CallerID       string

    // Parsed Data
    CustomerInfo   *CustomerInfo
    CallPurpose    string
    UrgencyLevel   string
    ActionItems    []string
    FollowUpNeeded bool
}

// 📞 Parse transcription email formats
func (p *TranscriptionParser) ParseTranscriptionEmail(
    ctx context.Context,
    emailContent string,
) (*TranscriptionEmail, error)

// 🔍 Extract phone number and caller info
func (p *TranscriptionParser) ExtractCallerInfo(
    transcription string,
) (*CustomerInfo, error)

// 🧠 Analyze call content with Gemma 3
func (p *TranscriptionParser) AnalyzeCallContent(
    ctx context.Context,
    transcription string,
) (*CallAnalysis, error)
```

### **2.2 Phone Call Intelligence**
```go
// internal/phone/call_intelligence.go
type CallIntelligenceService struct {
    log         *log.Helper
    customerSvc *customer.CustomerProfileService
    gemma3      *ai.Gemma3Service
}

type CallAnalysis struct {
    CallPurpose      string   // emergency, quote, follow_up, complaint
    UrgencyLevel     string   // critical, high, medium, low
    TechnicalIssues  []string
    ServiceRequests  []string
    CustomerMood     string   // frustrated, satisfied, neutral
    BusinessValue    string   // high, medium, low
    NextActions      []string
    FollowUpDate     *time.Time
}

// 📞 Process incoming transcription
func (s *CallIntelligenceService) ProcessTranscription(
    ctx context.Context,
    transcription *TranscriptionEmail,
) (*CallAnalysis, error)

// 👤 Link call to customer profile
func (s *CallIntelligenceService) LinkCallToCustomer(
    ctx context.Context,
    phoneNumber string,
    callData *CallAnalysis,
) error
```

### **2.3 Mailbox Configuration Update**
```yaml
# configs/email-intelligence.yaml
mailboxes:
  # Main business emails
  - name: "HVAC_Business_Main"
    host: "imap.gmail.com"
    port: 993
    username: "${BUSINESS_EMAIL}"
    password: "${BUSINESS_PASSWORD}"
    folder: "INBOX"
    poll_interval: 3
    enabled: true
    processing_type: "general_email"

  # Transcription mailbox
  - name: "HVAC_Transcriptions"
    host: "imap.gmail.com"
    port: 993
    username: "${TRANSCRIPTION_EMAIL}"
    password: "${TRANSCRIPTION_PASSWORD}"
    folder: "INBOX"
    poll_interval: 1  # More frequent for calls
    enabled: true
    processing_type: "phone_transcription"

  # Support mailbox
  - name: "HVAC_Support"
    host: "outlook.office365.com"
    port: 993
    username: "${SUPPORT_EMAIL}"
    password: "${SUPPORT_PASSWORD}"
    folder: "INBOX"
    poll_interval: 5
    enabled: true
    processing_type: "support_email"
```

---

## 📋 **PHASE 3: INTELLIGENT CUSTOMER MATCHING**
### 🎯 **Priority: MEDIUM** | ⏱️ **Timeline: 2-3 tygodnie**

### **3.1 Smart Customer Detection**
```go
// internal/customer/matcher.go
type CustomerMatcher struct {
    log    *log.Helper
    db     *gorm.DB
    gemma3 *ai.Gemma3Service
}

// 🔍 Find customer by multiple criteria
func (m *CustomerMatcher) FindCustomer(
    ctx context.Context,
    criteria *MatchCriteria,
) (*Customer, float64, error) // customer, confidence, error

type MatchCriteria struct {
    Email       string
    Phone       string
    Name        string
    Company     string
    Address     string
    Content     string // email/call content for context
}

// 🧠 AI-powered customer matching
func (m *CustomerMatcher) AIMatchCustomer(
    ctx context.Context,
    content string,
    knownCustomers []*Customer,
) (*Customer, float64, error)

// 📊 Customer similarity scoring
func (m *CustomerMatcher) CalculateSimilarity(
    customer1, customer2 *Customer,
) float64
```

### **3.2 Profile Enrichment**
```go
// internal/customer/enrichment.go
type ProfileEnrichmentService struct {
    log    *log.Helper
    gemma3 *ai.Gemma3Service
}

// 📈 Enrich customer profile from interactions
func (s *ProfileEnrichmentService) EnrichProfile(
    ctx context.Context,
    customer *Customer,
) error

// 🎯 Extract customer insights
func (s *ProfileEnrichmentService) ExtractInsights(
    ctx context.Context,
    interactions []*CustomerInteraction,
) (*CustomerInsights, error)

type CustomerInsights struct {
    PreferredContactTime    string
    CommunicationStyle      string
    TechnicalKnowledge      string
    ServiceHistory          []string
    PainPoints             []string
    SatisfactionTrends     []float64
    BusinessPotential      string
    ChurnRisk              float64
}
```

---

## 📋 **PHASE 4: UNIFIED DASHBOARD & ANALYTICS**
### 🎯 **Priority: MEDIUM** | ⏱️ **Timeline: 3-4 tygodnie**

### **4.1 Customer 360° View**
```go
// internal/dashboard/customer360.go
type Customer360Service struct {
    log         *log.Helper
    customerSvc *customer.CustomerProfileService
    analyticsSvc *analytics.AnalyticsService
}

type Customer360View struct {
    Profile      *Customer
    Timeline     []*InteractionTimeline
    Analytics    *CustomerAnalytics
    Insights     *CustomerInsights
    Predictions  *CustomerPredictions
    ActionItems  []*ActionItem
}

// 📊 Generate complete customer view
func (s *Customer360Service) GenerateCustomer360(
    ctx context.Context,
    customerID uuid.UUID,
) (*Customer360View, error)
```

### **4.2 Real-time Analytics**
```go
// internal/analytics/customer_analytics.go
type CustomerAnalytics struct {
    TotalInteractions    int
    EmailCount          int
    PhoneCallCount      int
    ServiceVisitCount   int

    ResponseTimes       *ResponseTimeMetrics
    SatisfactionTrend   []float64
    BusinessValue       *BusinessValueMetrics

    LastContact         time.Time
    NextFollowUp        *time.Time
    ChurnProbability    float64
}

// 📈 Real-time customer metrics
func (s *AnalyticsService) CalculateCustomerMetrics(
    ctx context.Context,
    customerID uuid.UUID,
) (*CustomerAnalytics, error)
```

---

## 📋 **PHASE 5: ADVANCED FEATURES**
### 🎯 **Priority: LOW** | ⏱️ **Timeline: 4-6 tygodni**

### **5.1 Predictive Analytics**
- 🔮 **Churn Prediction** - przewidywanie odejścia klientów
- 📈 **Lifetime Value** - predykcja wartości klienta
- 🎯 **Next Best Action** - rekomendacje działań
- 📞 **Optimal Contact Time** - najlepszy czas kontaktu

### **5.2 Automation & Workflows**
- 🤖 **Auto-response** - automatyczne odpowiedzi
- 📅 **Smart Scheduling** - inteligentne planowanie
- 🔔 **Proactive Alerts** - proaktywne powiadomienia
- 📊 **Performance Tracking** - śledzenie wydajności

### **5.3 Integration Ecosystem**
- 📱 **Mobile App** - aplikacja mobilna dla techników
- 💳 **Payment Integration** - integracja płatności
- 📋 **Inventory Management** - zarządzanie magazynem
- 🗺️ **Route Optimization** - optymalizacja tras

---

## 🛠️ **IMPLEMENTATION PLAN**

### **Week 1-2: Customer Database Foundation**
```bash
# Database setup
make setup-customer-db
make migrate-customer-schema

# Core services
make build-customer-service
make test-customer-matching
```

### **Week 3-4: Transcription Integration**
```bash
# Transcription parser
make build-transcription-parser
make setup-transcription-mailbox
make test-call-processing
```

### **Week 5-6: Profile Intelligence**
```bash
# AI-powered matching
make build-customer-matcher
make setup-profile-enrichment
make test-customer-insights
```

### **Week 7-10: Dashboard & Analytics**
```bash
# 360° customer view
make build-customer360-dashboard
make setup-real-time-analytics
make test-complete-system
```

---

## 🎯 **SUCCESS METRICS**

### **Customer Intelligence KPIs:**
- **Customer Match Accuracy**: >95%
- **Profile Completeness**: >90%
- **Response Time**: <2 minutes for critical
- **Customer Satisfaction**: >4.5/5.0
- **Churn Prediction Accuracy**: >85%

### **Business Impact:**
- **Customer Retention**: +25%
- **Response Efficiency**: +50%
- **Revenue per Customer**: +30%
- **Operational Costs**: -20%
- **Customer Satisfaction**: +40%

---

## 🚀 **NEXT STEPS**

1. **✅ Approve roadmap** - confirm priorities and timeline
2. **🗄️ Setup customer database** - implement schema
3. **📧 Configure transcription mailbox** - add second mailbox
4. **🧠 Integrate Gemma 3** - for customer intelligence
5. **📊 Build customer 360°** - comprehensive dashboard
6. **🔄 Test end-to-end** - complete workflow validation

---

**Ready to revolutionize HVAC customer intelligence!** 🚀🔧💙

---

## 📋 **DETAILED IMPLEMENTATION GUIDE**

### **PHASE 1A: Database Schema Implementation**

```sql
-- Execute in PostgreSQL
-- File: migrations/001_customer_intelligence.sql

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 👤 Core Customer Table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Basic Info
    email VARCHAR(255) UNIQUE,
    primary_phone VARCHAR(50),
    name VARCHAR(255),
    company VARCHAR(255),
    customer_type VARCHAR(50) CHECK (customer_type IN ('residential', 'commercial', 'industrial')),

    -- Profile Data
    communication_preference VARCHAR(50) DEFAULT 'email',
    knowledge_level VARCHAR(50) DEFAULT 'basic',
    satisfaction_score DECIMAL(3,2) DEFAULT 3.00,
    lifetime_value DECIMAL(10,2) DEFAULT 0.00,

    -- Contact Details
    address JSONB,
    emergency_contact JSONB,
    preferred_technician_id UUID,

    -- Business Intelligence
    customer_segment VARCHAR(50), -- vip, regular, new, at_risk
    acquisition_source VARCHAR(100), -- website, referral, advertising
    first_contact_date TIMESTAMP,
    last_contact_date TIMESTAMP,

    -- Metadata
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    status VARCHAR(50) DEFAULT 'active',

    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 📞 Customer Phone Numbers
CREATE TABLE customer_phones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    phone_number VARCHAR(50) NOT NULL,
    phone_type VARCHAR(50) DEFAULT 'mobile',
    is_primary BOOLEAN DEFAULT false,
    verified BOOLEAN DEFAULT false,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(customer_id, phone_number)
);

-- 📧 Customer Email Addresses
CREATE TABLE customer_emails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    email_address VARCHAR(255) NOT NULL,
    email_type VARCHAR(50) DEFAULT 'primary',
    is_primary BOOLEAN DEFAULT false,
    verified BOOLEAN DEFAULT false,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(customer_id, email_address)
);

-- 🔗 Customer Interactions (All touchpoints)
CREATE TABLE customer_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,

    -- Interaction Details
    interaction_type VARCHAR(50) NOT NULL, -- email, phone_call, service_visit, quote, complaint
    direction VARCHAR(20) NOT NULL, -- inbound, outbound
    channel VARCHAR(50), -- gmail, outlook, phone, in_person, website
    timestamp TIMESTAMP DEFAULT NOW(),

    -- Content
    subject VARCHAR(500),
    content TEXT,
    summary TEXT,

    -- Source Tracking
    source_system VARCHAR(50), -- email_intelligence, phone_transcription, crm_manual
    source_id VARCHAR(255), -- original email/call ID
    external_reference VARCHAR(255), -- ticket number, order ID, etc.

    -- AI Analysis Results
    sentiment VARCHAR(50),
    sentiment_score DECIMAL(3,2),
    priority_level VARCHAR(50),
    urgency_score INTEGER CHECK (urgency_score BETWEEN 1 AND 10),
    hvac_relevance BOOLEAN DEFAULT false,
    confidence_score DECIMAL(3,2),

    -- Business Data
    estimated_value DECIMAL(10,2),
    actual_value DECIMAL(10,2),
    service_category VARCHAR(100), -- repair, maintenance, installation, consultation
    equipment_mentioned TEXT[],

    -- Follow-up
    action_items TEXT[],
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date TIMESTAMP,
    follow_up_completed BOOLEAN DEFAULT false,

    -- Resolution
    resolution_status VARCHAR(50), -- open, in_progress, resolved, closed
    resolution_time INTERVAL,
    customer_satisfaction INTEGER CHECK (customer_satisfaction BETWEEN 1 AND 5),

    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 📞 Phone Call Specific Data
CREATE TABLE phone_calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    interaction_id UUID NOT NULL REFERENCES customer_interactions(id) ON DELETE CASCADE,

    -- Call Details
    phone_number VARCHAR(50) NOT NULL,
    duration_seconds INTEGER,
    call_direction VARCHAR(20) NOT NULL,
    call_status VARCHAR(50), -- completed, missed, voicemail, busy, failed
    call_quality_score DECIMAL(3,2), -- 1.00-5.00

    -- Transcription Data
    transcription_text TEXT,
    transcription_confidence DECIMAL(3,2),
    transcription_language VARCHAR(10) DEFAULT 'en',
    transcription_provider VARCHAR(50), -- whisper, google, azure

    -- Call Analysis
    call_purpose VARCHAR(100),
    technical_complexity VARCHAR(50), -- simple, moderate, complex, expert_required
    customer_mood VARCHAR(50), -- calm, frustrated, angry, satisfied, confused

    -- Business Intelligence
    sales_opportunity BOOLEAN DEFAULT false,
    upsell_potential VARCHAR(50), -- high, medium, low, none
    competitive_mention BOOLEAN DEFAULT false,

    -- Metadata
    recording_available BOOLEAN DEFAULT false,
    recording_url VARCHAR(500),
    caller_id VARCHAR(100),

    created_at TIMESTAMP DEFAULT NOW()
);

-- 📊 Customer Analytics Cache
CREATE TABLE customer_analytics (
    customer_id UUID PRIMARY KEY REFERENCES customers(id) ON DELETE CASCADE,

    -- Interaction Counts
    total_interactions INTEGER DEFAULT 0,
    email_count INTEGER DEFAULT 0,
    phone_call_count INTEGER DEFAULT 0,
    service_visit_count INTEGER DEFAULT 0,

    -- Response Metrics
    avg_response_time INTERVAL,
    first_response_time INTERVAL,
    resolution_rate DECIMAL(5,2),

    -- Satisfaction Metrics
    avg_satisfaction DECIMAL(3,2),
    satisfaction_trend VARCHAR(20), -- improving, declining, stable
    nps_score INTEGER, -- Net Promoter Score

    -- Business Metrics
    total_revenue DECIMAL(10,2) DEFAULT 0.00,
    avg_order_value DECIMAL(10,2) DEFAULT 0.00,
    purchase_frequency DECIMAL(5,2) DEFAULT 0.00,

    -- Risk Assessment
    churn_probability DECIMAL(3,2) DEFAULT 0.00,
    health_score INTEGER CHECK (health_score BETWEEN 1 AND 100),

    -- Timestamps
    last_calculated TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 🏷️ Customer Tags for Segmentation
CREATE TABLE customer_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    tag_name VARCHAR(100) NOT NULL,
    tag_value VARCHAR(255),
    tag_type VARCHAR(50), -- system, manual, ai_generated
    confidence DECIMAL(3,2), -- for AI-generated tags
    created_by VARCHAR(100), -- user_id or 'system'
    created_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(customer_id, tag_name)
);

-- 📈 Customer Journey Tracking
CREATE TABLE customer_journey (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,

    -- Journey Stage
    stage VARCHAR(50) NOT NULL, -- awareness, consideration, purchase, retention, advocacy
    stage_entered TIMESTAMP DEFAULT NOW(),
    stage_duration INTERVAL,

    -- Touchpoints
    touchpoint_count INTEGER DEFAULT 0,
    last_touchpoint TIMESTAMP,

    -- Conversion Tracking
    conversion_event VARCHAR(100), -- quote_requested, service_booked, contract_signed
    conversion_value DECIMAL(10,2),

    -- Attribution
    attribution_source VARCHAR(100),
    attribution_medium VARCHAR(100),
    attribution_campaign VARCHAR(100),

    created_at TIMESTAMP DEFAULT NOW()
);

-- 🔍 Indexes for Performance
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_phone ON customers(primary_phone);
CREATE INDEX idx_customers_company ON customers(company);
CREATE INDEX idx_customers_type ON customers(customer_type);
CREATE INDEX idx_customers_status ON customers(status);

CREATE INDEX idx_interactions_customer ON customer_interactions(customer_id);
CREATE INDEX idx_interactions_timestamp ON customer_interactions(timestamp);
CREATE INDEX idx_interactions_type ON customer_interactions(interaction_type);
CREATE INDEX idx_interactions_priority ON customer_interactions(priority_level);

CREATE INDEX idx_phone_calls_number ON phone_calls(phone_number);
CREATE INDEX idx_phone_calls_direction ON phone_calls(call_direction);

CREATE INDEX idx_customer_phones_number ON customer_phones(phone_number);
CREATE INDEX idx_customer_emails_address ON customer_emails(email_address);

-- 📊 Views for Common Queries
CREATE VIEW customer_summary AS
SELECT
    c.id,
    c.name,
    c.email,
    c.primary_phone,
    c.company,
    c.customer_type,
    c.satisfaction_score,
    c.lifetime_value,
    ca.total_interactions,
    ca.avg_satisfaction,
    ca.churn_probability,
    c.last_contact_date,
    c.created_at
FROM customers c
LEFT JOIN customer_analytics ca ON c.id = ca.customer_id
WHERE c.status = 'active';

CREATE VIEW recent_interactions AS
SELECT
    ci.*,
    c.name as customer_name,
    c.email as customer_email,
    c.company as customer_company
FROM customer_interactions ci
JOIN customers c ON ci.customer_id = c.id
WHERE ci.timestamp >= NOW() - INTERVAL '30 days'
ORDER BY ci.timestamp DESC;
```

### **PHASE 1B: Go Service Implementation**

```go
// File: internal/customer/models.go
package customer

import (
    "time"
    "github.com/google/uuid"
    "gorm.io/gorm"
)

type Customer struct {
    ID                     uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
    Email                  string    `gorm:"unique;size:255"`
    PrimaryPhone           string    `gorm:"size:50"`
    Name                   string    `gorm:"size:255"`
    Company                string    `gorm:"size:255"`
    CustomerType           string    `gorm:"size:50"`
    CommunicationPreference string   `gorm:"size:50;default:email"`
    KnowledgeLevel         string    `gorm:"size:50;default:basic"`
    SatisfactionScore      float64   `gorm:"type:decimal(3,2);default:3.00"`
    LifetimeValue          float64   `gorm:"type:decimal(10,2);default:0.00"`

    // JSON fields
    Address          map[string]interface{} `gorm:"type:jsonb"`
    EmergencyContact map[string]interface{} `gorm:"type:jsonb"`

    // Relations
    Phones       []CustomerPhone       `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`
    Emails       []CustomerEmail       `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`
    Interactions []CustomerInteraction `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`
    Analytics    *CustomerAnalytics    `gorm:"foreignKey:CustomerID"`
    Tags         []CustomerTag         `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`

    // Metadata
    Tags_Array []string `gorm:"type:text[]"`
    Notes      string   `gorm:"type:text"`
    Status     string   `gorm:"size:50;default:active"`

    CreatedAt time.Time
    UpdatedAt time.Time
}

type CustomerInteraction struct {
    ID               uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
    CustomerID       uuid.UUID `gorm:"type:uuid;not null"`
    InteractionType  string    `gorm:"size:50;not null"`
    Direction        string    `gorm:"size:20;not null"`
    Channel          string    `gorm:"size:50"`
    Timestamp        time.Time `gorm:"default:now()"`

    // Content
    Subject string `gorm:"size:500"`
    Content string `gorm:"type:text"`
    Summary string `gorm:"type:text"`

    // Source
    SourceSystem        string `gorm:"size:50"`
    SourceID           string `gorm:"size:255"`
    ExternalReference  string `gorm:"size:255"`

    // AI Analysis
    Sentiment         string  `gorm:"size:50"`
    SentimentScore    float64 `gorm:"type:decimal(3,2)"`
    PriorityLevel     string  `gorm:"size:50"`
    UrgencyScore      int     `gorm:"check:urgency_score BETWEEN 1 AND 10"`
    HVACRelevance     bool    `gorm:"default:false"`
    ConfidenceScore   float64 `gorm:"type:decimal(3,2)"`

    // Business
    EstimatedValue      float64  `gorm:"type:decimal(10,2)"`
    ActualValue         float64  `gorm:"type:decimal(10,2)"`
    ServiceCategory     string   `gorm:"size:100"`
    EquipmentMentioned  []string `gorm:"type:text[]"`

    // Follow-up
    ActionItems         []string   `gorm:"type:text[]"`
    FollowUpRequired    bool       `gorm:"default:false"`
    FollowUpDate        *time.Time
    FollowUpCompleted   bool       `gorm:"default:false"`

    // Resolution
    ResolutionStatus     string     `gorm:"size:50"`
    ResolutionTime       *time.Duration
    CustomerSatisfaction *int       `gorm:"check:customer_satisfaction BETWEEN 1 AND 5"`

    // Relations
    PhoneCall *PhoneCall `gorm:"foreignKey:InteractionID"`

    CreatedAt time.Time
    UpdatedAt time.Time
}

type PhoneCall struct {
    ID                      uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
    InteractionID           uuid.UUID `gorm:"type:uuid;not null"`
    PhoneNumber             string    `gorm:"size:50;not null"`
    DurationSeconds         int
    CallDirection           string  `gorm:"size:20;not null"`
    CallStatus              string  `gorm:"size:50"`
    CallQualityScore        float64 `gorm:"type:decimal(3,2)"`

    // Transcription
    TranscriptionText       string  `gorm:"type:text"`
    TranscriptionConfidence float64 `gorm:"type:decimal(3,2)"`
    TranscriptionLanguage   string  `gorm:"size:10;default:en"`
    TranscriptionProvider   string  `gorm:"size:50"`

    // Analysis
    CallPurpose         string `gorm:"size:100"`
    TechnicalComplexity string `gorm:"size:50"`
    CustomerMood        string `gorm:"size:50"`

    // Business
    SalesOpportunity    bool   `gorm:"default:false"`
    UpsellPotential     string `gorm:"size:50"`
    CompetitiveMention  bool   `gorm:"default:false"`

    // Metadata
    RecordingAvailable bool   `gorm:"default:false"`
    RecordingURL       string `gorm:"size:500"`
    CallerID           string `gorm:"size:100"`

    CreatedAt time.Time
}
```

---

---

## 🎯 **IMPLEMENTATION STATUS**

### ✅ **COMPLETED COMPONENTS**

#### **📊 Customer Intelligence Foundation**
- ✅ **Database Schema** - Complete PostgreSQL schema with all tables
- ✅ **Customer Models** - Full GORM models with relations and hooks
- ✅ **Customer Service** - Comprehensive customer profile management
- ✅ **Customer Matcher** - AI-powered intelligent customer matching
- ✅ **Transcription Parser** - Multi-provider phone call transcription parsing

#### **🤖 AI Integration**
- ✅ **Gemma 3 Service** - Advanced AI analysis for customer intelligence
- ✅ **Email Intelligence** - Existing email analysis system
- ✅ **Call Analysis** - AI-powered phone call content analysis

#### **📧 Email Processing**
- ✅ **Multi-mailbox Support** - Gmail, Outlook, Business Email
- ✅ **Attachment Processing** - Excel, Text, PDF ready
- ✅ **Vector Database** - Semantic search capabilities

---

## 🚀 **NEXT IMPLEMENTATION STEPS**

### **WEEK 1-2: Database & Core Services**

```bash
# 1. Setup Customer Database
cd /home/<USER>/HVAC/GoBackend-Kratos

# Create migration
mkdir -p migrations
# Copy SQL schema from roadmap to migrations/001_customer_intelligence.sql

# 2. Build Customer Services
make build-customer-service

# 3. Test Customer Matching
make test-customer-matching
```

### **WEEK 3-4: Transcription Integration**

```bash
# 1. Configure Second Mailbox
# Add transcription mailbox to configs/email-intelligence.yaml

# 2. Build Transcription Parser
make build-transcription-parser

# 3. Test Call Processing
make test-call-processing
```

### **WEEK 5-6: Integration & Testing**

```bash
# 1. Integrate All Components
make build-complete-system

# 2. Test End-to-End Workflow
make test-e2e-customer-intelligence

# 3. Performance Testing
make test-performance
```

---

## 📋 **DETAILED WORKFLOW**

### **📧 Email Processing Workflow**
```
1. Email Retrieved → 2. Customer Matching → 3. Profile Creation/Update → 4. Interaction Logging → 5. Analytics Update
```

### **📞 Phone Call Workflow**
```
1. Transcription Email → 2. Parse Call Data → 3. Extract Customer Info → 4. AI Analysis → 5. Link to Profile → 6. Update Analytics
```

### **👤 Customer Profile Building**
```
1. Multi-source Data → 2. AI Enrichment → 3. Interaction Timeline → 4. Insights Generation → 5. Predictions → 6. 360° View
```

---

## 🎯 **BUSINESS IMPACT PROJECTIONS**

### **Customer Intelligence KPIs:**
- **Customer Match Accuracy**: 95%+ (AI-powered matching)
- **Profile Completeness**: 90%+ (automatic enrichment)
- **Response Time**: <2 minutes for critical calls
- **Customer Satisfaction**: +40% improvement
- **Churn Prediction**: 85%+ accuracy

### **Operational Efficiency:**
- **Email Processing**: 100% automated categorization
- **Call Logging**: Automatic transcription → profile linking
- **Customer Insights**: Real-time AI-powered analytics
- **Follow-up Management**: Automated action item extraction
- **Business Intelligence**: Predictive customer analytics

### **Revenue Impact:**
- **Customer Retention**: +25% (better service)
- **Upselling Success**: +30% (AI-powered opportunities)
- **Response Efficiency**: +50% (automated prioritization)
- **Operational Costs**: -20% (automation)

---

## 🔧 **CONFIGURATION EXAMPLES**

### **Transcription Mailbox Setup**
```yaml
# configs/email-intelligence.yaml
mailboxes:
  - name: "HVAC_Transcriptions"
    host: "imap.gmail.com"
    port: 993
    username: "${TRANSCRIPTION_EMAIL}"
    password: "${TRANSCRIPTION_PASSWORD}"
    folder: "INBOX"
    poll_interval: 1  # Check every minute for calls
    enabled: true
    processing_type: "phone_transcription"
```

### **Customer Intelligence Config**
```yaml
# Customer Intelligence Settings
customer_intelligence:
  auto_create_customers: true
  matching_threshold: 0.7
  enable_ai_enrichment: true
  default_satisfaction: 3.0
  analytics_update_frequency: "5m"

# Transcription Settings
transcription:
  supported_providers: ["whisper", "google", "azure", "rev"]
  default_language: "en"
  min_confidence: 0.7
  max_duration: 3600
  enable_ai_analysis: true
```

---

## 📊 **MONITORING & ANALYTICS**

### **Real-time Dashboards:**
- **Customer 360° View** - Complete customer profile with timeline
- **Call Analytics** - Phone call insights and trends
- **Email Intelligence** - Email processing metrics
- **Business Intelligence** - Revenue opportunities and predictions

### **Key Metrics:**
- **Processing Speed** - Emails/calls processed per minute
- **Accuracy Rates** - Customer matching and AI analysis accuracy
- **Customer Health** - Satisfaction trends and churn risk
- **Business Value** - Revenue opportunities and conversion rates

---

## 🚀 **READY TO REVOLUTIONIZE HVAC CRM!**

Ten plan integracji tworzy **najnowocześniejszy system Customer Intelligence** dla branży HVAC:

✅ **Automatyczne budowanie profili klientów** z emaili i rozmów
✅ **AI-powered customer matching** z 95%+ accuracy
✅ **Comprehensive interaction tracking** - wszystkie touchpoints
✅ **Real-time analytics** i predictive insights
✅ **Seamless integration** z istniejącym Email Intelligence
✅ **Scalable architecture** gotowa na przyszłe rozszerzenia

**Następny krok**: Implementacja database schema i rozpoczęcie budowy Customer Intelligence System! 🎯🚀💙
