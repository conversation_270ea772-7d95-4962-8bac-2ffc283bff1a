"""
🎤 STT Service Data Models
Pydantic models for the HVAC STT Service API
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class STTEngineType(str, Enum):
    """STT Engine types"""
    NEMO = "nemo"
    ELEVENLABS = "elevenlabs"


class STTEngineStatus(str, Enum):
    """STT Engine status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNAVAILABLE = "unavailable"
    INITIALIZING = "initializing"


class AudioFormat(str, Enum):
    """Supported audio formats"""
    M4A = "m4a"
    MP3 = "mp3"
    WAV = "wav"
    FLAC = "flac"


class TranscriptionRequest(BaseModel):
    """Request model for transcription"""
    audio_data: bytes = Field(..., description="Audio file data")
    filename: str = Field(..., description="Original filename")
    language: str = Field(default="pl", description="Language code")
    use_primary_only: bool = Field(default=False, description="Force primary engine only")
    include_timestamps: bool = Field(default=True, description="Include word timestamps")
    include_confidence: bool = Field(default=True, description="Include confidence scores")
    
    class Config:
        arbitrary_types_allowed = True


class WordTimestamp(BaseModel):
    """Word-level timestamp information"""
    word: str = Field(..., description="The word")
    start_time: float = Field(..., description="Start time in seconds")
    end_time: float = Field(..., description="End time in seconds")
    confidence: Optional[float] = Field(None, description="Confidence score 0-1")


class SpeakerSegment(BaseModel):
    """Speaker diarization segment"""
    speaker_id: str = Field(..., description="Speaker identifier")
    start_time: float = Field(..., description="Segment start time")
    end_time: float = Field(..., description="Segment end time")
    text: str = Field(..., description="Transcribed text for this speaker")
    confidence: Optional[float] = Field(None, description="Confidence score")


class TranscriptionMetadata(BaseModel):
    """Metadata about the transcription process"""
    engine_used: STTEngineType = Field(..., description="Engine that performed transcription")
    processing_time: float = Field(..., description="Processing time in seconds")
    audio_duration: Optional[float] = Field(None, description="Audio duration in seconds")
    sample_rate: Optional[int] = Field(None, description="Audio sample rate")
    channels: Optional[int] = Field(None, description="Number of audio channels")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    language_detected: Optional[str] = Field(None, description="Detected language")
    quality_score: Optional[float] = Field(None, description="Audio quality score 0-1")


class TranscriptionResponse(BaseModel):
    """Response model for transcription"""
    success: bool = Field(..., description="Whether transcription succeeded")
    transcript: str = Field(..., description="Full transcribed text")
    confidence: Optional[float] = Field(None, description="Overall confidence score")
    
    # Detailed information
    words: Optional[List[WordTimestamp]] = Field(None, description="Word-level timestamps")
    speakers: Optional[List[SpeakerSegment]] = Field(None, description="Speaker segments")
    
    # Metadata
    metadata: TranscriptionMetadata = Field(..., description="Transcription metadata")
    
    # Error information
    error: Optional[str] = Field(None, description="Error message if failed")
    fallback_used: bool = Field(default=False, description="Whether fallback engine was used")
    
    # Timestamps
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('Confidence must be between 0 and 1')
        return v


class EngineHealthStatus(BaseModel):
    """Health status for individual engine"""
    status: STTEngineStatus = Field(..., description="Engine status")
    available: bool = Field(..., description="Whether engine is available")
    last_used: Optional[datetime] = Field(None, description="Last time engine was used")
    total_requests: int = Field(default=0, description="Total requests processed")
    successful_requests: int = Field(default=0, description="Successful requests")
    average_processing_time: Optional[float] = Field(None, description="Average processing time")
    error_rate: Optional[float] = Field(None, description="Error rate percentage")
    last_error: Optional[str] = Field(None, description="Last error message")


class SystemMetrics(BaseModel):
    """System performance metrics"""
    cpu_usage: float = Field(..., description="CPU usage percentage")
    memory_usage: float = Field(..., description="Memory usage percentage")
    gpu_usage: Optional[float] = Field(None, description="GPU usage percentage")
    gpu_memory: Optional[float] = Field(None, description="GPU memory usage percentage")
    disk_usage: float = Field(..., description="Disk usage percentage")
    uptime: float = Field(..., description="Service uptime in seconds")


class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Overall service status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    engines: Dict[str, EngineHealthStatus] = Field(..., description="Engine health status")
    system_metrics: SystemMetrics = Field(..., description="System metrics")
    version: str = Field(..., description="Service version")


class MetricsResponse(BaseModel):
    """Metrics response"""
    total_requests: int = Field(..., description="Total transcription requests")
    successful_requests: int = Field(..., description="Successful requests")
    failed_requests: int = Field(..., description="Failed requests")
    
    # Engine usage
    nemo_requests: int = Field(default=0, description="Requests processed by NeMo")
    elevenlabs_requests: int = Field(default=0, description="Requests processed by ElevenLabs")
    
    # Performance metrics
    average_processing_time: float = Field(..., description="Average processing time")
    total_processing_time: float = Field(..., description="Total processing time")
    total_audio_processed: float = Field(..., description="Total audio duration processed")
    
    # System metrics
    system: SystemMetrics = Field(..., description="Current system metrics")
    
    # Time ranges
    last_hour_requests: int = Field(default=0, description="Requests in last hour")
    last_day_requests: int = Field(default=0, description="Requests in last day")
    
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Metrics timestamp")


class CoordinatorConfig(BaseModel):
    """Configuration for dual engine coordinator"""
    primary_engine: STTEngineType = Field(default=STTEngineType.NEMO, description="Primary engine")
    fallback_enabled: bool = Field(default=True, description="Enable fallback to backup engine")
    fallback_threshold: float = Field(default=0.5, description="Confidence threshold for fallback")
    max_retries: int = Field(default=2, description="Maximum retry attempts")
    timeout_seconds: float = Field(default=30.0, description="Request timeout")
    health_check_interval: int = Field(default=60, description="Health check interval in seconds")


class NeMoConfig(BaseModel):
    """Configuration for NVIDIA NeMo engine"""
    model_path: str = Field(..., description="Path to NeMo model file")
    device: str = Field(default="cuda", description="Device to use (cuda/cpu)")
    batch_size: int = Field(default=1, description="Batch size for processing")
    beam_size: int = Field(default=1, description="Beam size for decoding")
    return_timestamps: bool = Field(default=True, description="Return word timestamps")
    preserve_alignment: bool = Field(default=True, description="Preserve alignment info")


class ElevenLabsConfig(BaseModel):
    """Configuration for ElevenLabs engine"""
    api_key: str = Field(..., description="ElevenLabs API key")
    api_url: str = Field(default="https://api.elevenlabs.io/v1/speech-to-text", description="API URL")
    timeout: float = Field(default=60.0, description="Request timeout")
    max_file_size: int = Field(default=25 * 1024 * 1024, description="Max file size (25MB)")
    supported_formats: List[str] = Field(default=["m4a", "mp3", "wav"], description="Supported formats")


class AudioConfig(BaseModel):
    """Configuration for audio processing"""
    max_file_size: int = Field(default=50 * 1024 * 1024, description="Max file size (50MB)")
    supported_formats: List[AudioFormat] = Field(
        default=[AudioFormat.M4A, AudioFormat.MP3, AudioFormat.WAV, AudioFormat.FLAC],
        description="Supported audio formats"
    )
    target_sample_rate: int = Field(default=16000, description="Target sample rate for processing")
    normalize_audio: bool = Field(default=True, description="Normalize audio levels")
    remove_silence: bool = Field(default=False, description="Remove silence from audio")
    temp_dir: str = Field(default="/tmp/stt", description="Temporary directory for processing")
