"""
🎤 NVIDIA NeMo STT Engine
Primary STT engine using NVIDIA NeMo FastConformer for Polish language
"""

import asyncio
import os
import tempfile
import time
from typing import Optional, Dict, Any, List
import logging

import torch
import nemo.collections.asr as nemo_asr
import structlog

from models.stt_models import (
    TranscriptionRequest,
    TranscriptionResponse,
    TranscriptionMetadata,
    WordTimestamp,
    STTEngineType,
    STTEngineStatus,
    EngineHealthStatus,
    NeMoConfig
)
from utils.audio_processor import AudioProcessor

logger = structlog.get_logger(__name__)


class NeMoSTTEngine:
    """NVIDIA NeMo FastConformer STT Engine"""
    
    def __init__(self, config: NeMoConfig):
        self.config = config
        self.model = None
        self.device = config.device
        self.is_initialized = False
        self.last_used = None
        self.total_requests = 0
        self.successful_requests = 0
        self.total_processing_time = 0.0
        self.last_error = None
        
        # Audio processor for preprocessing
        self.audio_processor = AudioProcessor()
        
        logger.info(
            "🧠 Initializing NVIDIA NeMo STT Engine",
            model_path=config.model_path,
            device=config.device
        )
    
    async def initialize(self) -> bool:
        """Initialize the NeMo model"""
        try:
            logger.info("🔄 Loading NVIDIA NeMo model...")
            
            # Check if CUDA is available
            if self.device == "cuda" and not torch.cuda.is_available():
                logger.warning("⚠️ CUDA not available, falling back to CPU")
                self.device = "cpu"
            
            # Load the model
            if os.path.exists(self.config.model_path):
                self.model = nemo_asr.models.ASRModel.restore_from(
                    restore_path=self.config.model_path,
                    map_location=self.device
                )
            else:
                # Try to load from NGC
                logger.info("📥 Downloading model from NGC...")
                self.model = nemo_asr.models.ASRModel.from_pretrained(
                    "stt_pl_fastconformer_hybrid_large_pc"
                )
            
            # Move model to device
            if self.device == "cuda":
                self.model = self.model.cuda()
            else:
                self.model = self.model.cpu()
            
            # Set model to evaluation mode
            self.model.eval()
            
            # Configure model for timestamps if supported
            if hasattr(self.model, 'cfg') and hasattr(self.model.cfg, 'preprocessor'):
                self.model.cfg.preprocessor.normalize = "per_feature"
            
            self.is_initialized = True
            logger.info("✅ NVIDIA NeMo model loaded successfully")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error("❌ Failed to initialize NeMo model", error=str(e))
            return False
    
    async def transcribe(self, request: TranscriptionRequest) -> TranscriptionResponse:
        """Transcribe audio using NeMo model"""
        if not self.is_initialized:
            raise RuntimeError("NeMo engine not initialized")
        
        start_time = time.time()
        self.total_requests += 1
        self.last_used = time.time()
        
        try:
            logger.info(
                "🎤 Starting NeMo transcription",
                filename=request.filename,
                language=request.language
            )
            
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                # Convert audio to WAV format if needed
                processed_audio = await self.audio_processor.convert_to_wav(
                    request.audio_data,
                    request.filename
                )
                temp_file.write(processed_audio)
                temp_file_path = temp_file.name
            
            try:
                # Perform transcription
                transcripts = self.model.transcribe([temp_file_path])
                
                if not transcripts or len(transcripts) == 0:
                    raise ValueError("No transcription result")
                
                transcript = transcripts[0]
                
                # Get word-level timestamps if available
                words = []
                if request.include_timestamps and hasattr(self.model, 'transcribe_with_timestamps'):
                    try:
                        # Try to get detailed timestamps
                        detailed_result = self.model.transcribe_with_timestamps([temp_file_path])
                        if detailed_result and len(detailed_result) > 0:
                            words = self._extract_word_timestamps(detailed_result[0])
                    except Exception as e:
                        logger.warning("Failed to extract timestamps", error=str(e))
                
                # Calculate confidence (NeMo doesn't always provide this)
                confidence = self._estimate_confidence(transcript)
                
                # Get audio metadata
                audio_duration = await self.audio_processor.get_audio_duration(processed_audio)
                
                processing_time = time.time() - start_time
                self.total_processing_time += processing_time
                self.successful_requests += 1
                
                metadata = TranscriptionMetadata(
                    engine_used=STTEngineType.NEMO,
                    processing_time=processing_time,
                    audio_duration=audio_duration,
                    sample_rate=16000,  # NeMo typically uses 16kHz
                    channels=1,
                    file_size=len(request.audio_data),
                    language_detected=request.language,
                    quality_score=confidence
                )
                
                logger.info(
                    "✅ NeMo transcription completed",
                    filename=request.filename,
                    processing_time=processing_time,
                    transcript_length=len(transcript),
                    confidence=confidence
                )
                
                return TranscriptionResponse(
                    success=True,
                    transcript=transcript,
                    confidence=confidence,
                    words=words if words else None,
                    metadata=metadata
                )
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
        
        except Exception as e:
            processing_time = time.time() - start_time
            self.last_error = str(e)
            
            logger.error(
                "❌ NeMo transcription failed",
                filename=request.filename,
                error=str(e),
                processing_time=processing_time
            )
            
            metadata = TranscriptionMetadata(
                engine_used=STTEngineType.NEMO,
                processing_time=processing_time,
                file_size=len(request.audio_data)
            )
            
            return TranscriptionResponse(
                success=False,
                transcript="",
                error=str(e),
                metadata=metadata
            )
    
    def _extract_word_timestamps(self, detailed_result: Any) -> List[WordTimestamp]:
        """Extract word-level timestamps from NeMo result"""
        words = []
        
        try:
            # This depends on the specific NeMo model output format
            # Implementation may vary based on model version
            if hasattr(detailed_result, 'words'):
                for word_info in detailed_result.words:
                    words.append(WordTimestamp(
                        word=word_info.word,
                        start_time=word_info.start_time,
                        end_time=word_info.end_time,
                        confidence=getattr(word_info, 'confidence', None)
                    ))
            
        except Exception as e:
            logger.warning("Failed to extract word timestamps", error=str(e))
        
        return words
    
    def _estimate_confidence(self, transcript: str) -> float:
        """Estimate confidence score based on transcript characteristics"""
        if not transcript or len(transcript.strip()) == 0:
            return 0.0
        
        # Simple heuristic - longer transcripts with proper punctuation
        # tend to have higher confidence
        base_confidence = 0.7
        
        # Boost for longer transcripts
        length_boost = min(0.2, len(transcript) / 1000)
        
        # Boost for proper punctuation
        punct_boost = 0.1 if any(p in transcript for p in '.!?') else 0.0
        
        return min(1.0, base_confidence + length_boost + punct_boost)
    
    async def get_status(self) -> EngineHealthStatus:
        """Get engine health status"""
        if not self.is_initialized:
            status = STTEngineStatus.UNAVAILABLE
            available = False
        elif self.device == "cuda" and not torch.cuda.is_available():
            status = STTEngineStatus.DEGRADED
            available = True
        else:
            status = STTEngineStatus.HEALTHY
            available = True
        
        error_rate = 0.0
        if self.total_requests > 0:
            error_rate = ((self.total_requests - self.successful_requests) / self.total_requests) * 100
        
        avg_processing_time = None
        if self.successful_requests > 0:
            avg_processing_time = self.total_processing_time / self.successful_requests
        
        return EngineHealthStatus(
            status=status,
            available=available,
            last_used=self.last_used,
            total_requests=self.total_requests,
            successful_requests=self.successful_requests,
            average_processing_time=avg_processing_time,
            error_rate=error_rate,
            last_error=self.last_error
        )
    
    async def shutdown(self):
        """Shutdown the engine"""
        logger.info("🛑 Shutting down NeMo engine...")
        
        if self.model:
            # Clear GPU memory
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            del self.model
            self.model = None
        
        self.is_initialized = False
        logger.info("✅ NeMo engine shutdown complete")
    
    def is_available(self) -> bool:
        """Check if engine is available"""
        return self.is_initialized and self.model is not None
