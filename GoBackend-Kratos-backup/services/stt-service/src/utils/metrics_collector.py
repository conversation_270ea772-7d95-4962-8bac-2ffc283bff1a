"""
📊 Metrics Collector
Collects and manages metrics for the STT service
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
import psutil
import structlog

from models.stt_models import (
    MetricsResponse,
    SystemMetrics,
    EngineHealthStatus,
    STTEngineType
)

logger = structlog.get_logger(__name__)


class MetricsCollector:
    """Collects and manages STT service metrics"""
    
    def __init__(self):
        self.start_time = time.time()
        
        # Request metrics
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        
        # Engine-specific metrics
        self.engine_requests = defaultdict(int)
        self.engine_processing_times = defaultdict(list)
        
        # Processing time tracking
        self.total_processing_time = 0.0
        self.processing_times = deque(maxlen=1000)  # Keep last 1000 requests
        
        # Audio metrics
        self.total_audio_processed = 0.0  # Total duration in seconds
        self.audio_file_sizes = deque(maxlen=1000)
        
        # Time-based metrics
        self.hourly_requests = deque(maxlen=24)  # Last 24 hours
        self.daily_requests = deque(maxlen=30)   # Last 30 days
        
        # Health check metrics
        self.last_health_check = None
        self.health_check_history = deque(maxlen=100)
        
        logger.info("📊 Metrics Collector initialized")
    
    async def record_transcription(
        self,
        engine_used: str,
        processing_time: float,
        success: bool,
        file_size: int,
        audio_duration: Optional[float] = None
    ):
        """Record transcription metrics"""
        self.total_requests += 1
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # Engine metrics
        self.engine_requests[engine_used] += 1
        self.engine_processing_times[engine_used].append(processing_time)
        
        # Processing time metrics
        self.total_processing_time += processing_time
        self.processing_times.append(processing_time)
        
        # Audio metrics
        self.audio_file_sizes.append(file_size)
        if audio_duration:
            self.total_audio_processed += audio_duration
        
        # Time-based metrics
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        self._update_time_metrics(current_hour)
        
        logger.debug(
            "📈 Transcription metrics recorded",
            engine=engine_used,
            processing_time=processing_time,
            success=success,
            total_requests=self.total_requests
        )
    
    def _update_time_metrics(self, current_hour: datetime):
        """Update hourly and daily metrics"""
        # Update hourly metrics
        if not self.hourly_requests or self.hourly_requests[-1][0] != current_hour:
            self.hourly_requests.append((current_hour, 1))
        else:
            # Increment current hour count
            hour, count = self.hourly_requests[-1]
            self.hourly_requests[-1] = (hour, count + 1)
        
        # Update daily metrics
        current_day = current_hour.replace(hour=0)
        if not self.daily_requests or self.daily_requests[-1][0] != current_day:
            self.daily_requests.append((current_day, 1))
        else:
            # Increment current day count
            day, count = self.daily_requests[-1]
            self.daily_requests[-1] = (day, count + 1)
    
    async def record_health_check(
        self,
        primary_status: EngineHealthStatus,
        backup_status: EngineHealthStatus
    ):
        """Record health check results"""
        self.last_health_check = datetime.utcnow()
        
        health_record = {
            "timestamp": self.last_health_check,
            "primary_available": primary_status.available,
            "backup_available": backup_status.available,
            "primary_status": primary_status.status,
            "backup_status": backup_status.status
        }
        
        self.health_check_history.append(health_record)
        
        logger.debug(
            "💓 Health check recorded",
            primary_status=primary_status.status,
            backup_status=backup_status.status
        )
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system metrics"""
        try:
            # CPU and memory
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # GPU metrics (if available)
            gpu_usage = None
            gpu_memory = None
            
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                
                # GPU utilization
                gpu_util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_usage = gpu_util.gpu
                
                # GPU memory
                gpu_mem = pynvml.nvmlDeviceGetMemoryInfo(handle)
                gpu_memory = (gpu_mem.used / gpu_mem.total) * 100
                
            except Exception:
                # GPU metrics not available
                pass
            
            uptime = time.time() - self.start_time
            
            return SystemMetrics(
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                gpu_usage=gpu_usage,
                gpu_memory=gpu_memory,
                disk_usage=disk.percent,
                uptime=uptime
            )
            
        except Exception as e:
            logger.warning("Failed to get system metrics", error=str(e))
            return SystemMetrics(
                cpu_usage=0.0,
                memory_usage=0.0,
                disk_usage=0.0,
                uptime=time.time() - self.start_time
            )
    
    def get_all_metrics(self) -> MetricsResponse:
        """Get comprehensive metrics response"""
        # Calculate averages
        avg_processing_time = 0.0
        if self.processing_times:
            avg_processing_time = sum(self.processing_times) / len(self.processing_times)
        
        # Get recent metrics
        last_hour_requests = self._get_recent_requests(hours=1)
        last_day_requests = self._get_recent_requests(hours=24)
        
        # System metrics
        system_metrics = self.get_system_metrics()
        
        return MetricsResponse(
            total_requests=self.total_requests,
            successful_requests=self.successful_requests,
            failed_requests=self.failed_requests,
            nemo_requests=self.engine_requests.get("nemo", 0),
            elevenlabs_requests=self.engine_requests.get("elevenlabs", 0),
            average_processing_time=avg_processing_time,
            total_processing_time=self.total_processing_time,
            total_audio_processed=self.total_audio_processed,
            system=system_metrics,
            last_hour_requests=last_hour_requests,
            last_day_requests=last_day_requests
        )
    
    def _get_recent_requests(self, hours: int) -> int:
        """Get request count for recent time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        count = 0
        
        for timestamp, requests in reversed(self.hourly_requests):
            if timestamp >= cutoff_time:
                count += requests
            else:
                break
        
        return count
    
    def get_engine_metrics(self, engine: str) -> Dict[str, Any]:
        """Get metrics for specific engine"""
        processing_times = self.engine_processing_times.get(engine, [])
        
        metrics = {
            "total_requests": self.engine_requests.get(engine, 0),
            "average_processing_time": 0.0,
            "min_processing_time": 0.0,
            "max_processing_time": 0.0,
            "success_rate": 0.0
        }
        
        if processing_times:
            metrics["average_processing_time"] = sum(processing_times) / len(processing_times)
            metrics["min_processing_time"] = min(processing_times)
            metrics["max_processing_time"] = max(processing_times)
        
        return metrics
    
    def get_current_timestamp(self) -> datetime:
        """Get current timestamp"""
        return datetime.utcnow()
    
    def reset_metrics(self):
        """Reset all metrics (for testing)"""
        logger.warning("🔄 Resetting all metrics")
        
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.engine_requests.clear()
        self.engine_processing_times.clear()
        self.total_processing_time = 0.0
        self.processing_times.clear()
        self.total_audio_processed = 0.0
        self.audio_file_sizes.clear()
        self.hourly_requests.clear()
        self.daily_requests.clear()
        self.health_check_history.clear()
        
        self.start_time = time.time()
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health check summary"""
        if not self.health_check_history:
            return {"status": "no_data"}
        
        recent_checks = list(self.health_check_history)[-10:]  # Last 10 checks
        
        primary_availability = sum(1 for check in recent_checks if check["primary_available"]) / len(recent_checks)
        backup_availability = sum(1 for check in recent_checks if check["backup_available"]) / len(recent_checks)
        
        return {
            "last_check": self.last_health_check,
            "total_checks": len(self.health_check_history),
            "primary_availability": primary_availability,
            "backup_availability": backup_availability,
            "recent_checks": len(recent_checks)
        }
