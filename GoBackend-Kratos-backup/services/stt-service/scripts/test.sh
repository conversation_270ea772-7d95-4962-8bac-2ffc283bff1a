#!/bin/bash

# 🧪 HVAC STT Service Test Script
# Comprehensive testing for the dual-engine STT service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_URL="http://localhost:8085"
TEST_AUDIO_DIR="test_audio"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create test audio files
create_test_audio() {
    log_info "🎵 Creating test audio files..."
    
    mkdir -p "$TEST_AUDIO_DIR"
    
    # Create a simple test audio file using ffmpeg (if available)
    if command -v ffmpeg &> /dev/null; then
        # Generate 5-second sine wave audio
        ffmpeg -f lavfi -i "sine=frequency=440:duration=5" -ac 1 -ar 16000 "$TEST_AUDIO_DIR/test_sine.wav" -y &> /dev/null
        
        # Convert to M4A for testing
        ffmpeg -i "$TEST_AUDIO_DIR/test_sine.wav" -c:a aac "$TEST_AUDIO_DIR/test_sine.m4a" -y &> /dev/null
        
        log_success "Test audio files created"
    else
        log_warning "ffmpeg not available - using mock audio data"
        # Create dummy files for testing
        echo "dummy audio data" > "$TEST_AUDIO_DIR/test_dummy.wav"
        echo "dummy audio data" > "$TEST_AUDIO_DIR/test_dummy.m4a"
    fi
}

# Test health endpoint
test_health() {
    log_info "💓 Testing health endpoint..."
    
    local response=$(curl -s -w "%{http_code}" "$SERVICE_URL/health" -o /tmp/health_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "Health check passed"
        
        # Show health details
        if command -v jq &> /dev/null; then
            log_info "Health details:"
            cat /tmp/health_response.json | jq '.'
        fi
    else
        log_error "Health check failed (HTTP $response)"
        return 1
    fi
}

# Test engines status
test_engines_status() {
    log_info "🔧 Testing engines status..."
    
    local response=$(curl -s -w "%{http_code}" "$SERVICE_URL/engines/status" -o /tmp/engines_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "Engines status check passed"
        
        # Show engine details
        if command -v jq &> /dev/null; then
            log_info "Engine status:"
            cat /tmp/engines_response.json | jq '.'
        fi
    else
        log_error "Engines status check failed (HTTP $response)"
        return 1
    fi
}

# Test transcription with audio file
test_transcription() {
    local audio_file="$1"
    local test_name="$2"
    
    log_info "🎤 Testing transcription: $test_name"
    
    if [ ! -f "$audio_file" ]; then
        log_warning "Audio file not found: $audio_file"
        return 1
    fi
    
    local response=$(curl -s -w "%{http_code}" \
        -X POST \
        -F "audio_file=@$audio_file" \
        -F "language=pl" \
        -F "include_timestamps=true" \
        "$SERVICE_URL/transcribe" \
        -o /tmp/transcription_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "Transcription test passed: $test_name"
        
        # Show transcription details
        if command -v jq &> /dev/null; then
            local transcript=$(cat /tmp/transcription_response.json | jq -r '.transcript // "No transcript"')
            local engine=$(cat /tmp/transcription_response.json | jq -r '.metadata.engine_used // "Unknown"')
            local processing_time=$(cat /tmp/transcription_response.json | jq -r '.metadata.processing_time // "Unknown"')
            
            log_info "Results:"
            echo "  Engine: $engine"
            echo "  Processing time: ${processing_time}s"
            echo "  Transcript: $transcript"
        fi
    else
        log_error "Transcription test failed: $test_name (HTTP $response)"
        
        # Show error details
        if [ -f /tmp/transcription_response.json ]; then
            cat /tmp/transcription_response.json
        fi
        
        return 1
    fi
}

# Test metrics endpoint
test_metrics() {
    log_info "📊 Testing metrics endpoint..."
    
    local response=$(curl -s -w "%{http_code}" "$SERVICE_URL/metrics" -o /tmp/metrics_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "Metrics test passed"
        
        # Show metrics summary
        if command -v jq &> /dev/null; then
            local total_requests=$(cat /tmp/metrics_response.json | jq -r '.total_requests // 0')
            local successful_requests=$(cat /tmp/metrics_response.json | jq -r '.successful_requests // 0')
            local avg_processing_time=$(cat /tmp/metrics_response.json | jq -r '.average_processing_time // 0')
            
            log_info "Metrics summary:"
            echo "  Total requests: $total_requests"
            echo "  Successful requests: $successful_requests"
            echo "  Average processing time: ${avg_processing_time}s"
        fi
    else
        log_error "Metrics test failed (HTTP $response)"
        return 1
    fi
}

# Test engine switching
test_engine_switching() {
    log_info "🔄 Testing engine switching..."
    
    # Switch to ElevenLabs
    local response=$(curl -s -w "%{http_code}" \
        -X POST \
        "$SERVICE_URL/engines/switch" \
        -H "Content-Type: application/json" \
        -d '{"engine": "elevenlabs"}' \
        -o /tmp/switch_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "Engine switch to ElevenLabs successful"
        
        # Switch back to NeMo
        response=$(curl -s -w "%{http_code}" \
            -X POST \
            "$SERVICE_URL/engines/switch" \
            -H "Content-Type: application/json" \
            -d '{"engine": "nemo"}' \
            -o /tmp/switch_response.json)
        
        if [ "$response" = "200" ]; then
            log_success "Engine switch back to NeMo successful"
        else
            log_warning "Failed to switch back to NeMo"
        fi
    else
        log_warning "Engine switching test failed (HTTP $response)"
    fi
}

# Performance test
performance_test() {
    log_info "⚡ Running performance test..."
    
    local test_file="$TEST_AUDIO_DIR/test_sine.m4a"
    if [ ! -f "$test_file" ]; then
        test_file="$TEST_AUDIO_DIR/test_dummy.m4a"
    fi
    
    if [ ! -f "$test_file" ]; then
        log_warning "No test file available for performance test"
        return 1
    fi
    
    local start_time=$(date +%s)
    local requests=5
    local successful=0
    
    for i in $(seq 1 $requests); do
        log_info "Performance test request $i/$requests"
        
        if test_transcription "$test_file" "performance-$i" &> /dev/null; then
            successful=$((successful + 1))
        fi
        
        sleep 1  # Small delay between requests
    done
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    local success_rate=$((successful * 100 / requests))
    
    log_info "Performance test results:"
    echo "  Total requests: $requests"
    echo "  Successful: $successful"
    echo "  Success rate: $success_rate%"
    echo "  Total time: ${total_time}s"
    echo "  Average time per request: $((total_time / requests))s"
}

# Load test (optional)
load_test() {
    log_info "🔥 Running load test..."
    
    local test_file="$TEST_AUDIO_DIR/test_sine.m4a"
    if [ ! -f "$test_file" ]; then
        test_file="$TEST_AUDIO_DIR/test_dummy.m4a"
    fi
    
    if [ ! -f "$test_file" ]; then
        log_warning "No test file available for load test"
        return 1
    fi
    
    # Run concurrent requests
    local concurrent_requests=3
    local requests_per_worker=2
    
    log_info "Starting $concurrent_requests concurrent workers with $requests_per_worker requests each"
    
    for i in $(seq 1 $concurrent_requests); do
        (
            for j in $(seq 1 $requests_per_worker); do
                curl -s -X POST \
                    -F "audio_file=@$test_file" \
                    "$SERVICE_URL/transcribe" \
                    > /tmp/load_test_${i}_${j}.json &
            done
            wait
        ) &
    done
    
    wait
    log_success "Load test completed"
}

# Cleanup test files
cleanup() {
    log_info "🧹 Cleaning up test files..."
    
    rm -rf "$TEST_AUDIO_DIR"
    rm -f /tmp/*_response.json
    rm -f /tmp/load_test_*.json
    
    log_success "Cleanup completed"
}

# Main test function
main() {
    log_info "🧪 Starting HVAC STT Service tests..."
    echo
    
    # Check if service is running
    if ! curl -s "$SERVICE_URL/health" > /dev/null; then
        log_error "Service is not running at $SERVICE_URL"
        log_info "Please start the service first with: ./scripts/deploy.sh"
        exit 1
    fi
    
    # Create test data
    create_test_audio
    
    # Run tests
    local failed_tests=0
    
    test_health || failed_tests=$((failed_tests + 1))
    echo
    
    test_engines_status || failed_tests=$((failed_tests + 1))
    echo
    
    # Test transcription with different formats
    if [ -f "$TEST_AUDIO_DIR/test_sine.wav" ]; then
        test_transcription "$TEST_AUDIO_DIR/test_sine.wav" "WAV format" || failed_tests=$((failed_tests + 1))
        echo
    fi
    
    if [ -f "$TEST_AUDIO_DIR/test_sine.m4a" ]; then
        test_transcription "$TEST_AUDIO_DIR/test_sine.m4a" "M4A format" || failed_tests=$((failed_tests + 1))
        echo
    fi
    
    test_metrics || failed_tests=$((failed_tests + 1))
    echo
    
    test_engine_switching || failed_tests=$((failed_tests + 1))
    echo
    
    # Optional performance tests
    if [ "${1:-}" = "performance" ]; then
        performance_test
        echo
    fi
    
    if [ "${1:-}" = "load" ]; then
        load_test
        echo
    fi
    
    # Cleanup
    cleanup
    
    # Results
    if [ $failed_tests -eq 0 ]; then
        log_success "🎉 All tests passed!"
    else
        log_error "❌ $failed_tests test(s) failed"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "performance")
        main "performance"
        ;;
    "load")
        main "load"
        ;;
    "health")
        test_health
        ;;
    "engines")
        test_engines_status
        ;;
    "metrics")
        test_metrics
        ;;
    *)
        main
        ;;
esac
