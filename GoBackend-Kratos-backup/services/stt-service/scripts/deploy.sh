#!/bin/bash

# 🎤 HVAC STT Service Deployment Script
# Deploys the dual-engine STT service for HVAC CRM

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SERVICE_NAME="hvac-stt-service"
DOCKER_IMAGE="hvac-stt-service:latest"
NETWORK_NAME="hvac-network"

# Check prerequisites
check_prerequisites() {
    log_info "🔍 Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check NVIDIA Docker (for GPU support)
    if ! docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi &> /dev/null; then
        log_warning "NVIDIA Docker runtime not available - GPU acceleration disabled"
        export GPU_SUPPORT=false
    else
        log_success "NVIDIA Docker runtime available"
        export GPU_SUPPORT=true
    fi
    
    log_success "Prerequisites check completed"
}

# Create network if it doesn't exist
create_network() {
    log_info "🌐 Setting up Docker network..."
    
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        docker network create "$NETWORK_NAME"
        log_success "Created network: $NETWORK_NAME"
    else
        log_info "Network already exists: $NETWORK_NAME"
    fi
}

# Create necessary directories
create_directories() {
    log_info "📁 Creating directories..."
    
    mkdir -p logs
    mkdir -p temp
    mkdir -p config
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    
    # Set permissions
    chmod 755 logs temp
    
    log_success "Directories created"
}

# Build Docker image
build_image() {
    log_info "🔨 Building Docker image..."
    
    # Build with GPU support if available
    if [ "$GPU_SUPPORT" = "true" ]; then
        docker build -t "$DOCKER_IMAGE" .
    else
        # Build CPU-only version
        docker build -t "$DOCKER_IMAGE" --build-arg CUDA_VERSION=cpu .
    fi
    
    log_success "Docker image built: $DOCKER_IMAGE"
}

# Configure environment
configure_environment() {
    log_info "⚙️ Configuring environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        cat > .env << EOF
# HVAC STT Service Environment Configuration

# ElevenLabs API Key (required for backup engine)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Service Configuration
STT_SERVICE_HOST=0.0.0.0
STT_SERVICE_PORT=8085
LOG_LEVEL=INFO

# GPU Configuration
CUDA_VISIBLE_DEVICES=0

# Integration URLs
HVAC_BACKEND_URL=http://hvac-backend:8080
EMAIL_SERVICE_URL=http://email-intelligence:8082
EOF
        log_warning "Created .env file - please configure your ElevenLabs API key"
    fi
    
    log_success "Environment configured"
}

# Deploy service
deploy_service() {
    log_info "🚀 Deploying STT service..."
    
    # Stop existing service
    docker-compose down || true
    
    # Start services
    if [ "$1" = "monitoring" ]; then
        docker-compose --profile monitoring up -d
        log_info "Started with monitoring stack"
    else
        docker-compose up -d
        log_info "Started core services only"
    fi
    
    log_success "STT service deployed"
}

# Wait for service to be ready
wait_for_service() {
    log_info "⏳ Waiting for service to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8085/health &> /dev/null; then
            log_success "Service is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for service..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    log_error "Service failed to start within timeout"
    return 1
}

# Test service
test_service() {
    log_info "🧪 Testing service..."
    
    # Test health endpoint
    if curl -f http://localhost:8085/health; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        return 1
    fi
    
    # Test engines status
    if curl -f http://localhost:8085/engines/status; then
        log_success "Engines status check passed"
    else
        log_warning "Engines status check failed"
    fi
    
    log_success "Service tests completed"
}

# Show service status
show_status() {
    log_info "📊 Service Status:"
    echo
    
    # Docker containers
    docker-compose ps
    echo
    
    # Service logs (last 20 lines)
    log_info "Recent logs:"
    docker-compose logs --tail=20 stt-service
    echo
    
    # Service endpoints
    log_info "Service endpoints:"
    echo "  Health: http://localhost:8085/health"
    echo "  Metrics: http://localhost:8085/metrics"
    echo "  Engines: http://localhost:8085/engines/status"
    echo "  API Docs: http://localhost:8085/docs"
    
    if docker-compose ps | grep -q grafana; then
        echo "  Grafana: http://localhost:3001 (admin/admin)"
    fi
}

# Main deployment function
main() {
    log_info "🎤 Starting HVAC STT Service deployment..."
    echo
    
    check_prerequisites
    create_network
    create_directories
    configure_environment
    build_image
    deploy_service "$1"
    
    if wait_for_service; then
        test_service
        show_status
        
        log_success "🎉 HVAC STT Service deployed successfully!"
        echo
        log_info "Next steps:"
        echo "  1. Configure your ElevenLabs API key in .env file"
        echo "  2. Test transcription with: curl -X POST -F 'audio_file=@test.m4a' http://localhost:8085/transcribe"
        echo "  3. Monitor logs with: docker-compose logs -f stt-service"
    else
        log_error "Deployment failed"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "monitoring")
        main "monitoring"
        ;;
    "stop")
        log_info "🛑 Stopping STT service..."
        docker-compose down
        log_success "Service stopped"
        ;;
    "restart")
        log_info "🔄 Restarting STT service..."
        docker-compose restart
        log_success "Service restarted"
        ;;
    "logs")
        docker-compose logs -f stt-service
        ;;
    "status")
        show_status
        ;;
    *)
        main
        ;;
esac
