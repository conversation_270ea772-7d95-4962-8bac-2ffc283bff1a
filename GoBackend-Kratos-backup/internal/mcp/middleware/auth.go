package middleware

import (
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"
)

// 🔐 Authentication Service for MCP Tools
// Integrates with Kratos framework for secure AI agent access
type AuthService struct {
	logger *log.Helper
	// Add Kratos client when available
	// kratosClient *kratos.Client
}

// User represents an authenticated user
type User struct {
	ID          string    `json:"id"`
	Email       string    `json:"email"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	IsActive    bool      `json:"is_active"`
	LastLogin   time.Time `json:"last_login"`
}

// Session represents an active user session
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	IsActive  bool      `json:"is_active"`
}

// Permission represents a system permission
type Permission struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// NewAuthService creates a new authentication service
func NewAuthService(logger *log.Helper) *AuthService {
	return &AuthService{
		logger: logger,
	}
}

// ValidateToken validates a JWT token and returns user information
func (a *AuthService) ValidateToken(token string) (*User, error) {
	if token == "" {
		return nil, fmt.Errorf("token is required")
	}

	// Remove Bearer prefix if present
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// TODO: Integrate with Kratos for actual token validation
	// For now, implement basic validation logic

	// Mock user for development - replace with actual Kratos integration
	if token == "dev-token" {
		return &User{
			ID:        "dev-user-1",
			Email:     "<EMAIL>",
			FirstName: "Admin",
			LastName:  "User",
			Role:      "admin",
			Permissions: []string{
				"customer:read", "customer:write", "customer:delete",
				"job:read", "job:write", "job:delete",
				"email:read", "email:write",
				"analytics:read",
				"workflow:read", "workflow:write",
				"system:read", "system:write",
			},
			IsActive:  true,
			LastLogin: time.Now(),
		}, nil
	}

	a.logger.Warn("Invalid token provided", zap.String("token_prefix", token[:min(len(token), 10)]))
	return nil, fmt.Errorf("invalid token")
}

// CheckPermission checks if a user has a specific permission
func (a *AuthService) CheckPermission(user *User, resource, action string) bool {
	if user == nil {
		return false
	}

	// Admin users have all permissions
	if user.Role == "admin" {
		return true
	}

	// Check specific permission
	requiredPermission := fmt.Sprintf("%s:%s", resource, action)
	for _, permission := range user.Permissions {
		if permission == requiredPermission {
			return true
		}
	}

	a.logger.Warn("Permission denied",
		zap.String("user_id", user.ID),
		zap.String("resource", resource),
		zap.String("action", action),
	)

	return false
}

// CreateSession creates a new user session
func (a *AuthService) CreateSession(userID string) (*Session, error) {
	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	// TODO: Integrate with Kratos session management
	session := &Session{
		ID:        fmt.Sprintf("session_%d", time.Now().UnixNano()),
		UserID:    userID,
		Token:     fmt.Sprintf("token_%d", time.Now().UnixNano()),
		ExpiresAt: time.Now().Add(24 * time.Hour),
		CreatedAt: time.Now(),
		IsActive:  true,
	}

	a.logger.Info("Session created",
		zap.String("session_id", session.ID),
		zap.String("user_id", userID),
	)

	return session, nil
}

// ValidateSession validates a session token
func (a *AuthService) ValidateSession(sessionToken string) (*Session, error) {
	if sessionToken == "" {
		return nil, fmt.Errorf("session token is required")
	}

	// TODO: Implement actual session validation with Kratos
	// For now, return mock session for development
	if strings.HasPrefix(sessionToken, "token_") {
		return &Session{
			ID:        "session_123",
			UserID:    "dev-user-1",
			Token:     sessionToken,
			ExpiresAt: time.Now().Add(24 * time.Hour),
			CreatedAt: time.Now().Add(-1 * time.Hour),
			IsActive:  true,
		}, nil
	}

	return nil, fmt.Errorf("invalid session token")
}

// RevokeSession revokes a user session
func (a *AuthService) RevokeSession(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID is required")
	}

	// TODO: Implement actual session revocation with Kratos
	a.logger.Info("Session revoked", zap.String("session_id", sessionID))
	return nil
}

// GetUserPermissions returns all permissions for a user
func (a *AuthService) GetUserPermissions(userID string) ([]Permission, error) {
	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	// TODO: Implement actual permission retrieval from Kratos
	// For now, return mock permissions based on role
	permissions := []Permission{
		{Name: "customer:read", Description: "Read customer data", Resource: "customer", Action: "read"},
		{Name: "customer:write", Description: "Create/update customer data", Resource: "customer", Action: "write"},
		{Name: "job:read", Description: "Read job data", Resource: "job", Action: "read"},
		{Name: "job:write", Description: "Create/update job data", Resource: "job", Action: "write"},
		{Name: "email:read", Description: "Read email data", Resource: "email", Action: "read"},
		{Name: "analytics:read", Description: "Read analytics data", Resource: "analytics", Action: "read"},
	}

	return permissions, nil
}

// CreateUser creates a new user (admin only)
func (a *AuthService) CreateUser(email, password, firstName, lastName, role string) (*User, error) {
	if email == "" || password == "" || firstName == "" || lastName == "" {
		return nil, fmt.Errorf("all fields are required")
	}

	// Validate role
	validRoles := map[string]bool{
		"admin":            true,
		"manager":          true,
		"technician":       true,
		"customer_service": true,
	}

	if !validRoles[role] {
		return nil, fmt.Errorf("invalid role: %s", role)
	}

	// TODO: Integrate with Kratos user creation
	user := &User{
		ID:        fmt.Sprintf("user_%d", time.Now().UnixNano()),
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Role:      role,
		IsActive:  true,
		LastLogin: time.Time{},
	}

	// Set permissions based on role
	user.Permissions = a.getPermissionsForRole(role)

	a.logger.Info("User created",
		zap.String("user_id", user.ID),
		zap.String("email", email),
		zap.String("role", role),
	)

	return user, nil
}

// UpdateUserRole updates a user's role and permissions
func (a *AuthService) UpdateUserRole(userID, newRole string) error {
	if userID == "" || newRole == "" {
		return fmt.Errorf("user ID and role are required")
	}

	// Validate role
	validRoles := map[string]bool{
		"admin":            true,
		"manager":          true,
		"technician":       true,
		"customer_service": true,
	}

	if !validRoles[newRole] {
		return fmt.Errorf("invalid role: %s", newRole)
	}

	// TODO: Implement actual user role update with Kratos
	a.logger.Info("User role updated",
		zap.String("user_id", userID),
		zap.String("new_role", newRole),
	)

	return nil
}

// getPermissionsForRole returns permissions based on user role
func (a *AuthService) getPermissionsForRole(role string) []string {
	switch role {
	case "admin":
		return []string{
			"customer:read", "customer:write", "customer:delete",
			"job:read", "job:write", "job:delete",
			"email:read", "email:write",
			"analytics:read", "analytics:write",
			"workflow:read", "workflow:write",
			"system:read", "system:write",
			"user:read", "user:write", "user:delete",
		}
	case "manager":
		return []string{
			"customer:read", "customer:write",
			"job:read", "job:write",
			"email:read", "email:write",
			"analytics:read",
			"workflow:read", "workflow:write",
		}
	case "technician":
		return []string{
			"customer:read",
			"job:read", "job:write",
			"email:read",
		}
	case "customer_service":
		return []string{
			"customer:read", "customer:write",
			"job:read",
			"email:read", "email:write",
		}
	default:
		return []string{}
	}
}

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
