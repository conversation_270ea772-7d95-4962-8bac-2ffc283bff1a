package tools

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	mcp_golang "github.com/metoro-io/mcp-golang"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/mcp/types"
)

// 👥 Customer Management Tools for MCP
// Comprehensive customer lifecycle management for HVAC CRM
type CustomerTools struct {
	customerUc *biz.CustomerUsecase
	validator  *validator.Validate
	logger     *log.Helper
}

// NewCustomerTools creates a new customer tools instance
func NewCustomerTools(customerUc *biz.CustomerUsecase, validator *validator.Validate, logger log.Logger) *CustomerTools {
	return &CustomerTools{
		customerUc: customerUc,
		validator:  validator,
		logger:     log.NewHelper(logger),
	}
}

// RegisterTools registers all customer management tools
func (t *CustomerTools) RegisterTools(server *mcp_golang.Server, middleware func(func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error)) error {
	// Create Customer Tool
	server.RegisterTool("create_customer", "Create a new HVAC customer with comprehensive profile",
		middleware(t.createCustomer))

	// Get Customer Tool
	server.RegisterTool("get_customer", "Retrieve customer details by ID",
		middleware(t.getCustomer))

	// Update Customer Tool
	server.RegisterTool("update_customer", "Update existing customer information",
		middleware(t.updateCustomer))

	// List Customers Tool
	server.RegisterTool("list_customers", "List customers with pagination and search",
		middleware(t.listCustomers))

	// Delete Customer Tool
	server.RegisterTool("delete_customer", "Delete a customer (admin only)",
		middleware(t.deleteCustomer))

	// Search Customers Tool
	server.RegisterTool("search_customers", "Search customers by name, email, or phone",
		middleware(t.searchCustomers))

	// Get Customer History Tool
	server.RegisterTool("get_customer_history", "Get complete customer interaction history",
		middleware(t.getCustomerHistory))

	// Get Customer Analytics Tool
	server.RegisterTool("get_customer_analytics", "Get analytics data for a specific customer",
		middleware(t.getCustomerAnalytics))

	t.logger.Info("Customer management tools registered successfully")
	return nil
}

// createCustomer creates a new customer
func (t *CustomerTools) createCustomer(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.CreateCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for create_customer")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Create customer entity
	customer := &biz.Customer{
		Name:    req.Name,
		Email:   req.Email,
		Phone:   req.Phone,
		Address: req.Address,
	}

	// Call business logic
	result, err := t.customerUc.CreateCustomer(context.Background(), customer)
	if err != nil {
		t.logger.Errorf("Failed to create customer: %v", err)
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	t.logger.Infof("Customer created successfully: ID=%d, Name=%s", result.ID, result.Name)

	_ = types.MCPResponse{
		Success: true,
		Data:    result,
		Message: fmt.Sprintf("Customer '%s' created successfully with ID %d", result.Name, result.ID),
	}

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Customer created: %s (ID: %d)", result.Name, result.ID)),
	), nil
}

// getCustomer retrieves a customer by ID
func (t *CustomerTools) getCustomer(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_customer")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Call business logic
	customer, err := t.customerUc.GetCustomer(context.Background(), req.ID)
	if err != nil {
		t.logger.Errorf("Failed to get customer ID=%d: %v", req.ID, err)
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}

	t.logger.Infof("Customer retrieved successfully: ID=%d", customer.ID)

	_ = types.MCPResponse{
		Success: true,
		Data:    customer,
		Message: fmt.Sprintf("Customer '%s' retrieved successfully", customer.Name),
	}

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📋 Customer: %s\n📧 Email: %s\n📞 Phone: %s\n📍 Address: %s",
			customer.Name, customer.Email, customer.Phone, customer.Address)),
	), nil
}

// updateCustomer updates an existing customer
func (t *CustomerTools) updateCustomer(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for update_customer")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Get existing customer
	existing, err := t.customerUc.GetCustomer(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("customer not found: %w", err)
	}

	// Update fields if provided
	if req.Name != "" {
		existing.Name = req.Name
	}
	if req.Email != "" {
		existing.Email = req.Email
	}
	if req.Phone != "" {
		existing.Phone = req.Phone
	}
	if req.Address != "" {
		existing.Address = req.Address
	}

	// Call business logic
	result, err := t.customerUc.UpdateCustomer(context.Background(), existing)
	if err != nil {
		t.logger.Errorf("Failed to update customer ID=%d: %v", req.ID, err)
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	t.logger.Infof("Customer updated successfully: ID=%d", result.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ Customer updated: %s (ID: %d)", result.Name, result.ID)),
	), nil
}

// listCustomers lists customers with pagination
func (t *CustomerTools) listCustomers(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.ListCustomersRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for list_customers")
	}

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Call business logic
	customers, total, err := t.customerUc.ListCustomers(context.Background(), req.Page, req.PageSize)
	if err != nil {
		t.logger.Errorf("Failed to list customers: %v", err)
		return nil, fmt.Errorf("failed to list customers: %w", err)
	}

	t.logger.Infof("Customers listed successfully: count=%d, total=%d", len(customers), total)

	// Format response
	customerList := ""
	for i, customer := range customers {
		customerList += fmt.Sprintf("%d. %s (ID: %d) - %s\n",
			i+1, customer.Name, customer.ID, customer.Email)
	}

	totalPages := (total + req.PageSize - 1) / req.PageSize

	_ = types.PaginatedResponse{
		Data:       customers,
		Page:       req.Page,
		PageSize:   req.PageSize,
		Total:      total,
		TotalPages: totalPages,
	}

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📋 Customers (Page %d/%d, Total: %d):\n%s",
			req.Page, totalPages, total, customerList)),
	), nil
}

// deleteCustomer deletes a customer
func (t *CustomerTools) deleteCustomer(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.DeleteCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for delete_customer")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// Get customer name for logging
	customer, err := t.customerUc.GetCustomer(context.Background(), req.ID)
	if err != nil {
		return nil, fmt.Errorf("customer not found: %w", err)
	}

	// Call business logic
	err = t.customerUc.DeleteCustomer(context.Background(), req.ID)
	if err != nil {
		t.logger.Errorf("Failed to delete customer ID=%d: %v", req.ID, err)
		return nil, fmt.Errorf("failed to delete customer: %w", err)
	}

	t.logger.Infof("Customer deleted successfully: ID=%d, Name=%s", req.ID, customer.Name)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🗑️ Customer '%s' (ID: %d) deleted successfully", customer.Name, req.ID)),
	), nil
}

// searchCustomers searches customers by various criteria
func (t *CustomerTools) searchCustomers(args interface{}) (*mcp_golang.ToolResponse, error) {
	_, ok := args.(types.ListCustomersRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for search_customers")
	}

	// For now, use the list customers functionality with search
	// TODO: Implement dedicated search functionality in business logic
	return t.listCustomers(args)
}

// getCustomerHistory gets customer interaction history
func (t *CustomerTools) getCustomerHistory(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_customer_history")
	}

	// TODO: Implement customer history retrieval
	// This would include jobs, emails, service calls, etc.

	t.logger.Infof("Customer history requested: ID=%d", req.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📊 Customer history for ID %d (Feature coming soon)", req.ID)),
	), nil
}

// getCustomerAnalytics gets analytics for a specific customer
func (t *CustomerTools) getCustomerAnalytics(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetCustomerRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_customer_analytics")
	}

	// TODO: Implement customer analytics
	// This would include revenue, job frequency, satisfaction scores, etc.

	t.logger.Infof("Customer analytics requested: ID=%d", req.ID)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📈 Customer analytics for ID %d (Feature coming soon)", req.ID)),
	), nil
}
