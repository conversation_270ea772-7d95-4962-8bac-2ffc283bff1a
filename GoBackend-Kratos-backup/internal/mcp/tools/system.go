package tools

import (
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	mcp_golang "github.com/metoro-io/mcp-golang"

	"gobackend-hvac-kratos/internal/mcp/metrics"
	"gobackend-hvac-kratos/internal/mcp/types"
)

// 🖥️ System Operations Tools for MCP
// Comprehensive system management and monitoring tools
type SystemTools struct {
	metrics     *metrics.MCPMetrics
	minioClient interface{} // Using interface{} to avoid import issues for now
	validator   *validator.Validate
	logger      *log.Helper
}

// NewSystemTools creates a new system tools instance
func NewSystemTools(metrics *metrics.MCPMetrics, minioClient interface{}, validator *validator.Validate, logger log.Logger) *SystemTools {
	return &SystemTools{
		metrics:     metrics,
		minioClient: minioClient,
		validator:   validator,
		logger:      log.NewHelper(logger),
	}
}

// RegisterTools registers all system operation tools
func (t *SystemTools) RegisterTools(server *mcp_golang.Server, middleware func(func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error)) error {
	// Health Check Tool
	server.RegisterTool("health_check", "Check system health status",
		middleware(t.healthCheck))

	// Get System Status Tool
	server.RegisterTool("get_system_status", "Get comprehensive system status",
		middleware(t.getSystemStatus))

	// Get Metrics Tool
	server.RegisterTool("get_metrics", "Get system performance metrics",
		middleware(t.getMetrics))

	// Upload File Tool
	server.RegisterTool("upload_file", "Upload file to MinIO storage",
		middleware(t.uploadFile))

	// Get File Tool
	server.RegisterTool("get_file", "Retrieve file from MinIO storage",
		middleware(t.getFile))

	// Delete File Tool
	server.RegisterTool("delete_file", "Delete file from MinIO storage",
		middleware(t.deleteFile))

	// List Files Tool
	server.RegisterTool("list_files", "List files in MinIO bucket",
		middleware(t.listFiles))

	// Backup Data Tool
	server.RegisterTool("backup_data", "Create system data backup",
		middleware(t.backupData))

	// Monitor Performance Tool
	server.RegisterTool("monitor_performance", "Monitor system performance",
		middleware(t.monitorPerformance))

	// Reset Metrics Tool
	server.RegisterTool("reset_metrics", "Reset system metrics",
		middleware(t.resetMetrics))

	t.logger.Info("System operation tools registered successfully")
	return nil
}

// healthCheck checks system health status
func (t *SystemTools) healthCheck(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.HealthCheckRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for health_check")
	}

	// TODO: Implement actual health checks for different components
	t.logger.Info("Health check requested")

	healthStatus := "✅ All systems operational"
	if req.Component != "" {
		healthStatus = fmt.Sprintf("✅ %s is healthy", req.Component)
	}

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(healthStatus),
	), nil
}

// getSystemStatus gets comprehensive system status
func (t *SystemTools) getSystemStatus(args interface{}) (*mcp_golang.ToolResponse, error) {
	// Get current metrics
	metricsData := t.metrics.GetMetrics()

	t.logger.Info("System status requested")

	status := fmt.Sprintf(`🖥️ System Status:
📊 Total Requests: %v
✅ Success Rate: %.2f%%
⚡ Avg Response Time: %v
🔄 Active Connections: %v
⏱️ Uptime: %v`,
		metricsData["total_requests"],
		t.metrics.GetSuccessRate(),
		metricsData["average_response_time"],
		metricsData["active_connections"],
		metricsData["uptime"])

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(status),
	), nil
}

// getMetrics gets system performance metrics
func (t *SystemTools) getMetrics(args interface{}) (*mcp_golang.ToolResponse, error) {
	metricsData := t.metrics.GetMetrics()

	t.logger.Info("Metrics requested")

	// Format metrics for display
	metricsText := "📊 System Metrics:\n"
	for key, value := range metricsData {
		if key != "tools" { // Skip tools for main display
			metricsText += fmt.Sprintf("• %s: %v\n", key, value)
		}
	}

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(metricsText),
	), nil
}

// uploadFile uploads file to MinIO storage
func (t *SystemTools) uploadFile(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UploadFileRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for upload_file")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// TODO: Implement actual MinIO file upload
	t.logger.Infof("File upload requested: file_name=%s, bucket=%s", req.FileName, req.BucketName)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📁 File uploaded: %s to bucket %s", req.FileName, req.BucketName)),
	), nil
}

// getFile retrieves file from MinIO storage
func (t *SystemTools) getFile(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetFileRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for get_file")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// TODO: Implement actual MinIO file retrieval
	t.logger.Infof("File retrieval requested: file_name=%s, bucket=%s", req.FileName, req.BucketName)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("📄 File retrieved: %s from bucket %s", req.FileName, req.BucketName)),
	), nil
}

// deleteFile deletes file from MinIO storage
func (t *SystemTools) deleteFile(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.GetFileRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for delete_file")
	}

	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// TODO: Implement actual MinIO file deletion
	t.logger.Infof("File deletion requested: file_name=%s, bucket=%s", req.FileName, req.BucketName)

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("🗑️ File deleted: %s from bucket %s", req.FileName, req.BucketName)),
	), nil
}

// listFiles lists files in MinIO bucket
func (t *SystemTools) listFiles(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement actual MinIO file listing
	t.logger.Info("File listing requested")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("📋 File List (Feature coming soon)"),
	), nil
}

// backupData creates system data backup
func (t *SystemTools) backupData(args interface{}) (*mcp_golang.ToolResponse, error) {
	_, ok := args.(types.BackupDataRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for backup_data")
	}

	// TODO: Implement actual data backup
	t.logger.Info("Data backup requested")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("💾 Data Backup (Feature coming soon)"),
	), nil
}

// monitorPerformance monitors system performance
func (t *SystemTools) monitorPerformance(args interface{}) (*mcp_golang.ToolResponse, error) {
	// Get current performance metrics
	rps := t.metrics.GetRequestsPerSecond()
	successRate := t.metrics.GetSuccessRate()

	t.logger.Info("Performance monitoring requested")

	performance := fmt.Sprintf(`⚡ Performance Monitor:
🚀 Requests/Second: %.2f
✅ Success Rate: %.2f%%
🔄 Circuit Breaker: %s`,
		rps,
		successRate,
		"Closed") // TODO: Get actual circuit breaker state

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(performance),
	), nil
}

// resetMetrics resets system metrics
func (t *SystemTools) resetMetrics(args interface{}) (*mcp_golang.ToolResponse, error) {
	t.metrics.ResetMetrics()

	t.logger.Info("Metrics reset requested")

	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("🔄 System metrics reset successfully"),
	), nil
}
