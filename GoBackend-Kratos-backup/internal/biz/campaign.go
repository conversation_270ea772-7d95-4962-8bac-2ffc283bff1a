package biz

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/entity" // Import the new entity package

	"github.com/go-kratos/kratos/v2/log"
)

// CampaignRepo defines the interface for campaign data operations
type CampaignRepo interface {
	CreateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error)
	GetCampaign(ctx context.Context, id int64) (*entity.Campaign, error)
	ListCampaigns(ctx context.Context, page, pageSize int32) ([]*entity.Campaign, int32, error)
	UpdateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error)
	DeleteCampaign(ctx context.Context, id int64) error
	TrackCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) error
	GetCampaignInteractions(ctx context.Context, campaignID int64) ([]*entity.LeadCampaignInteraction, error)
}

// CampaignUsecase encapsulates campaign business logic
type CampaignUsecase struct {
	repo CampaignRepo
	log  *log.Helper
}

// NewCampaignUsecase creates a new campaign usecase
func NewCampaignUsecase(repo CampaignRepo, logger log.Logger) *CampaignUsecase {
	return &CampaignUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateCampaign creates a new campaign with validation
func (uc *CampaignUsecase) CreateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error) {
	uc.log.WithContext(ctx).Infof("Creating campaign: %s", campaign.Name)

	// Business logic validation
	if campaign.Name == "" {
		return nil, ErrCampaignNameRequired
	}

	// Set timestamps
	now := time.Now()
	campaign.CreatedAt = now
	campaign.UpdatedAt = now

	return uc.repo.CreateCampaign(ctx, campaign)
}

// GetCampaign retrieves a campaign by ID
func (uc *CampaignUsecase) GetCampaign(ctx context.Context, id int64) (*entity.Campaign, error) {
	uc.log.WithContext(ctx).Infof("Getting campaign: %d", id)

	if id <= 0 {
		return nil, ErrInvalidCampaignID
	}

	return uc.repo.GetCampaign(ctx, id)
}

// ListCampaigns retrieves campaigns with pagination
func (uc *CampaignUsecase) ListCampaigns(ctx context.Context, page, pageSize int32) ([]*entity.Campaign, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing campaigns: page=%d, size=%d", page, pageSize)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListCampaigns(ctx, page, pageSize)
}

// UpdateCampaign updates an existing campaign
func (uc *CampaignUsecase) UpdateCampaign(ctx context.Context, campaign *entity.Campaign) (*entity.Campaign, error) {
	uc.log.WithContext(ctx).Infof("Updating campaign: %d", campaign.ID)

	if campaign.ID <= 0 {
		return nil, ErrInvalidCampaignID
	}

	// Update timestamp
	campaign.UpdatedAt = time.Now()

	return uc.repo.UpdateCampaign(ctx, campaign)
}

// DeleteCampaign deletes a campaign by ID
func (uc *CampaignUsecase) DeleteCampaign(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting campaign: %d", id)

	if id <= 0 {
		return ErrInvalidCampaignID
	}

	return uc.repo.DeleteCampaign(ctx, id)
}

// TrackCampaign tracks an interaction between a lead and a campaign
func (uc *CampaignUsecase) TrackCampaign(ctx context.Context, leadID, campaignID int64, eventType string) error {
	uc.log.WithContext(ctx).Infof("Tracking campaign interaction: LeadID=%d, CampaignID=%d, EventType=%s", leadID, campaignID, eventType)

	// Basic validation
	if leadID <= 0 || campaignID <= 0 || eventType == "" {
		return ErrCampaignAttributionFailed // Or a more specific error
	}

	interaction := &entity.LeadCampaignInteraction{
		LeadID:         leadID,
		CampaignID:     campaignID,
		EventType:      eventType,
		EventTimestamp: time.Now(),
	}

	return uc.repo.TrackCampaignInteraction(ctx, interaction)
}

// GetCampaignROI calculates the ROI for a given campaign
func (uc *CampaignUsecase) GetCampaignROI(ctx context.Context, campaignID int64) (roi, totalRevenue, totalCost float64, conversions int32, err error) {
	uc.log.WithContext(ctx).Infof("Calculating ROI for CampaignID=%d", campaignID)

	if campaignID <= 0 {
		return 0, 0, 0, 0, ErrInvalidCampaignID
	}

	// TODO: Fetch actual cost data for the campaign
	// For now, assume a dummy cost
	totalCost = 1000.0 // Example cost

	interactions, err := uc.repo.GetCampaignInteractions(ctx, campaignID)
	if err != nil {
		return 0, 0, 0, 0, err
	}

	// Calculate conversions and total revenue from interactions
	for _, interaction := range interactions {
		if interaction.EventType == "conversion" {
			conversions++
			// TODO: Link to actual revenue generated by this conversion
			totalRevenue += 500.0 // Example revenue per conversion
		}
	}

	if totalCost > 0 {
		roi = ((totalRevenue - totalCost) / totalCost) * 100
	} else {
		roi = 0 // Avoid division by zero
	}

	return roi, totalRevenue, totalCost, conversions, nil
}
