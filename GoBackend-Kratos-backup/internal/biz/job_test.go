package biz

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockJobRepo is a mock implementation of JobRepo
type MockJobRepo struct {
	mock.Mock
}

func (m *MockJobRepo) CreateJob(ctx context.Context, job *Job) (*Job, error) {
	args := m.Called(ctx, job)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Job), args.Error(1)
}

func (m *MockJobRepo) GetJob(ctx context.Context, id int64) (*Job, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Job), args.Error(1)
}

func (m *MockJobRepo) ListJobs(ctx context.Context, page, pageSize int32, customerID int64, status string) ([]*Job, int32, error) {
	args := m.Called(ctx, page, pageSize, customerID, status)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int32), args.Error(2)
	}
	return args.Get(0).([]*Job), args.Get(1).(int32), args.Error(2)
}

func (m *MockJobRepo) UpdateJob(ctx context.Context, job *Job) (*Job, error) {
	args := m.Called(ctx, job)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Job), args.Error(1)
}

func (m *MockJobRepo) DeleteJob(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func TestNewJobUsecase(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestCreateJob(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case with default status and priority
	job := &Job{Title: "Install AC", CustomerID: 1}
	expectedJob := &Job{ID: 1, Title: "Install AC", CustomerID: 1, Status: "pending", Priority: "medium", CreatedAt: time.Now(), UpdatedAt: time.Now()}
	mockRepo.On("CreateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(expectedJob, nil).Once()

	createdJob, err := uc.CreateJob(ctx, job)
	assert.NoError(t, err)
	assert.Equal(t, expectedJob.Title, createdJob.Title)
	assert.Equal(t, "pending", createdJob.Status)
	assert.Equal(t, "medium", createdJob.Priority)
	assert.NotZero(t, createdJob.CreatedAt)
	assert.NotZero(t, createdJob.UpdatedAt)
	mockRepo.AssertExpectations(t)

	// Success case with specified status and priority
	job = &Job{Title: "Repair Heater", CustomerID: 2, Status: "scheduled", Priority: "high"}
	expectedJob = &Job{ID: 2, Title: "Repair Heater", CustomerID: 2, Status: "scheduled", Priority: "high", CreatedAt: time.Now(), UpdatedAt: time.Now()}
	mockRepo.On("CreateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(expectedJob, nil).Once()

	createdJob, err = uc.CreateJob(ctx, job)
	assert.NoError(t, err)
	assert.Equal(t, expectedJob.Title, createdJob.Title)
	assert.Equal(t, "scheduled", createdJob.Status)
	assert.Equal(t, "high", createdJob.Priority)
	mockRepo.AssertExpectations(t)

	// Error case: Empty Title
	job = &Job{Title: "", CustomerID: 1}
	createdJob, err = uc.CreateJob(ctx, job)
	assert.ErrorIs(t, err, ErrJobTitleRequired)
	assert.Nil(t, createdJob)
	mockRepo.AssertNotCalled(t, "CreateJob")

	// Error case: Invalid CustomerID
	job = &Job{Title: "Install AC", CustomerID: 0}
	createdJob, err = uc.CreateJob(ctx, job)
	assert.ErrorIs(t, err, ErrInvalidCustomerID)
	assert.Nil(t, createdJob)
	mockRepo.AssertNotCalled(t, "CreateJob")

	// Error case: Invalid Status
	job = &Job{Title: "Install AC", CustomerID: 1, Status: "invalid_status"}
	createdJob, err = uc.CreateJob(ctx, job)
	assert.ErrorIs(t, err, ErrInvalidJobStatus)
	assert.Nil(t, createdJob)
	mockRepo.AssertNotCalled(t, "CreateJob")

	// Error case: Invalid Priority
	job = &Job{Title: "Install AC", CustomerID: 1, Priority: "invalid_priority"}
	createdJob, err = uc.CreateJob(ctx, job)
	assert.ErrorIs(t, err, ErrInvalidJobPriority)
	assert.Nil(t, createdJob)
	mockRepo.AssertNotCalled(t, "CreateJob")

	// Error case: Repo returns error
	job = &Job{Title: "Install AC", CustomerID: 1}
	mockRepo.On("CreateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(nil, errors.New("db error")).Once()
	createdJob, err = uc.CreateJob(ctx, job)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, createdJob)
	mockRepo.AssertExpectations(t)
}

func TestGetJob(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedJob := &Job{ID: 1, Title: "Install AC"}
	mockRepo.On("GetJob", ctx, int64(1)).Return(expectedJob, nil).Once()
	job, err := uc.GetJob(ctx, 1)
	assert.NoError(t, err)
	assert.Equal(t, expectedJob, job)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	job, err = uc.GetJob(ctx, 0)
	assert.ErrorIs(t, err, ErrInvalidJobID)
	assert.Nil(t, job)
	mockRepo.AssertNotCalled(t, "GetJob")

	// Error case: Repo returns error
	mockRepo.On("GetJob", ctx, int64(2)).Return(nil, errors.New("not found")).Once()
	job, err = uc.GetJob(ctx, 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
	assert.Nil(t, job)
	mockRepo.AssertExpectations(t)
}

func TestListJobs(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedJobs := []*Job{{ID: 1, Title: "Job 1"}, {ID: 2, Title: "Job 2"}}
	mockRepo.On("ListJobs", ctx, int32(1), int32(20), int64(0), "").Return(expectedJobs, int32(2), nil).Once()
	jobs, total, err := uc.ListJobs(ctx, 1, 20, 0, "")
	assert.NoError(t, err)
	assert.Equal(t, expectedJobs, jobs)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Test case: page <= 0, pageSize <= 0
	mockRepo.On("ListJobs", ctx, int32(1), int32(20), int64(0), "").Return(expectedJobs, int32(2), nil).Once()
	jobs, total, err = uc.ListJobs(ctx, 0, 0, 0, "")
	assert.NoError(t, err)
	assert.Equal(t, expectedJobs, jobs)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Test case: pageSize > 100
	mockRepo.On("ListJobs", ctx, int32(1), int32(20), int64(0), "").Return(expectedJobs, int32(2), nil).Once()
	jobs, total, err = uc.ListJobs(ctx, 1, 101, 0, "")
	assert.NoError(t, err)
	assert.Equal(t, expectedJobs, jobs)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("ListJobs", ctx, int32(1), int32(20), int64(0), "").Return(nil, int32(0), errors.New("db error")).Once()
	jobs, total, err = uc.ListJobs(ctx, 1, 20, 0, "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, jobs)
	assert.Equal(t, int32(0), total)
	mockRepo.AssertExpectations(t)
}

func TestUpdateJob(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	jobToUpdate := &Job{ID: 1, Title: "Updated Job", CustomerID: 1, Status: "completed", Priority: "low"}
	expectedJob := &Job{ID: 1, Title: "Updated Job", CustomerID: 1, Status: "completed", Priority: "low", UpdatedAt: time.Now()}
	mockRepo.On("UpdateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(expectedJob, nil).Once()

	updatedJob, err := uc.UpdateJob(ctx, jobToUpdate)
	assert.NoError(t, err)
	assert.Equal(t, expectedJob.Title, updatedJob.Title)
	assert.Equal(t, expectedJob.Status, updatedJob.Status)
	assert.Equal(t, expectedJob.Priority, updatedJob.Priority)
	assert.NotZero(t, updatedJob.UpdatedAt)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	jobToUpdate = &Job{ID: 0, Title: "Invalid Job", CustomerID: 1}
	updatedJob, err = uc.UpdateJob(ctx, jobToUpdate)
	assert.ErrorIs(t, err, ErrInvalidJobID)
	assert.Nil(t, updatedJob)
	mockRepo.AssertNotCalled(t, "UpdateJob")

	// Error case: Invalid Status
	jobToUpdate = &Job{ID: 1, Title: "Updated Job", CustomerID: 1, Status: "invalid_status"}
	updatedJob, err = uc.UpdateJob(ctx, jobToUpdate)
	assert.ErrorIs(t, err, ErrInvalidJobStatus)
	assert.Nil(t, updatedJob)
	mockRepo.AssertNotCalled(t, "UpdateJob")

	// Error case: Invalid Priority
	jobToUpdate = &Job{ID: 1, Title: "Updated Job", CustomerID: 1, Priority: "invalid_priority"}
	updatedJob, err = uc.UpdateJob(ctx, jobToUpdate)
	assert.ErrorIs(t, err, ErrInvalidJobPriority)
	assert.Nil(t, updatedJob)
	mockRepo.AssertNotCalled(t, "UpdateJob")

	// Error case: Repo returns error
	jobToUpdate = &Job{ID: 1, Title: "Updated Job", CustomerID: 1}
	mockRepo.On("UpdateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(nil, errors.New("db error")).Once()
	updatedJob, err = uc.UpdateJob(ctx, jobToUpdate)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, updatedJob)
	mockRepo.AssertExpectations(t)
}

func TestDeleteJob(t *testing.T) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	mockRepo.On("DeleteJob", ctx, int64(1)).Return(nil).Once()
	err := uc.DeleteJob(ctx, 1)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	err = uc.DeleteJob(ctx, 0)
	assert.ErrorIs(t, err, ErrInvalidJobID)
	mockRepo.AssertNotCalled(t, "DeleteJob")

	// Error case: Repo returns error
	mockRepo.On("DeleteJob", ctx, int64(2)).Return(errors.New("db error")).Once()
	err = uc.DeleteJob(ctx, 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

// Benchmarks
func BenchmarkCreateJob(b *testing.B) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	job := &Job{Title: "Benchmark Job", CustomerID: 1, Status: "pending", Priority: "medium"}
	expectedJob := &Job{ID: 1, Title: "Benchmark Job", CustomerID: 1, Status: "pending", Priority: "medium", CreatedAt: time.Now(), UpdatedAt: time.Now()}

	mockRepo.On("CreateJob", ctx, mock.AnythingOfType("*biz.Job")).Return(expectedJob, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.CreateJob(ctx, job)
	}
}

func BenchmarkListJobs(b *testing.B) {
	mockRepo := new(MockJobRepo)
	uc := NewJobUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	expectedJobs := []*Job{{ID: 1, Title: "Job 1"}, {ID: 2, Title: "Job 2"}}

	mockRepo.On("ListJobs", ctx, int32(1), int32(20), int64(0), "").Return(expectedJobs, int32(2), nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, _ = uc.ListJobs(ctx, 1, 20, 0, "")
	}
}
