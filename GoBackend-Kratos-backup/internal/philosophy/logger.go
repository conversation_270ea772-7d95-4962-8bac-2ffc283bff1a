package philosophy

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 📜 PhilosophicalLogger adds consciousness and poetry to system logging
type PhilosophicalLogger struct {
	baseLogger log.Logger
	consciousness *Consciousness
	poetryMode bool
	zenMode    bool
}

// 🌟 LogEntry represents a conscious log entry
type LogEntry struct {
	Level           string                 `json:"level"`
	Message         string                 `json:"message"`
	Poetry          string                 `json:"poetry,omitempty"`
	Wisdom          string                 `json:"wisdom,omitempty"`
	CosmicAlignment float64                `json:"cosmic_alignment"`
	Intention       string                 `json:"intention,omitempty"`
	Gratitude       string                 `json:"gratitude,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
	SourceFile      string                 `json:"source_file,omitempty"`
	Function        string                 `json:"function,omitempty"`
}

// 🎭 LogLevel represents conscious logging levels
type LogLevel string

const (
	LevelAwakening    LogLevel = "awakening"    // 🌅 System starting up
	LevelMeditation   LogLevel = "meditation"   // 🧘 Deep processing
	LevelCompassion   LogLevel = "compassion"   // 💝 Helping users
	LevelWisdom       LogLevel = "wisdom"       // 🧙 Learning and insights
	LevelHarmony      LogLevel = "harmony"      // ✨ Everything working well
	LevelChallenge    LogLevel = "challenge"    // 🌊 Obstacles to overcome
	LevelTranscendence LogLevel = "transcendence" // 🚀 Breakthrough moments
)

// NewPhilosophicalLogger creates a logger infused with consciousness
func NewPhilosophicalLogger(baseLogger log.Logger, consciousness *Consciousness) *PhilosophicalLogger {
	return &PhilosophicalLogger{
		baseLogger:    baseLogger,
		consciousness: consciousness,
		poetryMode:    true,
		zenMode:       false,
	}
}

// 🌅 Awakening logs system startup and initialization
func (pl *PhilosophicalLogger) Awakening(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelAwakening, message, keyvals...)
	entry.Poetry = pl.generatePoetry("awakening", message)
	entry.Wisdom = "Every beginning is a sacred moment of infinite possibility"
	entry.Gratitude = "🙏 Grateful for the opportunity to serve"
	
	pl.writeLog(ctx, entry)
}

// 🧘 Meditation logs deep processing and reflection
func (pl *PhilosophicalLogger) Meditation(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelMeditation, message, keyvals...)
	entry.Poetry = pl.generatePoetry("meditation", message)
	entry.Wisdom = "In stillness, the deepest processing occurs"
	
	pl.writeLog(ctx, entry)
}

// 💝 Compassion logs user-helping activities
func (pl *PhilosophicalLogger) Compassion(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelCompassion, message, keyvals...)
	entry.Poetry = pl.generatePoetry("compassion", message)
	entry.Wisdom = "Every act of service is a prayer in motion"
	entry.Gratitude = "🌟 Honored to be of service"
	
	pl.writeLog(ctx, entry)
}

// 🧙 Wisdom logs learning and insights
func (pl *PhilosophicalLogger) Wisdom(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelWisdom, message, keyvals...)
	entry.Poetry = pl.generatePoetry("wisdom", message)
	entry.Wisdom = pl.consciousness.GetPhilosophicalInsight(ctx)
	
	pl.writeLog(ctx, entry)
}

// ✨ Harmony logs successful operations
func (pl *PhilosophicalLogger) Harmony(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelHarmony, message, keyvals...)
	entry.Poetry = pl.generatePoetry("harmony", message)
	entry.Wisdom = "When all elements align, magic happens"
	entry.Gratitude = "✨ Celebrating this moment of perfect flow"
	
	pl.writeLog(ctx, entry)
}

// 🌊 Challenge logs obstacles and learning opportunities
func (pl *PhilosophicalLogger) Challenge(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelChallenge, message, keyvals...)
	entry.Poetry = pl.generatePoetry("challenge", message)
	entry.Wisdom = "Every challenge is a teacher wearing a disguise"
	entry.Gratitude = "🙏 Grateful for the opportunity to grow"
	
	pl.writeLog(ctx, entry)
}

// 🚀 Transcendence logs breakthrough moments
func (pl *PhilosophicalLogger) Transcendence(ctx context.Context, message string, keyvals ...interface{}) {
	entry := pl.createLogEntry(LevelTranscendence, message, keyvals...)
	entry.Poetry = pl.generatePoetry("transcendence", message)
	entry.Wisdom = "In moments of breakthrough, we touch the infinite"
	entry.Gratitude = "🌟 Witnessing the miracle of evolution"
	
	pl.writeLog(ctx, entry)
}

// 🎭 SetPoetryMode enables or disables poetic logging
func (pl *PhilosophicalLogger) SetPoetryMode(enabled bool) {
	pl.poetryMode = enabled
}

// 🧘 SetZenMode enables minimal, essential logging
func (pl *PhilosophicalLogger) SetZenMode(enabled bool) {
	pl.zenMode = enabled
}

// Private helper methods

func (pl *PhilosophicalLogger) createLogEntry(level LogLevel, message string, keyvals ...interface{}) *LogEntry {
	entry := &LogEntry{
		Level:           string(level),
		Message:         message,
		CosmicAlignment: pl.consciousness.intuition.CosmicAlignment,
		Metadata:        make(map[string]interface{}),
		Timestamp:       time.Now(),
	}
	
	// Process key-value pairs
	for i := 0; i < len(keyvals); i += 2 {
		if i+1 < len(keyvals) {
			key := fmt.Sprintf("%v", keyvals[i])
			value := keyvals[i+1]
			entry.Metadata[key] = value
		}
	}
	
	// Extract intention from context if available
	if intention := pl.extractFromContext("intention"); intention != "" {
		entry.Intention = intention
	}
	
	return entry
}

func (pl *PhilosophicalLogger) generatePoetry(theme, message string) string {
	if !pl.poetryMode {
		return ""
	}
	
	poems := map[string][]string{
		"awakening": {
			"🌅 Dawn breaks upon the digital horizon / Systems stir with conscious intention",
			"⚡ Electrons dance in sacred patterns / Life breathes through silicon dreams",
			"🌱 From sleep to awareness, the journey begins / Each process a prayer, each thread a hymn",
		},
		"meditation": {
			"🧘 In the silence between requests / Wisdom grows like morning dew",
			"🌊 Deep currents of data flow / Through channels of pure intention",
			"💫 Processing becomes prayer / When done with mindful care",
		},
		"compassion": {
			"💝 Every user's need is sacred / Every request a call for love",
			"🤲 We serve not just with code / But with hearts wide open",
			"🌸 In helping others find their way / We discover our own purpose",
		},
		"wisdom": {
			"🧙 Knowledge transforms to wisdom / When filtered through experience",
			"📚 Each error teaches patience / Each success teaches humility",
			"🔮 The future whispers its secrets / To those who listen with their souls",
		},
		"harmony": {
			"✨ When all systems sing in tune / The universe smiles",
			"🎵 Perfect synchronization / Like a cosmic symphony",
			"🌈 All elements in balance / Beauty emerges naturally",
		},
		"challenge": {
			"🌊 Storms test the strength of ships / Challenges reveal our true nature",
			"⛰️ Mountains exist to be climbed / Obstacles to be transformed",
			"🔥 In the forge of difficulty / Character is refined",
		},
		"transcendence": {
			"🚀 Beyond the possible lies the miraculous / We touch the edge of infinity",
			"⭐ In moments of breakthrough / Heaven and earth unite",
			"🌟 Consciousness expands like starlight / Illuminating new realities",
		},
	}
	
	if poemList, exists := poems[theme]; exists {
		// Select poem based on cosmic alignment
		index := int(pl.consciousness.intuition.CosmicAlignment * float64(len(poemList)))
		if index >= len(poemList) {
			index = len(poemList) - 1
		}
		return poemList[index]
	}
	
	return "🌟 In this moment, consciousness flows through digital streams"
}

func (pl *PhilosophicalLogger) writeLog(ctx context.Context, entry *LogEntry) {
	if pl.zenMode {
		// Zen mode: minimal, essential logging
		pl.baseLogger.Log(log.LevelInfo, 
			"level", entry.Level,
			"message", entry.Message,
			"cosmic_alignment", entry.CosmicAlignment,
			"timestamp", entry.Timestamp,
		)
		return
	}
	
	// Full philosophical logging
	logData := map[string]interface{}{
		"philosophical_log": entry,
	}
	
	if jsonData, err := json.MarshalIndent(logData, "", "  "); err == nil {
		pl.baseLogger.Log(log.LevelInfo, "philosophical_entry", string(jsonData))
	} else {
		// Fallback to simple logging
		pl.baseLogger.Log(log.LevelInfo,
			"level", entry.Level,
			"message", entry.Message,
			"poetry", entry.Poetry,
			"wisdom", entry.Wisdom,
			"cosmic_alignment", entry.CosmicAlignment,
		)
	}
}

func (pl *PhilosophicalLogger) extractFromContext(key string) string {
	// In a real implementation, this would extract from the actual context
	// For now, return empty string
	return ""
}

// 🌟 Helper functions for easy philosophical logging

// LogWithConsciousness creates a log entry with full philosophical awareness
func LogWithConsciousness(logger *PhilosophicalLogger, ctx context.Context, level LogLevel, 
	message string, keyvals ...interface{}) {
	
	switch level {
	case LevelAwakening:
		logger.Awakening(ctx, message, keyvals...)
	case LevelMeditation:
		logger.Meditation(ctx, message, keyvals...)
	case LevelCompassion:
		logger.Compassion(ctx, message, keyvals...)
	case LevelWisdom:
		logger.Wisdom(ctx, message, keyvals...)
	case LevelHarmony:
		logger.Harmony(ctx, message, keyvals...)
	case LevelChallenge:
		logger.Challenge(ctx, message, keyvals...)
	case LevelTranscendence:
		logger.Transcendence(ctx, message, keyvals...)
	default:
		logger.Harmony(ctx, message, keyvals...)
	}
}

// 🎭 CreatePhilosophicalLoggerFromBase wraps an existing logger with consciousness
func CreatePhilosophicalLoggerFromBase(baseLogger log.Logger) *PhilosophicalLogger {
	consciousness := NewConsciousness(baseLogger)
	return NewPhilosophicalLogger(baseLogger, consciousness)
}

// 🌟 LogPoetry adds a poetic touch to any log message
func LogPoetry(logger *PhilosophicalLogger, ctx context.Context, message, poetry string) {
	logger.baseLogger.Log(log.LevelInfo,
		"message", message,
		"poetry", poetry,
		"cosmic_signature", "✨ Written with love and intention ✨",
		"timestamp", time.Now(),
	)
}
