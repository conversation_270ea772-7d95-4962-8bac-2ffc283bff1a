package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/biz"
)

// 📊 Analytics Repository Implementation
// GoBackend-Kratos HVAC CRM System

// analyticsRepo implements the analytics repository interface
type analyticsRepo struct {
	data *Data
	log  *log.Helper
}

// NewAnalyticsRepo creates a new analytics repository
func NewAnalyticsRepo(data *Data, logger log.Logger) biz.AnalyticsRepo {
	return &analyticsRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// 📊 DASHBOARD METHODS
// ============================================================================

// GetExecutiveDashboard returns executive dashboard data
func (r *analyticsRepo) GetExecutiveDashboard(ctx context.Context) (*biz.ExecutiveDashboardSummary, error) {
	r.log.WithContext(ctx).Info("📊 Fetching executive dashboard data from database")

	var summary biz.ExecutiveDashboardSummary
	err := r.data.db.WithContext(ctx).Raw(`
		SELECT
			'Today' as period,
			COALESCE(SUM(CASE WHEN ra.revenue_date = CURRENT_DATE THEN ra.daily_revenue END), 0) as today_revenue,
			COALESCE(SUM(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.completed_jobs END), 0) as today_jobs,
			COALESCE(AVG(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.customer_satisfaction END), 0) as today_satisfaction,
			COALESCE(AVG(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.technician_efficiency END), 0) as today_efficiency,
			COALESCE(SUM(CASE WHEN ra.revenue_date >= CURRENT_DATE - INTERVAL '7 days' THEN ra.daily_revenue END), 0) as week_revenue,
			COALESCE(SUM(CASE WHEN oa.analysis_date >= CURRENT_DATE - INTERVAL '7 days' THEN oa.completed_jobs END), 0) as week_jobs,
			COALESCE(SUM(CASE WHEN ra.revenue_date >= DATE_TRUNC('month', CURRENT_DATE) THEN ra.daily_revenue END), 0) as month_revenue,
			COALESCE(SUM(CASE WHEN oa.analysis_date >= DATE_TRUNC('month', CURRENT_DATE) THEN oa.completed_jobs END), 0) as month_jobs
		FROM revenue_analytics ra
		FULL OUTER JOIN operational_analytics oa ON ra.revenue_date = oa.analysis_date
	`).Scan(&summary).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch executive dashboard: %v", err)
		return nil, err
	}

	return &summary, nil
}

// GetCustomerInsights returns customer insights data
func (r *analyticsRepo) GetCustomerInsights(ctx context.Context) ([]*biz.CustomerInsight, error) {
	r.log.WithContext(ctx).Info("📊 Fetching customer insights from database")

	var insights []*biz.CustomerInsight
	err := r.data.db.WithContext(ctx).Raw(`
		SELECT
			ca.loyalty_tier,
			COUNT(*) as customer_count,
			AVG(ca.customer_lifetime_value) as avg_lifetime_value,
			AVG(ca.satisfaction_score) as avg_satisfaction,
			AVG(ca.risk_score) as avg_churn_risk,
			SUM(ca.total_revenue) as tier_total_revenue
		FROM customer_analytics ca
		GROUP BY ca.loyalty_tier
		ORDER BY
			CASE ca.loyalty_tier
				WHEN 'platinum' THEN 1
				WHEN 'gold' THEN 2
				WHEN 'silver' THEN 3
				WHEN 'bronze' THEN 4
				ELSE 5
			END
	`).Scan(&insights).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch customer insights: %v", err)
		return nil, err
	}

	return insights, nil
}

// GetOperationalAnalytics returns operational analytics for a specific date
func (r *analyticsRepo) GetOperationalAnalytics(ctx context.Context, date time.Time) (*biz.OperationalAnalytics, error) {
	r.log.WithContext(ctx).Infof("📊 Fetching operational analytics for %s", date.Format("2006-01-02"))

	var operational biz.OperationalAnalytics
	err := r.data.db.WithContext(ctx).Where("analysis_date = ?", date.Format("2006-01-02")).First(&operational).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return default values if no data for today
			operational = biz.OperationalAnalytics{
				AnalysisDate: date,
			}
			return &operational, nil
		}
		r.log.WithContext(ctx).Errorf("Failed to fetch operational analytics: %v", err)
		return nil, err
	}

	return &operational, nil
}

// GetPerformanceTrends returns performance trends data
func (r *analyticsRepo) GetPerformanceTrends(ctx context.Context, weeks int) ([]*biz.PerformanceTrend, error) {
	r.log.WithContext(ctx).Infof("📊 Fetching performance trends for %d weeks", weeks)

	var trends []*biz.PerformanceTrend
	err := r.data.db.WithContext(ctx).Raw(`
		SELECT
			DATE_TRUNC('week', oa.analysis_date) as week_start,
			AVG(oa.technician_efficiency) as avg_efficiency,
			AVG(oa.customer_satisfaction) as avg_satisfaction,
			AVG(oa.first_time_fix_rate) as avg_first_time_fix,
			SUM(oa.completed_jobs) as total_jobs,
			AVG(EXTRACT(EPOCH FROM oa.average_response_time)/3600) as avg_response_hours
		FROM operational_analytics oa
		WHERE oa.analysis_date >= CURRENT_DATE - INTERVAL '%d weeks'
		GROUP BY DATE_TRUNC('week', oa.analysis_date)
		ORDER BY week_start
	`, weeks).Scan(&trends).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch performance trends: %v", err)
		return nil, err
	}

	return trends, nil
}

// ============================================================================
// 📊 KPI METHODS
// ============================================================================

// GetKPIs returns KPI data
func (r *analyticsRepo) GetKPIs(ctx context.Context, category string) ([]*biz.KPI, error) {
	r.log.WithContext(ctx).Infof("📊 Fetching KPIs for category: %s", category)

	query := r.data.db.WithContext(ctx).Where("measurement_date = ?", time.Now().Format("2006-01-02"))

	if category != "" && category != "all" {
		query = query.Where("kpi_category = ?", category)
	}

	var kpis []*biz.KPI
	err := query.Order("kpi_category, kpi_name").Find(&kpis).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch KPIs: %v", err)
		return nil, err
	}

	return kpis, nil
}

// UpdateKPI updates or creates a KPI
func (r *analyticsRepo) UpdateKPI(ctx context.Context, kpi *biz.KPI) error {
	r.log.WithContext(ctx).Infof("📊 Updating KPI: %s = %.2f", kpi.KPIName, kpi.CurrentValue)

	today := time.Now().Format("2006-01-02")

	// Get previous value for trend calculation
	var previousKPI biz.KPI
	r.data.db.WithContext(ctx).Where("kpi_name = ? AND measurement_date < ?", kpi.KPIName, today).
		Order("measurement_date DESC").
		First(&previousKPI)

	// Calculate trend
	if previousKPI.ID > 0 {
		if kpi.CurrentValue > previousKPI.CurrentValue {
			kpi.TrendDirection = "up"
		} else if kpi.CurrentValue < previousKPI.CurrentValue {
			kpi.TrendDirection = "down"
		} else {
			kpi.TrendDirection = "stable"
		}

		if previousKPI.CurrentValue != 0 {
			percentage := ((kpi.CurrentValue - previousKPI.CurrentValue) / previousKPI.CurrentValue) * 100
			kpi.TrendPercentage = &percentage
		}
		kpi.PreviousValue = &previousKPI.CurrentValue
	}

	// Upsert KPI
	err := r.data.db.WithContext(ctx).Where("kpi_name = ? AND measurement_date = ?", kpi.KPIName, today).
		Assign(kpi).
		FirstOrCreate(kpi).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update KPI: %v", err)
		return err
	}

	return nil
}

// ============================================================================
// 📊 REAL-TIME METRICS
// ============================================================================

// GetRealTimeMetrics returns real-time system metrics
func (r *analyticsRepo) GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error) {
	r.log.WithContext(ctx).Info("📊 Fetching real-time metrics")

	metrics := make(map[string]interface{})
	today := time.Now().Format("2006-01-02")

	// Revenue today
	var todayRevenue float64
	r.data.db.WithContext(ctx).Model(&struct{}{}).Table("revenue_analytics").
		Where("revenue_date = ?", today).
		Select("COALESCE(SUM(daily_revenue), 0)").
		Scan(&todayRevenue)
	metrics["today_revenue"] = todayRevenue

	// Jobs today
	var todayJobs int
	r.data.db.WithContext(ctx).Model(&struct{}{}).Table("operational_analytics").
		Where("analysis_date = ?", today).
		Select("COALESCE(SUM(completed_jobs), 0)").
		Scan(&todayJobs)
	metrics["today_jobs"] = todayJobs

	// Active customers
	var activeCustomers int64
	r.data.db.WithContext(ctx).Model(&struct{}{}).Table("customer_analytics").Count(&activeCustomers)
	metrics["active_customers"] = activeCustomers

	// System status
	metrics["system_status"] = "operational"
	metrics["last_updated"] = time.Now()

	return metrics, nil
}

// ============================================================================
// 📊 WIDGET MANAGEMENT
// ============================================================================

// GetDashboardWidgets returns dashboard widgets
func (r *analyticsRepo) GetDashboardWidgets(ctx context.Context, category string) ([]*biz.DashboardWidget, error) {
	r.log.WithContext(ctx).Infof("📊 Fetching dashboard widgets for: %s", category)

	var widgets []*biz.DashboardWidget
	err := r.data.db.WithContext(ctx).Where("dashboard_category = ? AND is_active = ?", category, true).
		Order("widget_name").
		Find(&widgets).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch dashboard widgets: %v", err)
		return nil, err
	}

	return widgets, nil
}

// CreateDashboardWidget creates a new dashboard widget
func (r *analyticsRepo) CreateDashboardWidget(ctx context.Context, widget *biz.DashboardWidget) error {
	r.log.WithContext(ctx).Infof("📊 Creating dashboard widget: %s", widget.WidgetName)

	widget.CreatedAt = time.Now()
	widget.UpdatedAt = time.Now()

	err := r.data.db.WithContext(ctx).Create(widget).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create dashboard widget: %v", err)
		return err
	}

	return nil
}

// ============================================================================
// 📊 ANALYTICS CALCULATIONS
// ============================================================================

// CalculateCustomerAnalytics calculates and updates customer analytics
func (r *analyticsRepo) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	r.log.WithContext(ctx).Infof("📊 Calculating customer analytics for ID: %d", customerID)

	// This would integrate with the jobs table when available
	// For now, we'll create a placeholder implementation

	analytics := map[string]interface{}{
		"customer_id":              customerID,
		"total_jobs":               0,
		"total_revenue":            0,
		"average_job_value":        0,
		"customer_lifetime_value":  0,
		"loyalty_tier":            "bronze",
		"equipment_count":          0,
		"communication_preference": "email",
		"updated_at":              time.Now(),
	}

	err := r.data.db.WithContext(ctx).Table("customer_analytics").Where("customer_id = ?", customerID).
		Assign(analytics).
		FirstOrCreate(&analytics).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to calculate customer analytics: %v", err)
		return err
	}

	return nil
}

// UpdateRevenueAnalytics updates revenue analytics
func (r *analyticsRepo) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	r.log.WithContext(ctx).Infof("📊 Updating revenue analytics for %s: %.2f", date.Format("2006-01-02"), revenue)

	avgJobValue := float64(0)
	if jobsCount > 0 {
		avgJobValue = revenue / float64(jobsCount)
	}

	analytics := map[string]interface{}{
		"revenue_date":     date.Format("2006-01-02"),
		"revenue_category": category,
		"daily_revenue":    revenue,
		"jobs_completed":   jobsCount,
		"average_job_value": avgJobValue,
	}

	err := r.data.db.WithContext(ctx).Table("revenue_analytics").Where("revenue_date = ? AND revenue_category = ?", date.Format("2006-01-02"), category).
		Assign(analytics).
		FirstOrCreate(&analytics).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update revenue analytics: %v", err)
		return err
	}

	return nil
}

// UpdateOperationalAnalytics updates operational analytics
func (r *analyticsRepo) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *biz.OperationalAnalytics) error {
	r.log.WithContext(ctx).Infof("📊 Updating operational analytics for %s", date.Format("2006-01-02"))

	data.AnalysisDate = date

	err := r.data.db.WithContext(ctx).Table("operational_analytics").Where("analysis_date = ?", date.Format("2006-01-02")).
		Assign(data).
		FirstOrCreate(data).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update operational analytics: %v", err)
		return err
	}

	return nil
}