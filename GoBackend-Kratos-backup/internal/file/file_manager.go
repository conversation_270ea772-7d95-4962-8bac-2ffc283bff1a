package file

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"time"

	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/extraction"
	"gobackend-hvac-kratos/internal/storage"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 📁 File Manager for Dolores Email Intelligence
// Comprehensive file management with hybrid storage strategy

type FileManager struct {
	log          *log.Helper
	db           *gorm.DB
	minioService *storage.MinIOService
	extractor    *extraction.ContentExtractor
	config       *FileManagerConfig
}

type FileManagerConfig struct {
	SmallFileThreshold  int64  `yaml:"small_file_threshold"` // 1MB
	LargeFileStorage    string `yaml:"large_file_storage"`   // "minio"
	MetadataStorage     string `yaml:"metadata_storage"`     // "database"
	EnableCompression   bool   `yaml:"enable_compression"`
	EnableDeduplication bool   `yaml:"enable_deduplication"`
	MaxFileSize         int64  `yaml:"max_file_size"`    // 100MB
	TempFileExpiry      string `yaml:"temp_file_expiry"` // "7d"
}

type FileUploadRequest struct {
	EmailID        *int64            `json:"email_id,omitempty"`
	OriginalName   string            `json:"original_name"`
	ContentType    string            `json:"content_type"`
	Data           io.Reader         `json:"-"`
	Size           int64             `json:"size"`
	Tags           []string          `json:"tags,omitempty"`
	Metadata       map[string]string `json:"metadata,omitempty"`
	AccessLevel    data.AccessLevel  `json:"access_level"`
	ExpirationDays int               `json:"expiration_days,omitempty"`
	ExtractContent bool              `json:"extract_content"`
	Priority       data.JobPriority  `json:"priority"`
}

type FileUploadResult struct {
	FileID          int64                      `json:"file_id"`
	StorageLocation string                     `json:"storage_location"`
	StorageType     data.StorageType           `json:"storage_type"`
	ChecksumMD5     string                     `json:"checksum_md5"`
	ChecksumSHA256  string                     `json:"checksum_sha256"`
	ProcessingJob   *data.ContentExtractionJob `json:"processing_job,omitempty"`
}

// NewFileManager creates a new file manager
func NewFileManager(
	logger log.Logger,
	db *gorm.DB,
	minioService *storage.MinIOService,
	extractor *extraction.ContentExtractor,
	config *FileManagerConfig,
) *FileManager {
	return &FileManager{
		log:          log.NewHelper(logger),
		db:           db,
		minioService: minioService,
		extractor:    extractor,
		config:       config,
	}
}

// UploadFile uploads a file using hybrid storage strategy
func (fm *FileManager) UploadFile(ctx context.Context, req *FileUploadRequest) (*FileUploadResult, error) {
	fm.log.WithContext(ctx).Infof("📤 Uploading file: %s (%d bytes)", req.OriginalName, req.Size)

	// Validate file size
	if req.Size > fm.config.MaxFileSize {
		return nil, fmt.Errorf("file too large: %d bytes (max: %d)", req.Size, fm.config.MaxFileSize)
	}

	// Read data into buffer for checksum calculation and storage
	buffer := &bytes.Buffer{}
	teeReader := io.TeeReader(req.Data, buffer)

	// Calculate checksums
	md5Hash, sha256Hash, err := fm.calculateChecksums(teeReader)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksums: %w", err)
	}

	// Check for deduplication if enabled
	if fm.config.EnableDeduplication {
		if existingFile, err := fm.findExistingFile(ctx, md5Hash); err == nil && existingFile != nil {
			fm.log.WithContext(ctx).Infof("🔄 File already exists (deduplication): %s", existingFile.StorageName)
			return &FileUploadResult{
				FileID:          existingFile.ID,
				StorageLocation: existingFile.GetStorageLocation(),
				StorageType:     existingFile.StorageType,
				ChecksumMD5:     existingFile.ChecksumMD5,
				ChecksumSHA256:  existingFile.ChecksumSHA256,
			}, nil
		}
	}

	// Determine storage strategy
	storageType := fm.determineStorageType(req.Size)

	// Create file storage record
	fileStorage := &data.FileStorage{
		EmailID:          req.EmailID,
		OriginalName:     req.OriginalName,
		StorageName:      fm.generateStorageName(req.OriginalName),
		ContentType:      req.ContentType,
		Size:             req.Size,
		ChecksumMD5:      md5Hash,
		ChecksumSHA256:   sha256Hash,
		StorageType:      storageType,
		AccessLevel:      req.AccessLevel,
		ProcessingStatus: data.ProcessingStatusPending,
		Tags:             data.StringArray(req.Tags),
	}

	// Store file based on strategy
	reader := bytes.NewReader(buffer.Bytes())

	switch storageType {
	case data.StorageTypeDatabase:
		err = fm.storeInDatabase(ctx, fileStorage, reader)
	case data.StorageTypeMinIO:
		err = fm.storeInMinIO(ctx, fileStorage, reader, req)
	default:
		return nil, fmt.Errorf("unsupported storage type: %s", storageType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to store file: %w", err)
	}

	// Save to database
	if err := fm.db.WithContext(ctx).Create(fileStorage).Error; err != nil {
		// Cleanup storage if database save fails
		fm.cleanupFailedUpload(ctx, fileStorage)
		return nil, fmt.Errorf("failed to save file metadata: %w", err)
	}

	result := &FileUploadResult{
		FileID:          fileStorage.ID,
		StorageLocation: fileStorage.GetStorageLocation(),
		StorageType:     fileStorage.StorageType,
		ChecksumMD5:     fileStorage.ChecksumMD5,
		ChecksumSHA256:  fileStorage.ChecksumSHA256,
	}

	// Schedule content extraction if requested
	if req.ExtractContent && fm.extractor.IsSupported(req.ContentType, req.OriginalName) {
		job, err := fm.scheduleContentExtraction(ctx, fileStorage, req.Priority)
		if err != nil {
			fm.log.WithContext(ctx).Warnf("Failed to schedule content extraction: %v", err)
		} else {
			result.ProcessingJob = job
		}
	}

	fm.log.WithContext(ctx).Infof("✅ File uploaded successfully: %s (ID: %d)", req.OriginalName, fileStorage.ID)
	return result, nil
}

// DownloadFile downloads a file from storage
func (fm *FileManager) DownloadFile(ctx context.Context, fileID int64) (io.ReadCloser, *data.FileStorage, error) {
	fm.log.WithContext(ctx).Infof("📥 Downloading file: %d", fileID)

	// Get file metadata
	var fileStorage data.FileStorage
	if err := fm.db.WithContext(ctx).First(&fileStorage, fileID).Error; err != nil {
		return nil, nil, fmt.Errorf("file not found: %w", err)
	}

	// Update access time
	now := time.Now()
	fileStorage.AccessedAt = &now
	fm.db.WithContext(ctx).Save(&fileStorage)

	var reader io.ReadCloser
	var err error

	// Download based on storage type
	switch fileStorage.StorageType {
	case data.StorageTypeDatabase:
		reader, err = fm.downloadFromDatabase(ctx, &fileStorage)
	case data.StorageTypeMinIO:
		reader, err = fm.downloadFromMinIO(ctx, &fileStorage)
	default:
		return nil, nil, fmt.Errorf("unsupported storage type: %s", fileStorage.StorageType)
	}

	if err != nil {
		return nil, nil, fmt.Errorf("failed to download file: %w", err)
	}

	fm.log.WithContext(ctx).Infof("✅ File downloaded successfully: %s", fileStorage.OriginalName)
	return reader, &fileStorage, nil
}

// DeleteFile deletes a file from storage
func (fm *FileManager) DeleteFile(ctx context.Context, fileID int64) error {
	fm.log.WithContext(ctx).Infof("🗑️ Deleting file: %d", fileID)

	// Get file metadata
	var fileStorage data.FileStorage
	if err := fm.db.WithContext(ctx).First(&fileStorage, fileID).Error; err != nil {
		return fmt.Errorf("file not found: %w", err)
	}

	// Delete from storage
	switch fileStorage.StorageType {
	case data.StorageTypeMinIO:
		if err := fm.minioService.DeleteFile(ctx, fileStorage.StoragePath); err != nil {
			fm.log.WithContext(ctx).Warnf("Failed to delete from MinIO: %v", err)
		}
	case data.StorageTypeDatabase:
		// Data will be deleted with the record
	}

	// Delete from database (cascade will handle related records)
	if err := fm.db.WithContext(ctx).Delete(&fileStorage).Error; err != nil {
		return fmt.Errorf("failed to delete file metadata: %w", err)
	}

	fm.log.WithContext(ctx).Infof("✅ File deleted successfully: %s", fileStorage.OriginalName)
	return nil
}

// GetFileInfo gets file information
func (fm *FileManager) GetFileInfo(ctx context.Context, fileID int64) (*data.FileStorage, error) {
	var fileStorage data.FileStorage
	if err := fm.db.WithContext(ctx).Preload("ExtractionJobs").First(&fileStorage, fileID).Error; err != nil {
		return nil, fmt.Errorf("file not found: %w", err)
	}
	return &fileStorage, nil
}

// ListFiles lists files with filtering
func (fm *FileManager) ListFiles(ctx context.Context, emailID *int64, limit, offset int) ([]*data.FileStorage, int64, error) {
	query := fm.db.WithContext(ctx).Model(&data.FileStorage{})

	if emailID != nil {
		query = query.Where("email_id = ?", *emailID)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count files: %w", err)
	}

	var files []*data.FileStorage
	if err := query.Limit(limit).Offset(offset).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list files: %w", err)
	}

	return files, total, nil
}

// Helper methods

// determineStorageType determines where to store the file based on size
func (fm *FileManager) determineStorageType(size int64) data.StorageType {
	if size <= fm.config.SmallFileThreshold {
		return data.StorageTypeDatabase
	}
	return data.StorageTypeMinIO
}

// generateStorageName generates a unique storage name
func (fm *FileManager) generateStorageName(originalName string) string {
	timestamp := time.Now().Format("20060102150405")
	hash := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s-%d", originalName, time.Now().UnixNano()))))[:8]
	return fmt.Sprintf("%s-%s-%s", timestamp, hash, originalName)
}

// calculateChecksums calculates MD5 and SHA256 checksums
func (fm *FileManager) calculateChecksums(reader io.Reader) (string, string, error) {
	md5Hash := md5.New()
	sha256Hash := sha256.New()

	// Use TeeReader to calculate both hashes simultaneously
	teeReader := io.TeeReader(reader, md5Hash)
	_, err := io.Copy(sha256Hash, teeReader)
	if err != nil {
		return "", "", err
	}

	md5Sum := hex.EncodeToString(md5Hash.Sum(nil))
	sha256Sum := hex.EncodeToString(sha256Hash.Sum(nil))

	return md5Sum, sha256Sum, nil
}

// findExistingFile finds existing file by MD5 hash for deduplication
func (fm *FileManager) findExistingFile(ctx context.Context, md5Hash string) (*data.FileStorage, error) {
	var fileStorage data.FileStorage
	err := fm.db.WithContext(ctx).Where("checksum_md5 = ?", md5Hash).First(&fileStorage).Error
	if err != nil {
		return nil, err
	}
	return &fileStorage, nil
}

// storeInDatabase stores file in PostgreSQL
func (fm *FileManager) storeInDatabase(ctx context.Context, fileStorage *data.FileStorage, reader io.Reader) error {
	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("failed to read file data: %w", err)
	}

	fileStorage.BinaryData = data
	fileStorage.StorageType = data.StorageTypeDatabase

	return nil
}

// storeInMinIO stores file in MinIO
func (fm *FileManager) storeInMinIO(ctx context.Context, fileStorage *data.FileStorage, reader io.Reader, req *FileUploadRequest) error {
	// Generate storage key
	storageKey := fm.minioService.GenerateStorageKey(fileStorage.OriginalName, req.EmailID)

	// Prepare upload options
	uploadOpts := &storage.UploadOptions{
		ContentType:    fileStorage.ContentType,
		Metadata:       req.Metadata,
		Tags:           make(map[string]string),
		Encryption:     true,
		ExpirationDays: req.ExpirationDays,
	}

	// Add tags
	for _, tag := range req.Tags {
		uploadOpts.Tags[tag] = "true"
	}

	// Upload to MinIO
	_, err := fm.minioService.UploadFile(ctx, storageKey, reader, fileStorage.Size, uploadOpts)
	if err != nil {
		return fmt.Errorf("failed to upload to MinIO: %w", err)
	}

	fileStorage.StorageType = data.StorageTypeMinIO
	fileStorage.StoragePath = storageKey
	fileStorage.StorageBucket = "dolores-attachments"

	return nil
}

// downloadFromDatabase downloads file from PostgreSQL
func (fm *FileManager) downloadFromDatabase(ctx context.Context, fileStorage *data.FileStorage) (io.ReadCloser, error) {
	if len(fileStorage.BinaryData) == 0 {
		return nil, fmt.Errorf("no binary data found in database")
	}

	reader := io.NopCloser(bytes.NewReader(fileStorage.BinaryData))
	return reader, nil
}

// downloadFromMinIO downloads file from MinIO
func (fm *FileManager) downloadFromMinIO(ctx context.Context, fileStorage *data.FileStorage) (io.ReadCloser, error) {
	if fileStorage.StoragePath == "" {
		return nil, fmt.Errorf("no storage path found for MinIO file")
	}

	reader, _, err := fm.minioService.DownloadFile(ctx, fileStorage.StoragePath, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to download from MinIO: %w", err)
	}

	return reader, nil
}

// scheduleContentExtraction schedules content extraction job
func (fm *FileManager) scheduleContentExtraction(ctx context.Context, fileStorage *data.FileStorage, priority data.JobPriority) (*data.ContentExtractionJob, error) {
	job := &data.ContentExtractionJob{
		FileID:     fileStorage.ID,
		JobType:    data.ExtractionJobTypeFull,
		Status:     data.ProcessingStatusPending,
		Priority:   priority,
		MaxRetries: 3,
	}

	if err := fm.db.WithContext(ctx).Create(job).Error; err != nil {
		return nil, fmt.Errorf("failed to create extraction job: %w", err)
	}

	fm.log.WithContext(ctx).Infof("📋 Content extraction job scheduled: %d", job.ID)
	return job, nil
}

// cleanupFailedUpload cleans up storage if upload fails
func (fm *FileManager) cleanupFailedUpload(ctx context.Context, fileStorage *data.FileStorage) {
	if fileStorage.StorageType == data.StorageTypeMinIO && fileStorage.StoragePath != "" {
		if err := fm.minioService.DeleteFile(ctx, fileStorage.StoragePath); err != nil {
			fm.log.WithContext(ctx).Warnf("Failed to cleanup MinIO file: %v", err)
		}
	}
}

// ProcessExtractionJob processes a content extraction job
func (fm *FileManager) ProcessExtractionJob(ctx context.Context, jobID int64) error {
	fm.log.WithContext(ctx).Infof("🔍 Processing extraction job: %d", jobID)

	// Get job
	var job data.ContentExtractionJob
	if err := fm.db.WithContext(ctx).Preload("File").First(&job, jobID).Error; err != nil {
		return fmt.Errorf("job not found: %w", err)
	}

	// Update job status
	now := time.Now()
	job.Status = data.ProcessingStatusProcessing
	job.StartedAt = &now
	fm.db.WithContext(ctx).Save(&job)

	// Download file
	reader, fileStorage, err := fm.DownloadFile(ctx, job.FileID)
	if err != nil {
		return fm.markJobFailed(ctx, &job, fmt.Sprintf("failed to download file: %v", err))
	}
	defer reader.Close()

	// Extract content
	result, err := fm.extractor.ExtractContent(ctx, reader, fileStorage.ContentType, fileStorage.OriginalName)
	if err != nil {
		return fm.markJobFailed(ctx, &job, fmt.Sprintf("extraction failed: %v", err))
	}

	if !result.Success {
		return fm.markJobFailed(ctx, &job, result.Error)
	}

	// Update job with results
	completedAt := time.Now()
	job.Status = data.ProcessingStatusCompleted
	job.CompletedAt = &completedAt
	job.ExtractedText = result.Text
	job.ExtractedMeta = *result.Metadata
	job.ProcessingTime = &[]int{int(result.ProcessingTime.Milliseconds())}[0]

	if result.Metadata != nil {
		job.WordCount = &result.Metadata.WordCount
		job.PageCount = &result.Metadata.PageCount
	}

	// Update file storage
	fileStorage.ExtractedText = result.Text
	fileStorage.ExtractionMeta = *result.Metadata
	fileStorage.ProcessingStatus = data.ProcessingStatusCompleted
	fileStorage.ProcessedAt = &completedAt

	// Save both records
	if err := fm.db.WithContext(ctx).Save(&job).Error; err != nil {
		return fmt.Errorf("failed to save job: %w", err)
	}

	if err := fm.db.WithContext(ctx).Save(fileStorage).Error; err != nil {
		return fmt.Errorf("failed to save file storage: %w", err)
	}

	fm.log.WithContext(ctx).Infof("✅ Extraction job completed: %d (%v)", jobID, result.ProcessingTime)
	return nil
}

// markJobFailed marks a job as failed
func (fm *FileManager) markJobFailed(ctx context.Context, job *data.ContentExtractionJob, errorMsg string) error {
	job.Status = data.ProcessingStatusFailed
	job.ErrorMessage = errorMsg
	job.RetryCount++

	completedAt := time.Now()
	job.CompletedAt = &completedAt

	if err := fm.db.WithContext(ctx).Save(job).Error; err != nil {
		fm.log.WithContext(ctx).Errorf("Failed to save failed job: %v", err)
	}

	fm.log.WithContext(ctx).Errorf("❌ Extraction job failed: %d - %s", job.ID, errorMsg)
	return fmt.Errorf("extraction job failed: %s", errorMsg)
}

// GetPendingJobs gets pending extraction jobs
func (fm *FileManager) GetPendingJobs(ctx context.Context, limit int) ([]*data.ContentExtractionJob, error) {
	var jobs []*data.ContentExtractionJob

	err := fm.db.WithContext(ctx).
		Where("status = ?", data.ProcessingStatusPending).
		Order("priority DESC, queued_at ASC").
		Limit(limit).
		Find(&jobs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending jobs: %w", err)
	}

	return jobs, nil
}

// GetStorageStats returns storage statistics
func (fm *FileManager) GetStorageStats(ctx context.Context) (*StorageStats, error) {
	stats := &StorageStats{}

	// Count files by storage type
	var dbFiles, minioFiles int64
	var dbSize, minioSize int64

	fm.db.WithContext(ctx).Model(&data.FileStorage{}).
		Where("storage_type = ?", data.StorageTypeDatabase).
		Count(&dbFiles)

	fm.db.WithContext(ctx).Model(&data.FileStorage{}).
		Where("storage_type = ?", data.StorageTypeMinIO).
		Count(&minioFiles)

	// Calculate sizes
	fm.db.WithContext(ctx).Model(&data.FileStorage{}).
		Where("storage_type = ?", data.StorageTypeDatabase).
		Select("COALESCE(SUM(size), 0)").Scan(&dbSize)

	fm.db.WithContext(ctx).Model(&data.FileStorage{}).
		Where("storage_type = ?", data.StorageTypeMinIO).
		Select("COALESCE(SUM(size), 0)").Scan(&minioSize)

	stats.DatabaseFiles = dbFiles
	stats.MinIOFiles = minioFiles
	stats.DatabaseSize = dbSize
	stats.MinIOSize = minioSize
	stats.TotalFiles = dbFiles + minioFiles
	stats.TotalSize = dbSize + minioSize

	return stats, nil
}

// StorageStats represents storage statistics
type StorageStats struct {
	TotalFiles    int64 `json:"total_files"`
	TotalSize     int64 `json:"total_size"`
	DatabaseFiles int64 `json:"database_files"`
	DatabaseSize  int64 `json:"database_size"`
	MinIOFiles    int64 `json:"minio_files"`
	MinIOSize     int64 `json:"minio_size"`
}

// SearchFiles searches files by content
func (fm *FileManager) SearchFiles(ctx context.Context, query string, limit, offset int) ([]*data.FileStorage, int64, error) {
	dbQuery := fm.db.WithContext(ctx).Model(&data.FileStorage{}).
		Where("extracted_text ILIKE ? OR original_name ILIKE ?", "%"+query+"%", "%"+query+"%")

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}

	var files []*data.FileStorage
	if err := dbQuery.Limit(limit).Offset(offset).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search files: %w", err)
	}

	return files, total, nil
}
