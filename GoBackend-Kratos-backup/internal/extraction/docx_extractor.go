package extraction

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"
	"archive/zip"
	"encoding/xml"
	"bytes"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/data"
)

// 📝 DOCX Content Extractor
// Extracts text and metadata from Microsoft Word DOCX files

type DOCXExtractor struct {
	log *log.Helper
}

// DOCX XML structures for parsing
type Document struct {
	XMLName xml.Name `xml:"document"`
	Body    Body     `xml:"body"`
}

type Body struct {
	Paragraphs []Paragraph `xml:"p"`
}

type Paragraph struct {
	Runs []Run `xml:"r"`
}

type Run struct {
	Text []Text `xml:"t"`
}

type Text struct {
	Value string `xml:",chardata"`
}

// Core properties structure
type CoreProperties struct {
	XMLName      xml.Name  `xml:"coreProperties"`
	Title        string    `xml:"title,omitempty"`
	Subject      string    `xml:"subject,omitempty"`
	Creator      string    `xml:"creator,omitempty"`
	Keywords     string    `xml:"keywords,omitempty"`
	Description  string    `xml:"description,omitempty"`
	LastModified string    `xml:"modified,omitempty"`
	Created      string    `xml:"created,omitempty"`
	Category     string    `xml:"category,omitempty"`
	Language     string    `xml:"language,omitempty"`
}

// App properties structure
type AppProperties struct {
	XMLName     xml.Name `xml:"Properties"`
	Application string   `xml:"Application,omitempty"`
	DocSecurity int      `xml:"DocSecurity,omitempty"`
	Company     string   `xml:"Company,omitempty"`
	Pages       int      `xml:"Pages,omitempty"`
	Words       int      `xml:"Words,omitempty"`
	Characters  int      `xml:"Characters,omitempty"`
	Lines       int      `xml:"Lines,omitempty"`
	Paragraphs  int      `xml:"Paragraphs,omitempty"`
}

// NewDOCXExtractor creates a new DOCX extractor
func NewDOCXExtractor(logger log.Logger) *DOCXExtractor {
	return &DOCXExtractor{
		log: log.NewHelper(logger),
	}
}

// Extract extracts text and metadata from DOCX
func (de *DOCXExtractor) Extract(ctx context.Context, reader io.Reader) (string, *data.ExtractionMetadata, error) {
	startTime := time.Now()
	de.log.WithContext(ctx).Debug("📝 Starting DOCX extraction...")

	// Read DOCX data
	docxData, err := io.ReadAll(reader)
	if err != nil {
		return "", nil, fmt.Errorf("failed to read DOCX data: %w", err)
	}

	// Create zip reader from bytes
	zipReader, err := zip.NewReader(bytes.NewReader(docxData), int64(len(docxData)))
	if err != nil {
		return "", nil, fmt.Errorf("failed to create zip reader: %w", err)
	}

	// Extract text content
	text, err := de.extractText(zipReader)
	if err != nil {
		return "", nil, fmt.Errorf("failed to extract text: %w", err)
	}

	// Extract metadata
	metadata, err := de.extractMetadata(zipReader)
	if err != nil {
		de.log.WithContext(ctx).Warnf("Failed to extract metadata: %v", err)
		// Continue with basic metadata
		metadata = &data.ExtractionMetadata{
			FileType:         "docx",
			ProcessorUsed:    "docx-parser",
			ProcessorVersion: "1.0",
			ExtractionTime:   int(time.Since(startTime).Milliseconds()),
		}
	}

	// Update processing info
	metadata.ExtractionTime = int(time.Since(startTime).Milliseconds())
	metadata.ProcessorUsed = "docx-parser"
	metadata.ProcessorVersion = "1.0"
	metadata.WordCount = len(strings.Fields(text))
	metadata.CharCount = len(text)

	de.log.WithContext(ctx).Debugf("📝 DOCX extraction completed: %d words", metadata.WordCount)

	return text, metadata, nil
}

// extractText extracts text content from DOCX
func (de *DOCXExtractor) extractText(zipReader *zip.Reader) (string, error) {
	// Find document.xml file
	var documentFile *zip.File
	for _, file := range zipReader.File {
		if file.Name == "word/document.xml" {
			documentFile = file
			break
		}
	}

	if documentFile == nil {
		return "", fmt.Errorf("document.xml not found in DOCX")
	}

	// Open and read document.xml
	rc, err := documentFile.Open()
	if err != nil {
		return "", fmt.Errorf("failed to open document.xml: %w", err)
	}
	defer rc.Close()

	xmlData, err := io.ReadAll(rc)
	if err != nil {
		return "", fmt.Errorf("failed to read document.xml: %w", err)
	}

	// Parse XML
	var doc Document
	if err := xml.Unmarshal(xmlData, &doc); err != nil {
		return "", fmt.Errorf("failed to parse document.xml: %w", err)
	}

	// Extract text from paragraphs
	var textBuilder strings.Builder
	for _, paragraph := range doc.Body.Paragraphs {
		paragraphText := de.extractParagraphText(paragraph)
		if paragraphText != "" {
			textBuilder.WriteString(paragraphText)
			textBuilder.WriteString("\n")
		}
	}

	return textBuilder.String(), nil
}

// extractParagraphText extracts text from a paragraph
func (de *DOCXExtractor) extractParagraphText(paragraph Paragraph) string {
	var textBuilder strings.Builder
	
	for _, run := range paragraph.Runs {
		for _, text := range run.Text {
			textBuilder.WriteString(text.Value)
		}
	}
	
	return textBuilder.String()
}

// extractMetadata extracts metadata from DOCX
func (de *DOCXExtractor) extractMetadata(zipReader *zip.Reader) (*data.ExtractionMetadata, error) {
	metadata := &data.ExtractionMetadata{
		FileType: "docx",
	}

	// Extract core properties
	coreProps, err := de.extractCoreProperties(zipReader)
	if err == nil {
		metadata.Title = coreProps.Title
		metadata.Subject = coreProps.Subject
		metadata.Author = coreProps.Creator
		metadata.Language = coreProps.Language

		// Parse keywords
		if coreProps.Keywords != "" {
			keywords := strings.FieldsFunc(coreProps.Keywords, func(c rune) bool {
				return c == ',' || c == ';'
			})
			for i, keyword := range keywords {
				keywords[i] = strings.TrimSpace(keyword)
			}
			metadata.Keywords = keywords
		}

		// Parse dates
		if coreProps.Created != "" {
			if createdDate, err := time.Parse(time.RFC3339, coreProps.Created); err == nil {
				metadata.CreatedDate = &createdDate
			}
		}

		if coreProps.LastModified != "" {
			if modifiedDate, err := time.Parse(time.RFC3339, coreProps.LastModified); err == nil {
				metadata.ModifiedDate = &modifiedDate
			}
		}
	}

	// Extract app properties
	appProps, err := de.extractAppProperties(zipReader)
	if err == nil {
		metadata.Application = appProps.Application
		metadata.WordCount = appProps.Words
		metadata.CharCount = appProps.Characters
		metadata.PageCount = appProps.Pages

		// Store additional info in custom metadata
		if metadata.Custom == nil {
			metadata.Custom = make(map[string]interface{})
		}
		metadata.Custom["company"] = appProps.Company
		metadata.Custom["lines"] = appProps.Lines
		metadata.Custom["paragraphs"] = appProps.Paragraphs
		metadata.Custom["doc_security"] = appProps.DocSecurity
	}

	return metadata, nil
}

// extractCoreProperties extracts core properties from DOCX
func (de *DOCXExtractor) extractCoreProperties(zipReader *zip.Reader) (*CoreProperties, error) {
	// Find core properties file
	var corePropsFile *zip.File
	for _, file := range zipReader.File {
		if file.Name == "docProps/core.xml" {
			corePropsFile = file
			break
		}
	}

	if corePropsFile == nil {
		return nil, fmt.Errorf("core.xml not found")
	}

	// Open and read core.xml
	rc, err := corePropsFile.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open core.xml: %w", err)
	}
	defer rc.Close()

	xmlData, err := io.ReadAll(rc)
	if err != nil {
		return nil, fmt.Errorf("failed to read core.xml: %w", err)
	}

	// Parse XML
	var coreProps CoreProperties
	if err := xml.Unmarshal(xmlData, &coreProps); err != nil {
		return nil, fmt.Errorf("failed to parse core.xml: %w", err)
	}

	return &coreProps, nil
}

// extractAppProperties extracts app properties from DOCX
func (de *DOCXExtractor) extractAppProperties(zipReader *zip.Reader) (*AppProperties, error) {
	// Find app properties file
	var appPropsFile *zip.File
	for _, file := range zipReader.File {
		if file.Name == "docProps/app.xml" {
			appPropsFile = file
			break
		}
	}

	if appPropsFile == nil {
		return nil, fmt.Errorf("app.xml not found")
	}

	// Open and read app.xml
	rc, err := appPropsFile.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open app.xml: %w", err)
	}
	defer rc.Close()

	xmlData, err := io.ReadAll(rc)
	if err != nil {
		return nil, fmt.Errorf("failed to read app.xml: %w", err)
	}

	// Parse XML
	var appProps AppProperties
	if err := xml.Unmarshal(xmlData, &appProps); err != nil {
		return nil, fmt.Errorf("failed to parse app.xml: %w", err)
	}

	return &appProps, nil
}

// ValidateDOCX validates if the DOCX is readable and not corrupted
func (de *DOCXExtractor) ValidateDOCX(ctx context.Context, reader io.Reader) error {
	de.log.WithContext(ctx).Debug("🔍 Validating DOCX...")

	// Read DOCX data
	docxData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("failed to read DOCX data: %w", err)
	}

	// Try to create zip reader
	zipReader, err := zip.NewReader(bytes.NewReader(docxData), int64(len(docxData)))
	if err != nil {
		return fmt.Errorf("invalid DOCX: not a valid zip file: %w", err)
	}

	// Check for required files
	requiredFiles := []string{
		"word/document.xml",
		"[Content_Types].xml",
		"_rels/.rels",
	}

	fileMap := make(map[string]bool)
	for _, file := range zipReader.File {
		fileMap[file.Name] = true
	}

	for _, required := range requiredFiles {
		if !fileMap[required] {
			return fmt.Errorf("invalid DOCX: missing required file %s", required)
		}
	}

	de.log.WithContext(ctx).Debug("✅ DOCX validation successful")
	return nil
}

// GetDOCXInfo returns basic DOCX information without full extraction
func (de *DOCXExtractor) GetDOCXInfo(ctx context.Context, reader io.Reader) (*DOCXInfo, error) {
	// Read DOCX data
	docxData, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read DOCX data: %w", err)
	}

	// Create zip reader
	zipReader, err := zip.NewReader(bytes.NewReader(docxData), int64(len(docxData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create zip reader: %w", err)
	}

	info := &DOCXInfo{
		FileSize: int64(len(docxData)),
	}

	// Try to get app properties for quick stats
	if appProps, err := de.extractAppProperties(zipReader); err == nil {
		info.WordCount = appProps.Words
		info.PageCount = appProps.Pages
		info.CharCount = appProps.Characters
		info.Application = appProps.Application
	}

	return info, nil
}

// DOCXInfo represents basic DOCX information
type DOCXInfo struct {
	WordCount   int    `json:"word_count"`
	PageCount   int    `json:"page_count"`
	CharCount   int    `json:"char_count"`
	FileSize    int64  `json:"file_size"`
	Application string `json:"application"`
}

// ExtractImages extracts images from DOCX (if needed)
func (de *DOCXExtractor) ExtractImages(ctx context.Context, reader io.Reader) ([]ImageInfo, error) {
	de.log.WithContext(ctx).Debug("🖼️ Extracting images from DOCX...")

	// Read DOCX data
	docxData, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read DOCX data: %w", err)
	}

	// Create zip reader
	zipReader, err := zip.NewReader(bytes.NewReader(docxData), int64(len(docxData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create zip reader: %w", err)
	}

	var images []ImageInfo

	// Look for images in word/media/ directory
	for _, file := range zipReader.File {
		if strings.HasPrefix(file.Name, "word/media/") {
			// Extract image info
			imageInfo := ImageInfo{
				Name:   file.Name,
				Size:   int64(file.UncompressedSize64),
				Format: de.getImageFormat(file.Name),
			}

			// Read image data if needed
			if rc, err := file.Open(); err == nil {
				imageData, err := io.ReadAll(rc)
				rc.Close()
				if err == nil {
					imageInfo.Data = imageData
				}
			}

			images = append(images, imageInfo)
		}
	}

	de.log.WithContext(ctx).Debugf("🖼️ Extracted %d images from DOCX", len(images))
	return images, nil
}

// getImageFormat determines image format from filename
func (de *DOCXExtractor) getImageFormat(filename string) string {
	ext := strings.ToLower(filename[strings.LastIndex(filename, "."):])
	switch ext {
	case ".jpg", ".jpeg":
		return "jpeg"
	case ".png":
		return "png"
	case ".gif":
		return "gif"
	case ".bmp":
		return "bmp"
	case ".tiff", ".tif":
		return "tiff"
	default:
		return "unknown"
	}
}
