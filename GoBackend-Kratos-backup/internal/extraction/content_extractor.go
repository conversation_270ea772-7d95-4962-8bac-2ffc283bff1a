package extraction

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"
	"path/filepath"
	"regexp"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/data"
)

// 🔍 Content Extraction Service for Dolores Email Intelligence
// Advanced content extraction from PDF, DOCX, XLSX files using best Go libraries 2025

type ContentExtractor struct {
	log             *log.Helper
	pdfExtractor    *PDFExtractor
	docxExtractor   *DOCXExtractor
	xlsxExtractor   *XLSXExtractor
	config          *ExtractionConfig
}

type ExtractionConfig struct {
	// Performance settings
	MaxFileSize       int64         `yaml:"max_file_size"`        // Default: 100MB
	ProcessingTimeout time.Duration `yaml:"processing_timeout"`   // Default: 5m
	MaxWorkers        int           `yaml:"max_workers"`          // Default: 5
	
	// Content settings
	MaxTextLength     int           `yaml:"max_text_length"`      // Default: 1MB
	ExtractImages     bool          `yaml:"extract_images"`       // Default: false
	ExtractMetadata   bool          `yaml:"extract_metadata"`     // Default: true
	
	// Language detection
	EnableLanguageDetection bool     `yaml:"enable_language_detection"`
	SupportedLanguages     []string  `yaml:"supported_languages"`
	
	// HVAC-specific extraction
	ExtractHVACTerms       bool     `yaml:"extract_hvac_terms"`
	HVACTermsDatabase      string   `yaml:"hvac_terms_database"`
}

type ExtractionResult struct {
	Text            string                         `json:"text"`
	Metadata        *data.ExtractionMetadata       `json:"metadata"`
	ProcessingTime  time.Duration                  `json:"processing_time"`
	Success         bool                           `json:"success"`
	Error           string                         `json:"error,omitempty"`
	
	// HVAC-specific results
	HVACTerms       []HVACTerm                     `json:"hvac_terms,omitempty"`
	TechnicalData   map[string]interface{}         `json:"technical_data,omitempty"`
}

type HVACTerm struct {
	Term        string  `json:"term"`
	Category    string  `json:"category"`
	Confidence  float64 `json:"confidence"`
	Context     string  `json:"context"`
	Position    int     `json:"position"`
}

// NewContentExtractor creates a new content extraction service
func NewContentExtractor(logger log.Logger, config *ExtractionConfig) *ContentExtractor {
	return &ContentExtractor{
		log:           log.NewHelper(logger),
		pdfExtractor:  NewPDFExtractor(logger),
		docxExtractor: NewDOCXExtractor(logger),
		xlsxExtractor: NewXLSXExtractor(logger),
		config:        config,
	}
}

// ExtractContent extracts content from file based on content type
func (ce *ContentExtractor) ExtractContent(ctx context.Context, reader io.Reader, contentType, filename string) (*ExtractionResult, error) {
	startTime := time.Now()
	
	ce.log.WithContext(ctx).Infof("🔍 Starting content extraction: %s (%s)", filename, contentType)

	// Determine file type
	fileType := ce.determineFileType(contentType, filename)
	
	var result *ExtractionResult
	var err error

	// Extract based on file type
	switch fileType {
	case "pdf":
		result, err = ce.extractPDF(ctx, reader, filename)
	case "docx":
		result, err = ce.extractDOCX(ctx, reader, filename)
	case "xlsx":
		result, err = ce.extractXLSX(ctx, reader, filename)
	case "txt":
		result, err = ce.extractText(ctx, reader, filename)
	default:
		return nil, fmt.Errorf("unsupported file type: %s", fileType)
	}

	if err != nil {
		ce.log.WithContext(ctx).Errorf("❌ Content extraction failed: %v", err)
		return &ExtractionResult{
			Success:        false,
			Error:          err.Error(),
			ProcessingTime: time.Since(startTime),
		}, nil
	}

	// Post-process extracted content
	if err := ce.postProcessContent(ctx, result); err != nil {
		ce.log.WithContext(ctx).Warnf("⚠️ Post-processing failed: %v", err)
	}

	result.ProcessingTime = time.Since(startTime)
	result.Success = true

	ce.log.WithContext(ctx).Infof("✅ Content extraction completed: %s (%v)", 
		filename, result.ProcessingTime)

	return result, nil
}

// extractPDF extracts content from PDF files
func (ce *ContentExtractor) extractPDF(ctx context.Context, reader io.Reader, filename string) (*ExtractionResult, error) {
	ce.log.WithContext(ctx).Debug("📄 Extracting PDF content...")

	// Use pdfcpu or unidoc for PDF extraction
	text, metadata, err := ce.pdfExtractor.Extract(ctx, reader)
	if err != nil {
		return nil, fmt.Errorf("PDF extraction failed: %w", err)
	}

	return &ExtractionResult{
		Text:     text,
		Metadata: metadata,
	}, nil
}

// extractDOCX extracts content from DOCX files
func (ce *ContentExtractor) extractDOCX(ctx context.Context, reader io.Reader, filename string) (*ExtractionResult, error) {
	ce.log.WithContext(ctx).Debug("📝 Extracting DOCX content...")

	text, metadata, err := ce.docxExtractor.Extract(ctx, reader)
	if err != nil {
		return nil, fmt.Errorf("DOCX extraction failed: %w", err)
	}

	return &ExtractionResult{
		Text:     text,
		Metadata: metadata,
	}, nil
}

// extractXLSX extracts content from XLSX files
func (ce *ContentExtractor) extractXLSX(ctx context.Context, reader io.Reader, filename string) (*ExtractionResult, error) {
	ce.log.WithContext(ctx).Debug("📊 Extracting XLSX content...")

	text, metadata, err := ce.xlsxExtractor.Extract(ctx, reader)
	if err != nil {
		return nil, fmt.Errorf("XLSX extraction failed: %w", err)
	}

	return &ExtractionResult{
		Text:     text,
		Metadata: metadata,
	}, nil
}

// extractText extracts content from plain text files
func (ce *ContentExtractor) extractText(ctx context.Context, reader io.Reader, filename string) (*ExtractionResult, error) {
	ce.log.WithContext(ctx).Debug("📄 Extracting text content...")

	content, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read text file: %w", err)
	}

	text := string(content)
	
	metadata := &data.ExtractionMetadata{
		FileType:         "text",
		WordCount:        len(strings.Fields(text)),
		CharCount:        len(text),
		ProcessorUsed:    "text-reader",
		ProcessorVersion: "1.0",
	}

	return &ExtractionResult{
		Text:     text,
		Metadata: metadata,
	}, nil
}

// postProcessContent performs post-processing on extracted content
func (ce *ContentExtractor) postProcessContent(ctx context.Context, result *ExtractionResult) error {
	// Clean and normalize text
	result.Text = ce.cleanText(result.Text)
	
	// Detect language if enabled
	if ce.config.EnableLanguageDetection {
		language := ce.detectLanguage(result.Text)
		if result.Metadata != nil {
			result.Metadata.Language = language
		}
	}
	
	// Extract HVAC terms if enabled
	if ce.config.ExtractHVACTerms {
		hvacTerms := ce.extractHVACTerms(result.Text)
		result.HVACTerms = hvacTerms
	}
	
	// Truncate text if too long
	if len(result.Text) > ce.config.MaxTextLength {
		result.Text = result.Text[:ce.config.MaxTextLength] + "... [truncated]"
	}
	
	return nil
}

// cleanText cleans and normalizes extracted text
func (ce *ContentExtractor) cleanText(text string) string {
	// Remove excessive whitespace
	re := regexp.MustCompile(`\s+`)
	text = re.ReplaceAllString(text, " ")
	
	// Remove control characters
	re = regexp.MustCompile(`[\x00-\x1f\x7f-\x9f]`)
	text = re.ReplaceAllString(text, "")
	
	// Trim whitespace
	text = strings.TrimSpace(text)
	
	return text
}

// detectLanguage detects the language of the text
func (ce *ContentExtractor) detectLanguage(text string) string {
	// Simple language detection based on common words
	// In production, you might want to use a proper language detection library
	
	if len(text) < 50 {
		return "unknown"
	}
	
	// Check for Polish HVAC terms
	polishTerms := []string{"klimatyzacja", "wentylacja", "ogrzewanie", "chłodzenie", "pompa", "ciepła"}
	polishCount := 0
	
	textLower := strings.ToLower(text)
	for _, term := range polishTerms {
		if strings.Contains(textLower, term) {
			polishCount++
		}
	}
	
	if polishCount >= 2 {
		return "pl"
	}
	
	// Default to English
	return "en"
}

// extractHVACTerms extracts HVAC-specific terms from text
func (ce *ContentExtractor) extractHVACTerms(text string) []HVACTerm {
	var terms []HVACTerm
	
	// HVAC terms database
	hvacTermsDB := map[string]string{
		// Equipment
		"air conditioning": "equipment",
		"heat pump":        "equipment",
		"furnace":          "equipment",
		"boiler":           "equipment",
		"chiller":          "equipment",
		"compressor":       "component",
		"condenser":        "component",
		"evaporator":       "component",
		"thermostat":       "control",
		
		// Polish terms
		"klimatyzacja":     "equipment",
		"pompa ciepła":     "equipment",
		"wentylacja":       "system",
		"ogrzewanie":       "system",
		"chłodzenie":       "system",
		
		// Issues
		"not cooling":      "issue",
		"not heating":      "issue",
		"noise":            "issue",
		"leak":             "issue",
		"broken":           "issue",
		
		// Maintenance
		"filter":           "maintenance",
		"cleaning":         "maintenance",
		"service":          "maintenance",
		"repair":           "maintenance",
		"installation":     "maintenance",
	}
	
	textLower := strings.ToLower(text)
	
	for term, category := range hvacTermsDB {
		if strings.Contains(textLower, term) {
			// Find all occurrences
			positions := ce.findTermPositions(textLower, term)
			for _, pos := range positions {
				// Get context around the term
				context := ce.getContext(text, pos, len(term), 50)
				
				terms = append(terms, HVACTerm{
					Term:       term,
					Category:   category,
					Confidence: 0.8, // Simple confidence score
					Context:    context,
					Position:   pos,
				})
			}
		}
	}
	
	return terms
}

// findTermPositions finds all positions of a term in text
func (ce *ContentExtractor) findTermPositions(text, term string) []int {
	var positions []int
	start := 0
	
	for {
		pos := strings.Index(text[start:], term)
		if pos == -1 {
			break
		}
		
		actualPos := start + pos
		positions = append(positions, actualPos)
		start = actualPos + len(term)
	}
	
	return positions
}

// getContext extracts context around a term
func (ce *ContentExtractor) getContext(text string, position, termLength, contextSize int) string {
	start := position - contextSize
	if start < 0 {
		start = 0
	}
	
	end := position + termLength + contextSize
	if end > len(text) {
		end = len(text)
	}
	
	context := text[start:end]
	return strings.TrimSpace(context)
}

// determineFileType determines file type from content type and filename
func (ce *ContentExtractor) determineFileType(contentType, filename string) string {
	// Check content type first
	switch contentType {
	case "application/pdf":
		return "pdf"
	case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		return "docx"
	case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
		return "xlsx"
	case "text/plain":
		return "txt"
	}
	
	// Fallback to file extension
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".pdf":
		return "pdf"
	case ".docx":
		return "docx"
	case ".xlsx":
		return "xlsx"
	case ".txt":
		return "txt"
	default:
		return "unknown"
	}
}

// GetSupportedTypes returns list of supported file types
func (ce *ContentExtractor) GetSupportedTypes() []string {
	return []string{"pdf", "docx", "xlsx", "txt"}
}

// IsSupported checks if file type is supported
func (ce *ContentExtractor) IsSupported(contentType, filename string) bool {
	fileType := ce.determineFileType(contentType, filename)
	for _, supported := range ce.GetSupportedTypes() {
		if fileType == supported {
			return true
		}
	}
	return false
}
