package octopus

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// 👥 Customer Metrics Handler
func (o *MorphicOctopusInterface) handleCustomerMetrics(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	metrics, err := o.buildCustomerMetrics(ctx)
	if err != nil {
		http.Error(w, "Failed to get customer metrics", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// 🎯 Customer Segments Handler
func (o *MorphicOctopusInterface) handleCustomerSegments(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		// Get all customer segments
		var segments []map[string]interface{}
		o.db.Table("customer_segments").Find(&segments)
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(segments)
	} else if r.Method == "POST" {
		// Create new customer segment
		var segment map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&segment); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}
		
		segment["created_at"] = time.Now()
		segment["updated_at"] = time.Now()
		
		if err := o.db.Table("customer_segments").Create(&segment).Error; err != nil {
			http.Error(w, "Failed to create segment", http.StatusInternalServerError)
			return
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(segment)
	}
}

// 🧠 Customer Intelligence Handler
func (o *MorphicOctopusInterface) handleCustomerIntelligence(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerID := vars["id"]
	
	// Placeholder customer intelligence data
	intelligence := map[string]interface{}{
		"customer_id": customerID,
		"profile": map[string]interface{}{
			"satisfaction_score": 4.2,
			"churn_probability": 0.15,
			"lifetime_value":    2500.00,
			"preferred_contact": "email",
		},
		"insights": []string{
			"High satisfaction customer",
			"Regular maintenance schedule",
			"Prefers email communication",
		},
		"predictions": map[string]interface{}{
			"next_service_date": time.Now().AddDate(0, 3, 0),
			"upsell_probability": 0.75,
		},
		"timestamp": time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(intelligence)
}

// 🔍 Customer Search Handler
func (o *MorphicOctopusInterface) handleCustomerSearch(w http.ResponseWriter, r *http.Request) {
	var searchRequest struct {
		Query    string `json:"query"`
		Filters  map[string]interface{} `json:"filters,omitempty"`
		Limit    int    `json:"limit,omitempty"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&searchRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	// Placeholder search results
	results := []map[string]interface{}{
		{
			"id":    1,
			"name":  "John Smith",
			"email": "<EMAIL>",
			"phone": "******-0101",
			"score": 0.95,
		},
		{
			"id":    2,
			"name":  "Jane Doe",
			"email": "<EMAIL>",
			"phone": "******-0102",
			"score": 0.87,
		},
	}
	
	response := map[string]interface{}{
		"query":   searchRequest.Query,
		"results": results,
		"total":   len(results),
		"timestamp": time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 🔄 Customer Analytics Refresh Handler
func (o *MorphicOctopusInterface) handleCustomerAnalyticsRefresh(w http.ResponseWriter, r *http.Request) {
	o.log.Info("🔄 Customer analytics refresh requested")
	
	// In a real implementation, this would trigger analytics recalculation
	response := map[string]interface{}{
		"status":    "started",
		"message":   "Customer analytics refresh initiated",
		"timestamp": time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 📤 Customer Export Handler
func (o *MorphicOctopusInterface) handleCustomerExport(w http.ResponseWriter, r *http.Request) {
	var exportRequest struct {
		Format  string   `json:"format"` // csv, json, xlsx
		Fields  []string `json:"fields,omitempty"`
		Filters map[string]interface{} `json:"filters,omitempty"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&exportRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	o.log.Infof("📤 Customer export requested: format=%s", exportRequest.Format)
	
	response := map[string]interface{}{
		"status":     "started",
		"export_id":  "export_" + time.Now().Format("20060102_150405"),
		"format":     exportRequest.Format,
		"message":    "Customer data export initiated",
		"timestamp":  time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 📞 Transcription Stats Handler
func (o *MorphicOctopusInterface) handleTranscriptionStats(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	stats, err := o.buildTranscriptionStats(ctx)
	if err != nil {
		http.Error(w, "Failed to get transcription stats", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// 📡 Transcription Sources Handler
func (o *MorphicOctopusInterface) handleTranscriptionSources(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		var sources []map[string]interface{}
		o.db.Table("transcription.call_sources").Find(&sources)
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(sources)
	} else if r.Method == "POST" {
		var source map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&source); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}
		
		source["created_at"] = time.Now()
		source["updated_at"] = time.Now()
		
		if err := o.db.Table("transcription.call_sources").Create(&source).Error; err != nil {
			http.Error(w, "Failed to create source", http.StatusInternalServerError)
			return
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(source)
	}
}

// 📡 Transcription Source Handler (individual)
func (o *MorphicOctopusInterface) handleTranscriptionSource(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	sourceID := vars["id"]
	
	if r.Method == "GET" {
		var source map[string]interface{}
		if err := o.db.Table("transcription.call_sources").Where("id = ?", sourceID).First(&source).Error; err != nil {
			http.Error(w, "Source not found", http.StatusNotFound)
			return
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(source)
	} else if r.Method == "PUT" {
		var updates map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&updates); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}
		
		updates["updated_at"] = time.Now()
		
		if err := o.db.Table("transcription.call_sources").Where("id = ?", sourceID).Updates(updates).Error; err != nil {
			http.Error(w, "Failed to update source", http.StatusInternalServerError)
			return
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{"status": "updated", "id": sourceID})
	} else if r.Method == "DELETE" {
		if err := o.db.Table("transcription.call_sources").Where("id = ?", sourceID).Delete(nil).Error; err != nil {
			http.Error(w, "Failed to delete source", http.StatusInternalServerError)
			return
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{"status": "deleted", "id": sourceID})
	}
}

// 📞 Transcription Calls Handler
func (o *MorphicOctopusInterface) handleTranscriptionCalls(w http.ResponseWriter, r *http.Request) {
	var calls []map[string]interface{}
	
	query := o.db.Table("transcription.call_transcriptions").
		Select("id, phone_number, caller_name, call_timestamp, call_purpose, urgency_level, hvac_relevance").
		Order("call_timestamp DESC").
		Limit(50)
	
	if err := query.Find(&calls).Error; err != nil {
		http.Error(w, "Failed to get calls", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(calls)
}

// 📞 Transcription Call Handler (individual)
func (o *MorphicOctopusInterface) handleTranscriptionCall(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	callID := vars["id"]
	
	var call map[string]interface{}
	if err := o.db.Table("transcription.call_transcriptions").Where("id = ?", callID).First(&call).Error; err != nil {
		http.Error(w, "Call not found", http.StatusNotFound)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(call)
}

// 🔄 Transcription Reprocess Handler
func (o *MorphicOctopusInterface) handleTranscriptionReprocess(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	callID := vars["id"]
	
	o.log.Infof("🔄 Reprocessing transcription: %s", callID)
	
	response := map[string]interface{}{
		"status":    "started",
		"call_id":   callID,
		"message":   "Transcription reprocessing initiated",
		"timestamp": time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ⚡ Transcription Process Handler
func (o *MorphicOctopusInterface) handleTranscriptionProcess(w http.ResponseWriter, r *http.Request) {
	o.log.Info("⚡ Processing pending transcriptions")
	
	response := map[string]interface{}{
		"status":    "started",
		"message":   "Transcription processing initiated",
		"timestamp": time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}