package octopus

import (
	"context"
	"fmt"
	"time"
)

// 👥 Build Customer Metrics
func (o *MorphicOctopusInterface) buildCustomerMetrics(ctx context.Context) (*CustomerMetrics, error) {
	var metrics CustomerMetrics

	// Total customers
	o.db.Model(&struct{ ID int64 }{}).Table("customers").Count(&metrics.TotalCustomers)

	// New customers today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("created_at >= ?", today).Count(&metrics.NewToday)

	// New customers this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("created_at >= ?", weekStart).Count(&metrics.NewThisWeek)

	// Active customers (contacted in last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("last_contact_date >= ?", thirtyDaysAgo).Count(&metrics.ActiveCustomers)

	// High value customers (lifetime value > 5000)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("lifetime_value > ?", 5000).Count(&metrics.HighValueCustomers)

	// At risk customers (churn probability > 0.7)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("churn_probability > ?", 0.7).Count(&metrics.AtRiskCustomers)

	// Average satisfaction
	o.db.Model(&struct{ AvgSatisfaction float64 }{}).Table("customer_analytics").
		Select("AVG(avg_satisfaction)").Scan(&metrics.AvgSatisfaction)

	// Churn rate (placeholder calculation)
	metrics.ChurnRate = 0.05 // 5% placeholder

	// Average lifetime value
	o.db.Model(&struct{ AvgLTV float64 }{}).Table("customer_analytics").
		Select("AVG(lifetime_value)").Scan(&metrics.LifetimeValue)

	return &metrics, nil
}

// 📞 Build Transcription Statistics
func (o *MorphicOctopusInterface) buildTranscriptionStats(ctx context.Context) (*TranscriptionStats, error) {
	var stats TranscriptionStats

	// Total calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").Count(&stats.TotalCalls)

	// Calls today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("call_timestamp >= ?", today).Count(&stats.CallsToday)

	// Calls this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("call_timestamp >= ?", weekStart).Count(&stats.CallsThisWeek)

	// HVAC relevant calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("hvac_relevance = ?", true).Count(&stats.HVACRelevantCalls)

	// Emergency calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("urgency_level = ?", "critical").Count(&stats.EmergencyCalls)

	// Average confidence
	o.db.Model(&struct{ AvgConfidence float64 }{}).Table("transcription.call_transcriptions").
		Select("AVG(confidence_score)").Scan(&stats.AvgConfidence)

	// Processing backlog
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.transcription_emails").
		Where("processing_status = ?", "pending").Count(&stats.ProcessingBacklog)

	// Top caller companies
	var companies []string
	o.db.Model(&struct{ CallerCompany string }{}).Table("transcription.call_transcriptions").
		Select("caller_company").Where("caller_company IS NOT NULL AND caller_company != ''").
		Group("caller_company").Order("COUNT(*) DESC").Limit(5).Pluck("caller_company", &companies)
	stats.TopCallerCompanies = companies

	return &stats, nil
}

// 📧 Build Email Intelligence
func (o *MorphicOctopusInterface) buildEmailIntelligence(ctx context.Context) (*EmailIntelligence, error) {
	var intelligence EmailIntelligence

	// Total emails
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").Count(&intelligence.TotalEmails)

	// Emails today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").
		Where("created_at >= ?", today).Count(&intelligence.EmailsToday)

	// Emails this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").
		Where("created_at >= ?", weekStart).Count(&intelligence.EmailsThisWeek)

	// HVAC relevant emails (from customer interactions)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND hvac_relevance = ?", "email", true).Count(&intelligence.HVACRelevantEmails)

	// Positive sentiment
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND sentiment IN (?)", "email", []string{"positive", "very_positive"}).
		Count(&intelligence.PositiveSentiment)

	// Negative sentiment
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND sentiment IN (?)", "email", []string{"negative", "very_negative"}).
		Count(&intelligence.NegativeSentiment)

	// Average processing time (placeholder)
	intelligence.AvgProcessingTime = 2 * time.Second

	// Top keywords (placeholder)
	intelligence.TopKeywords = []string{"HVAC", "repair", "maintenance", "emergency", "installation"}

	return &intelligence, nil
}

// 🤖 Build AI Performance
func (o *MorphicOctopusInterface) buildAIPerformance(ctx context.Context) (*AIPerformance, error) {
	var performance AIPerformance

	// Placeholder data - in real implementation, this would come from AI service metrics
	performance.TotalRequests = 10000
	performance.RequestsToday = 150
	performance.AvgResponseTime = 250 * time.Millisecond
	performance.SuccessRate = 97.5
	performance.ModelAccuracy = 92.3
	performance.TokensProcessed = 500000
	performance.ActiveModels = []string{"gemma:3b-instruct-q4_0", "bielik-v3", "nomic-embed-text"}
	performance.QueueLength = 5

	return &performance, nil
}

// 🚨 Build Realtime Alerts
func (o *MorphicOctopusInterface) buildRealtimeAlerts(ctx context.Context) []*Alert {
	alerts := []*Alert{}

	// Check for high churn risk customers
	var atRiskCount int64
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("churn_probability > ?", 0.8).Count(&atRiskCount)

	if atRiskCount > 0 {
		alerts = append(alerts, &Alert{
			ID:        "high_churn_risk",
			Type:      "warning",
			Title:     "High Churn Risk Customers",
			Message:   fmt.Sprintf("%d customers have high churn risk (>80%%)", atRiskCount),
			Source:    "customer_analytics",
			Timestamp: time.Now(),
			Actions:   []string{"view_customers", "send_retention_campaign"},
		})
	}

	// Check for processing backlog
	var backlogCount int64
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.transcription_emails").
		Where("processing_status = ?", "pending").Count(&backlogCount)

	if backlogCount > 10 {
		alerts = append(alerts, &Alert{
			ID:        "transcription_backlog",
			Type:      "warning",
			Title:     "Transcription Processing Backlog",
			Message:   fmt.Sprintf("%d emails pending transcription processing", backlogCount),
			Source:    "transcription_service",
			Timestamp: time.Now(),
			Actions:   []string{"process_backlog", "check_service_health"},
		})
	}

	// Check for emergency calls
	var emergencyCount int64
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("urgency_level = ? AND call_timestamp >= ?", "critical", time.Now().Add(-time.Hour)).
		Count(&emergencyCount)

	if emergencyCount > 0 {
		alerts = append(alerts, &Alert{
			ID:        "emergency_calls",
			Type:      "critical",
			Title:     "Emergency Calls Detected",
			Message:   fmt.Sprintf("%d emergency calls in the last hour", emergencyCount),
			Source:    "transcription_service",
			Timestamp: time.Now(),
			Actions:   []string{"view_emergency_calls", "notify_technicians"},
		})
	}

	return alerts
}

// ⚡ Build Quick Actions
func (o *MorphicOctopusInterface) buildQuickActions() []*QuickAction {
	return []*QuickAction{
		{
			ID:          "refresh_analytics",
			Title:       "Refresh Customer Analytics",
			Description: "Recalculate all customer analytics and insights",
			Icon:        "refresh",
			Endpoint:    "/api/customers/analytics/refresh",
			Method:      "POST",
			Category:    "analytics",
		},
		{
			ID:          "process_transcriptions",
			Title:       "Process Pending Transcriptions",
			Description: "Process all pending transcription emails",
			Icon:        "play_arrow",
			Endpoint:    "/api/transcription/process",
			Method:      "POST",
			Category:    "transcription",
		},
		{
			ID:          "send_maintenance_reminders",
			Title:       "Send Maintenance Reminders",
			Description: "Send automated maintenance reminders to customers",
			Icon:        "email",
			Endpoint:    "/api/email/campaigns",
			Method:      "POST",
			Category:    "email",
		},
		{
			ID:          "backup_database",
			Title:       "Backup Database",
			Description: "Create a full database backup",
			Icon:        "backup",
			Endpoint:    "/api/system/backup",
			Method:      "POST",
			Category:    "system",
		},
		{
			ID:          "restart_ai_service",
			Title:       "Restart AI Service",
			Description: "Restart the AI analysis service",
			Icon:        "restart_alt",
			Endpoint:    "/api/services/ai/restart",
			Method:      "POST",
			Category:    "ai",
			Dangerous:   true,
		},
		{
			ID:          "export_customer_data",
			Title:       "Export Customer Data",
			Description: "Export customer data for analysis",
			Icon:        "download",
			Endpoint:    "/api/customers/export",
			Method:      "POST",
			Category:    "data",
		},
	}
}

// 🎨 Generate Dashboard HTML (moved to separate template files)
func (o *MorphicOctopusInterface) generateDashboardHTML() string {
	return `<html><body><h1>🐙 Octopus Dashboard</h1><p>Dashboard moved to separate template files</p></body></html>`
}
