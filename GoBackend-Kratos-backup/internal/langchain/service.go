package langchain

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/ollama"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
)

// 🚀 LangChain Go Service - Unified AI Pipeline for HVAC CRM
// Provides standardized LLM operations with advanced chaining capabilities

type Service struct {
	llm               llms.LLM
	embedder          embeddings.Embedder
	hvacChain         chains.Chain
	emailChain        chains.Chain
	analysisChain     chains.Chain
	config            *conf.AI_Model
	log               *log.Helper
	metrics           *LangChainMetrics

	// 🚀 Enhanced AI Components
	hvacAgents        *HVACAgentChains      // HVAC-specific agent chains
	semanticSearch    *SemanticSearchService // Vector-based semantic search
	workflowEngine    *WorkflowEngine       // Intelligent workflow automation
}

// LangChainMetrics tracks performance metrics
type LangChainMetrics struct {
	ChainExecutions   int64
	EmbeddingRequests int64
	LLMCalls          int64
	ErrorCount        int64
	AvgResponseTime   float64
}

// NewService creates a new LangChain service
func NewService(config *conf.AI_Model, logger log.Logger) (*Service, error) {
	helper := log.NewHelper(logger)
	helper.Info("🚀 Initializing LangChain Go Service...")

	// Initialize Ollama LLM
	llm, err := ollama.New(
		ollama.WithModel("gemma:3b-instruct-q4_0"),
		ollama.WithServerURL(config.Endpoint),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Ollama LLM: %w", err)
	}

	// Initialize embedder
	embedder, err := embeddings.NewEmbedder(llm)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize embedder: %w", err)
	}

	service := &Service{
		llm:      llm,
		embedder: embedder,
		config:   config,
		log:      helper,
		metrics:  &LangChainMetrics{},
	}

	// Initialize chains
	if err := service.initializeChains(); err != nil {
		return nil, fmt.Errorf("failed to initialize chains: %w", err)
	}

	// 🚀 Initialize enhanced AI components
	if err := service.initializeEnhancedComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize enhanced components: %w", err)
	}

	helper.Info("✅ LangChain Go Service with enhanced AI capabilities initialized successfully!")
	return service, nil
}

// initializeChains sets up specialized processing chains
func (s *Service) initializeChains() error {
	var err error

	// HVAC Analysis Chain
	s.hvacChain, err = s.createHVACAnalysisChain()
	if err != nil {
		return fmt.Errorf("failed to create HVAC chain: %w", err)
	}

	// Email Processing Chain
	s.emailChain, err = s.createEmailProcessingChain()
	if err != nil {
		return fmt.Errorf("failed to create email chain: %w", err)
	}

	// General Analysis Chain
	s.analysisChain, err = s.createAnalysisChain()
	if err != nil {
		return fmt.Errorf("failed to create analysis chain: %w", err)
	}

	return nil
}

// 🚀 initializeEnhancedComponents sets up advanced AI components
func (s *Service) initializeEnhancedComponents() error {
	var err error

	// Initialize HVAC Agent Chains
	s.hvacAgents, err = NewHVACAgentChains(s.llm, log.DefaultLogger)
	if err != nil {
		return fmt.Errorf("failed to initialize HVAC agents: %w", err)
	}

	// Initialize Semantic Search Service
	s.semanticSearch, err = NewSemanticSearchService(s.llm, log.DefaultLogger)
	if err != nil {
		return fmt.Errorf("failed to initialize semantic search: %w", err)
	}

	// Initialize Workflow Engine
	s.workflowEngine, err = NewWorkflowEngine(s.llm, log.DefaultLogger)
	if err != nil {
		return fmt.Errorf("failed to initialize workflow engine: %w", err)
	}

	s.log.Info("✅ Enhanced AI components initialized successfully!")
	return nil
}

// Chat processes a chat request using LangChain
func (s *Service) Chat(ctx context.Context, req *biz.ChatRequest) (*biz.ChatResponse, error) {
	s.log.WithContext(ctx).Infof("🤖 Processing chat with LangChain: %s", req.Message)
	s.metrics.LLMCalls++

	// Use appropriate chain based on context
	var response string
	var err error

	if s.isHVACRelated(req.Message) {
		response, err = s.processWithHVACChain(ctx, req)
	} else if s.isEmailRelated(req.Message) {
		response, err = s.processWithEmailChain(ctx, req)
	} else {
		response, err = s.processWithGeneralLLM(ctx, req)
	}

	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("LangChain chat failed: %w", err)
	}

	return &biz.ChatResponse{
		Response:   response,
		ModelUsed:  "langchain-gemma-3b",
		TokensUsed: int32(len(response) / 4), // Rough token estimation
	}, nil
}

// ProcessHVACIssue processes HVAC-specific issues using specialized chain
func (s *Service) ProcessHVACIssue(ctx context.Context, customerIssue, systemType string) (*HVACAnalysisResult, error) {
	s.log.WithContext(ctx).Info("🔧 Processing HVAC issue with specialized chain")
	s.metrics.ChainExecutions++

	input := map[string]interface{}{
		"customer_issue": customerIssue,
		"system_type":    systemType,
		"context":        "professional HVAC analysis",
	}

	result, err := chains.Run(ctx, s.hvacChain, input)
	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("HVAC chain execution failed: %w", err)
	}

	return s.parseHVACResult(result), nil
}

// ProcessEmail processes emails using specialized email chain
func (s *Service) ProcessEmail(ctx context.Context, emailContent, sender string) (*EmailAnalysisResult, error) {
	s.log.WithContext(ctx).Info("📧 Processing email with specialized chain")
	s.metrics.ChainExecutions++

	input := map[string]interface{}{
		"email_content": emailContent,
		"sender":        sender,
		"context":       "email classification and routing",
	}

	result, err := chains.Run(ctx, s.emailChain, input)
	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("email chain execution failed: %w", err)
	}

	return s.parseEmailResult(result), nil
}

// GenerateEmbeddings creates embeddings for text using LangChain embedder
func (s *Service) GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error) {
	s.log.WithContext(ctx).Infof("🧠 Generating embeddings for %d texts", len(texts))
	s.metrics.EmbeddingRequests++

	embeddings, err := s.embedder.EmbedDocuments(ctx, texts)
	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("embedding generation failed: %w", err)
	}

	return embeddings, nil
}

// SearchSimilarContent finds similar content using embeddings
func (s *Service) SearchSimilarContent(ctx context.Context, query string, documents []string, topK int) ([]SimilarityResult, error) {
	s.log.WithContext(ctx).Infof("🔍 Searching similar content for query: %s", query)

	// Generate query embedding
	queryEmbedding, err := s.embedder.EmbedQuery(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to embed query: %w", err)
	}

	// Generate document embeddings
	docEmbeddings, err := s.embedder.EmbedDocuments(ctx, documents)
	if err != nil {
		return nil, fmt.Errorf("failed to embed documents: %w", err)
	}

	// Calculate similarities
	similarities := make([]SimilarityResult, len(documents))
	for i, docEmb := range docEmbeddings {
		similarity := s.cosineSimilarity(queryEmbedding, docEmb)
		similarities[i] = SimilarityResult{
			Index:      i,
			Document:   documents[i],
			Similarity: similarity,
		}
	}

	// Sort by similarity and return top K
	return s.getTopKSimilar(similarities, topK), nil
}

// AnalyzeContent performs general content analysis
func (s *Service) AnalyzeContent(ctx context.Context, content, analysisType string) (*AnalysisResult, error) {
	s.log.WithContext(ctx).Infof("📊 Analyzing content with type: %s", analysisType)
	s.metrics.ChainExecutions++

	input := map[string]interface{}{
		"content":       content,
		"analysis_type": analysisType,
		"context":       "comprehensive content analysis",
	}

	result, err := chains.Run(ctx, s.analysisChain, input)
	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("analysis chain execution failed: %w", err)
	}

	return s.parseAnalysisResult(result), nil
}

// GetMetrics returns current LangChain metrics
func (s *Service) GetMetrics() *LangChainMetrics {
	return s.metrics
}

// HealthCheck verifies LangChain service health
func (s *Service) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🏥 Performing LangChain health check")

	// Test LLM connectivity
	_, err := s.llm.Call(ctx, "Health check test")
	if err != nil {
		return fmt.Errorf("LLM health check failed: %w", err)
	}

	// Test embedder
	_, err = s.embedder.EmbedQuery(ctx, "test")
	if err != nil {
		return fmt.Errorf("embedder health check failed: %w", err)
	}

	return nil
}

// 🚀 Enhanced AI Methods - World-Class HVAC Intelligence

// DiagnoseEquipment performs advanced equipment diagnostic analysis
func (s *Service) DiagnoseEquipment(ctx context.Context, req *DiagnosticRequest) (*DiagnosticResult, error) {
	s.log.WithContext(ctx).Info("🔧 Performing advanced equipment diagnosis")

	if s.hvacAgents == nil {
		return nil, fmt.Errorf("HVAC agents not initialized")
	}

	return s.hvacAgents.DiagnoseEquipment(ctx, req)
}

// PredictMaintenance performs predictive maintenance analysis
func (s *Service) PredictMaintenance(ctx context.Context, req *MaintenanceRequest) (*MaintenanceResult, error) {
	s.log.WithContext(ctx).Info("🔮 Performing predictive maintenance analysis")

	if s.hvacAgents == nil {
		return nil, fmt.Errorf("HVAC agents not initialized")
	}

	return s.hvacAgents.PredictMaintenance(ctx, req)
}

// GenerateCustomerCommunication creates intelligent customer communications
func (s *Service) GenerateCustomerCommunication(ctx context.Context, req *CustomerCommRequest) (*CustomerCommResult, error) {
	s.log.WithContext(ctx).Info("💬 Generating intelligent customer communication")

	if s.hvacAgents == nil {
		return nil, fmt.Errorf("HVAC agents not initialized")
	}

	return s.hvacAgents.GenerateCustomerCommunication(ctx, req)
}

// SearchKnowledge performs semantic search across HVAC knowledge base
func (s *Service) SearchKnowledge(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	s.log.WithContext(ctx).Info("🔍 Performing semantic knowledge search")

	if s.semanticSearch == nil {
		return nil, fmt.Errorf("semantic search not initialized")
	}

	return s.semanticSearch.Search(ctx, req)
}

// FindSimilarIssues finds similar HVAC issues using semantic search
func (s *Service) FindSimilarIssues(ctx context.Context, issueDescription string, topK int) (*SearchResponse, error) {
	s.log.WithContext(ctx).Info("🔧 Finding similar HVAC issues")

	if s.semanticSearch == nil {
		return nil, fmt.Errorf("semantic search not initialized")
	}

	return s.semanticSearch.FindSimilarIssues(ctx, issueDescription, topK)
}

// FindEquipmentInfo finds equipment information using semantic search
func (s *Service) FindEquipmentInfo(ctx context.Context, equipmentQuery string, topK int) (*SearchResponse, error) {
	s.log.WithContext(ctx).Info("📖 Finding equipment information")

	if s.semanticSearch == nil {
		return nil, fmt.Errorf("semantic search not initialized")
	}

	return s.semanticSearch.FindEquipmentInfo(ctx, equipmentQuery, topK)
}

// ExecuteWorkflow executes an intelligent workflow
func (s *Service) ExecuteWorkflow(ctx context.Context, req *WorkflowRequest) (*WorkflowExecution, error) {
	s.log.WithContext(ctx).Info("🔄 Executing intelligent workflow")

	if s.workflowEngine == nil {
		return nil, fmt.Errorf("workflow engine not initialized")
	}

	return s.workflowEngine.ExecuteWorkflow(ctx, req)
}

// IndexKnowledgeDocument adds a document to the knowledge base
func (s *Service) IndexKnowledgeDocument(ctx context.Context, doc *KnowledgeDocument, collection string) error {
	s.log.WithContext(ctx).Info("📚 Indexing knowledge document")

	if s.semanticSearch == nil {
		return fmt.Errorf("semantic search not initialized")
	}

	return s.semanticSearch.IndexDocument(ctx, doc, collection)
}

// GetEnhancedMetrics returns comprehensive AI metrics
func (s *Service) GetEnhancedMetrics() map[string]interface{} {
	metrics := map[string]interface{}{
		"langchain_metrics": s.metrics,
	}

	if s.semanticSearch != nil {
		metrics["search_metrics"] = s.semanticSearch.GetSearchMetrics()
	}

	if s.workflowEngine != nil {
		metrics["workflow_metrics"] = s.workflowEngine.GetWorkflowMetrics()
	}

	return metrics
}

// Helper methods

func (s *Service) isHVACRelated(message string) bool {
	hvacKeywords := []string{
		"hvac", "heating", "cooling", "air conditioning", "ventilation",
		"furnace", "boiler", "heat pump", "ductwork", "thermostat",
		"filter", "maintenance", "repair", "installation",
	}

	lowerMessage := strings.ToLower(message)
	for _, keyword := range hvacKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return true
		}
	}
	return false
}

func (s *Service) isEmailRelated(message string) bool {
	emailKeywords := []string{
		"email", "message", "sender", "recipient", "subject",
		"classification", "routing", "priority", "urgent",
	}

	lowerMessage := strings.ToLower(message)
	for _, keyword := range emailKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return true
		}
	}
	return false
}

func (s *Service) processWithHVACChain(ctx context.Context, req *biz.ChatRequest) (string, error) {
	input := map[string]interface{}{
		"message": req.Message,
		"context": strings.Join(req.Context, "\n"),
	}

	result, err := chains.Run(ctx, s.hvacChain, input)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%v", result), nil
}

func (s *Service) processWithEmailChain(ctx context.Context, req *biz.ChatRequest) (string, error) {
	input := map[string]interface{}{
		"message": req.Message,
		"context": strings.Join(req.Context, "\n"),
	}

	result, err := chains.Run(ctx, s.emailChain, input)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%v", result), nil
}

func (s *Service) processWithGeneralLLM(ctx context.Context, req *biz.ChatRequest) (string, error) {
	prompt := req.Message
	if len(req.Context) > 0 {
		prompt = fmt.Sprintf("Context: %s\n\nQuestion: %s", strings.Join(req.Context, "\n"), req.Message)
	}

	return s.llm.Call(ctx, prompt)
}

// Helper methods for parsing results

func (s *Service) parseHVACResult(result interface{}) *HVACAnalysisResult {
	resultStr := fmt.Sprintf("%v", result)

	return &HVACAnalysisResult{
		IssueID:        fmt.Sprintf("hvac_%d", time.Now().Unix()),
		PrimaryProblem: s.extractPrimaryProblem(resultStr),
		Category:       s.extractHVACCategory(resultStr),
		UrgencyLevel:   s.extractUrgencyLevel(resultStr),
		RootCauses:     s.extractRootCauses(resultStr),
		Confidence:     s.calculateResultConfidence(resultStr),
		ProcessedAt:    time.Now(),
		Metadata:       map[string]interface{}{"raw_result": resultStr},
	}
}

func (s *Service) parseEmailResult(result interface{}) *EmailAnalysisResult {
	resultStr := fmt.Sprintf("%v", result)

	return &EmailAnalysisResult{
		EmailID:     fmt.Sprintf("email_%d", time.Now().Unix()),
		Classification: EmailClassification{
			Category:  s.extractEmailCategory(resultStr),
			Priority:  s.extractPriority(resultStr),
			Sentiment: s.extractSentiment(resultStr),
		},
		ContentAnalysis: EmailContentAnalysis{
			MainTopic:      s.extractMainTopic(resultStr),
			KeyIssues:      s.extractKeyIssues(resultStr),
			CustomerIntent: s.extractCustomerIntent(resultStr),
		},
		RoutingInfo: EmailRoutingInfo{
			Department:       s.extractDepartment(resultStr),
			ResponseTimeline: s.extractResponseTimeline(resultStr),
		},
		Confidence:  s.calculateResultConfidence(resultStr),
		ProcessedAt: time.Now(),
		Metadata:    map[string]interface{}{"raw_result": resultStr},
	}
}

func (s *Service) parseAnalysisResult(result interface{}) *AnalysisResult {
	resultStr := fmt.Sprintf("%v", result)

	return &AnalysisResult{
		AnalysisID:   fmt.Sprintf("analysis_%d", time.Now().Unix()),
		AnalysisType: "general",
		Content:      resultStr,
		Results:      map[string]interface{}{"analysis": resultStr},
		Insights:     s.extractInsights(resultStr),
		Recommendations: s.extractRecommendations(resultStr),
		Confidence:   s.calculateResultConfidence(resultStr),
		ProcessedAt:  time.Now(),
		Metadata:     map[string]interface{}{"raw_result": resultStr},
	}
}

// Cosine similarity calculation
func (s *Service) cosineSimilarity(a, b []float32) float64 {
	if len(a) != len(b) {
		return 0.0
	}

	var dotProduct, normA, normB float64
	for i := range a {
		dotProduct += float64(a[i] * b[i])
		normA += float64(a[i] * a[i])
		normB += float64(b[i] * b[i])
	}

	if normA == 0 || normB == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

// Get top K similar results
func (s *Service) getTopKSimilar(similarities []SimilarityResult, topK int) []SimilarityResult {
	sort.Slice(similarities, func(i, j int) bool {
		return similarities[i].Similarity > similarities[j].Similarity
	})

	if len(similarities) > topK {
		return similarities[:topK]
	}
	return similarities
}

// Extraction helper methods
func (s *Service) extractPrimaryProblem(text string) string {
	// Simple extraction - in production, use more sophisticated NLP
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "problem") ||
		   strings.Contains(strings.ToLower(line), "issue") {
			return strings.TrimSpace(line)
		}
	}
	return "Issue analysis pending"
}

func (s *Service) extractHVACCategory(text string) HVACIssueCategory {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "heating") || strings.Contains(lowerText, "furnace") {
		return CategoryHeating
	}
	if strings.Contains(lowerText, "cooling") || strings.Contains(lowerText, "air conditioning") {
		return CategoryCooling
	}
	if strings.Contains(lowerText, "ventilation") || strings.Contains(lowerText, "duct") {
		return CategoryVentilation
	}
	if strings.Contains(lowerText, "maintenance") {
		return CategoryMaintenance
	}
	if strings.Contains(lowerText, "emergency") {
		return CategoryEmergency
	}

	return CategoryOther
}

func (s *Service) extractUrgencyLevel(text string) UrgencyLevel {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "emergency") || strings.Contains(lowerText, "urgent") {
		return UrgencyEmergency
	}
	if strings.Contains(lowerText, "high") {
		return UrgencyHigh
	}
	if strings.Contains(lowerText, "medium") {
		return UrgencyMedium
	}

	return UrgencyLow
}

func (s *Service) extractRootCauses(text string) []string {
	var causes []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "cause") {
			causes = append(causes, strings.TrimSpace(line))
		}
	}

	if len(causes) == 0 {
		causes = append(causes, "Root cause analysis in progress")
	}

	return causes
}

func (s *Service) extractEmailCategory(text string) EmailCategory {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "service") {
		return CategoryServiceRequest
	}
	if strings.Contains(lowerText, "complaint") {
		return CategoryComplaint
	}
	if strings.Contains(lowerText, "appointment") {
		return CategoryAppointment
	}
	if strings.Contains(lowerText, "billing") {
		return CategoryBilling
	}
	if strings.Contains(lowerText, "emergency") {
		return CategoryEmergencyEmail
	}

	return CategoryOtherEmail
}

func (s *Service) extractPriority(text string) Priority {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "urgent") {
		return PriorityUrgent
	}
	if strings.Contains(lowerText, "high") {
		return PriorityHigh
	}
	if strings.Contains(lowerText, "medium") {
		return PriorityMedium
	}

	return PriorityLow
}

func (s *Service) extractSentiment(text string) Sentiment {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "angry") || strings.Contains(lowerText, "frustrated") {
		return SentimentAngry
	}
	if strings.Contains(lowerText, "negative") || strings.Contains(lowerText, "unhappy") {
		return SentimentNegative
	}
	if strings.Contains(lowerText, "positive") || strings.Contains(lowerText, "satisfied") {
		return SentimentPositive
	}

	return SentimentNeutral
}

func (s *Service) extractMainTopic(text string) string {
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "topic") {
			return strings.TrimSpace(line)
		}
	}
	return "General inquiry"
}

func (s *Service) extractKeyIssues(text string) []string {
	var issues []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "issue") {
			issues = append(issues, strings.TrimSpace(line))
		}
	}

	return issues
}

func (s *Service) extractCustomerIntent(text string) string {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "schedule") {
		return "Schedule service"
	}
	if strings.Contains(lowerText, "quote") {
		return "Request quote"
	}
	if strings.Contains(lowerText, "repair") {
		return "Request repair"
	}

	return "General inquiry"
}

func (s *Service) extractDepartment(text string) Department {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "technical") {
		return DepartmentTechnical
	}
	if strings.Contains(lowerText, "sales") {
		return DepartmentSales
	}
	if strings.Contains(lowerText, "billing") {
		return DepartmentBilling
	}
	if strings.Contains(lowerText, "emergency") {
		return DepartmentEmergencyTeam
	}

	return DepartmentCustomerService
}

func (s *Service) extractResponseTimeline(text string) string {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "immediate") || strings.Contains(lowerText, "urgent") {
		return "Within 1 hour"
	}
	if strings.Contains(lowerText, "same day") {
		return "Same day"
	}
	if strings.Contains(lowerText, "24 hours") {
		return "Within 24 hours"
	}

	return "Within 48 hours"
}

func (s *Service) extractInsights(text string) []string {
	var insights []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "insight") ||
		   strings.Contains(strings.ToLower(line), "finding") {
			insights = append(insights, strings.TrimSpace(line))
		}
	}

	if len(insights) == 0 {
		insights = append(insights, "Analysis completed successfully")
	}

	return insights
}

func (s *Service) extractRecommendations(text string) []string {
	var recommendations []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "recommend") ||
		   strings.Contains(strings.ToLower(line), "suggest") {
			recommendations = append(recommendations, strings.TrimSpace(line))
		}
	}

	return recommendations
}

func (s *Service) calculateResultConfidence(text string) float64 {
	// Simple confidence calculation based on text characteristics
	confidence := 0.5

	if len(text) > 100 {
		confidence += 0.2
	}

	confidenceWords := []string{"definitely", "clearly", "certainly", "confirmed"}
	lowerText := strings.ToLower(text)

	for _, word := range confidenceWords {
		if strings.Contains(lowerText, word) {
			confidence += 0.1
		}
	}

	if confidence > 0.95 {
		confidence = 0.95
	}

	return confidence
}
