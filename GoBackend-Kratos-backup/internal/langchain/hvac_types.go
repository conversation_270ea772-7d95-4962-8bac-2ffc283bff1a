package langchain

import (
	"time"
)

// 🤖 HVAC Agent Types - Comprehensive type definitions for HVAC AI agents

// DiagnosticRequest represents a request for equipment diagnostic analysis
type DiagnosticRequest struct {
	SystemType       string            `json:"system_type"`
	Model            string            `json:"model"`
	Age              string            `json:"age"`
	LastMaintenance  string            `json:"last_maintenance"`
	CustomerIssue    string            `json:"customer_issue"`
	Symptoms         string            `json:"symptoms"`
	Temperature      string            `json:"temperature"`
	Humidity         string            `json:"humidity"`
	Season           string            `json:"season"`
	AdditionalData   map[string]interface{} `json:"additional_data,omitempty"`
}

// DiagnosticResult represents the result of equipment diagnostic analysis
type DiagnosticResult struct {
	DiagnosisID        string    `json:"diagnosis_id"`
	PrimaryDiagnosis   string    `json:"primary_diagnosis"`
	DiagnosticSteps    []string  `json:"diagnostic_steps"`
	SafetyWarnings     []string  `json:"safety_warnings"`
	PartsNeeded        []string  `json:"parts_needed"`
	UrgencyLevel       string    `json:"urgency_level"`
	RepairTime         string    `json:"repair_time"`
	CostEstimate       string    `json:"cost_estimate"`
	PreventiveMeasures []string  `json:"preventive_measures"`
	Confidence         float64   `json:"confidence"`
	ProcessedAt        time.Time `json:"processed_at"`
	RawResult          string    `json:"raw_result"`
}

// MaintenanceRequest represents a request for predictive maintenance analysis
type MaintenanceRequest struct {
	SystemType           string            `json:"system_type"`
	InstallDate          string            `json:"install_date"`
	UsageHours           string            `json:"usage_hours"`
	PerformanceMetrics   string            `json:"performance_metrics"`
	HistoricalIssues     string            `json:"historical_issues"`
	EnvironmentalFactors string            `json:"environmental_factors"`
	LastService          string            `json:"last_service"`
	CurrentPerformance   string            `json:"current_performance"`
	SensorReadings       string            `json:"sensor_readings"`
	CustomerType         string            `json:"customer_type"`
	ServiceContract      string            `json:"service_contract"`
	BudgetConstraints    string            `json:"budget_constraints"`
	AdditionalData       map[string]interface{} `json:"additional_data,omitempty"`
}

// MaintenanceResult represents the result of predictive maintenance analysis
type MaintenanceResult struct {
	MaintenanceID           string    `json:"maintenance_id"`
	NextServiceDate         time.Time `json:"next_service_date"`
	MaintenanceType         string    `json:"maintenance_type"`
	PriorityTasks           []string  `json:"priority_tasks"`
	PartsToOrder            []string  `json:"parts_to_order"`
	FailurePredictions      []string  `json:"failure_predictions"`
	CostOptimization        []string  `json:"cost_optimization"`
	PerformanceImprovements []string  `json:"performance_improvements"`
	SeasonalAdjustments     []string  `json:"seasonal_adjustments"`
	Confidence              float64   `json:"confidence"`
	ProcessedAt             time.Time `json:"processed_at"`
	RawResult               string    `json:"raw_result"`
}

// CustomerCommRequest represents a request for customer communication generation
type CustomerCommRequest struct {
	CustomerName               string            `json:"customer_name"`
	CustomerType               string            `json:"customer_type"`
	CustomerHistory            string            `json:"customer_history"`
	CommunicationPreferences   string            `json:"communication_preferences"`
	PreviousIssues             string            `json:"previous_issues"`
	CommunicationPurpose       string            `json:"communication_purpose"`
	UrgencyLevel               string            `json:"urgency_level"`
	TechnicalDetails           string            `json:"technical_details"`
	ServiceStatus              string            `json:"service_status"`
	CompanyPolicy              string            `json:"company_policy"`
	ServiceLevel               string            `json:"service_level"`
	PricingInfo                string            `json:"pricing_info"`
	AdditionalData             map[string]interface{} `json:"additional_data,omitempty"`
}

// CustomerCommResult represents the result of customer communication generation
type CustomerCommResult struct {
	CommunicationID      string    `json:"communication_id"`
	SubjectLine          string    `json:"subject_line"`
	Greeting             string    `json:"greeting"`
	MainMessage          string    `json:"main_message"`
	TechnicalExplanation string    `json:"technical_explanation"`
	NextSteps            []string  `json:"next_steps"`
	Timeline             string    `json:"timeline"`
	ContactInfo          string    `json:"contact_info"`
	Closing              string    `json:"closing"`
	Tone                 string    `json:"tone"`
	ProcessedAt          time.Time `json:"processed_at"`
	RawResult            string    `json:"raw_result"`
}

// TechnicalRequest represents a request for technical documentation assistance
type TechnicalRequest struct {
	TechnicalQuery       string            `json:"technical_query"`
	Manufacturer         string            `json:"manufacturer"`
	Model                string            `json:"model"`
	SystemType           string            `json:"system_type"`
	Specifications       string            `json:"specifications"`
	KnowledgeBaseContext string            `json:"knowledge_base_context"`
	TechnicianLevel      string            `json:"technician_level"`
	AdditionalData       map[string]interface{} `json:"additional_data,omitempty"`
}

// TechnicalResult represents the result of technical documentation assistance
type TechnicalResult struct {
	TechnicalID          string    `json:"technical_id"`
	TechnicalExplanation string    `json:"technical_explanation"`
	StepByStepProcedures []string  `json:"step_by_step_procedures"`
	SafetyRequirements   []string  `json:"safety_requirements"`
	ToolsRequired        []string  `json:"tools_required"`
	ReferenceMaterials   []string  `json:"reference_materials"`
	TroubleshootingTips  []string  `json:"troubleshooting_tips"`
	BestPractices        []string  `json:"best_practices"`
	RelatedTopics        []string  `json:"related_topics"`
	Confidence           float64   `json:"confidence"`
	ProcessedAt          time.Time `json:"processed_at"`
	RawResult            string    `json:"raw_result"`
}

// BusinessRequest represents a request for business optimization analysis
type BusinessRequest struct {
	RevenueData           string            `json:"revenue_data"`
	ServiceMetrics        string            `json:"service_metrics"`
	CustomerSatisfaction  string            `json:"customer_satisfaction"`
	TechnicianPerformance string            `json:"technician_performance"`
	OperationalCosts      string            `json:"operational_costs"`
	SeasonalTrends        string            `json:"seasonal_trends"`
	CompetitionAnalysis   string            `json:"competition_analysis"`
	MarketOpportunities   string            `json:"market_opportunities"`
	BusinessGoals         string            `json:"business_goals"`
	AdditionalData        map[string]interface{} `json:"additional_data,omitempty"`
}

// BusinessResult represents the result of business optimization analysis
type BusinessResult struct {
	BusinessID               string    `json:"business_id"`
	PerformanceAnalysis      string    `json:"performance_analysis"`
	EfficiencyOpportunities  []string  `json:"efficiency_opportunities"`
	RevenueOptimization      []string  `json:"revenue_optimization"`
	CostReduction            []string  `json:"cost_reduction"`
	CustomerRetention        []string  `json:"customer_retention"`
	MarketExpansion          []string  `json:"market_expansion"`
	OperationalImprovements  []string  `json:"operational_improvements"`
	StrategicRecommendations []string  `json:"strategic_recommendations"`
	Confidence               float64   `json:"confidence"`
	ProcessedAt              time.Time `json:"processed_at"`
	RawResult                string    `json:"raw_result"`
}

// EmergencyRequest represents a request for emergency response coordination
type EmergencyRequest struct {
	EmergencyType       string            `json:"emergency_type"`
	Location            string            `json:"location"`
	CustomerInfo        string            `json:"customer_info"`
	SituationDescription string           `json:"situation_description"`
	TimeReported        string            `json:"time_reported"`
	WeatherConditions   string            `json:"weather_conditions"`
	Temperature         string            `json:"temperature"`
	BuildingOccupancy   string            `json:"building_occupancy"`
	AvailableTechnicians string           `json:"available_technicians"`
	EquipmentInventory  string            `json:"equipment_inventory"`
	EmergencyContacts   string            `json:"emergency_contacts"`
	AdditionalData      map[string]interface{} `json:"additional_data,omitempty"`
}

// EmergencyResult represents the result of emergency response coordination
type EmergencyResult struct {
	EmergencyID          string    `json:"emergency_id"`
	SeverityAssessment   int       `json:"severity_assessment"`
	ImmediateActions     []string  `json:"immediate_actions"`
	ResourceAllocation   string    `json:"resource_allocation"`
	ResponseTimeline     string    `json:"response_timeline"`
	SafetyProtocols      []string  `json:"safety_protocols"`
	CustomerCommunication string   `json:"customer_communication"`
	EscalationProcedures []string  `json:"escalation_procedures"`
	FollowUpActions      []string  `json:"follow_up_actions"`
	Confidence           float64   `json:"confidence"`
	ProcessedAt          time.Time `json:"processed_at"`
	RawResult            string    `json:"raw_result"`
}

// QuoteRequest represents a request for intelligent quote generation
type QuoteRequest struct {
	ServiceType         string            `json:"service_type"`
	SystemRequirements  string            `json:"system_requirements"`
	PropertyDetails     string            `json:"property_details"`
	CustomerType        string            `json:"customer_type"`
	Timeline            string            `json:"timeline"`
	LaborRequirements   string            `json:"labor_requirements"`
	MaterialCosts       string            `json:"material_costs"`
	EquipmentNeeded     string            `json:"equipment_needed"`
	ComplexityLevel     string            `json:"complexity_level"`
	SeasonalFactors     string            `json:"seasonal_factors"`
	ProfitMargins       string            `json:"profit_margins"`
	CompetitionPricing  string            `json:"competition_pricing"`
	CustomerBudget      string            `json:"customer_budget"`
	AdditionalData      map[string]interface{} `json:"additional_data,omitempty"`
}

// QuoteResult represents the result of intelligent quote generation
type QuoteResult struct {
	QuoteID           string    `json:"quote_id"`
	ItemizedBreakdown string    `json:"itemized_breakdown"`
	LaborCosts        string    `json:"labor_costs"`
	MaterialCosts     string    `json:"material_costs"`
	TotalPricing      string    `json:"total_pricing"`
	ValueProposition  string    `json:"value_proposition"`
	Timeline          string    `json:"timeline"`
	WarrantyTerms     string    `json:"warranty_terms"`
	PaymentOptions    string    `json:"payment_options"`
	Confidence        float64   `json:"confidence"`
	ProcessedAt       time.Time `json:"processed_at"`
	RawResult         string    `json:"raw_result"`
}

// SchedulingRequest represents a request for smart scheduling optimization
type SchedulingRequest struct {
	ServiceType           string            `json:"service_type"`
	PriorityLevel         string            `json:"priority_level"`
	EstimatedDuration     string            `json:"estimated_duration"`
	CustomerPreferences   string            `json:"customer_preferences"`
	Location              string            `json:"location"`
	AvailableTechnicians  string            `json:"available_technicians"`
	TechnicianSkills      string            `json:"technician_skills"`
	EquipmentAvailability string            `json:"equipment_availability"`
	VehicleAssignments    string            `json:"vehicle_assignments"`
	TravelTime            string            `json:"travel_time"`
	WorkingHours          string            `json:"working_hours"`
	EmergencySlots        string            `json:"emergency_slots"`
	ExistingSchedule      string            `json:"existing_schedule"`
	AdditionalData        map[string]interface{} `json:"additional_data,omitempty"`
}

// SchedulingResult represents the result of smart scheduling optimization
type SchedulingResult struct {
	SchedulingID        string    `json:"scheduling_id"`
	OptimalTimeSlot     string    `json:"optimal_time_slot"`
	TechnicianAssignment string   `json:"technician_assignment"`
	RouteOptimization   string    `json:"route_optimization"`
	BufferTime          string    `json:"buffer_time"`
	BackupPlans         []string  `json:"backup_plans"`
	CustomerNotification string   `json:"customer_notification"`
	ResourceAllocation  string    `json:"resource_allocation"`
	EfficiencyMetrics   string    `json:"efficiency_metrics"`
	Confidence          float64   `json:"confidence"`
	ProcessedAt         time.Time `json:"processed_at"`
	RawResult           string    `json:"raw_result"`
}

// AgentMetrics represents performance metrics for HVAC agents
type AgentMetrics struct {
	AgentType           string    `json:"agent_type"`
	TotalExecutions     int64     `json:"total_executions"`
	SuccessfulExecutions int64    `json:"successful_executions"`
	FailedExecutions    int64     `json:"failed_executions"`
	AverageResponseTime float64   `json:"average_response_time"`
	AverageConfidence   float64   `json:"average_confidence"`
	LastExecution       time.Time `json:"last_execution"`
	LastUpdated         time.Time `json:"last_updated"`
}

// AgentConfiguration represents configuration for HVAC agents
type AgentConfiguration struct {
	AgentType       string                 `json:"agent_type"`
	Enabled         bool                   `json:"enabled"`
	MaxRetries      int                    `json:"max_retries"`
	TimeoutSeconds  int                    `json:"timeout_seconds"`
	ConfidenceThreshold float64            `json:"confidence_threshold"`
	CustomPrompts   map[string]string      `json:"custom_prompts,omitempty"`
	Parameters      map[string]interface{} `json:"parameters,omitempty"`
	LastUpdated     time.Time              `json:"last_updated"`
}
