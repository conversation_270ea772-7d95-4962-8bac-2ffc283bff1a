package email

import (
	"context"
	"fmt"
	"io"
	"net/smtp"
	"strings"
	"time"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"go.uber.org/zap"
)

// EmailService handles enhanced email operations
type EmailService struct {
	imapClient *client.Client
	config     *EmailConfig
	logger     *zap.Logger
}

// EmailConfig represents email configuration
type EmailConfig struct {
	// IMAP Configuration
	IMAPServer   string `yaml:"imap_server"`
	IMAPPort     int    `yaml:"imap_port"`
	IMAPUsername string `yaml:"imap_username"`
	IMAPPassword string `yaml:"imap_password"`
	IMAPTLS      bool   `yaml:"imap_tls"`

	// SMTP Configuration
	SMTPServer   string `yaml:"smtp_server"`
	SMTPPort     int    `yaml:"smtp_port"`
	SMTPUsername string `yaml:"smtp_username"`
	SMTPPassword string `yaml:"smtp_password"`
	SMTPTLS      bool   `yaml:"smtp_tls"`

	// Processing Configuration
	PollInterval    time.Duration `yaml:"poll_interval"`
	MaxMessages     int           `yaml:"max_messages"`
	MarkAsRead      bool          `yaml:"mark_as_read"`
	ProcessedFolder string        `yaml:"processed_folder"`
	ErrorFolder     string        `yaml:"error_folder"`
}

// DefaultEmailConfig returns default email configuration
func DefaultEmailConfig() *EmailConfig {
	return &EmailConfig{
		IMAPServer:      "imap.gmail.com",
		IMAPPort:        993,
		IMAPUsername:    "<EMAIL>",
		IMAPPassword:    "", // Set from environment
		IMAPTLS:         true,
		SMTPServer:      "smtp.gmail.com",
		SMTPPort:        587,
		SMTPUsername:    "<EMAIL>",
		SMTPPassword:    "", // Set from environment
		SMTPTLS:         true,
		PollInterval:    5 * time.Minute,
		MaxMessages:     100,
		MarkAsRead:      true,
		ProcessedFolder: "Processed",
		ErrorFolder:     "Errors",
	}
}

// NewEmailService creates a new email service instance
func NewEmailService(config *EmailConfig, logger *zap.Logger) (*EmailService, error) {
	if config == nil {
		config = DefaultEmailConfig()
	}

	return &EmailService{
		config: config,
		logger: logger,
	}, nil
}

// Connect connects to IMAP server
func (s *EmailService) Connect(ctx context.Context) error {
	address := fmt.Sprintf("%s:%d", s.config.IMAPServer, s.config.IMAPPort)

	var err error
	if s.config.IMAPTLS {
		s.imapClient, err = client.DialTLS(address, nil)
	} else {
		s.imapClient, err = client.Dial(address)
	}

	if err != nil {
		return fmt.Errorf("failed to connect to IMAP server: %w", err)
	}

	// Login
	if err := s.imapClient.Login(s.config.IMAPUsername, s.config.IMAPPassword); err != nil {
		return fmt.Errorf("failed to login to IMAP server: %w", err)
	}

	s.logger.Info("Connected to IMAP server", zap.String("server", address))
	return nil
}

// Disconnect disconnects from IMAP server
func (s *EmailService) Disconnect() error {
	if s.imapClient != nil {
		if err := s.imapClient.Logout(); err != nil {
			s.logger.Warn("Failed to logout from IMAP server", zap.Error(err))
		}
		s.imapClient = nil
	}
	return nil
}

// FetchUnreadEmails fetches unread emails from inbox
func (s *EmailService) FetchUnreadEmails(ctx context.Context) ([]*EmailMessage, error) {
	if s.imapClient == nil {
		return nil, fmt.Errorf("not connected to IMAP server")
	}

	// Select INBOX
	mbox, err := s.imapClient.Select("INBOX", false)
	if err != nil {
		return nil, fmt.Errorf("failed to select INBOX: %w", err)
	}

	if mbox.Messages == 0 {
		return []*EmailMessage{}, nil
	}

	// Search for unread messages
	criteria := imap.NewSearchCriteria()
	criteria.WithoutFlags = []string{imap.SeenFlag}

	uids, err := s.imapClient.Search(criteria)
	if err != nil {
		return nil, fmt.Errorf("failed to search for unread messages: %w", err)
	}

	if len(uids) == 0 {
		return []*EmailMessage{}, nil
	}

	// Limit number of messages
	if len(uids) > s.config.MaxMessages {
		uids = uids[:s.config.MaxMessages]
	}

	// Fetch messages
	seqset := new(imap.SeqSet)
	seqset.AddNum(uids...)

	messages := make(chan *imap.Message, len(uids))
	done := make(chan error, 1)

	go func() {
		done <- s.imapClient.Fetch(seqset, []imap.FetchItem{imap.FetchEnvelope, imap.FetchBody, imap.FetchFlags}, messages)
	}()

	var emailMessages []*EmailMessage
	for msg := range messages {
		emailMsg, err := s.parseMessage(msg)
		if err != nil {
			s.logger.Warn("Failed to parse message", zap.Error(err))
			continue
		}
		emailMessages = append(emailMessages, emailMsg)
	}

	if err := <-done; err != nil {
		return nil, fmt.Errorf("failed to fetch messages: %w", err)
	}

	s.logger.Info("Fetched unread emails", zap.Int("count", len(emailMessages)))
	return emailMessages, nil
}

// parseMessage parses IMAP message to EmailMessage
func (s *EmailService) parseMessage(msg *imap.Message) (*EmailMessage, error) {
	if msg.Envelope == nil {
		return nil, fmt.Errorf("message envelope is nil")
	}

	// Parse sender
	var sender string
	if len(msg.Envelope.From) > 0 {
		sender = msg.Envelope.From[0].Address()
	}

	// Parse recipients
	var recipients []string
	for _, addr := range msg.Envelope.To {
		recipients = append(recipients, addr.Address())
	}

	// Get body
	var body string
	for _, part := range msg.Body {
		if part != nil {
			bodyBytes, err := io.ReadAll(part)
			if err == nil {
				body = string(bodyBytes)
			}
		}
	}

	return &EmailMessage{
		UID:            msg.Uid,
		Subject:        msg.Envelope.Subject,
		Sender:         sender,
		Recipients:     recipients,
		Body:           body,
		Date:           msg.Envelope.Date,
		Flags:          msg.Flags,
		HasAttachments: s.hasAttachments(body),
	}, nil
}

// hasAttachments checks if email has attachments
func (s *EmailService) hasAttachments(body string) bool {
	return strings.Contains(strings.ToLower(body), "content-disposition: attachment")
}

// MarkAsRead marks email as read
func (s *EmailService) MarkAsRead(uid uint32) error {
	if s.imapClient == nil {
		return fmt.Errorf("not connected to IMAP server")
	}

	seqset := new(imap.SeqSet)
	seqset.AddNum(uid)

	item := imap.FormatFlagsOp(imap.AddFlags, true)
	flags := []interface{}{imap.SeenFlag}

	return s.imapClient.Store(seqset, item, flags, nil)
}

// SendEmail sends an email via SMTP
func (s *EmailService) SendEmail(ctx context.Context, email *OutgoingEmail) error {
	// Create message
	msg := fmt.Sprintf("From: %s\r\nTo: %s\r\nSubject: %s\r\n\r\n%s",
		email.From, strings.Join(email.To, ","), email.Subject, email.Body)

	// SMTP authentication
	auth := smtp.PlainAuth("", s.config.SMTPUsername, s.config.SMTPPassword, s.config.SMTPServer)

	// Send email
	addr := fmt.Sprintf("%s:%d", s.config.SMTPServer, s.config.SMTPPort)
	err := smtp.SendMail(addr, auth, email.From, email.To, []byte(msg))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	s.logger.Info("Email sent successfully",
		zap.String("to", strings.Join(email.To, ",")),
		zap.String("subject", email.Subject),
	)

	return nil
}

// ProcessEmails processes emails with custom handler
func (s *EmailService) ProcessEmails(ctx context.Context, handler EmailHandler) error {
	emails, err := s.FetchUnreadEmails(ctx)
	if err != nil {
		return fmt.Errorf("failed to fetch emails: %w", err)
	}

	for _, email := range emails {
		if err := handler.HandleEmail(ctx, email); err != nil {
			s.logger.Error("Failed to process email",
				zap.String("subject", email.Subject),
				zap.String("sender", email.Sender),
				zap.Error(err),
			)
			continue
		}

		// Mark as read if configured
		if s.config.MarkAsRead {
			if err := s.MarkAsRead(email.UID); err != nil {
				s.logger.Warn("Failed to mark email as read", zap.Uint32("uid", email.UID), zap.Error(err))
			}
		}
	}

	return nil
}

// EmailMessage represents an email message
type EmailMessage struct {
	UID            uint32       `json:"uid"`
	Subject        string       `json:"subject"`
	Sender         string       `json:"sender"`
	Recipients     []string     `json:"recipients"`
	Body           string       `json:"body"`
	Date           time.Time    `json:"date"`
	Flags          []string     `json:"flags"`
	HasAttachments bool         `json:"has_attachments"`
	Attachments    []Attachment `json:"attachments,omitempty"`
}

// OutgoingEmail represents an outgoing email
type OutgoingEmail struct {
	From    string   `json:"from"`
	To      []string `json:"to"`
	CC      []string `json:"cc,omitempty"`
	BCC     []string `json:"bcc,omitempty"`
	Subject string   `json:"subject"`
	Body    string   `json:"body"`
	IsHTML  bool     `json:"is_html"`
}

// Attachment represents an email attachment
type Attachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Size        int64  `json:"size"`
	Data        []byte `json:"data"`
}

// EmailHandler interface for processing emails
type EmailHandler interface {
	HandleEmail(ctx context.Context, email *EmailMessage) error
}

// EmailFoundation combines all email functionality
type EmailFoundation struct {
	Service *EmailService
	Config  *EmailConfig
	Logger  *zap.Logger
}

// NewEmailFoundation creates a complete email foundation
func NewEmailFoundation(config *EmailConfig, logger *zap.Logger) (*EmailFoundation, error) {
	if config == nil {
		config = DefaultEmailConfig()
	}

	service, err := NewEmailService(config, logger)
	if err != nil {
		return nil, err
	}

	return &EmailFoundation{
		Service: service,
		Config:  config,
		Logger:  logger,
	}, nil
}
