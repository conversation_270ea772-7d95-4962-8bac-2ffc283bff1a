package web

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GinServer represents enhanced Gin web server
type GinServer struct {
	engine *gin.Engine
	server *http.Server
	logger *zap.Logger
	config *WebConfig
}

// WebConfig represents web server configuration
type WebConfig struct {
	Port            string        `yaml:"port"`
	Mode            string        `yaml:"mode"` // debug, release, test
	ReadTimeout     time.Duration `yaml:"read_timeout"`
	WriteTimeout    time.Duration `yaml:"write_timeout"`
	IdleTimeout     time.Duration `yaml:"idle_timeout"`
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
	EnableCORS      bool          `yaml:"enable_cors"`
	EnableMetrics   bool          `yaml:"enable_metrics"`
	EnableRecovery  bool          `yaml:"enable_recovery"`
	TrustedProxies  []string      `yaml:"trusted_proxies"`
}

// DefaultWebConfig returns default web configuration
func DefaultWebConfig() *WebConfig {
	return &WebConfig{
		Port:            "8080",
		Mode:            gin.ReleaseMode,
		ReadTimeout:     30 * time.Second,
		WriteTimeout:    30 * time.Second,
		IdleTimeout:     60 * time.Second,
		ShutdownTimeout: 10 * time.Second,
		EnableCORS:      true,
		EnableMetrics:   true,
		EnableRecovery:  true,
		TrustedProxies:  []string{"127.0.0.1"},
	}
}

// NewGinServer creates a new Gin server instance
func NewGinServer(config *WebConfig, logger *zap.Logger) *GinServer {
	if config == nil {
		config = DefaultWebConfig()
	}

	// Set Gin mode
	gin.SetMode(config.Mode)

	// Create Gin engine
	engine := gin.New()

	// Setup middleware
	setupMiddleware(engine, config, logger)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + config.Port,
		Handler:      engine,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
	}

	// Set trusted proxies
	if len(config.TrustedProxies) > 0 {
		engine.SetTrustedProxies(config.TrustedProxies)
	}

	return &GinServer{
		engine: engine,
		server: server,
		logger: logger,
		config: config,
	}
}

// setupMiddleware configures all middleware
func setupMiddleware(engine *gin.Engine, config *WebConfig, logger *zap.Logger) {
	// Recovery middleware
	if config.EnableRecovery {
		engine.Use(ginzap.RecoveryWithZap(logger, true))
	}

	// Logging middleware
	engine.Use(ginzap.Ginzap(logger, time.RFC3339, true))

	// CORS middleware
	if config.EnableCORS {
		corsConfig := cors.DefaultConfig()
		corsConfig.AllowOrigins = []string{"*"} // Configure properly for production
		corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
		corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
		engine.Use(cors.New(corsConfig))
	}

	// Health check endpoint
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
			"service":   "hvac-crm-backend",
		})
	})

	// Metrics endpoint (if enabled)
	if config.EnableMetrics {
		engine.GET("/metrics", func(c *gin.Context) {
			// Placeholder for Prometheus metrics
			c.JSON(http.StatusOK, gin.H{
				"metrics": "prometheus_metrics_here",
			})
		})
	}
}

// Engine returns the Gin engine for route registration
func (s *GinServer) Engine() *gin.Engine {
	return s.engine
}

// Start starts the HTTP server
func (s *GinServer) Start() error {
	s.logger.Info("Starting HTTP server", 
		zap.String("port", s.config.Port),
		zap.String("mode", s.config.Mode),
	)

	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

// Stop gracefully stops the HTTP server
func (s *GinServer) Stop(ctx context.Context) error {
	s.logger.Info("Stopping HTTP server...")

	shutdownCtx, cancel := context.WithTimeout(ctx, s.config.ShutdownTimeout)
	defer cancel()

	if err := s.server.Shutdown(shutdownCtx); err != nil {
		return fmt.Errorf("failed to shutdown server: %w", err)
	}

	s.logger.Info("HTTP server stopped successfully")
	return nil
}

// RouteGroup represents a group of routes
type RouteGroup struct {
	group *gin.RouterGroup
}

// NewRouteGroup creates a new route group
func (s *GinServer) NewRouteGroup(prefix string, middleware ...gin.HandlerFunc) *RouteGroup {
	group := s.engine.Group(prefix, middleware...)
	return &RouteGroup{group: group}
}

// GET adds a GET route
func (rg *RouteGroup) GET(path string, handlers ...gin.HandlerFunc) {
	rg.group.GET(path, handlers...)
}

// POST adds a POST route
func (rg *RouteGroup) POST(path string, handlers ...gin.HandlerFunc) {
	rg.group.POST(path, handlers...)
}

// PUT adds a PUT route
func (rg *RouteGroup) PUT(path string, handlers ...gin.HandlerFunc) {
	rg.group.PUT(path, handlers...)
}

// DELETE adds a DELETE route
func (rg *RouteGroup) DELETE(path string, handlers ...gin.HandlerFunc) {
	rg.group.DELETE(path, handlers...)
}

// PATCH adds a PATCH route
func (rg *RouteGroup) PATCH(path string, handlers ...gin.HandlerFunc) {
	rg.group.PATCH(path, handlers...)
}

// Use adds middleware to the route group
func (rg *RouteGroup) Use(middleware ...gin.HandlerFunc) {
	rg.group.Use(middleware...)
}

// WebFoundation combines all web-related functionality
type WebFoundation struct {
	Server *GinServer
	Config *WebConfig
	Logger *zap.Logger
}

// NewWebFoundation creates a complete web foundation
func NewWebFoundation(config *WebConfig, logger *zap.Logger) *WebFoundation {
	if config == nil {
		config = DefaultWebConfig()
	}

	server := NewGinServer(config, logger)

	return &WebFoundation{
		Server: server,
		Config: config,
		Logger: logger,
	}
}

// RegisterRoutes helper for registering API routes
func (wf *WebFoundation) RegisterRoutes(registerFunc func(*gin.Engine)) {
	registerFunc(wf.Server.Engine())
}

// StartServer starts the web server
func (wf *WebFoundation) StartServer() error {
	return wf.Server.Start()
}

// StopServer stops the web server
func (wf *WebFoundation) StopServer(ctx context.Context) error {
	return wf.Server.Stop(ctx)
}
