package validation

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// ValidatorService handles input validation
type ValidatorService struct {
	validator *validator.Validate
	logger    *zap.Logger
}

// NewValidatorService creates a new validator service instance
func NewValidatorService(logger *zap.Logger) *ValidatorService {
	validate := validator.New()
	
	// Register custom validations
	registerCustomValidations(validate)
	
	// Use JSON tag names for field names
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	return &ValidatorService{
		validator: validate,
		logger:    logger,
	}
}

// registerCustomValidations registers custom validation rules
func registerCustomValidations(validate *validator.Validate) {
	// HVAC specific validations
	validate.RegisterValidation("hvac_priority", validateHVACPriority)
	validate.RegisterValidation("hvac_status", validateHVACStatus)
	validate.RegisterValidation("phone_number", validatePhoneNumber)
	validate.RegisterValidation("postal_code", validatePostalCode)
}

// validateHVACPriority validates HVAC priority levels
func validateHVACPriority(fl validator.FieldLevel) bool {
	priority := fl.Field().String()
	validPriorities := []string{"low", "medium", "high", "critical", "emergency"}
	
	for _, valid := range validPriorities {
		if priority == valid {
			return true
		}
	}
	return false
}

// validateHVACStatus validates HVAC status values
func validateHVACStatus(fl validator.FieldLevel) bool {
	status := fl.Field().String()
	validStatuses := []string{"new", "assigned", "in_progress", "completed", "cancelled", "on_hold"}
	
	for _, valid := range validStatuses {
		if status == valid {
			return true
		}
	}
	return false
}

// validatePhoneNumber validates phone number format
func validatePhoneNumber(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	// Simple phone validation - can be enhanced
	return len(phone) >= 9 && len(phone) <= 15
}

// validatePostalCode validates postal code format
func validatePostalCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	// Polish postal code format: XX-XXX
	if len(code) != 6 {
		return false
	}
	return code[2] == '-'
}

// ValidateStruct validates a struct and returns formatted errors
func (v *ValidatorService) ValidateStruct(s interface{}) *ValidationResult {
	err := v.validator.Struct(s)
	if err == nil {
		return &ValidationResult{
			Valid:  true,
			Errors: nil,
		}
	}

	var validationErrors []ValidationError
	
	if validationErrs, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErrs {
			validationErrors = append(validationErrors, ValidationError{
				Field:   fieldErr.Field(),
				Tag:     fieldErr.Tag(),
				Value:   fmt.Sprintf("%v", fieldErr.Value()),
				Message: getErrorMessage(fieldErr),
			})
		}
	}

	v.logger.Debug("Validation failed",
		zap.String("struct", reflect.TypeOf(s).String()),
		zap.Int("error_count", len(validationErrors)),
	)

	return &ValidationResult{
		Valid:  false,
		Errors: validationErrors,
	}
}

// getErrorMessage returns user-friendly error message
func getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fe.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fe.Field(), fe.Param())
	case "hvac_priority":
		return fmt.Sprintf("%s must be one of: low, medium, high, critical, emergency", fe.Field())
	case "hvac_status":
		return fmt.Sprintf("%s must be one of: new, assigned, in_progress, completed, cancelled, on_hold", fe.Field())
	case "phone_number":
		return fmt.Sprintf("%s must be a valid phone number", fe.Field())
	case "postal_code":
		return fmt.Sprintf("%s must be a valid postal code (XX-XXX)", fe.Field())
	default:
		return fmt.Sprintf("%s is invalid", fe.Field())
	}
}

// ValidationResult represents validation result
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors,omitempty"`
}

// ValidationError represents a single validation error
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// HVAC Domain Models for Validation

// CustomerRequest represents customer creation/update request
type CustomerRequest struct {
	FirstName   string `json:"first_name" validate:"required,min=2,max=50"`
	LastName    string `json:"last_name" validate:"required,min=2,max=50"`
	Email       string `json:"email" validate:"required,email"`
	Phone       string `json:"phone" validate:"required,phone_number"`
	Address     string `json:"address" validate:"required,min=5,max=200"`
	City        string `json:"city" validate:"required,min=2,max=50"`
	PostalCode  string `json:"postal_code" validate:"required,postal_code"`
	CompanyName string `json:"company_name,omitempty" validate:"omitempty,min=2,max=100"`
}

// ServiceTicketRequest represents service ticket creation request
type ServiceTicketRequest struct {
	CustomerID  string `json:"customer_id" validate:"required,uuid"`
	Title       string `json:"title" validate:"required,min=5,max=100"`
	Description string `json:"description" validate:"required,min=10,max=1000"`
	Priority    string `json:"priority" validate:"required,hvac_priority"`
	Category    string `json:"category" validate:"required,min=2,max=50"`
	Location    string `json:"location" validate:"required,min=5,max=200"`
}

// ServiceTicketUpdateRequest represents service ticket update request
type ServiceTicketUpdateRequest struct {
	Title       string `json:"title,omitempty" validate:"omitempty,min=5,max=100"`
	Description string `json:"description,omitempty" validate:"omitempty,min=10,max=1000"`
	Status      string `json:"status,omitempty" validate:"omitempty,hvac_status"`
	Priority    string `json:"priority,omitempty" validate:"omitempty,hvac_priority"`
	TechnicianID string `json:"technician_id,omitempty" validate:"omitempty,uuid"`
}

// EquipmentRequest represents equipment creation request
type EquipmentRequest struct {
	CustomerID     string `json:"customer_id" validate:"required,uuid"`
	Name           string `json:"name" validate:"required,min=2,max=100"`
	Model          string `json:"model" validate:"required,min=2,max=50"`
	Manufacturer   string `json:"manufacturer" validate:"required,min=2,max=50"`
	SerialNumber   string `json:"serial_number" validate:"required,min=3,max=50"`
	InstallDate    string `json:"install_date" validate:"required"`
	WarrantyExpiry string `json:"warranty_expiry,omitempty"`
	Location       string `json:"location" validate:"required,min=2,max=100"`
}

// UserRegistrationRequest represents user registration request
type UserRegistrationRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8,max=128"`
	FirstName string `json:"first_name" validate:"required,min=2,max=50"`
	LastName  string `json:"last_name" validate:"required,min=2,max=50"`
	Phone     string `json:"phone" validate:"required,phone_number"`
	Role      string `json:"role,omitempty" validate:"omitempty,oneof=admin technician customer"`
}

// LoginRequest represents login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
}

// EmailRequest represents email sending request
type EmailRequest struct {
	To      []string `json:"to" validate:"required,min=1,dive,email"`
	Subject string   `json:"subject" validate:"required,min=3,max=200"`
	Body    string   `json:"body" validate:"required,min=10"`
	IsHTML  bool     `json:"is_html"`
}

// ValidationFoundation combines all validation functionality
type ValidationFoundation struct {
	Validator *ValidatorService
	Logger    *zap.Logger
}

// NewValidationFoundation creates a complete validation foundation
func NewValidationFoundation(logger *zap.Logger) *ValidationFoundation {
	validator := NewValidatorService(logger)

	return &ValidationFoundation{
		Validator: validator,
		Logger:    logger,
	}
}

// ValidateCustomerRequest validates customer request
func (vf *ValidationFoundation) ValidateCustomerRequest(req *CustomerRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateServiceTicketRequest validates service ticket request
func (vf *ValidationFoundation) ValidateServiceTicketRequest(req *ServiceTicketRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateServiceTicketUpdateRequest validates service ticket update request
func (vf *ValidationFoundation) ValidateServiceTicketUpdateRequest(req *ServiceTicketUpdateRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateEquipmentRequest validates equipment request
func (vf *ValidationFoundation) ValidateEquipmentRequest(req *EquipmentRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateUserRegistrationRequest validates user registration request
func (vf *ValidationFoundation) ValidateUserRegistrationRequest(req *UserRegistrationRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateLoginRequest validates login request
func (vf *ValidationFoundation) ValidateLoginRequest(req *LoginRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}

// ValidateEmailRequest validates email request
func (vf *ValidationFoundation) ValidateEmailRequest(req *EmailRequest) *ValidationResult {
	return vf.Validator.ValidateStruct(req)
}
