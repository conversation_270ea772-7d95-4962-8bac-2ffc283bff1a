package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	pb "gobackend-hvac-kratos/api/ai/v1"
	"gobackend-hvac-kratos/internal/biz"
)

// AIService implements the AI service
type AIService struct {
	pb.UnimplementedAIServiceServer

	aiUc *biz.AIUsecase
	log  *log.Helper
}

// NewAIService creates a new AI service
func NewAIService(aiUc *biz.AIUsecase, logger log.Logger) *AIService {
	return &AIService{
		aiUc: aiUc,
		log:  log.NewHelper(logger),
	}
}

// Chat processes a chat request
func (s *AIService) Chat(ctx context.Context, req *pb.ChatRequest) (*pb.ChatResponse, error) {
	s.log.WithContext(ctx).Infof("Processing chat request with model: %s", req.Model)

	chatReq := &biz.ChatRequest{
		Message: req.Message,
		Model:   req.Model,
		Context: req.Context,
	}

	result, err := s.aiUc.Chat(ctx, chatReq)
	if err != nil {
		return nil, err
	}

	return &pb.ChatResponse{
		Response:   result.Response,
		ModelUsed:  result.ModelUsed,
		TokensUsed: result.TokensUsed,
	}, nil
}

// Analyze processes an analysis request
func (s *AIService) Analyze(ctx context.Context, req *pb.AnalyzeRequest) (*pb.AnalyzeResponse, error) {
	s.log.WithContext(ctx).Infof("Processing analysis request: %s", req.AnalysisType)

	analyzeReq := &biz.AnalyzeRequest{
		Content:      req.Content,
		AnalysisType: req.AnalysisType,
		Model:        req.Model,
	}

	result, err := s.aiUc.Analyze(ctx, analyzeReq)
	if err != nil {
		return nil, err
	}

	return &pb.AnalyzeResponse{
		Analysis:   result.Analysis,
		Confidence: result.Confidence,
		Metadata:   result.Metadata,
	}, nil
}// ListModels returns available AI models
func (s *AIService) ListModels(ctx context.Context, req *pb.ListModelsRequest) (*pb.ListModelsResponse, error) {
	s.log.WithContext(ctx).Info("Listing available AI models")

	models, err := s.aiUc.ListModels(ctx)
	if err != nil {
		return nil, err
	}

	pbModels := make([]*pb.AIModel, len(models))
	for i, model := range models {
		pbModels[i] = &pb.AIModel{
			Name:      model.Name,
			Type:      model.Type,
			Available: model.Available,
			Endpoint:  model.Endpoint,
		}
	}

	return &pb.ListModelsResponse{
		Models: pbModels,
	}, nil
}