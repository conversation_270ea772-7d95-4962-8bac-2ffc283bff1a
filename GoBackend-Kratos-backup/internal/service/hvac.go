package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gobackend-hvac-kratos/api/hvac/v1"
	"gobackend-hvac-kratos/internal/biz"
)

// HVACService implements the HVAC service
type HVACService struct {
	pb.UnimplementedHVACServiceServer

	customerUc *biz.CustomerUsecase
	jobUc      *biz.JobUsecase
	log        *log.Helper
}

// NewHVACService creates a new HVAC service
func NewHVACService(customerUc *biz.CustomerUsecase, jobUc *biz.JobUsecase, logger log.Logger) *HVACService {
	return &HVACService{
		customerUc: customerUc,
		jobUc:      jobUc,
		log:        log.NewHelper(logger),
	}
}

// CreateCustomer creates a new customer
func (s *HVACService) CreateCustomer(ctx context.Context, req *pb.CreateCustomerRequest) (*pb.CreateCustomerResponse, error) {
	s.log.WithContext(ctx).Infof("Creating customer: %s", req.Name)

	customer := &biz.Customer{
		Name:    req.Name,
		Email:   req.Email,
		Phone:   req.Phone,
		Address: req.Address,
	}

	result, err := s.customerUc.CreateCustomer(ctx, customer)
	if err != nil {
		return nil, err
	}

	// Simplified response for now
	return &pb.CreateCustomerResponse{
		Customer: &pb.Customer{
			Id:      result.ID,
			Name:    result.Name,
			Email:   result.Email,
			Phone:   result.Phone,
			Address: result.Address,
		},
	}, nil
}

// GetCustomer retrieves a customer by ID
func (s *HVACService) GetCustomer(ctx context.Context, req *pb.GetCustomerRequest) (*pb.GetCustomerResponse, error) {
	s.log.WithContext(ctx).Infof("Getting customer: %d", req.Id)

	customer, err := s.customerUc.GetCustomer(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.GetCustomerResponse{
		Customer: s.convertCustomerToPB(customer),
	}, nil
}

// ListCustomers retrieves customers with pagination
func (s *HVACService) ListCustomers(ctx context.Context, req *pb.ListCustomersRequest) (*pb.ListCustomersResponse, error) {
	s.log.WithContext(ctx).Infof("Listing customers: page=%d, size=%d", req.Page, req.PageSize)

	customers, total, err := s.customerUc.ListCustomers(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	pbCustomers := make([]*pb.Customer, len(customers))
	for i, customer := range customers {
		pbCustomers[i] = s.convertCustomerToPB(customer)
	}

	return &pb.ListCustomersResponse{
		Customers: pbCustomers,
		Total:     total,
	}, nil
}

// CreateJob creates a new job
func (s *HVACService) CreateJob(ctx context.Context, req *pb.CreateJobRequest) (*pb.CreateJobResponse, error) {
	s.log.WithContext(ctx).Infof("Creating job: %s", req.Title)

	job := &biz.Job{
		CustomerID:  req.CustomerId,
		Title:       req.Title,
		Description: req.Description,
		Priority:    req.Priority,
	}

	result, err := s.jobUc.CreateJob(ctx, job)
	if err != nil {
		return nil, err
	}

	return &pb.CreateJobResponse{
		Job: s.convertJobToPB(result),
	}, nil
}

// GetJob retrieves a job by ID
func (s *HVACService) GetJob(ctx context.Context, req *pb.GetJobRequest) (*pb.GetJobResponse, error) {
	s.log.WithContext(ctx).Infof("Getting job: %d", req.Id)

	job, err := s.jobUc.GetJob(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.GetJobResponse{
		Job: s.convertJobToPB(job),
	}, nil
}

// ListJobs retrieves jobs with filtering and pagination
func (s *HVACService) ListJobs(ctx context.Context, req *pb.ListJobsRequest) (*pb.ListJobsResponse, error) {
	s.log.WithContext(ctx).Infof("Listing jobs: page=%d, size=%d", req.Page, req.PageSize)

	jobs, total, err := s.jobUc.ListJobs(ctx, req.Page, req.PageSize, 0, "")
	if err != nil {
		return nil, err
	}

	pbJobs := make([]*pb.Job, len(jobs))
	for i, job := range jobs {
		pbJobs[i] = s.convertJobToPB(job)
	}

	return &pb.ListJobsResponse{
		Jobs:  pbJobs,
		Total: total,
	}, nil
}

// convertCustomerToPB converts business entity to protobuf
func (s *HVACService) convertCustomerToPB(customer *biz.Customer) *pb.Customer {
	return &pb.Customer{
		Id:        customer.ID,
		Name:      customer.Name,
		Email:     customer.Email,
		Phone:     customer.Phone,
		Address:   customer.Address,
		CreatedAt: timestamppb.New(customer.CreatedAt),
		UpdatedAt: timestamppb.New(customer.UpdatedAt),
	}
}

// convertJobToPB converts business entity to protobuf
func (s *HVACService) convertJobToPB(job *biz.Job) *pb.Job {
	return &pb.Job{
		Id:          job.ID,
		CustomerId:  job.CustomerID,
		Title:       job.Title,
		Description: job.Description,
		Status:      job.Status,
		Priority:    job.Priority,
		ScheduledAt: timestamppb.New(job.ScheduledAt),
		CreatedAt:   timestamppb.New(job.CreatedAt),
		UpdatedAt:   timestamppb.New(job.UpdatedAt),
	}
}