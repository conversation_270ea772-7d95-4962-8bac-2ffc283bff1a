package customer

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
)

// 🔍 Customer Matcher - Intelligent Customer Detection & Matching
type CustomerMatcher struct {
	log    *log.Helper
	db     *gorm.DB
	gemma3 *ai.Gemma3Service
}

// 📊 Match Result with Confidence Score
type MatchResult struct {
	Customer   *Customer `json:"customer"`
	Confidence float64   `json:"confidence"`
	MatchType  string    `json:"match_type"` // exact, fuzzy, ai_assisted
	Reasons    []string  `json:"reasons"`
}

// 🎯 Matching Strategy
type MatchingStrategy struct {
	ExactEmailMatch    bool    `json:"exact_email_match"`
	ExactPhoneMatch    bool    `json:"exact_phone_match"`
	FuzzyNameMatch     bool    `json:"fuzzy_name_match"`
	CompanyMatch       bool    `json:"company_match"`
	AIAssistedMatch    bool    `json:"ai_assisted_match"`
	MinConfidence      float64 `json:"min_confidence"`
	MaxResults         int     `json:"max_results"`
}

// NewCustomerMatcher creates a new customer matcher
func NewCustomerMatcher(db *gorm.DB, gemma3 *ai.Gemma3Service, logger log.Logger) *CustomerMatcher {
	return &CustomerMatcher{
		log:    log.NewHelper(logger),
		db:     db,
		gemma3: gemma3,
	}
}

// 🔍 Find Customer with Multiple Strategies
func (m *CustomerMatcher) FindCustomer(
	ctx context.Context,
	criteria *MatchCriteria,
) (*Customer, float64, error) {
	m.log.WithContext(ctx).Infof("Searching for customer with criteria: %+v", criteria)
	
	// Default matching strategy
	strategy := &MatchingStrategy{
		ExactEmailMatch: true,
		ExactPhoneMatch: true,
		FuzzyNameMatch:  true,
		CompanyMatch:    true,
		AIAssistedMatch: true,
		MinConfidence:   0.7,
		MaxResults:      10,
	}
	
	// Try multiple matching strategies in order of confidence
	results := []*MatchResult{}
	
	// 1. Exact email match (highest confidence)
	if criteria.Email != "" && strategy.ExactEmailMatch {
		emailResults, err := m.findByExactEmail(ctx, criteria.Email)
		if err == nil {
			results = append(results, emailResults...)
		}
	}
	
	// 2. Exact phone match (high confidence)
	if criteria.Phone != "" && strategy.ExactPhoneMatch {
		phoneResults, err := m.findByExactPhone(ctx, criteria.Phone)
		if err == nil {
			results = append(results, phoneResults...)
		}
	}
	
	// 3. Company + name match (medium confidence)
	if criteria.Company != "" && criteria.Name != "" && strategy.CompanyMatch {
		companyResults, err := m.findByCompanyAndName(ctx, criteria.Company, criteria.Name)
		if err == nil {
			results = append(results, companyResults...)
		}
	}
	
	// 4. Fuzzy name matching (lower confidence)
	if criteria.Name != "" && strategy.FuzzyNameMatch {
		fuzzyResults, err := m.findByFuzzyName(ctx, criteria.Name)
		if err == nil {
			results = append(results, fuzzyResults...)
		}
	}
	
	// 5. AI-assisted matching (variable confidence)
	if strategy.AIAssistedMatch && len(results) == 0 {
		aiResults, err := m.findByAIAssisted(ctx, criteria)
		if err == nil {
			results = append(results, aiResults...)
		}
	}
	
	// Return best match above confidence threshold
	bestMatch := m.selectBestMatch(results, strategy.MinConfidence)
	if bestMatch != nil {
		m.log.WithContext(ctx).Infof("Found customer match: %s (confidence: %.2f, type: %s)", 
			bestMatch.Customer.Name, bestMatch.Confidence, bestMatch.MatchType)
		return bestMatch.Customer, bestMatch.Confidence, nil
	}
	
	m.log.WithContext(ctx).Info("No customer match found above confidence threshold")
	return nil, 0.0, nil
}

// 📧 Find by Exact Email Match
func (m *CustomerMatcher) findByExactEmail(ctx context.Context, email string) ([]*MatchResult, error) {
	var customers []Customer
	
	// Search in main customer table
	if err := m.db.Where("email = ?", strings.ToLower(email)).Find(&customers).Error; err != nil {
		return nil, err
	}
	
	// Search in customer_emails table
	var emailRecords []CustomerEmail
	if err := m.db.Preload("Customer").Where("email_address = ?", strings.ToLower(email)).
		Find(&emailRecords).Error; err != nil {
		return nil, err
	}
	
	results := []*MatchResult{}
	
	// Add main table matches
	for _, customer := range customers {
		results = append(results, &MatchResult{
			Customer:   &customer,
			Confidence: 0.95,
			MatchType:  "exact_email",
			Reasons:    []string{"exact email match in primary field"},
		})
	}
	
	// Add email table matches
	for _, emailRecord := range emailRecords {
		// Avoid duplicates
		found := false
		for _, existing := range results {
			if existing.Customer.ID == emailRecord.Customer.ID {
				found = true
				break
			}
		}
		
		if !found {
			confidence := 0.90
			if emailRecord.IsPrimary {
				confidence = 0.95
			}
			
			results = append(results, &MatchResult{
				Customer:   &emailRecord.Customer,
				Confidence: confidence,
				MatchType:  "exact_email",
				Reasons:    []string{"exact email match in email records"},
			})
		}
	}
	
	return results, nil
}

// 📞 Find by Exact Phone Match
func (m *CustomerMatcher) findByExactPhone(ctx context.Context, phone string) ([]*MatchResult, error) {
	normalizedPhone := m.normalizePhoneNumber(phone)
	
	var customers []Customer
	var phoneRecords []CustomerPhone
	
	// Search in main customer table
	if err := m.db.Where("primary_phone = ? OR primary_phone = ?", phone, normalizedPhone).
		Find(&customers).Error; err != nil {
		return nil, err
	}
	
	// Search in customer_phones table
	if err := m.db.Preload("Customer").
		Where("phone_number = ? OR phone_number = ?", phone, normalizedPhone).
		Find(&phoneRecords).Error; err != nil {
		return nil, err
	}
	
	results := []*MatchResult{}
	
	// Add main table matches
	for _, customer := range customers {
		results = append(results, &MatchResult{
			Customer:   &customer,
			Confidence: 0.90,
			MatchType:  "exact_phone",
			Reasons:    []string{"exact phone match in primary field"},
		})
	}
	
	// Add phone table matches
	for _, phoneRecord := range phoneRecords {
		// Avoid duplicates
		found := false
		for _, existing := range results {
			if existing.Customer.ID == phoneRecord.Customer.ID {
				found = true
				break
			}
		}
		
		if !found {
			confidence := 0.85
			if phoneRecord.IsPrimary {
				confidence = 0.90
			}
			
			results = append(results, &MatchResult{
				Customer:   &phoneRecord.Customer,
				Confidence: confidence,
				MatchType:  "exact_phone",
				Reasons:    []string{"exact phone match in phone records"},
			})
		}
	}
	
	return results, nil
}

// 🏢 Find by Company and Name
func (m *CustomerMatcher) findByCompanyAndName(ctx context.Context, company, name string) ([]*MatchResult, error) {
	var customers []Customer
	
	// Search for company + name combination
	if err := m.db.Where("LOWER(company) = ? AND LOWER(name) ILIKE ?", 
		strings.ToLower(company), "%"+strings.ToLower(name)+"%").
		Find(&customers).Error; err != nil {
		return nil, err
	}
	
	results := []*MatchResult{}
	for _, customer := range customers {
		// Calculate confidence based on name similarity
		nameSimilarity := m.calculateStringSimilarity(name, customer.Name)
		confidence := 0.6 + (nameSimilarity * 0.3) // Base 0.6, up to 0.9
		
		results = append(results, &MatchResult{
			Customer:   &customer,
			Confidence: confidence,
			MatchType:  "company_name",
			Reasons:    []string{fmt.Sprintf("company match + name similarity: %.2f", nameSimilarity)},
		})
	}
	
	return results, nil
}

// 🔤 Find by Fuzzy Name Matching
func (m *CustomerMatcher) findByFuzzyName(ctx context.Context, name string) ([]*MatchResult, error) {
	var customers []Customer
	
	// Use PostgreSQL similarity functions if available, otherwise use ILIKE
	if err := m.db.Where("LOWER(name) ILIKE ?", "%"+strings.ToLower(name)+"%").
		Limit(20).Find(&customers).Error; err != nil {
		return nil, err
	}
	
	results := []*MatchResult{}
	for _, customer := range customers {
		similarity := m.calculateStringSimilarity(name, customer.Name)
		
		// Only include if similarity is above threshold
		if similarity >= 0.6 {
			confidence := 0.3 + (similarity * 0.4) // Base 0.3, up to 0.7
			
			results = append(results, &MatchResult{
				Customer:   &customer,
				Confidence: confidence,
				MatchType:  "fuzzy_name",
				Reasons:    []string{fmt.Sprintf("name similarity: %.2f", similarity)},
			})
		}
	}
	
	return results, nil
}

// 🤖 AI-Assisted Customer Matching
func (m *CustomerMatcher) findByAIAssisted(ctx context.Context, criteria *MatchCriteria) ([]*MatchResult, error) {
	// Get potential candidates from database
	var candidates []Customer
	
	// Build a broader search query
	query := m.db.Model(&Customer{})
	
	if criteria.Company != "" {
		query = query.Or("LOWER(company) ILIKE ?", "%"+strings.ToLower(criteria.Company)+"%")
	}
	
	if criteria.Name != "" {
		query = query.Or("LOWER(name) ILIKE ?", "%"+strings.ToLower(criteria.Name)+"%")
	}
	
	// Get recent customers as candidates
	query = query.Or("created_at >= ?", time.Now().AddDate(0, 0, -90))
	
	if err := query.Limit(50).Find(&candidates).Error; err != nil {
		return nil, err
	}
	
	if len(candidates) == 0 {
		return []*MatchResult{}, nil
	}
	
	// Use Gemma 3 for intelligent matching
	matchResult, err := m.aiMatchCustomer(ctx, criteria, candidates)
	if err != nil {
		m.log.WithContext(ctx).Warnf("AI matching failed: %v", err)
		return []*MatchResult{}, nil
	}
	
	return matchResult, nil
}

// 🧠 AI-Powered Customer Matching with Gemma 3
func (m *CustomerMatcher) aiMatchCustomer(
	ctx context.Context,
	criteria *MatchCriteria,
	candidates []Customer,
) ([]*MatchResult, error) {
	// Build context for AI analysis
	var contextBuilder strings.Builder
	contextBuilder.WriteString("Customer Matching Analysis\n\n")
	contextBuilder.WriteString("Target Customer Information:\n")
	contextBuilder.WriteString(fmt.Sprintf("Email: %s\n", criteria.Email))
	contextBuilder.WriteString(fmt.Sprintf("Phone: %s\n", criteria.Phone))
	contextBuilder.WriteString(fmt.Sprintf("Name: %s\n", criteria.Name))
	contextBuilder.WriteString(fmt.Sprintf("Company: %s\n", criteria.Company))
	contextBuilder.WriteString(fmt.Sprintf("Content Context: %s\n\n", criteria.Content))
	
	contextBuilder.WriteString("Candidate Customers:\n")
	for i, candidate := range candidates {
		contextBuilder.WriteString(fmt.Sprintf("%d. %s (%s) - %s - %s\n", 
			i+1, candidate.Name, candidate.Company, candidate.Email, candidate.PrimaryPhone))
	}
	
	contextBuilder.WriteString("\nPlease analyze and identify the best matching customer(s) with confidence scores.")
	
	// Prepare Gemma 3 request
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: contextBuilder.String(),
		Subject:      "Customer Matching Analysis",
		AnalysisType: "customer_matching",
	}
	
	// Get AI analysis
	response, err := m.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze customer matching: %w", err)
	}
	
	// Parse AI response for matches
	// This is a simplified implementation - in production, you'd want more sophisticated parsing
	results := []*MatchResult{}
	
	// For now, return the first candidate with AI-determined confidence
	if len(candidates) > 0 {
		// Use business insights confidence as matching confidence
		confidence := 0.5 // Default
		if response.BusinessInsights != nil {
			switch response.BusinessInsights.RevenueOpportunity {
			case "high":
				confidence = 0.8
			case "medium":
				confidence = 0.6
			case "low":
				confidence = 0.4
			}
		}
		
		if confidence >= 0.5 {
			results = append(results, &MatchResult{
				Customer:   &candidates[0],
				Confidence: confidence,
				MatchType:  "ai_assisted",
				Reasons:    []string{"AI analysis based on context and patterns"},
			})
		}
	}
	
	return results, nil
}

// 🎯 Select Best Match from Results
func (m *CustomerMatcher) selectBestMatch(results []*MatchResult, minConfidence float64) *MatchResult {
	var bestMatch *MatchResult
	
	for _, result := range results {
		if result.Confidence >= minConfidence {
			if bestMatch == nil || result.Confidence > bestMatch.Confidence {
				bestMatch = result
			}
		}
	}
	
	return bestMatch
}

// 📊 Calculate String Similarity (Levenshtein-based)
func (m *CustomerMatcher) calculateStringSimilarity(s1, s2 string) float64 {
	s1 = strings.ToLower(strings.TrimSpace(s1))
	s2 = strings.ToLower(strings.TrimSpace(s2))
	
	if s1 == s2 {
		return 1.0
	}
	
	if len(s1) == 0 || len(s2) == 0 {
		return 0.0
	}
	
	// Simple similarity calculation
	// In production, use a proper string similarity algorithm
	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}
	
	distance := m.levenshteinDistance(s1, s2)
	similarity := 1.0 - (float64(distance) / float64(maxLen))
	
	if similarity < 0 {
		similarity = 0
	}
	
	return similarity
}

// 📏 Levenshtein Distance Calculation
func (m *CustomerMatcher) levenshteinDistance(s1, s2 string) int {
	if len(s1) == 0 {
		return len(s2)
	}
	if len(s2) == 0 {
		return len(s1)
	}
	
	matrix := make([][]int, len(s1)+1)
	for i := range matrix {
		matrix[i] = make([]int, len(s2)+1)
		matrix[i][0] = i
	}
	
	for j := 0; j <= len(s2); j++ {
		matrix[0][j] = j
	}
	
	for i := 1; i <= len(s1); i++ {
		for j := 1; j <= len(s2); j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}
			
			matrix[i][j] = min(
				matrix[i-1][j]+1,      // deletion
				matrix[i][j-1]+1,      // insertion
				matrix[i-1][j-1]+cost, // substitution
			)
		}
	}
	
	return matrix[len(s1)][len(s2)]
}

// 📞 Normalize Phone Number
func (m *CustomerMatcher) normalizePhoneNumber(phone string) string {
	// Remove all non-digit characters
	re := regexp.MustCompile(`[^\d]`)
	normalized := re.ReplaceAllString(phone, "")
	
	// Handle different formats
	if len(normalized) == 10 {
		// US format: add country code
		normalized = "1" + normalized
	}
	
	return normalized
}

// 🔧 Helper function for minimum of three integers
func min(a, b, c int) int {
	if a < b {
		if a < c {
			return a
		}
		return c
	}
	if b < c {
		return b
	}
	return c
}
