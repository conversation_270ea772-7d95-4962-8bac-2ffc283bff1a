package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/transport/grpc"

	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	aiv1 "gobackend-hvac-kratos/api/ai/v1"
	analyticsv1 "gobackend-hvac-kratos/api/analytics/v1"
	workflowv1 "gobackend-hvac-kratos/api/workflow/v1"
	// emailv1 "gobackend-hvac-kratos/api/email/v1" // Temporarily unused
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/service"
)

// NewGRPCServer creates a new gRPC server
func NewGRPCServer(
	c *conf.Server,
	hvacService *service.HVACService,
	aiService *service.AIService,
	analyticsService *service.AnalyticsService,
	workflowService *service.WorkflowService,
	// emailService *service.EmailService, // Temporarily removed
	logger log.Logger,
) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			logging.Server(logger),
			metrics.Server(),
		),
	}

	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}

	srv := grpc.NewServer(opts...)

	// Register HVAC service
	hvacv1.RegisterHVACServiceServer(srv.Server, hvacService)

	// Register AI service
	aiv1.RegisterAIServiceServer(srv.Server, aiService)

	// Register Analytics service
	analyticsv1.RegisterAnalyticsServiceServer(srv.Server, analyticsService)

	// Register Workflow service
	workflowv1.RegisterWorkflowServiceServer(srv.Server, workflowService)

	// Register Email service (temporarily disabled)
	// emailv1.RegisterEmailServiceServer(srv.Server, emailService)

	return srv
}