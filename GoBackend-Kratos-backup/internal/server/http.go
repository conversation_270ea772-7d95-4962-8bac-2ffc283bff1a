package server

import (
	"net/http"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/ratelimit"
	kratoshttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/mux"

	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	aiv1 "gobackend-hvac-kratos/api/ai/v1"
	analyticsv1 "gobackend-hvac-kratos/api/analytics/v1"
	workflowv1 "gobackend-hvac-kratos/api/workflow/v1"
	// emailv1 "gobackend-hvac-kratos/api/email/v1" // Temporarily disabled
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/service"
)

// NewHTTPServer creates a new optimized HTTP server with tRPC bridge
func NewHTTPServer(
	c *conf.Server,
	hvacService *service.HVACService,
	aiService *service.AIService,
	analyticsService *service.AnalyticsService,
	workflowService *service.WorkflowService,
	// emailService *service.EmailService, // Temporarily removed
	logger log.Logger,
) *kratoshttp.Server {
	var opts = []kratoshttp.ServerOption{
		kratoshttp.Middleware(
			recovery.Recovery(),
			ratelimit.Server(), // 🚦 Rate limiting for API protection
			tracing.Server(),
			logging.Server(logger),
			metrics.Server(),
		),
	}

	if c.Http.Network != "" {
		opts = append(opts, kratoshttp.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, kratoshttp.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, kratoshttp.Timeout(c.Http.Timeout.AsDuration()))
	}

	srv := kratoshttp.NewServer(opts...)

	// Register HVAC service
	hvacv1.RegisterHVACServiceHTTPServer(srv, hvacService)

	// Register AI service
	aiv1.RegisterAIServiceHTTPServer(srv, aiService)

	// Register Analytics service
	analyticsv1.RegisterAnalyticsServiceHTTPServer(srv, analyticsService)

	// Register Workflow service
	workflowv1.RegisterWorkflowServiceHTTPServer(srv, workflowService)

	// Register Email service (temporarily disabled until email service is ready)
	// emailv1.RegisterEmailServiceHTTPServer(srv, emailService)

	// 🌉 Add tRPC Bridge for frontend compatibility
	trpcBridge := NewTRPCBridge(hvacService, logger)

	// Create a custom router for tRPC endpoints
	router := mux.NewRouter()
	trpcBridge.RegisterRoutes(router)

	// Mount the tRPC router on the Kratos server
	srv.HandlePrefix("/api/trpc/", http.StripPrefix("/api/trpc", router))

	log.NewHelper(logger).Info("🚀 HTTP Server with tRPC Bridge initialized")

	return srv
}