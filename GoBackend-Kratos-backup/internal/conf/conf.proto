syntax = "proto3";

package kratos.api;

option go_package = "gobackend-hvac-kratos/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  AI ai = 3;
  Email email = 4;
  MCP mcp = 5;
  Logging logging = 6;
  Tracing tracing = 7;
  Metrics metrics = 8;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string addr = 1;
    google.protobuf.Duration read_timeout = 2;
    google.protobuf.Duration write_timeout = 3;
  }
  Database database = 1;
  Redis redis = 2;
}

message AI {
  message Model {
    string endpoint = 1;
    string model_name = 2;
    int32 max_tokens = 3;
  }
  Model gemma = 1;
  Model bielik = 2;
}

message Email {
  message BillionMail {
    string api_url = 1;
    string web_ui_url = 2;
    string admin_email = 3;
    string admin_password = 4;
  }
  message SMTP {
    string host = 1;
    int32 port = 2;
    string username = 3;
    string password = 4;
    string from = 5;
    bool use_tls = 6;
  }
  message IMAP {
    string host = 1;
    int32 port = 2;
    string username = 3;
    string password = 4;
    bool use_tls = 5;
  }
  message Templates {
    string service_reminder = 1;
    string quote_follow_up = 2;
    string invoice_notification = 3;
    string appointment_confirmation = 4;
  }
  BillionMail billionmail = 1;
  SMTP smtp = 2;
  IMAP imap = 3;
  Templates templates = 4;
}

message MCP {
  message Server {
    string addr = 1;
    string transport = 2;
  }
  message Tools {
    bool enabled = 1;
    bool hvac_tools = 2;
    bool email_tools = 3;
  }
  Server server = 1;
  Tools tools = 2;
}

message Logging {
  string level = 1;
  string format = 2;
  string output = 3;
}

message Tracing {
  string endpoint = 1;
  float sampler = 2;
}

message Metrics {
  string addr = 1;
  string path = 2;
}