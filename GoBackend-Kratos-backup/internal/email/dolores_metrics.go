package email

import (
	"sync"
	"time"
)

// 📊 DoloresMetrics - Metrics collection for Dolores email processing
type DoloresMetrics struct {
	mu                    sync.RWMutex
	EmailsProcessed       int64     `json:"emails_processed"`
	TranscriptionsFound   int64     `json:"transcriptions_found"`
	ErrorCount            int64     `json:"error_count"`
	AverageProcessingTime float64   `json:"average_processing_time"`
	LastProcessedAt       time.Time `json:"last_processed_at"`
	StartTime             time.Time `json:"start_time"`
}

// NewDoloresMetrics creates a new metrics collector
func NewDoloresMetrics() *DoloresMetrics {
	return &DoloresMetrics{
		StartTime: time.Now(),
	}
}

// IncrementProcessed increments processed email count
func (dm *DoloresMetrics) IncrementProcessed() {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dm.EmailsProcessed++
	dm.LastProcessedAt = time.Now()
}

// IncrementTranscriptions increments transcription count
func (dm *DoloresMetrics) IncrementTranscriptions() {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dm.TranscriptionsFound++
}

// IncrementErrors increments error count
func (dm *DoloresMetrics) IncrementErrors() {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dm.ErrorCount++
}

// UpdateProcessingTime updates average processing time
func (dm *DoloresMetrics) UpdateProcessingTime(duration time.Duration) {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	// Simple moving average calculation
	if dm.AverageProcessingTime == 0 {
		dm.AverageProcessingTime = duration.Seconds()
	} else {
		dm.AverageProcessingTime = (dm.AverageProcessingTime + duration.Seconds()) / 2
	}
}

// GetMetrics returns current metrics snapshot
func (dm *DoloresMetrics) GetMetrics() DoloresMetrics {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	return DoloresMetrics{
		EmailsProcessed:       dm.EmailsProcessed,
		TranscriptionsFound:   dm.TranscriptionsFound,
		ErrorCount:            dm.ErrorCount,
		AverageProcessingTime: dm.AverageProcessingTime,
		LastProcessedAt:       dm.LastProcessedAt,
		StartTime:             dm.StartTime,
	}
}

// GetSuccessRate returns success rate percentage
func (dm *DoloresMetrics) GetSuccessRate() float64 {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	if dm.EmailsProcessed == 0 {
		return 0.0
	}
	
	successCount := dm.EmailsProcessed - dm.ErrorCount
	return (float64(successCount) / float64(dm.EmailsProcessed)) * 100.0
}

// GetUptime returns service uptime
func (dm *DoloresMetrics) GetUptime() time.Duration {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	return time.Since(dm.StartTime)
}
