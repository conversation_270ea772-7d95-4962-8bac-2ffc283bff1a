package email

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
)

// 📧 Email Retrieval Service - Multi-mailbox email fetching
type EmailRetrievalService struct {
	log        *log.Helper
	mailboxes  []*MailboxConfig
	analysis   *EmailAnalysisService
	dashboard  *EmailDashboardService
}

// 📮 Mailbox Configuration
type MailboxConfig struct {
	Name         string `json:"name"`
	Host         string `json:"host"`
	Port         int    `json:"port"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	UseSSL       bool   `json:"use_ssl"`
	Folder       string `json:"folder"`       // INBOX, Sent, etc.
	PollInterval int    `json:"poll_interval"` // minutes
	Enabled      bool   `json:"enabled"`
}

// 📨 Retrieved Email
type RetrievedEmail struct {
	MailboxName string    `json:"mailbox_name"`
	MessageID   string    `json:"message_id"`
	Subject     string    `json:"subject"`
	From        string    `json:"from"`
	To          []string  `json:"to"`
	Date        time.Time `json:"date"`
	Size        uint32    `json:"size"`
	Flags       []string  `json:"flags"`
	RawData     []byte    `json:"raw_data,omitempty"`
	Processed   bool      `json:"processed"`
}

// 📊 Retrieval Statistics
type RetrievalStats struct {
	TotalRetrieved   int                    `json:"total_retrieved"`
	ByMailbox        map[string]int         `json:"by_mailbox"`
	LastRetrieval    time.Time              `json:"last_retrieval"`
	ErrorCount       int                    `json:"error_count"`
	ProcessingRate   float64                `json:"processing_rate"`
	ActiveMailboxes  int                    `json:"active_mailboxes"`
}

// NewEmailRetrievalService creates a new email retrieval service
func NewEmailRetrievalService(
	mailboxes []*MailboxConfig,
	analysis *EmailAnalysisService,
	dashboard *EmailDashboardService,
	logger log.Logger,
) *EmailRetrievalService {
	return &EmailRetrievalService{
		log:       log.NewHelper(logger),
		mailboxes: mailboxes,
		analysis:  analysis,
		dashboard: dashboard,
	}
}

// 🚀 Start email retrieval for all configured mailboxes
func (s *EmailRetrievalService) StartRetrieval(ctx context.Context) error {
	s.log.WithContext(ctx).Info("Starting email retrieval service")

	for _, mailbox := range s.mailboxes {
		if !mailbox.Enabled {
			s.log.WithContext(ctx).Infof("Skipping disabled mailbox: %s", mailbox.Name)
			continue
		}

		// Start goroutine for each mailbox
		go s.retrieveFromMailbox(ctx, mailbox)
	}

	return nil
}

// 📮 Retrieve emails from a specific mailbox
func (s *EmailRetrievalService) retrieveFromMailbox(ctx context.Context, config *MailboxConfig) {
	s.log.WithContext(ctx).Infof("Starting retrieval from mailbox: %s", config.Name)

	ticker := time.NewTicker(time.Duration(config.PollInterval) * time.Minute)
	defer ticker.Stop()

	// Initial retrieval
	s.fetchEmails(ctx, config)

	// Periodic retrieval
	for {
		select {
		case <-ctx.Done():
			s.log.WithContext(ctx).Infof("Stopping retrieval for mailbox: %s", config.Name)
			return
		case <-ticker.C:
			s.fetchEmails(ctx, config)
		}
	}
}

// 📥 Fetch emails from mailbox
func (s *EmailRetrievalService) fetchEmails(ctx context.Context, config *MailboxConfig) {
	s.log.WithContext(ctx).Infof("Fetching emails from: %s", config.Name)

	// Connect to IMAP server
	c, err := s.connectToMailbox(config)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to connect to %s: %v", config.Name, err)
		return
	}
	defer c.Close()

	// Login
	if err := c.Login(config.Username, config.Password); err != nil {
		s.log.WithContext(ctx).Errorf("Failed to login to %s: %v", config.Name, err)
		return
	}
	defer c.Logout()

	// Select folder
	folder := config.Folder
	if folder == "" {
		folder = "INBOX"
	}

	mbox, err := c.Select(folder, false)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to select folder %s in %s: %v", folder, config.Name, err)
		return
	}

	s.log.WithContext(ctx).Infof("Selected folder %s with %d messages", folder, mbox.Messages)

	if mbox.Messages == 0 {
		s.log.WithContext(ctx).Infof("No messages in %s", config.Name)
		return
	}

	// Fetch recent emails (last 50 or all if less)
	from := uint32(1)
	to := mbox.Messages
	if mbox.Messages > 50 {
		from = mbox.Messages - 49 // Last 50 messages
	}

	seqset := new(imap.SeqSet)
	seqset.AddRange(from, to)

	// Fetch message headers and flags
	messages := make(chan *imap.Message, 10)
	done := make(chan error, 1)

	go func() {
		done <- c.Fetch(seqset, []imap.FetchItem{
			imap.FetchEnvelope,
			imap.FetchFlags,
			imap.FetchRFC822Size,
			imap.FetchRFC822,
		}, messages)
	}()

	// Process messages
	var retrievedCount int
	for msg := range messages {
		email := s.processMessage(ctx, config.Name, msg)
		if email != nil {
			// Analyze email
			go s.analyzeRetrievedEmail(ctx, email)
			retrievedCount++
		}
	}

	if err := <-done; err != nil {
		s.log.WithContext(ctx).Errorf("Failed to fetch messages from %s: %v", config.Name, err)
		return
	}

	s.log.WithContext(ctx).Infof("Retrieved %d emails from %s", retrievedCount, config.Name)
}

// 🔌 Connect to mailbox
func (s *EmailRetrievalService) connectToMailbox(config *MailboxConfig) (*client.Client, error) {
	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)

	var c *client.Client
	var err error

	if config.UseSSL {
		// Connect with SSL/TLS
		tlsConfig := &tls.Config{
			ServerName: config.Host,
		}
		c, err = client.DialTLS(addr, tlsConfig)
	} else {
		// Connect without SSL (will use STARTTLS if available)
		c, err = client.Dial(addr)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", addr, err)
	}

	return c, nil
}

// 📨 Process individual message
func (s *EmailRetrievalService) processMessage(ctx context.Context, mailboxName string, msg *imap.Message) *RetrievedEmail {
	if msg.Envelope == nil {
		s.log.WithContext(ctx).Warn("Message envelope is nil")
		return nil
	}

	email := &RetrievedEmail{
		MailboxName: mailboxName,
		MessageID:   msg.Envelope.MessageId,
		Subject:     msg.Envelope.Subject,
		Date:        msg.Envelope.Date,
		Size:        msg.Size,
		Flags:       msg.Flags,
	}

	// Extract From address
	if len(msg.Envelope.From) > 0 {
		from := msg.Envelope.From[0]
		if from.PersonalName != "" {
			email.From = fmt.Sprintf("%s <%s@%s>", from.PersonalName, from.MailboxName, from.HostName)
		} else {
			email.From = fmt.Sprintf("%s@%s", from.MailboxName, from.HostName)
		}
	}

	// Extract To addresses
	for _, to := range msg.Envelope.To {
		if to.PersonalName != "" {
			email.To = append(email.To, fmt.Sprintf("%s <%s@%s>", to.PersonalName, to.MailboxName, to.HostName))
		} else {
			email.To = append(email.To, fmt.Sprintf("%s@%s", to.MailboxName, to.HostName))
		}
	}

	// Get raw email data
	for _, value := range msg.Body {
		if reader, ok := value.(io.Reader); ok {
			rawData, err := io.ReadAll(reader)
			if err != nil {
				s.log.WithContext(ctx).Warnf("Failed to read message body: %v", err)
				continue
			}
			email.RawData = rawData
			break
		}
	}

	return email
}

// 🔍 Analyze retrieved email
func (s *EmailRetrievalService) analyzeRetrievedEmail(ctx context.Context, email *RetrievedEmail) {
	if len(email.RawData) == 0 {
		s.log.WithContext(ctx).Warnf("No raw data for email: %s", email.Subject)
		return
	}

	s.log.WithContext(ctx).Infof("Analyzing email: %s", email.Subject)

	// Analyze email using analysis service
	result, err := s.analysis.AnalyzeEmail(ctx, email.RawData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to analyze email %s: %v", email.Subject, err)
		return
	}

	// Store in dashboard
	err = s.dashboard.emailStore.Store(result)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to store analysis result: %v", err)
		return
	}

	email.Processed = true
	s.log.WithContext(ctx).Infof("Successfully analyzed and stored email: %s", email.Subject)
}

// 📊 Get retrieval statistics
func (s *EmailRetrievalService) GetRetrievalStats(ctx context.Context) (*RetrievalStats, error) {
	stats := &RetrievalStats{
		ByMailbox: make(map[string]int),
	}

	// Count active mailboxes
	for _, mailbox := range s.mailboxes {
		if mailbox.Enabled {
			stats.ActiveMailboxes++
		}
	}

	// Get stats from dashboard
	dashStats, err := s.dashboard.GetDashboardStats(ctx)
	if err != nil {
		return nil, err
	}

	stats.TotalRetrieved = dashStats.TotalEmails
	stats.LastRetrieval = time.Now() // Placeholder
	stats.ProcessingRate = dashStats.ProcessingMetrics.SuccessRate

	return stats, nil
}

// 🔧 Test mailbox connection
func (s *EmailRetrievalService) TestMailboxConnection(ctx context.Context, config *MailboxConfig) error {
	s.log.WithContext(ctx).Infof("Testing connection to mailbox: %s", config.Name)

	// Connect to IMAP server
	c, err := s.connectToMailbox(config)
	if err != nil {
		return fmt.Errorf("connection failed: %w", err)
	}
	defer c.Close()

	// Test login
	if err := c.Login(config.Username, config.Password); err != nil {
		return fmt.Errorf("login failed: %w", err)
	}
	defer c.Logout()

	// Test folder selection
	folder := config.Folder
	if folder == "" {
		folder = "INBOX"
	}

	_, err = c.Select(folder, true) // Read-only
	if err != nil {
		return fmt.Errorf("folder selection failed: %w", err)
	}

	s.log.WithContext(ctx).Infof("Successfully tested mailbox: %s", config.Name)
	return nil
}

// 📧 Manually retrieve from specific mailbox
func (s *EmailRetrievalService) RetrieveFromMailbox(ctx context.Context, mailboxName string) error {
	for _, config := range s.mailboxes {
		if config.Name == mailboxName && config.Enabled {
			s.fetchEmails(ctx, config)
			return nil
		}
	}

	return fmt.Errorf("mailbox %s not found or disabled", mailboxName)
}

// 📮 Add new mailbox configuration
func (s *EmailRetrievalService) AddMailbox(config *MailboxConfig) error {
	// Test connection first
	ctx := context.Background()
	if err := s.TestMailboxConnection(ctx, config); err != nil {
		return fmt.Errorf("mailbox test failed: %w", err)
	}

	s.mailboxes = append(s.mailboxes, config)
	s.log.Infof("Added new mailbox: %s", config.Name)

	// Start retrieval if enabled
	if config.Enabled {
		go s.retrieveFromMailbox(ctx, config)
	}

	return nil
}

// 🗑️ Remove mailbox configuration
func (s *EmailRetrievalService) RemoveMailbox(mailboxName string) error {
	for i, config := range s.mailboxes {
		if config.Name == mailboxName {
			s.mailboxes = append(s.mailboxes[:i], s.mailboxes[i+1:]...)
			s.log.Infof("Removed mailbox: %s", mailboxName)
			return nil
		}
	}

	return fmt.Errorf("mailbox %s not found", mailboxName)
}

// 📋 List all mailbox configurations
func (s *EmailRetrievalService) ListMailboxes() []*MailboxConfig {
	return s.mailboxes
}

// 🔄 Update mailbox configuration
func (s *EmailRetrievalService) UpdateMailbox(mailboxName string, config *MailboxConfig) error {
	for i, existing := range s.mailboxes {
		if existing.Name == mailboxName {
			// Test new configuration
			ctx := context.Background()
			if err := s.TestMailboxConnection(ctx, config); err != nil {
				return fmt.Errorf("mailbox test failed: %w", err)
			}

			s.mailboxes[i] = config
			s.log.Infof("Updated mailbox: %s", mailboxName)
			return nil
		}
	}

	return fmt.Errorf("mailbox %s not found", mailboxName)
}

// 🔧 Default mailbox configurations for common providers
func GetDefaultMailboxConfigs() []*MailboxConfig {
	return []*MailboxConfig{
		{
			Name:         "Gmail",
			Host:         "imap.gmail.com",
			Port:         993,
			UseSSL:       true,
			Folder:       "INBOX",
			PollInterval: 5,
			Enabled:      false,
		},
		{
			Name:         "Outlook",
			Host:         "outlook.office365.com",
			Port:         993,
			UseSSL:       true,
			Folder:       "INBOX",
			PollInterval: 5,
			Enabled:      false,
		},
		{
			Name:         "Yahoo",
			Host:         "imap.mail.yahoo.com",
			Port:         993,
			UseSSL:       true,
			Folder:       "INBOX",
			PollInterval: 10,
			Enabled:      false,
		},
	}
}
