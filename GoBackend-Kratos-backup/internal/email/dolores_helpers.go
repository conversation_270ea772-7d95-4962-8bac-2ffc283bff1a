package email

import (
	"strings"
	"time"
)

// Helper methods for Dolores email analysis

// analyzeCommunicationStyle analyzes customer communication style
func (dc *DoloresConnector) analyzeCommunicationStyle(result *DoloresEmailResult) string {
	if result.EmailAnalysisResult == nil {
		return "unknown"
	}

	// Analyze based on email content characteristics
	content := strings.ToLower(result.Subject + " " + result.From)

	if strings.Contains(content, "urgent") || strings.Contains(content, "asap") || strings.Contains(content, "emergency") {
		return "direct"
	}

	if strings.Contains(content, "please") || strings.Contains(content, "thank") || strings.Contains(content, "appreciate") {
		return "polite"
	}

	if strings.Contains(content, "technical") || strings.Contains(content, "specification") || strings.Contains(content, "model") {
		return "technical"
	}

	return "standard"
}

// assessTechnicalKnowledge assesses customer's technical knowledge level
func (dc *DoloresConnector) assessTechnicalKnowledge(result *DoloresEmailResult) string {
	if result.TranscriptionData == nil {
		return "basic"
	}

	technicalTermCount := len(result.TranscriptionData.TechnicalTerms)

	if technicalTermCount >= 5 {
		return "advanced"
	} else if technicalTermCount >= 2 {
		return "intermediate"
	}

	return "basic"
}

// calculateSatisfactionScore calculates customer satisfaction score
func (dc *DoloresConnector) calculateSatisfactionScore(result *DoloresEmailResult) float64 {
	if result.EmailAnalysisResult == nil {
		return 5.0 // Default neutral score
	}

	// Base score on sentiment
	switch result.Sentiment {
	case "positive":
		return 8.0
	case "negative":
		return 3.0
	default:
		return 5.0
	}
}

// assessChurnRisk assesses customer churn risk
func (dc *DoloresConnector) assessChurnRisk(result *DoloresEmailResult) float64 {
	risk := 0.3 // Default low risk

	if result.EmailAnalysisResult == nil {
		return risk
	}

	// Increase risk for negative sentiment
	if result.Sentiment == "negative" {
		risk += 0.4
	}

	// Increase risk for complaints
	if strings.Contains(strings.ToLower(result.Subject), "complaint") ||
		strings.Contains(strings.ToLower(result.Subject), "problem") {
		risk += 0.3
	}

	// Cap at 1.0
	if risk > 1.0 {
		risk = 1.0
	}

	return risk
}

// generateCustomerTags generates tags based on analysis
func (dc *DoloresConnector) generateCustomerTags(result *DoloresEmailResult) []string {
	var tags []string

	if result.EmailAnalysisResult == nil {
		return tags
	}

	// Add sentiment-based tags
	if result.Sentiment == "positive" {
		tags = append(tags, "satisfied_customer")
	} else if result.Sentiment == "negative" {
		tags = append(tags, "needs_attention")
	}

	// Add priority-based tags
	if result.Priority == "high" || result.Priority == "urgent" {
		tags = append(tags, "high_priority")
	}

	// Add transcription-based tags
	if result.TranscriptionData != nil && result.TranscriptionData.HasTranscription {
		tags = append(tags, "has_call_history")

		if result.TranscriptionData.FollowUpRequired {
			tags = append(tags, "requires_follow_up")
		}
	}

	return tags
}

// calculateLeadScore calculates lead scoring
func (dc *DoloresConnector) calculateLeadScore(result *DoloresEmailResult) float64 {
	score := 50.0 // Base score

	if result.EmailAnalysisResult == nil {
		return score
	}

	// Adjust based on priority
	switch result.Priority {
	case "urgent":
		score += 30
	case "high":
		score += 20
	case "medium":
		score += 10
	}

	// Adjust based on sentiment
	switch result.Sentiment {
	case "positive":
		score += 15
	case "negative":
		score -= 10
	}

	// Adjust based on transcription data
	if result.TranscriptionData != nil {
		score += 20 // Having call data increases lead quality

		if len(result.TranscriptionData.ServiceRequests) > 0 {
			score += 25 // Active service requests are valuable
		}
	}

	// Cap between 0 and 100
	if score > 100 {
		score = 100
	} else if score < 0 {
		score = 0
	}

	return score
}

// calculateConversionProbability calculates conversion probability
func (dc *DoloresConnector) calculateConversionProbability(result *DoloresEmailResult) float64 {
	probability := 0.3 // Base 30% probability

	if result.EmailAnalysisResult == nil {
		return probability
	}

	// Increase for service requests
	if strings.Contains(strings.ToLower(result.Subject), "service") ||
		strings.Contains(strings.ToLower(result.Subject), "repair") {
		probability += 0.4
	}

	// Increase for urgent requests
	if result.Priority == "urgent" || result.Priority == "high" {
		probability += 0.2
	}

	// Adjust based on sentiment
	if result.Sentiment == "positive" {
		probability += 0.1
	} else if result.Sentiment == "negative" {
		probability -= 0.1
	}

	// Cap between 0 and 1
	if probability > 1.0 {
		probability = 1.0
	} else if probability < 0 {
		probability = 0
	}

	return probability
}

// estimateRevenue estimates potential revenue
func (dc *DoloresConnector) estimateRevenue(result *DoloresEmailResult) float64 {
	baseRevenue := 500.0 // Base service call revenue

	if result.EmailAnalysisResult == nil {
		return baseRevenue
	}

	// Increase for equipment mentions
	if result.TranscriptionData != nil && len(result.TranscriptionData.EquipmentMentioned) > 0 {
		baseRevenue += 1000.0 // Equipment work typically more expensive
	}

	// Increase for urgent requests
	if result.Priority == "urgent" {
		baseRevenue += 200.0 // Emergency service premium
	}

	// Increase for multiple service requests
	if result.TranscriptionData != nil && len(result.TranscriptionData.ServiceRequests) > 1 {
		baseRevenue += float64(len(result.TranscriptionData.ServiceRequests)-1) * 300.0
	}

	return baseRevenue
}

// determineServiceCategory determines service category
func (dc *DoloresConnector) determineServiceCategory(result *DoloresEmailResult) string {
	if result.EmailAnalysisResult == nil {
		return "general"
	}

	content := strings.ToLower(result.Subject)

	if strings.Contains(content, "install") {
		return "installation"
	} else if strings.Contains(content, "repair") || strings.Contains(content, "fix") {
		return "repair"
	} else if strings.Contains(content, "maintenance") || strings.Contains(content, "service") {
		return "maintenance"
	} else if strings.Contains(content, "emergency") || strings.Contains(content, "urgent") {
		return "emergency"
	}

	return "general"
}

// extractSeasonalTrends extracts seasonal trend indicators
func (dc *DoloresConnector) extractSeasonalTrends(result *DoloresEmailResult) []string {
	var trends []string

	if result.EmailAnalysisResult == nil {
		return trends
	}

	content := strings.ToLower(result.Subject)
	currentMonth := time.Now().Month()

	// Summer trends
	if currentMonth >= 5 && currentMonth <= 9 {
		if strings.Contains(content, "cooling") || strings.Contains(content, "ac") {
			trends = append(trends, "summer_cooling_demand")
		}
	}

	// Winter trends
	if currentMonth >= 11 || currentMonth <= 3 {
		if strings.Contains(content, "heating") || strings.Contains(content, "furnace") {
			trends = append(trends, "winter_heating_demand")
		}
	}

	return trends
}

// detectCompetitorMentions detects competitor mentions
func (dc *DoloresConnector) detectCompetitorMentions(result *DoloresEmailResult) []string {
	var competitors []string

	if result.EmailAnalysisResult == nil {
		return competitors
	}

	content := strings.ToLower(result.Subject)

	// Common HVAC competitors (example list)
	competitorNames := []string{
		"carrier", "trane", "lennox", "rheem", "goodman", "york", "daikin", "mitsubishi",
	}

	for _, competitor := range competitorNames {
		if strings.Contains(content, competitor) {
			competitors = append(competitors, competitor)
		}
	}

	return competitors
}

// generateRecommendedActions generates recommended actions
func (dc *DoloresConnector) generateRecommendedActions(result *DoloresEmailResult) []string {
	var actions []string

	if result.EmailAnalysisResult == nil {
		return actions
	}

	// High priority actions
	if result.Priority == "urgent" || result.Priority == "high" {
		actions = append(actions, "immediate_response_required")
		actions = append(actions, "assign_senior_technician")
	}

	// Negative sentiment actions
	if result.Sentiment == "negative" {
		actions = append(actions, "manager_review_required")
		actions = append(actions, "customer_satisfaction_follow_up")
	}

	// Service request actions
	if strings.Contains(strings.ToLower(result.Subject), "service") {
		actions = append(actions, "schedule_service_appointment")
		actions = append(actions, "prepare_service_quote")
	}

	// Transcription-based actions
	if result.TranscriptionData != nil && result.TranscriptionData.FollowUpRequired {
		actions = append(actions, "schedule_follow_up_call")
	}

	return actions
}
