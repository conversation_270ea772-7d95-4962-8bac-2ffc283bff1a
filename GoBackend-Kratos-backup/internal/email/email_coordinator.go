package email

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/transcription"
	"gobackend-hvac-kratos/internal/biz"
)

// 🎯 Email Coordinator - Orchestrates dual-source email processing
type EmailCoordinator struct {
	log                    *log.Helper
	dualSourceProcessor    *DualSourceEmailProcessor
	emailRetrieval         *EmailRetrievalService
	config                 *EmailCoordinatorConfig
	isRunning              bool
	stopChan               chan bool
	mutex                  sync.RWMutex
	stats                  *EmailProcessingStats
}

// EmailCoordinatorConfig configuration for email coordinator
type EmailCoordinatorConfig struct {
	CustomerEmailPolling    time.Duration `yaml:"customer_email_polling"`
	TranscriptionPolling    time.Duration `yaml:"transcription_polling"`
	MaxConcurrentProcessing int           `yaml:"max_concurrent_processing"`
	RetryAttempts          int           `yaml:"retry_attempts"`
	RetryDelay             time.Duration `yaml:"retry_delay"`
	EnableHealthCheck      bool          `yaml:"enable_health_check"`
	HealthCheckInterval    time.Duration `yaml:"health_check_interval"`
}

// EmailProcessingStats tracks processing statistics
type EmailProcessingStats struct {
	CustomerEmailsProcessed    int64     `json:"customer_emails_processed"`
	TranscriptionEmailsProcessed int64   `json:"transcription_emails_processed"`
	TotalProcessingTime        time.Duration `json:"total_processing_time"`
	SuccessfulProcessing       int64     `json:"successful_processing"`
	FailedProcessing           int64     `json:"failed_processing"`
	LastProcessingTime         time.Time `json:"last_processing_time"`
	AverageProcessingTime      time.Duration `json:"average_processing_time"`
	AudioFilesTranscribed      int64     `json:"audio_files_transcribed"`
	WorkflowsTriggered         int64     `json:"workflows_triggered"`
	ServiceOrdersCreated       int64     `json:"service_orders_created"`
}

// NewEmailCoordinator creates a new email coordinator
func NewEmailCoordinator(
	emailAnalysis *EmailAnalysisService,
	transcriptionParser *transcription.TranscriptionParser,
	gemma3 *ai.Gemma3Service,
	emailUsecase *biz.EmailUsecase,
	workflowUsecase *biz.WorkflowUsecase,
	emailRetrieval *EmailRetrievalService,
	logger log.Logger,
) *EmailCoordinator {
	config := &EmailCoordinatorConfig{
		CustomerEmailPolling:    5 * time.Minute,
		TranscriptionPolling:    30 * time.Second,
		MaxConcurrentProcessing: 5,
		RetryAttempts:          3,
		RetryDelay:             10 * time.Second,
		EnableHealthCheck:      true,
		HealthCheckInterval:    1 * time.Minute,
	}

	dualSourceProcessor := NewDualSourceEmailProcessor(
		emailAnalysis,
		transcriptionParser,
		gemma3,
		emailUsecase,
		workflowUsecase,
		logger,
	)

	return &EmailCoordinator{
		log:                 log.NewHelper(logger),
		dualSourceProcessor: dualSourceProcessor,
		emailRetrieval:      emailRetrieval,
		config:              config,
		isRunning:           false,
		stopChan:            make(chan bool),
		stats:               &EmailProcessingStats{},
	}
}

// Start begins the email coordination process
func (c *EmailCoordinator) Start(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		return fmt.Errorf("email coordinator is already running")
	}

	c.log.WithContext(ctx).Info("🚀 Starting Email Coordinator...")

	c.isRunning = true

	// Start customer email polling
	go c.pollCustomerEmails(ctx)

	// Start transcription email polling
	go c.pollTranscriptionEmails(ctx)

	// Start health check if enabled
	if c.config.EnableHealthCheck {
		go c.healthCheck(ctx)
	}

	c.log.WithContext(ctx).Info("✅ Email Coordinator started successfully")
	return nil
}

// Stop stops the email coordination process
func (c *EmailCoordinator) Stop(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isRunning {
		return fmt.Errorf("email coordinator is not running")
	}

	c.log.WithContext(ctx).Info("🛑 Stopping Email Coordinator...")

	c.isRunning = false
	close(c.stopChan)

	c.log.WithContext(ctx).Info("✅ Email Coordinator stopped successfully")
	return nil
}

// pollCustomerEmails polls customer <NAME_EMAIL>
func (c *EmailCoordinator) pollCustomerEmails(ctx context.Context) {
	ticker := time.NewTicker(c.config.CustomerEmailPolling)
	defer ticker.Stop()

	c.log.WithContext(ctx).Info("📧 Starting customer email polling...")

	for {
		select {
		case <-c.stopChan:
			c.log.WithContext(ctx).Info("📧 Customer email polling stopped")
			return
		case <-ticker.C:
			c.processCustomerEmails(ctx)
		}
	}
}

// pollTranscriptionEmails polls transcription <NAME_EMAIL>
func (c *EmailCoordinator) pollTranscriptionEmails(ctx context.Context) {
	ticker := time.NewTicker(c.config.TranscriptionPolling)
	defer ticker.Stop()

	c.log.WithContext(ctx).Info("🎵 Starting transcription email polling...")

	for {
		select {
		case <-c.stopChan:
			c.log.WithContext(ctx).Info("🎵 Transcription email polling stopped")
			return
		case <-ticker.C:
			c.processTranscriptionEmails(ctx)
		}
	}
}

// processCustomerEmails processes emails from customer account
func (c *EmailCoordinator) processCustomerEmails(ctx context.Context) {
	c.log.WithContext(ctx).Debug("📧 Checking for new customer emails...")

	// Retrieve emails from customer account
	err := c.emailRetrieval.RetrieveFromMailbox(ctx, "primary")
	if err != nil {
		c.log.WithContext(ctx).Errorf("Failed to retrieve customer emails: %v", err)
		c.stats.FailedProcessing++
		return
	}

	// TODO: Get actual emails and process them
	// For now, simulate processing
	c.stats.CustomerEmailsProcessed++
	c.stats.LastProcessingTime = time.Now()
	c.log.WithContext(ctx).Debug("✅ Customer emails processed")
}

// processTranscriptionEmails processes emails from transcription account
func (c *EmailCoordinator) processTranscriptionEmails(ctx context.Context) {
	c.log.WithContext(ctx).Debug("🎵 Checking for new transcription emails...")

	// Retrieve emails from transcription account
	err := c.emailRetrieval.RetrieveFromMailbox(ctx, "audio")
	if err != nil {
		c.log.WithContext(ctx).Errorf("Failed to retrieve transcription emails: %v", err)
		c.stats.FailedProcessing++
		return
	}

	// TODO: Get actual emails and process them
	// For now, simulate processing
	c.stats.TranscriptionEmailsProcessed++
	c.stats.LastProcessingTime = time.Now()
	c.log.WithContext(ctx).Debug("✅ Transcription emails processed")
}

// healthCheck performs periodic health checks
func (c *EmailCoordinator) healthCheck(ctx context.Context) {
	ticker := time.NewTicker(c.config.HealthCheckInterval)
	defer ticker.Stop()

	c.log.WithContext(ctx).Info("💓 Starting health check monitoring...")

	for {
		select {
		case <-c.stopChan:
			c.log.WithContext(ctx).Info("💓 Health check monitoring stopped")
			return
		case <-ticker.C:
			c.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck checks system health
func (c *EmailCoordinator) performHealthCheck(ctx context.Context) {
	c.log.WithContext(ctx).Debug("💓 Performing health check...")

	// Check if coordinator is running
	c.mutex.RLock()
	isRunning := c.isRunning
	c.mutex.RUnlock()

	if !isRunning {
		c.log.WithContext(ctx).Warn("⚠️ Email coordinator is not running")
		return
	}

	// Check last processing time
	timeSinceLastProcessing := time.Since(c.stats.LastProcessingTime)
	if timeSinceLastProcessing > 10*time.Minute {
		c.log.WithContext(ctx).Warnf("⚠️ No email processing in last %v", timeSinceLastProcessing)
	}

	c.log.WithContext(ctx).Debug("✅ Health check completed")
}

// GetStats returns current processing statistics
func (c *EmailCoordinator) GetStats() *EmailProcessingStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// Calculate average processing time
	if c.stats.SuccessfulProcessing > 0 {
		c.stats.AverageProcessingTime = c.stats.TotalProcessingTime / time.Duration(c.stats.SuccessfulProcessing)
	}

	return c.stats
}

// IsRunning returns whether the coordinator is currently running
func (c *EmailCoordinator) IsRunning() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isRunning
}

// ProcessEmailManually manually processes a specific email
func (c *EmailCoordinator) ProcessEmailManually(ctx context.Context, emailData []byte, sourceAccount string) (*ProcessingResult, error) {
	c.log.WithContext(ctx).Infof("🔧 Manually processing email from: %s", sourceAccount)

	startTime := time.Now()
	result, err := c.dualSourceProcessor.ProcessEmail(ctx, emailData, sourceAccount)
	processingTime := time.Since(startTime)

	// Update stats
	c.mutex.Lock()
	c.stats.TotalProcessingTime += processingTime
	if err == nil {
		c.stats.SuccessfulProcessing++
		if result.WorkflowTriggered {
			c.stats.WorkflowsTriggered++
		}
		if result.Transcription != nil {
			c.stats.AudioFilesTranscribed += int64(len(result.Transcription.AudioFiles))
		}
	} else {
		c.stats.FailedProcessing++
	}
	c.stats.LastProcessingTime = time.Now()
	c.mutex.Unlock()

	return result, err
}
