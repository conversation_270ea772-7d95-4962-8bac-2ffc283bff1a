package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
)

// 🚀 Redis Cache Service for high-performance caching
type RedisCache struct {
	client *redis.Client
	log    *log.Helper
}

// CacheConfig represents Redis cache configuration
type CacheConfig struct {
	Addr         string        `yaml:"addr"`
	Password     string        `yaml:"password"`
	DB           int           `yaml:"db"`
	PoolSize     int           `yaml:"pool_size"`
	MinIdleConns int           `yaml:"min_idle_conns"`
	DialTimeout  time.Duration `yaml:"dial_timeout"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// NewRedisCache creates a new Redis cache instance with optimized settings
func NewRedisCache(config *CacheConfig, logger log.Logger) (*RedisCache, error) {
	log := log.NewHelper(logger)

	// 🔧 Optimized Redis client configuration
	rdb := redis.NewClient(&redis.Options{
		Addr:         config.Addr,
		Password:     config.Password,
		DB:           config.DB,
		PoolSize:     config.PoolSize,     // Connection pool size
		MinIdleConns: config.MinIdleConns, // Minimum idle connections
		DialTimeout:  config.DialTimeout,  // Connection timeout
		ReadTimeout:  config.ReadTimeout,  // Read timeout
		WriteTimeout: config.WriteTimeout, // Write timeout
		IdleTimeout:  config.IdleTimeout,  // Idle connection timeout
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	log.Info("🚀 Redis cache connected successfully")

	return &RedisCache{
		client: rdb,
		log:    log,
	}, nil
}

// 💾 Set stores a value in cache with expiration
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.Set(ctx, key, data, expiration).Err()
}

// 📖 Get retrieves a value from cache
func (r *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// 🗑️ Delete removes a key from cache
func (r *RedisCache) Delete(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

// ⏰ Exists checks if a key exists in cache
func (r *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	count, err := r.client.Exists(ctx, key).Result()
	return count > 0, err
}

// 🔄 SetNX sets a key only if it doesn't exist (atomic operation)
func (r *RedisCache) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return false, err
	}

	return r.client.SetNX(ctx, key, data, expiration).Result()
}

// 📊 GetStats returns Redis statistics
func (r *RedisCache) GetStats(ctx context.Context) (*CacheStats, error) {
	info, err := r.client.Info(ctx, "stats").Result()
	if err != nil {
		return nil, err
	}

	poolStats := r.client.PoolStats()

	return &CacheStats{
		Info:      info,
		PoolStats: poolStats,
	}, nil
}

// 🧹 FlushAll clears all cache (use with caution!)
func (r *RedisCache) FlushAll(ctx context.Context) error {
	return r.client.FlushAll(ctx).Err()
}

// 🔍 Keys returns all keys matching pattern
func (r *RedisCache) Keys(ctx context.Context, pattern string) ([]string, error) {
	return r.client.Keys(ctx, pattern).Result()
}

// ⏱️ TTL returns time to live for a key
func (r *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	return r.client.TTL(ctx, key).Result()
}

// 🔄 Expire sets expiration for a key
func (r *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Expire(ctx, key, expiration).Err()
}

// 📈 Increment increments a numeric value
func (r *RedisCache) Increment(ctx context.Context, key string) (int64, error) {
	return r.client.Incr(ctx, key).Result()
}

// 📉 Decrement decrements a numeric value
func (r *RedisCache) Decrement(ctx context.Context, key string) (int64, error) {
	return r.client.Decr(ctx, key).Result()
}

// 🏷️ SetHash stores a hash field
func (r *RedisCache) SetHash(ctx context.Context, key, field string, value interface{}) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.HSet(ctx, key, field, data).Err()
}

// 📖 GetHash retrieves a hash field
func (r *RedisCache) GetHash(ctx context.Context, key, field string, dest interface{}) error {
	data, err := r.client.HGet(ctx, key, field).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// 📊 Cache Statistics
type CacheStats struct {
	Info      string             `json:"info"`
	PoolStats *redis.PoolStats   `json:"pool_stats"`
}

// 🔧 Close closes the Redis connection
func (r *RedisCache) Close() error {
	r.log.Info("🔌 Closing Redis cache connection")
	return r.client.Close()
}

// 🎯 Cache Keys for HVAC system
const (
	CustomerCacheKey     = "hvac:customer:%s"
	JobCacheKey         = "hvac:job:%s"
	EmailCacheKey       = "hvac:email:%s"
	TranscriptionCacheKey = "hvac:transcription:%s"
	AIAnalysisCacheKey  = "hvac:ai:analysis:%s"
	DashboardCacheKey   = "hvac:dashboard:stats"
	SystemMetricsCacheKey = "hvac:system:metrics"
)

// 🕐 Cache TTL constants
const (
	ShortCacheTTL  = 5 * time.Minute   // For frequently changing data
	MediumCacheTTL = 30 * time.Minute  // For moderately changing data
	LongCacheTTL   = 2 * time.Hour     // For rarely changing data
	DayCacheTTL    = 24 * time.Hour    // For daily aggregations
)

// 🎯 Helper functions for HVAC-specific caching

// CacheCustomer caches customer data
func (r *RedisCache) CacheCustomer(ctx context.Context, customerID string, customer interface{}) error {
	key := fmt.Sprintf(CustomerCacheKey, customerID)
	return r.Set(ctx, key, customer, MediumCacheTTL)
}

// GetCachedCustomer retrieves cached customer data
func (r *RedisCache) GetCachedCustomer(ctx context.Context, customerID string, dest interface{}) error {
	key := fmt.Sprintf(CustomerCacheKey, customerID)
	return r.Get(ctx, key, dest)
}

// CacheJob caches job data
func (r *RedisCache) CacheJob(ctx context.Context, jobID string, job interface{}) error {
	key := fmt.Sprintf(JobCacheKey, jobID)
	return r.Set(ctx, key, job, MediumCacheTTL)
}

// GetCachedJob retrieves cached job data
func (r *RedisCache) GetCachedJob(ctx context.Context, jobID string, dest interface{}) error {
	key := fmt.Sprintf(JobCacheKey, jobID)
	return r.Get(ctx, key, dest)
}

// CacheDashboardStats caches dashboard statistics
func (r *RedisCache) CacheDashboardStats(ctx context.Context, stats interface{}) error {
	return r.Set(ctx, DashboardCacheKey, stats, ShortCacheTTL)
}

// GetCachedDashboardStats retrieves cached dashboard statistics
func (r *RedisCache) GetCachedDashboardStats(ctx context.Context, dest interface{}) error {
	return r.Get(ctx, DashboardCacheKey, dest)
}
