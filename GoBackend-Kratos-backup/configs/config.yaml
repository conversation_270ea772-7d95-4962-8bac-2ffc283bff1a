server:
  http:
    addr: 0.0.0.0:8080
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s

data:
  database:
    driver: postgres
    source: *************************************************/hvacdb?sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

ai:
  gemma:
    endpoint: "http://************:1234"
    model_name: "gemma-3-4b-it-qat"
    max_tokens: 100000
  bielik:
    endpoint: "http://************:1234"
    model_name: "gemma-3-12b-it-qat"
    max_tokens: 100000

email:
  # 📧 Primary HVAC Email Account (Customer emails)
  primary:
    smtp:
      host: "serwer2440139.home.pl"
      port: 587
      username: "grz<PERSON><PERSON>@koldbringers.pl"
      password: "Blaeritipol1"
      from: "g<PERSON><PERSON><PERSON>@koldbringers.pl"
      sender_name: "HVAC CRM"
      use_tls: true
      secure: false
    imap:
      host: "serwer2440139.home.pl"
      port: 993
      username: "grz<PERSON><PERSON>@koldbringers.pl"
      password: "Blaeritipol1"
      use_ssl: true
      folders: ["INBOX"]
      batch_size: 20
      polling_interval: "5m"

  # 🎵 Audio Email Account (Transcription attachments)
  audio:
    imap:
      host: "serwer2440139.home.pl"
      port: 993
      username: "<EMAIL>"
      password: "Blaeritipol1"
      use_ssl: true
      folders: ["INBOX"]
      batch_size: 10
      polling_interval: "15s"
  templates:
    service_reminder: "service_reminder"
    quote_follow_up: "quote_follow_up"
    invoice_notification: "invoice_notification"
    appointment_confirmation: "appointment_confirmation"

mcp:
  server:
    addr: "0.0.0.0:8081"
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true