#!/bin/bash

# Fix job.go syntax errors
echo "🔧 Fixing job.go syntax errors..."

# Fix the specific syntax errors in job.go
sed -i 's/t\.logger\.<PERSON><PERSON><PERSON>("Failed to create job"": %v", err)/t.logger.Errorf("Failed to create job: %v", err)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Infof("Job created successfully"":/t.logger.Infof("Job created successfully: job_id=%d, title=%s, customer_id=%d", result.ID, result.Title, result.CustomerID)/g' internal/mcp/tools/job.go
sed -i 's/"job_id"=result\.ID,//g' internal/mcp/tools/job.go
sed -i 's/"title"=result\.Title,//g' internal/mcp/tools/job.go
sed -i 's/"customer_id"=result\.CustomerID,//g' internal/mcp/tools/job.go

# Fix other similar patterns
sed -i 's/t\.logger\.Error("Failed to get job", err, "job_id"=req\.ID)/t.logger.Errorf("Failed to get job: %v, job_id=%d", err, req.ID)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Infof("Job retrieved successfully"": "job_id"=job\.ID)/t.logger.Infof("Job retrieved successfully: job_id=%d", job.ID)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Error("Failed to update job", err, "job_id"=req\.ID)/t.logger.Errorf("Failed to update job: %v, job_id=%d", err, req.ID)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Infof("Job updated successfully"": "job_id"=result\.ID)/t.logger.Infof("Job updated successfully: job_id=%d", result.ID)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Errorf("Failed to list jobs"": %v", err)/t.logger.Errorf("Failed to list jobs: %v", err)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Infof("Jobs listed successfully"":/t.logger.Infof("Jobs listed successfully: count=%d, total=%d", len(jobs), total)/g' internal/mcp/tools/job.go
sed -i 's/"count"=len(jobs),//g' internal/mcp/tools/job.go
sed -i 's/"total"=total,//g' internal/mcp/tools/job.go

# Fix remaining patterns
sed -i 's/t\.logger\.Error("Failed to delete job", err, "job_id"=req\.ID)/t.logger.Errorf("Failed to delete job: %v, job_id=%d", err, req.ID)/g' internal/mcp/tools/job.go
sed -i 's/t\.logger\.Infof("Job deleted successfully"":/t.logger.Infof("Job deleted successfully: job_id=%d, title=%s", req.ID, job.Title)/g' internal/mcp/tools/job.go
sed -i 's/"job_id"=req\.ID,//g' internal/mcp/tools/job.go
sed -i 's/"title"=job\.Title,//g' internal/mcp/tools/job.go

sed -i 's/t\.logger\.Infof("Job status updated"":/t.logger.Infof("Job status updated: job_id=%d, new_status=%s", result.ID, result.Status)/g' internal/mcp/tools/job.go
sed -i 's/"job_id"=result\.ID,//g' internal/mcp/tools/job.go
sed -i 's/"new_status"=result\.Status,//g' internal/mcp/tools/job.go

sed -i 's/t\.logger\.Infof("Technician assignment requested"":/t.logger.Infof("Technician assignment requested: job_id=%d, technician_id=%s", req.ID, req.TechnicianID)/g' internal/mcp/tools/job.go
sed -i 's/"job_id"=req\.ID,//g' internal/mcp/tools/job.go
sed -i 's/"technician_id"=req\.TechnicianID,//g' internal/mcp/tools/job.go

sed -i 's/t\.logger\.Infof("Job scheduled"":/t.logger.Infof("Job scheduled: job_id=%d, scheduled_at=%s", result.ID, result.ScheduledAt.String())/g' internal/mcp/tools/job.go
sed -i 's/"job_id"=result\.ID,//g' internal/mcp/tools/job.go
sed -i 's/"scheduled_at"=result\.ScheduledAt,//g' internal/mcp/tools/job.go

sed -i 's/t\.logger\.Infof("Job completed"": "job_id"=result\.ID)/t.logger.Infof("Job completed: job_id=%d", result.ID)/g' internal/mcp/tools/job.go

echo "✅ job.go syntax errors fixed!"
