// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/server"
	"gobackend-hvac-kratos/internal/service"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, ai *conf.AI, email *conf.Email, mcp *conf.MCP, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	customerRepo := data.NewCustomerRepo(dataData, logger)
	customerUsecase := biz.NewCustomerUsecase(customerRepo, logger)
	jobRepo := data.NewJobRepo(dataData, logger)
	jobUsecase := biz.NewJobUsecase(jobRepo, logger)
	hvacService := service.NewHVACService(customerUsecase, jobUsecase, logger)
	aiRepo := data.NewAIRepo(dataData, ai, logger)
	aiUsecase := biz.NewAIUsecase(aiRepo, logger)
	aiService := service.NewAIService(aiUsecase, logger)
	analyticsRepo := data.NewAnalyticsRepo(dataData, logger)
	analyticsUsecase := biz.NewAnalyticsUsecase(analyticsRepo, logger)
	analyticsService := service.NewAnalyticsService(analyticsUsecase, logger)
	workflowRepo := data.NewWorkflowRepo(dataData, logger)
	workflowUsecase := biz.NewWorkflowUsecase(workflowRepo, logger)
	workflowService := service.NewWorkflowService(workflowUsecase, logger)
	httpServer := server.NewHTTPServer(confServer, hvacService, aiService, analyticsService, workflowService, logger)
	grpcServer := server.NewGRPCServer(confServer, hvacService, aiService, analyticsService, workflowService, logger)
	app := newApp(logger, httpServer, grpcServer)
	return app, func() {
		cleanup()
	}, nil
}
