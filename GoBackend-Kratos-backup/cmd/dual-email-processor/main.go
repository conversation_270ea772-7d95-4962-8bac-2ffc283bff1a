package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/transcription"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = "dual-email-processor"
	// Version is the version of the compiled software.
	Version string = "v1.0.0"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs/config.yaml", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, coordinator *email.EmailCoordinator) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(),
	)
}

func main() {
	flag.Parse()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)

	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// Initialize components
	logger.Log(log.LevelInfo, "msg", "🚀 Initializing Dual Email Processor...")

	// Initialize data layer
	dataProvider, cleanup, err := data.NewData(bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// Initialize business layer
	emailUsecase := biz.NewEmailUsecase(data.NewEmailRepo(dataProvider, logger), logger)
	workflowUsecase := biz.NewWorkflowUsecase(data.NewWorkflowRepo(dataProvider, logger), logger)

	// Initialize AI services
	gemma3Service := ai.NewGemma3Service(bc.Ai, logger)

	// Initialize transcription service
	transcriptionParser := transcription.NewTranscriptionParser(
		gemma3Service,
		nil, // Enhanced Gemma service - can be nil for now
		nil, // Customer service - can be nil for now
		logger,
	)

	// Initialize email analysis service
	emailAnalysisService := email.NewEmailAnalysisService(
		nil, // Vector DB - can be nil for now
		nil, // LLM - can be nil for now
		gemma3Service,
		logger,
	)

	// Initialize email retrieval service
	emailRetrievalService := email.NewEmailRetrievalService(
		bc.Email,
		emailAnalysisService,
		nil, // Dashboard service - can be nil for now
		logger,
	)

	// Initialize email coordinator
	emailCoordinator := email.NewEmailCoordinator(
		emailAnalysisService,
		transcriptionParser,
		gemma3Service,
		emailUsecase,
		workflowUsecase,
		emailRetrievalService,
		logger,
	)

	// Initialize enhanced retrieval service
	enhancedRetrieval := email.NewEnhancedEmailRetrievalService(
		bc.Email,
		emailCoordinator,
		logger,
	)

	// Create app
	app := newApp(logger, emailCoordinator)

	// Start the application
	logger.Log(log.LevelInfo, "msg", "🎯 Starting Dual Email Processor...")

	// Connect to mailboxes
	if err := enhancedRetrieval.ConnectToMailboxes(ctx); err != nil {
		logger.Log(log.LevelError, "msg", "Failed to connect to mailboxes", "error", err)
		panic(err)
	}

	// Start email coordinator
	if err := emailCoordinator.Start(ctx); err != nil {
		logger.Log(log.LevelError, "msg", "Failed to start email coordinator", "error", err)
		panic(err)
	}

	// Setup graceful shutdown
	quitChan := make(chan os.Signal, 1)
	signal.Notify(quitChan, syscall.SIGTERM, syscall.SIGINT)

	go func() {
		<-quitChan
		logger.Log(log.LevelInfo, "msg", "🛑 Shutting down Dual Email Processor...")

		// Stop email coordinator
		if err := emailCoordinator.Stop(ctx); err != nil {
			logger.Log(log.LevelError, "msg", "Failed to stop email coordinator", "error", err)
		}

		// Close email connections
		if err := enhancedRetrieval.Close(); err != nil {
			logger.Log(log.LevelError, "msg", "Failed to close email connections", "error", err)
		}

		// Stop app
		if err := app.Stop(); err != nil {
			logger.Log(log.LevelError, "msg", "Failed to stop app", "error", err)
		}
	}()

	// Print status information
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				stats := emailCoordinator.GetStats()
				logger.Log(log.LevelInfo,
					"msg", "📊 Email Processing Stats",
					"customer_emails", stats.CustomerEmailsProcessed,
					"transcription_emails", stats.TranscriptionEmailsProcessed,
					"successful", stats.SuccessfulProcessing,
					"failed", stats.FailedProcessing,
					"audio_files", stats.AudioFilesTranscribed,
					"workflows_triggered", stats.WorkflowsTriggered,
					"last_processing", stats.LastProcessingTime.Format("15:04:05"),
				)
			case <-ctx.Done():
				return
			}
		}
	}()

	logger.Log(log.LevelInfo, "msg", "✅ Dual Email Processor started successfully")
	logger.Log(log.LevelInfo, "msg", "📧 Monitoring <EMAIL> for customer emails")
	logger.Log(log.LevelInfo, "msg", "🎵 Monitoring <EMAIL> for transcription attachments")

	// Start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
