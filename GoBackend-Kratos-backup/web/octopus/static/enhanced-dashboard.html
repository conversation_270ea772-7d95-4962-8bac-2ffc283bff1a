<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>🐙 Enhanced Morphic Octopus Interface | HVAC CRM Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background: #0f172a; color: #e2e8f0; }
        .service-status { @apply bg-gray-700 p-3 rounded-lg border border-gray-600; }
        .metric-card { @apply bg-gray-800 p-4 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors; }
        .alert-critical { @apply bg-red-900 border-red-700 text-red-100; }
        .alert-warning { @apply bg-yellow-900 border-yellow-700 text-yellow-100; }
        .alert-info { @apply bg-blue-900 border-blue-700 text-blue-100; }
        .status-healthy { @apply bg-green-500; }
        .status-degraded { @apply bg-yellow-500; }
        .status-unhealthy { @apply bg-red-500; }
    </style>
</head>
<body class="bg-slate-900 text-slate-100">
    <header class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold text-white">🐙 Enhanced Morphic Octopus Interface</h1>
                    <span class="bg-blue-600 text-xs px-2 py-1 rounded">v2.1.0</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-400">Last Updated: <span id="last-updated">Loading...</span></div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-green-400">Live</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- System Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">System Uptime</h3>
                <div class="text-2xl font-bold text-white"><span id="system-uptime">0h 0m</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">CPU</span>
                    <span id="cpu-usage" class="text-blue-400">0%</span>
                </div>
            </div>

            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">Customers</h3>
                <div class="text-2xl font-bold text-white"><span id="total-customers">0</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">New Today</span>
                    <span id="new-customers-today" class="text-green-400">0</span>
                </div>
            </div>

            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">Email Intel</h3>
                <div class="text-2xl font-bold text-white"><span id="total-emails">0</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Today</span>
                    <span id="emails-today" class="text-blue-400">0</span>
                </div>
            </div>

            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">AI Performance</h3>
                <div class="text-2xl font-bold text-white"><span id="ai-requests">0</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Success</span>
                    <span id="ai-success-rate" class="text-green-400">0%</span>
                </div>
            </div>

            <!-- NEW: LangChain Metrics Card -->
            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">🧠 LangChain</h3>
                <div class="text-2xl font-bold text-white"><span id="langchain-workflows">0</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Today</span>
                    <span id="langchain-workflows-today" class="text-blue-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Success</span>
                    <span id="langchain-success-rate" class="text-green-400">0%</span>
                </div>
            </div>

            <!-- NEW: Workflow Metrics Card -->
            <div class="metric-card">
                <h3 class="text-sm font-medium text-gray-400 mb-2">🕸️ Workflows</h3>
                <div class="text-2xl font-bold text-white"><span id="workflow-executions">0</span></div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Running</span>
                    <span id="workflow-running" class="text-yellow-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Success</span>
                    <span id="workflow-success-rate" class="text-green-400">0%</span>
                </div>
            </div>
        </div>

        <!-- NEW: LangChain & Workflow Details Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- LangChain Details -->
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 class="text-lg font-semibold text-white mb-4">🧠 LangChain Analytics</h3>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Chain Executions</div>
                        <div class="text-xl font-bold text-blue-400"><span id="langchain-executions">0</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Tokens Consumed</div>
                        <div class="text-xl font-bold text-purple-400"><span id="langchain-tokens">0</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Avg Time</div>
                        <div class="text-xl font-bold text-green-400"><span id="langchain-avg-time">0ms</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Cost Today</div>
                        <div class="text-xl font-bold text-yellow-400"><span id="langchain-cost">$0</span></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="text-sm text-gray-400 mb-2">Active Chains</div>
                    <div id="langchain-active-chains" class="flex flex-wrap gap-1">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>

            <!-- Workflow Details -->
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 class="text-lg font-semibold text-white mb-4">🕸️ Workflow Analytics</h3>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Completed</div>
                        <div class="text-xl font-bold text-green-400"><span id="workflow-completed">0</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Failed</div>
                        <div class="text-xl font-bold text-red-400"><span id="workflow-failed">0</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Avg Time</div>
                        <div class="text-xl font-bold text-blue-400"><span id="workflow-avg-time">0s</span></div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">Queued</div>
                        <div class="text-xl font-bold text-yellow-400"><span id="workflow-queued">0</span></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="text-sm text-gray-400 mb-2">Workflow Types</div>
                    <div id="workflow-types" class="flex flex-wrap gap-1">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>
        </div>

        <!-- System Performance Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 class="text-lg font-semibold text-white mb-4">System Performance</h3>
                <canvas id="system-performance-chart" width="400" height="200"></canvas>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 class="text-lg font-semibold text-white mb-4">AI & Workflow Trends</h3>
                <canvas id="ai-workflow-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Service Health -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
            <h3 class="text-lg font-semibold text-white mb-4">Service Health</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div id="email-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">Email Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
                <div id="transcription-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">Transcription Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
                <div id="customer-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">Customer Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
                <div id="ai-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">AI Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
                <div id="langchain-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">LangChain Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
                <div id="workflow-service-status" class="service-status">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span class="text-sm">Workflow Service</span>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Real-time Alerts -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
            <h3 class="text-lg font-semibold text-white mb-4">Real-time Alerts</h3>
            <div id="alerts-container" class="space-y-2">
                <div class="text-gray-400 text-sm">No alerts at this time</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                    📊 Analytics
                </button>
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                    👥 Customers
                </button>
                <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm">
                    📧 Emails
                </button>
                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm">
                    🤖 AI Status
                </button>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded text-sm">
                    🧠 LangChain
                </button>
                <button class="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded text-sm">
                    🕸️ Workflows
                </button>
            </div>
        </div>
    </main>

    <footer class="bg-gray-800 border-t border-gray-700 mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="text-gray-400 text-sm">🐙 Enhanced Morphic Octopus Interface v2.1.0 | HVAC CRM Dashboard</div>
                <div class="text-gray-400 text-sm">Powered by Go + LangChain + Workflows + WebSocket</div>
            </div>
        </div>
    </footer>

    <script src="/js/octopus.js"></script>
    <script src="/js/websocket.js"></script>
    <script src="/js/charts.js"></script>
</body>
</html>
