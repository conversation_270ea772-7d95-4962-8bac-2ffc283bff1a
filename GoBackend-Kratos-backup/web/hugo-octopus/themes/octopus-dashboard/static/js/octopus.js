// 🐙 Morphic Octopus Interface - Main JavaScript
class OctopusInterface {
    constructor() {
        this.config = window.OCTOPUS_CONFIG || {};
        this.isRealtimeEnabled = true;
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        console.log('🐙 Initializing Morphic Octopus Interface...');
        this.setupEventListeners();
        this.startPeriodicUpdates();
        this.loadInitialData();
    }
    
    setupEventListeners() {
        // Quick action buttons
        this.setupQuickActions();
        
        // Real-time toggle
        const realtimeToggle = document.getElementById('realtime-toggle');
        if (realtimeToggle) {
            realtimeToggle.addEventListener('click', () => {
                this.toggleRealtime();
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }
    
    setupQuickActions() {
        const quickActionButtons = document.querySelectorAll('[class*="bg-"][class*="hover:bg-"]');
        quickActionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleQuickAction(e.target);
            });
        });
    }
    
    handleQuickAction(button) {
        const text = button.textContent.trim();
        
        if (text.includes('View Analytics')) {
            this.showAnalytics();
        } else if (text.includes('Add Customer')) {
            this.showAddCustomer();
        } else if (text.includes('Check Emails')) {
            this.showEmailIntelligence();
        } else if (text.includes('AI Status')) {
            this.showAIStatus();
        }
        
        // Visual feedback
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }
    
    showAnalytics() {
        console.log('📊 Opening Analytics Dashboard...');
        // Implementation for analytics view
        this.showNotification('Analytics dashboard opened', 'info');
    }
    
    showAddCustomer() {
        console.log('👥 Opening Add Customer Dialog...');
        // Implementation for add customer
        this.showNotification('Add customer dialog opened', 'info');
    }
    
    showEmailIntelligence() {
        console.log('📧 Opening Email Intelligence...');
        // Implementation for email intelligence
        this.showNotification('Email intelligence opened', 'info');
    }
    
    showAIStatus() {
        console.log('🤖 Opening AI Status...');
        // Implementation for AI status
        this.showNotification('AI status opened', 'info');
    }
    
    toggleRealtime() {
        this.isRealtimeEnabled = !this.isRealtimeEnabled;
        const toggle = document.getElementById('realtime-toggle');
        
        if (this.isRealtimeEnabled) {
            toggle.textContent = 'Real-time: ON';
            toggle.className = 'bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm';
            this.startPeriodicUpdates();
            console.log('✅ Real-time updates enabled');
        } else {
            toggle.textContent = 'Real-time: OFF';
            toggle.className = 'bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm';
            this.stopPeriodicUpdates();
            console.log('⏸️ Real-time updates disabled');
        }
        
        this.showNotification(
            `Real-time updates ${this.isRealtimeEnabled ? 'enabled' : 'disabled'}`,
            'info'
        );
    }
    
    startPeriodicUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.isRealtimeEnabled) {
            this.refreshInterval = setInterval(() => {
                this.fetchDashboardData();
            }, this.config.refreshInterval || 5000);
        }
    }
    
    stopPeriodicUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    async loadInitialData() {
        console.log('📊 Loading initial dashboard data...');
        await this.fetchDashboardData();
    }
    
    async fetchDashboardData() {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/dashboard/data`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.updateDashboard(data);
            
        } catch (error) {
            console.error('❌ Error fetching dashboard data:', error);
            this.showNotification('Failed to fetch dashboard data', 'error');
        }
    }
    
    updateDashboard(data) {
        // This method is called when we receive data via HTTP API
        // WebSocket updates are handled separately in websocket.js
        console.log('📊 Updating dashboard with HTTP data:', data);
        
        if (data.system_status) {
            this.updateSystemStatus(data.system_status);
        }
        
        if (data.customer_metrics) {
            this.updateCustomerMetrics(data.customer_metrics);
        }
        
        // Update timestamp
        this.updateLastUpdated();
    }
    
    updateSystemStatus(status) {
        // Fallback updates if WebSocket is not available
        if (!window.octopusWS || !window.octopusWS.isConnected) {
            const uptimeElement = document.getElementById('system-uptime');
            if (uptimeElement && status.uptime) {
                uptimeElement.textContent = this.formatUptime(status.uptime);
            }
        }
    }
    
    updateCustomerMetrics(metrics) {
        // Fallback updates if WebSocket is not available
        if (!window.octopusWS || !window.octopusWS.isConnected) {
            this.updateElement('total-customers', metrics.total_customers);
            this.updateElement('new-customers-today', metrics.new_today);
        }
    }
    
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }
    
    updateLastUpdated() {
        const element = document.getElementById('last-updated');
        if (element) {
            element.textContent = new Date().toLocaleTimeString();
        }
    }
    
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + R: Toggle real-time
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.toggleRealtime();
        }
        
        // Ctrl/Cmd + D: Refresh dashboard
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            this.fetchDashboardData();
            this.showNotification('Dashboard refreshed', 'info');
        }
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, reduce update frequency
            this.stopPeriodicUpdates();
        } else {
            // Page is visible, resume normal updates
            if (this.isRealtimeEnabled) {
                this.startPeriodicUpdates();
                this.fetchDashboardData(); // Immediate update
            }
        }
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getNotificationClasses(type)}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    getNotificationClasses(type) {
        const baseClasses = 'transform translate-x-full opacity-0 transition-all duration-300';
        
        switch (type) {
            case 'success':
                return `${baseClasses} bg-green-600 text-white`;
            case 'error':
                return `${baseClasses} bg-red-600 text-white`;
            case 'warning':
                return `${baseClasses} bg-yellow-600 text-white`;
            default:
                return `${baseClasses} bg-blue-600 text-white`;
        }
    }
    
    formatUptime(uptimeNs) {
        const seconds = Math.floor(uptimeNs / 1000000000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }
    
    // Public API
    refresh() {
        this.fetchDashboardData();
    }
    
    destroy() {
        this.stopPeriodicUpdates();
    }
}

// Initialize interface when page loads
let octopusInterface;
document.addEventListener('DOMContentLoaded', () => {
    octopusInterface = new OctopusInterface();
    console.log('🐙 Morphic Octopus Interface initialized successfully!');
});

// Export for global access
window.OctopusInterface = OctopusInterface;