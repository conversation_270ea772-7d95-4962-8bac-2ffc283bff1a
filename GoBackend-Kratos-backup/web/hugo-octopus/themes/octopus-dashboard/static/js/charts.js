// 🐙 Morphic Octopus Charts - Real-time Data Visualization
class OctopusCharts {
    constructor() {
        this.charts = {};
        this.chartData = {
            systemPerformance: {
                labels: [],
                cpu: [],
                memory: []
            },
            customerActivity: {
                labels: [],
                newCustomers: [],
                activeCustomers: []
            }
        };
        this.maxDataPoints = 20;
        
        this.init();
    }
    
    init() {
        this.createSystemPerformanceChart();
        this.createCustomerActivityChart();
        this.setupDataUpdates();
    }
    
    createSystemPerformanceChart() {
        const ctx = document.getElementById('system-performance-chart');
        if (!ctx) return;
        
        this.charts.systemPerformance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.chartData.systemPerformance.labels,
                datasets: [
                    {
                        label: 'CPU Usage (%)',
                        data: this.chartData.systemPerformance.cpu,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Memory Usage (%)',
                        data: this.chartData.systemPerformance.memory,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#d1d5db'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    }
                },
                animation: {
                    duration: 750,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
    
    createCustomerActivityChart() {
        const ctx = document.getElementById('customer-activity-chart');
        if (!ctx) return;
        
        this.charts.customerActivity = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.chartData.customerActivity.labels,
                datasets: [
                    {
                        label: 'New Customers',
                        data: this.chartData.customerActivity.newCustomers,
                        backgroundColor: '#8b5cf6',
                        borderColor: '#7c3aed',
                        borderWidth: 1
                    },
                    {
                        label: 'Active Customers',
                        data: this.chartData.customerActivity.activeCustomers,
                        backgroundColor: '#f59e0b',
                        borderColor: '#d97706',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#d1d5db'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    }
                },
                animation: {
                    duration: 750,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
    
    updateSystemPerformanceChart(cpuUsage, memoryUsage) {
        const now = new Date().toLocaleTimeString();
        
        // Add new data point
        this.chartData.systemPerformance.labels.push(now);
        this.chartData.systemPerformance.cpu.push(cpuUsage);
        this.chartData.systemPerformance.memory.push(memoryUsage);
        
        // Remove old data points if we exceed max
        if (this.chartData.systemPerformance.labels.length > this.maxDataPoints) {
            this.chartData.systemPerformance.labels.shift();
            this.chartData.systemPerformance.cpu.shift();
            this.chartData.systemPerformance.memory.shift();
        }
        
        // Update chart
        if (this.charts.systemPerformance) {
            this.charts.systemPerformance.update('none');
        }
    }
    
    updateCustomerActivityChart(newCustomers, activeCustomers) {
        const now = new Date().toLocaleTimeString();
        
        // Add new data point
        this.chartData.customerActivity.labels.push(now);
        this.chartData.customerActivity.newCustomers.push(newCustomers);
        this.chartData.customerActivity.activeCustomers.push(activeCustomers);
        
        // Remove old data points if we exceed max
        if (this.chartData.customerActivity.labels.length > this.maxDataPoints) {
            this.chartData.customerActivity.labels.shift();
            this.chartData.customerActivity.newCustomers.shift();
            this.chartData.customerActivity.activeCustomers.shift();
        }
        
        // Update chart
        if (this.charts.customerActivity) {
            this.charts.customerActivity.update('none');
        }
    }
    
    setupDataUpdates() {
        // Listen for WebSocket data updates
        if (window.octopusWS) {
            window.octopusWS.onMessage('system_status', (data) => {
                this.updateSystemPerformanceChart(data.cpu_usage, data.memory_usage);
            });
            
            window.octopusWS.onMessage('customer_metrics', (data) => {
                this.updateCustomerActivityChart(data.new_today, data.active_customers);
            });
        }
        
        // Fallback: Update charts with mock data every 5 seconds for demo
        setInterval(() => {
            if (!window.octopusWS || !window.octopusWS.isConnected) {
                this.updateWithMockData();
            }
        }, 5000);
    }
    
    updateWithMockData() {
        // Generate mock system performance data
        const mockCPU = Math.random() * 80 + 10;
        const mockMemory = Math.random() * 70 + 20;
        this.updateSystemPerformanceChart(mockCPU, mockMemory);
        
        // Generate mock customer activity data
        const mockNewCustomers = Math.floor(Math.random() * 10);
        const mockActiveCustomers = Math.floor(Math.random() * 50) + 20;
        this.updateCustomerActivityChart(mockNewCustomers, mockActiveCustomers);
    }
    
    // Utility method to resize charts
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }
    
    // Destroy all charts
    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Initialize charts when page loads
let octopusCharts;
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for WebSocket to initialize
    setTimeout(() => {
        octopusCharts = new OctopusCharts();
    }, 1000);
});

// Handle window resize
window.addEventListener('resize', () => {
    if (octopusCharts) {
        octopusCharts.resizeCharts();
    }
});

// Export for global access
window.OctopusCharts = OctopusCharts;