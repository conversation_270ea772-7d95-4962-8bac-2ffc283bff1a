<!DOCTYPE html>
<html lang="{{ .Site.LanguageCode | default "en" }}" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }} | {{ .Site.Title }}{{ end }}</title>
    <meta name="description" content="{{ .Site.Params.description }}">
    <meta name="author" content="{{ .Site.Params.author }}">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐙</text></svg>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ "css/octopus.css" | relURL }}">
    
    <!-- WebSocket Configuration -->
    <script>
        window.OCTOPUS_CONFIG = {
            websocketUrl: "{{ .Site.Params.dashboard.websocket_url }}",
            apiBaseUrl: "{{ .Site.Params.dashboard.api_base_url }}",
            refreshInterval: {{ .Site.Params.dashboard.refresh_interval }},
            theme: "{{ .Site.Params.dashboard.theme }}",
            realtimeEnabled: {{ .Site.Params.realtime.enabled }},
            autoRefresh: {{ .Site.Params.realtime.auto_refresh }}
        };
    </script>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">🐙</span>
                        <span class="ml-2 text-xl font-bold text-blue-400">Morphic Octopus</span>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="/" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                            <a href="/customers" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Customers</a>
                            <a href="/emails" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Email Intelligence</a>
                            <a href="/transcriptions" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Transcriptions</a>
                            <a href="/ai" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">AI Performance</a>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Connection Status -->
                    <div id="connection-status" class="flex items-center space-x-2">
                        <div id="status-indicator" class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span id="status-text" class="text-sm text-gray-400">Connecting...</span>
                    </div>
                    <!-- Real-time Toggle -->
                    <button id="realtime-toggle" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Real-time: ON
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {{ block "main" . }}{{ end }}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700 mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="text-gray-400 text-sm">
                    🐙 Morphic Octopus Interface v{{ .Site.Params.version }} | HVAC CRM Dashboard
                </div>
                <div class="text-gray-400 text-sm">
                    Powered by Hugo + Go + WebSocket
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ "js/octopus.js" | relURL }}"></script>
    <script src="{{ "js/websocket.js" | relURL }}"></script>
    <script src="{{ "js/charts.js" | relURL }}"></script>
</body>
</html>