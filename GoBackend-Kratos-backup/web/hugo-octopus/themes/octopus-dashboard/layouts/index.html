{{ define "main" }}
<div class="px-4 py-6">
    <!-- Dashboard Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">🐙 Morphic Octopus Dashboard</h1>
        <p class="mt-2 text-gray-400">Real-time HVAC CRM Management & Analytics</p>
        <div class="mt-4 flex items-center space-x-4">
            <div class="text-sm text-gray-400">
                Last Updated: <span id="last-updated" class="text-blue-400">Loading...</span>
            </div>
            <div class="text-sm text-gray-400">
                Active Connections: <span id="active-connections" class="text-green-400">0</span>
            </div>
        </div>
    </div>

    <!-- System Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- System Status -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">System Status</p>
                    <p id="system-uptime" class="text-2xl font-bold text-green-400">Loading...</p>
                </div>
                <div class="text-3xl">🖥️</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">CPU Usage</span>
                    <span id="cpu-usage" class="text-blue-400">0%</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Memory</span>
                    <span id="memory-usage" class="text-blue-400">0%</span>
                </div>
            </div>
        </div>

        <!-- Customer Metrics -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">Total Customers</p>
                    <p id="total-customers" class="text-2xl font-bold text-blue-400">Loading...</p>
                </div>
                <div class="text-3xl">👥</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">New Today</span>
                    <span id="new-customers-today" class="text-green-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">At Risk</span>
                    <span id="at-risk-customers" class="text-red-400">0</span>
                </div>
            </div>
        </div>

        <!-- Email Intelligence -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">Total Emails</p>
                    <p id="total-emails" class="text-2xl font-bold text-purple-400">Loading...</p>
                </div>
                <div class="text-3xl">📧</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Today</span>
                    <span id="emails-today" class="text-green-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Processed</span>
                    <span id="emails-processed" class="text-blue-400">0</span>
                </div>
            </div>
        </div>

        <!-- AI Performance -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">AI Requests</p>
                    <p id="ai-requests" class="text-2xl font-bold text-yellow-400">Loading...</p>
                </div>
                <div class="text-3xl">🤖</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Success Rate</span>
                    <span id="ai-success-rate" class="text-green-400">0%</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Avg Response</span>
                    <span id="ai-response-time" class="text-blue-400">0ms</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- System Performance Chart -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">System Performance</h3>
            <canvas id="system-performance-chart" width="400" height="200"></canvas>
        </div>

        <!-- Customer Activity Chart -->
        <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">Customer Activity</h3>
            <canvas id="customer-activity-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Service Health -->
    <div class="bg-gray-900 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Service Health</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div id="email-service-status" class="service-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Email Service</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
            <div id="transcription-service-status" class="service-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Transcription Service</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
            <div id="customer-service-status" class="service-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Customer Service</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
            <div id="ai-service-status" class="service-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">AI Service</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
        </div>
    </div>

    <!-- Real-time Alerts -->
    <div class="bg-gray-900 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Real-time Alerts</h3>
        <div id="alerts-container" class="space-y-2">
            <div class="text-gray-400 text-sm">No alerts at this time</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                📊 View Analytics
            </button>
            <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                👥 Add Customer
            </button>
            <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm">
                📧 Check Emails
            </button>
            <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm">
                🤖 AI Status
            </button>
        </div>
    </div>
</div>
{{ end }}