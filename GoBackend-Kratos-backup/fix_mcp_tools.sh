#!/bin/bash

# 🔧 Fix MCP Tools Logger Issues
# This script fixes all logger-related issues in MCP tools

echo "🔧 Fixing MCP Tools Logger Issues..."

# Directory containing tools
TOOLS_DIR="internal/mcp/tools"

# List of tool files to fix
TOOL_FILES=(
    "analytics.go"
    "auth.go" 
    "customer.go"
    "email.go"
    "job.go"
    "system.go"
    "workflow.go"
)

# Function to fix a single file
fix_tool_file() {
    local file="$1"
    local filepath="$TOOLS_DIR/$file"
    
    if [ ! -f "$filepath" ]; then
        echo "❌ File not found: $filepath"
        return 1
    fi
    
    echo "🔄 Fixing $file..."
    
    # Create backup
    cp "$filepath" "$filepath.backup"
    
    # Fix imports - remove zap, add kratos log
    sed -i 's|"go.uber.org/zap"|"github.com/go-kratos/kratos/v2/log"|g' "$filepath"
    
    # Fix logger type in struct
    sed -i 's|\*zap\.Logger|\*log.Helper|g' "$filepath"
    
    # Fix constructor parameter
    sed -i 's|logger \*zap\.Logger|logger log.Logger|g' "$filepath"
    
    # Fix logger initialization in constructor
    sed -i 's|logger:      logger,|logger:      log.NewHelper(logger),|g' "$filepath"
    
    # Fix logger calls - remove zap.String, zap.Int64 etc.
    sed -i 's|t\.logger\.Info(\([^,]*\), zap\.[^)]*)|t.logger.Info(\1)|g' "$filepath"
    sed -i 's|t\.logger\.Error(\([^,]*\), zap\.[^)]*)|t.logger.Error(\1)|g' "$filepath"
    sed -i 's|t\.logger\.Warn(\([^,]*\), zap\.[^)]*)|t.logger.Warn(\1)|g' "$filepath"
    sed -i 's|t\.logger\.Debug(\([^,]*\), zap\.[^)]*)|t.logger.Debug(\1)|g' "$filepath"
    
    # Fix simple logger calls
    sed -i 's|t\.logger\.Info(|t.logger.Info(|g' "$filepath"
    sed -i 's|t\.logger\.Error(|t.logger.Error(|g' "$filepath"
    sed -i 's|t\.logger\.Warn(|t.logger.Warn(|g' "$filepath"
    sed -i 's|t\.logger\.Debug(|t.logger.Debug(|g' "$filepath"
    
    echo "✅ Fixed $file"
}

# Fix all tool files
for file in "${TOOL_FILES[@]}"; do
    fix_tool_file "$file"
done

echo "🎉 All MCP tools fixed!"
echo "📝 Backup files created with .backup extension"
echo ""
echo "🔍 Next steps:"
echo "1. Check if compilation works: go build ./internal/mcp/..."
echo "2. If issues remain, check individual files manually"
echo "3. Remove backup files when satisfied: rm internal/mcp/tools/*.backup"
