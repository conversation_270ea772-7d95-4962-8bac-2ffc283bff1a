# 🚀 HVAC CRM - Foundations Makefile
# Quick commands for building and testing foundations

.PHONY: help build test clean run-demo deps check lint format

# Default target
help:
	@echo "🚀 HVAC CRM - Foundations Build System"
	@echo ""
	@echo "Available commands:"
	@echo "  deps        - Download and install dependencies"
	@echo "  build       - Build all foundation components"
	@echo "  test        - Run all tests"
	@echo "  run-demo    - Run foundations demo server"
	@echo "  check       - Run code quality checks"
	@echo "  lint        - Run linter"
	@echo "  format      - Format code"
	@echo "  clean       - Clean build artifacts"
	@echo ""

# Download dependencies
deps:
	@echo "📦 Downloading dependencies..."
	go mod download
	go mod tidy
	@echo "✅ Dependencies ready!"

# Build all components
build: deps
	@echo "🔨 Building foundations..."
	go build -o bin/foundations-demo ./cmd/foundations-demo
	@echo "✅ Build complete!"

# Run tests
test: deps
	@echo "🧪 Running tests..."
	go test -v ./internal/foundations/...
	@echo "✅ Tests complete!"

# Run foundations demo
run-demo: build
	@echo "🚀 Starting foundations demo..."
	@echo "🌐 Server will be available at: http://localhost:8080"
	@echo "📊 Health check: http://localhost:8080/api/v1/public/health"
	@echo ""
	./bin/foundations-demo

# Run with custom config
run-demo-custom: build
	@echo "🚀 Starting foundations demo with custom config..."
	JWT_SECRET=hvac-super-secret-key \
	MINIO_ENDPOINT=**************:9000 \
	MINIO_ACCESS_KEY=koldbringer \
	MINIO_SECRET_KEY=Blaeritipol1 \
	./bin/foundations-demo

# Code quality checks
check: deps
	@echo "🔍 Running code quality checks..."
	go vet ./internal/foundations/...
	go fmt ./internal/foundations/...
	@echo "✅ Quality checks complete!"

# Run linter (requires golangci-lint)
lint:
	@echo "🔍 Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run ./internal/foundations/...; \
	else \
		echo "⚠️  golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# Format code
format:
	@echo "🎨 Formatting code..."
	go fmt ./internal/foundations/...
	@echo "✅ Code formatted!"

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf bin/
	go clean
	@echo "✅ Clean complete!"

# Quick test individual foundations
test-auth:
	@echo "🔐 Testing Auth Foundation..."
	go test -v ./internal/foundations/auth/...

test-web:
	@echo "🌐 Testing Web Foundation..."
	go test -v ./internal/foundations/web/...

test-storage:
	@echo "💾 Testing Storage Foundation..."
	go test -v ./internal/foundations/storage/...

test-documents:
	@echo "📄 Testing Documents Foundation..."
	go test -v ./internal/foundations/documents/...

test-workflow:
	@echo "⚡ Testing Workflow Foundation..."
	go test -v ./internal/foundations/workflow/...

test-monitoring:
	@echo "📊 Testing Monitoring Foundation..."
	go test -v ./internal/foundations/monitoring/...

test-email:
	@echo "📧 Testing Email Foundation..."
	go test -v ./internal/foundations/email/...

test-validation:
	@echo "✅ Testing Validation Foundation..."
	go test -v ./internal/foundations/validation/...

test-middleware:
	@echo "🔄 Testing Middleware Foundation..."
	go test -v ./internal/foundations/middleware/...

# Development helpers
dev-setup:
	@echo "🛠️  Setting up development environment..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/air-verse/air@latest
	@echo "✅ Development tools installed!"

# Live reload development (requires air)
dev-run:
	@echo "🔄 Starting live reload development..."
	@if command -v air >/dev/null 2>&1; then \
		air -c .air.toml; \
	else \
		echo "⚠️  air not installed. Install with: make dev-setup"; \
		echo "🔄 Falling back to regular run..."; \
		make run-demo; \
	fi

# Docker helpers
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t hvac-crm-foundations .

docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 8080:8080 hvac-crm-foundations

# Database helpers (if needed)
db-migrate:
	@echo "🗄️  Running database migrations..."
	# Add migration commands here

db-seed:
	@echo "🌱 Seeding database..."
	# Add seed commands here

# Generate documentation
docs:
	@echo "📚 Generating documentation..."
	go doc -all ./internal/foundations/... > docs/foundations-api.md
	@echo "✅ Documentation generated!"

# Security scan
security:
	@echo "🔒 Running security scan..."
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./internal/foundations/...; \
	else \
		echo "⚠️  gosec not installed. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Performance benchmark
benchmark:
	@echo "⚡ Running performance benchmarks..."
	go test -bench=. -benchmem ./internal/foundations/...

# Coverage report
coverage:
	@echo "📊 Generating coverage report..."
	go test -coverprofile=coverage.out ./internal/foundations/...
	go tool cover -html=coverage.out -o coverage.html
	@echo "✅ Coverage report generated: coverage.html"

# All-in-one quality check
quality: format lint test security
	@echo "🎯 All quality checks complete!"

# Production build
prod-build:
	@echo "🏭 Building for production..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o bin/foundations-demo ./cmd/foundations-demo
	@echo "✅ Production build complete!"
