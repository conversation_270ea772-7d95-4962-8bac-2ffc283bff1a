#!/bin/bash

# 🧪 GoBackend Kratos Test Script

set -e

echo "🧪 Testing GoBackend HVAC Kratos..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

# Run unit tests
print_status "Running unit tests..."
go test -v ./internal/...

# Run integration tests
print_status "Running integration tests..."
go test -v -tags=integration ./...

# Test build
print_status "Testing build..."
go build -o /tmp/test-server ./cmd/server

# Cleanup
rm -f /tmp/test-server

print_success "All tests passed! 🎉"