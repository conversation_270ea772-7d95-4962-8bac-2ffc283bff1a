#!/bin/bash

# 🔧 GoBackend-Kratos Analytics & Workflow Testing Script
# Comprehensive testing and optimization suite

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
DB_HOST="**************"
DB_USER="hvacdb"
DB_NAME="hvacdb"
TEST_RESULTS_DIR="./test_results"

echo -e "${CYAN}🚀 GoBackend-Kratos Analytics & Workflow Testing Suite${NC}"
echo -e "${CYAN}=================================================${NC}"

# Create test results directory
mkdir -p $TEST_RESULTS_DIR

# Function to log test results
log_test() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] $test_name: $status - $details" >> "$TEST_RESULTS_DIR/test_log.txt"
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $test_name: $status${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $test_name: $status${NC}"
        echo -e "${RED}   Details: $details${NC}"
    else
        echo -e "${YELLOW}⚠️  $test_name: $status${NC}"
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local expected_status="$3"
    local data="$4"
    local test_name="$5"
    
    echo -e "${BLUE}🔍 Testing: $test_name${NC}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o "$TEST_RESULTS_DIR/response.json" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -o "$TEST_RESULTS_DIR/response.json" \
                   -X "$method" \
                   -H "Content-Type: application/json" \
                   -d "$data" \
                   "$BASE_URL$endpoint")
    fi
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "$expected_status" ]; then
        log_test "$test_name" "PASS" "HTTP $http_code"
        return 0
    else
        log_test "$test_name" "FAIL" "Expected HTTP $expected_status, got $http_code"
        return 1
    fi
}

# Function to test database connectivity
test_database() {
    echo -e "${BLUE}🔍 Testing Database Connectivity${NC}"
    
    if command -v psql &> /dev/null; then
        if PGPASSWORD=blaeritipol psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
            log_test "Database Connectivity" "PASS" "Connected to PostgreSQL"
            return 0
        else
            log_test "Database Connectivity" "FAIL" "Cannot connect to PostgreSQL"
            return 1
        fi
    else
        log_test "Database Connectivity" "SKIP" "psql not available"
        return 1
    fi
}

# Function to test analytics tables
test_analytics_tables() {
    echo -e "${BLUE}🔍 Testing Analytics Tables${NC}"
    
    tables=(
        "business_metrics"
        "dashboard_widgets" 
        "kpi_tracking"
        "customer_analytics"
        "revenue_analytics"
        "operational_analytics"
        "performance_analytics"
    )
    
    for table in "${tables[@]}"; do
        if PGPASSWORD=blaeritipol psql -h $DB_HOST -U $DB_USER -d $DB_NAME \
           -c "SELECT COUNT(*) FROM $table;" &> /dev/null; then
            log_test "Table: $table" "PASS" "Table exists and accessible"
        else
            log_test "Table: $table" "FAIL" "Table missing or inaccessible"
        fi
    done
}

# Function to test workflow tables
test_workflow_tables() {
    echo -e "${BLUE}🔍 Testing Workflow Tables${NC}"
    
    tables=(
        "workflow_rules"
        "workflow_executions"
        "workflow_templates"
    )
    
    for table in "${tables[@]}"; do
        if PGPASSWORD=blaeritipol psql -h $DB_HOST -U $DB_USER -d $DB_NAME \
           -c "SELECT COUNT(*) FROM $table;" &> /dev/null; then
            log_test "Table: $table" "PASS" "Table exists and accessible"
        else
            log_test "Table: $table" "FAIL" "Table missing or inaccessible"
        fi
    done
}

# Function to run performance tests
test_performance() {
    echo -e "${BLUE}🔍 Running Performance Tests${NC}"
    
    # Test response times
    endpoints=(
        "/api/v1/analytics/health"
        "/api/v1/workflow/health"
        "/api/v1/analytics/dashboard/executive"
        "/api/v1/analytics/metrics/realtime"
    )
    
    for endpoint in "${endpoints[@]}"; do
        echo -e "${YELLOW}⏱️  Testing response time: $endpoint${NC}"
        
        start_time=$(date +%s%N)
        curl -s "$BASE_URL$endpoint" > /dev/null
        end_time=$(date +%s%N)
        
        duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
        
        if [ $duration -lt 1000 ]; then
            log_test "Performance: $endpoint" "PASS" "${duration}ms response time"
        elif [ $duration -lt 2000 ]; then
            log_test "Performance: $endpoint" "WARN" "${duration}ms response time (acceptable)"
        else
            log_test "Performance: $endpoint" "FAIL" "${duration}ms response time (too slow)"
        fi
    done
}

# Function to test analytics endpoints
test_analytics_endpoints() {
    echo -e "${PURPLE}📊 Testing Analytics Endpoints${NC}"
    
    # Health check
    test_endpoint "GET" "/api/v1/analytics/health" "200" "" "Analytics Health Check"
    
    # Executive dashboard
    test_endpoint "GET" "/api/v1/analytics/dashboard/executive" "200" "" "Executive Dashboard"
    
    # Customer insights
    test_endpoint "GET" "/api/v1/analytics/dashboard/customer" "200" "" "Customer Insights Dashboard"
    
    # Operational dashboard
    test_endpoint "GET" "/api/v1/analytics/dashboard/operations" "200" "" "Operational Dashboard"
    
    # Performance trends
    test_endpoint "GET" "/api/v1/analytics/trends/performance?weeks=4" "200" "" "Performance Trends"
    
    # KPIs
    test_endpoint "GET" "/api/v1/analytics/kpis" "200" "" "Get KPIs"
    
    # Real-time metrics
    test_endpoint "GET" "/api/v1/analytics/metrics/realtime" "200" "" "Real-time Metrics"
    
    # Dashboard widgets
    test_endpoint "GET" "/api/v1/analytics/widgets?dashboard_category=executive" "200" "" "Dashboard Widgets"
    
    # Update KPI
    kpi_data='{
        "kpi_name": "Test KPI",
        "category": "test",
        "value": 85.5,
        "target": 90.0
    }'
    test_endpoint "POST" "/api/v1/analytics/kpis" "200" "$kpi_data" "Update KPI"
}

# Function to test workflow endpoints
test_workflow_endpoints() {
    echo -e "${PURPLE}⚡ Testing Workflow Endpoints${NC}"
    
    # Health check
    test_endpoint "GET" "/api/v1/workflow/health" "200" "" "Workflow Health Check"
    
    # Get workflow rules
    test_endpoint "GET" "/api/v1/workflow/rules" "200" "" "Get Workflow Rules"
    
    # Get workflow templates
    test_endpoint "GET" "/api/v1/workflow/templates" "200" "" "Get Workflow Templates"
    
    # Create workflow rule
    rule_data='{
        "rule_name": "Test Emergency Rule",
        "description": "Test rule for emergency scenarios",
        "trigger_type": "email",
        "trigger_conditions": [
            {
                "field": "subject",
                "operator": "contains",
                "value": "emergency"
            }
        ],
        "actions": [
            {
                "type": "notification",
                "parameters": {
                    "message": "Emergency detected",
                    "recipients": ["<EMAIL>"]
                }
            }
        ],
        "priority": 8,
        "is_active": true,
        "created_by": "test_script"
    }'
    test_endpoint "POST" "/api/v1/workflow/rules" "200" "$rule_data" "Create Workflow Rule"
    
    # Execute workflow
    execute_data='{
        "trigger_type": "email",
        "entity_id": 123,
        "entity_data": {
            "subject": "EMERGENCY: System failure",
            "from": "<EMAIL>",
            "urgency_score": 0.9
        }
    }'
    test_endpoint "POST" "/api/v1/workflow/execute" "200" "$execute_data" "Execute Workflow"
    
    # Get workflow executions
    test_endpoint "GET" "/api/v1/workflow/executions" "200" "" "Get Workflow Executions"
}

# Function to test data integrity
test_data_integrity() {
    echo -e "${BLUE}🔍 Testing Data Integrity${NC}"
    
    # Test foreign key constraints
    if PGPASSWORD=blaeritipol psql -h $DB_HOST -U $DB_USER -d $DB_NAME \
       -c "SELECT COUNT(*) FROM customer_analytics ca LEFT JOIN customers c ON ca.customer_id = c.id WHERE c.id IS NULL;" \
       | grep -q "0"; then
        log_test "Data Integrity: Customer Analytics FK" "PASS" "No orphaned records"
    else
        log_test "Data Integrity: Customer Analytics FK" "FAIL" "Orphaned records found"
    fi
    
    # Test data consistency
    if PGPASSWORD=blaeritipol psql -h $DB_HOST -U $DB_USER -d $DB_NAME \
       -c "SELECT COUNT(*) FROM kpi_tracking WHERE current_value < 0;" \
       | grep -q "0"; then
        log_test "Data Integrity: KPI Values" "PASS" "No negative KPI values"
    else
        log_test "Data Integrity: KPI Values" "WARN" "Negative KPI values found"
    fi
}

# Function to test AI integration
test_ai_integration() {
    echo -e "${BLUE}🔍 Testing AI Integration${NC}"
    
    # Test LM Studio connectivity
    if curl -s "http://localhost:1234/v1/models" > /dev/null; then
        log_test "AI Integration: LM Studio" "PASS" "LM Studio accessible"
    else
        log_test "AI Integration: LM Studio" "FAIL" "LM Studio not accessible"
    fi
    
    # Test AI workflow action
    ai_workflow_data='{
        "trigger_type": "test",
        "entity_id": 1,
        "entity_data": {
            "content": "Test content for AI analysis"
        }
    }'
    
    # This would test an AI-enabled workflow if one exists
    echo -e "${YELLOW}⚠️  AI workflow testing requires specific AI-enabled rules${NC}"
}

# Function to generate test report
generate_report() {
    echo -e "${CYAN}📋 Generating Test Report${NC}"
    
    report_file="$TEST_RESULTS_DIR/test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 🔧 GoBackend-Kratos Test Report

**Generated:** $(date '+%Y-%m-%d %H:%M:%S')
**Test Suite:** Analytics & Workflow Enhancement

## 📊 Test Summary

EOF
    
    # Count test results
    total_tests=$(grep -c ":" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    passed_tests=$(grep -c "PASS" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    failed_tests=$(grep -c "FAIL" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    warned_tests=$(grep -c "WARN" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    
    cat >> "$report_file" << EOF
- **Total Tests:** $total_tests
- **Passed:** $passed_tests ✅
- **Failed:** $failed_tests ❌
- **Warnings:** $warned_tests ⚠️

## 📋 Detailed Results

\`\`\`
$(cat "$TEST_RESULTS_DIR/test_log.txt")
\`\`\`

## 🎯 Recommendations

EOF
    
    if [ $failed_tests -gt 0 ]; then
        echo "- ❌ **Critical Issues Found:** $failed_tests tests failed. Immediate attention required." >> "$report_file"
    fi
    
    if [ $warned_tests -gt 0 ]; then
        echo "- ⚠️ **Performance Issues:** $warned_tests tests showed warnings. Consider optimization." >> "$report_file"
    fi
    
    if [ $failed_tests -eq 0 ] && [ $warned_tests -eq 0 ]; then
        echo "- ✅ **All Tests Passed:** System is performing optimally." >> "$report_file"
    fi
    
    echo -e "${GREEN}📋 Test report generated: $report_file${NC}"
}

# Main execution
main() {
    echo -e "${CYAN}🚀 Starting Comprehensive Test Suite${NC}"
    echo "Test started at: $(date)"
    
    # Initialize test log
    echo "# GoBackend-Kratos Test Log - $(date)" > "$TEST_RESULTS_DIR/test_log.txt"
    
    # Run all tests
    echo -e "\n${YELLOW}🔧 Phase 1: Infrastructure Tests${NC}"
    test_database
    test_analytics_tables
    test_workflow_tables
    
    echo -e "\n${YELLOW}📊 Phase 2: Analytics API Tests${NC}"
    test_analytics_endpoints
    
    echo -e "\n${YELLOW}⚡ Phase 3: Workflow API Tests${NC}"
    test_workflow_endpoints
    
    echo -e "\n${YELLOW}🔍 Phase 4: Data Integrity Tests${NC}"
    test_data_integrity
    
    echo -e "\n${YELLOW}⏱️  Phase 5: Performance Tests${NC}"
    test_performance
    
    echo -e "\n${YELLOW}🧠 Phase 6: AI Integration Tests${NC}"
    test_ai_integration
    
    echo -e "\n${YELLOW}📋 Phase 7: Report Generation${NC}"
    generate_report
    
    echo -e "\n${GREEN}🎉 Test Suite Completed!${NC}"
    echo "Results available in: $TEST_RESULTS_DIR"
    
    # Final summary
    total_tests=$(grep -c ":" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    passed_tests=$(grep -c "PASS" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    failed_tests=$(grep -c "FAIL" "$TEST_RESULTS_DIR/test_log.txt" || echo "0")
    
    echo -e "\n${CYAN}📊 Final Summary:${NC}"
    echo -e "Total Tests: $total_tests"
    echo -e "Passed: ${GREEN}$passed_tests${NC}"
    echo -e "Failed: ${RED}$failed_tests${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All critical tests passed! System is ready for production.${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some tests failed. Please review the results before proceeding.${NC}"
        exit 1
    fi
}

# Check if script is run with arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --quick        Run only basic health checks"
    echo "  --performance  Run only performance tests"
    echo "  --analytics    Run only analytics tests"
    echo "  --workflow     Run only workflow tests"
    exit 0
elif [ "$1" = "--quick" ]; then
    echo -e "${CYAN}🚀 Running Quick Health Checks${NC}"
    test_database
    test_endpoint "GET" "/api/v1/analytics/health" "200" "" "Analytics Health"
    test_endpoint "GET" "/api/v1/workflow/health" "200" "" "Workflow Health"
elif [ "$1" = "--performance" ]; then
    echo -e "${CYAN}⏱️  Running Performance Tests${NC}"
    test_performance
elif [ "$1" = "--analytics" ]; then
    echo -e "${CYAN}📊 Running Analytics Tests${NC}"
    test_analytics_endpoints
elif [ "$1" = "--workflow" ]; then
    echo -e "${CYAN}⚡ Running Workflow Tests${NC}"
    test_workflow_endpoints
else
    main
fi