#!/bin/bash

# 🧪 GoBackend-Kratos Comprehensive Testing Suite
# Automated testing for all system components including load testing and optimization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Function to print colored output
print_status() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_header "Running: $test_name"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command"; then
        print_status "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ $test_name")
    else
        print_error "$test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ $test_name")
    fi
    echo ""
}

# Check if required tools are installed
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Go
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed"
        exit 1
    fi
    print_info "Go version: $(go version)"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_info "Docker version: $(docker --version)"
    
    # Check if k6 is available for load testing
    if command -v k6 &> /dev/null; then
        print_info "k6 load testing tool available: $(k6 version)"
    else
        print_warning "k6 not installed - load tests will be skipped"
    fi
    
    print_status "Prerequisites check completed"
    echo ""
}

# Unit Tests
run_unit_tests() {
    print_header "Running Unit Tests"
    
    # Test analytics service
    run_test "Analytics Service Unit Tests" "go test -v ./internal/service/analytics_test.go ./internal/service/analytics.go"
    
    # Test workflow engine
    run_test "Workflow Engine Unit Tests" "go test -v ./internal/biz/workflow_test.go ./internal/biz/workflow.go"
    
    # Test AI service
    run_test "AI Service Unit Tests" "go test -v ./internal/ai/..."
    
    # Test email processing
    run_test "Email Processing Unit Tests" "go test -v ./internal/email/..."
    
    # Test customer service
    run_test "Customer Service Unit Tests" "go test -v ./internal/customer/..."
    
    # Test octopus interface
    run_test "Octopus Interface Unit Tests" "go test -v ./internal/octopus/..."
}

# Integration Tests
run_integration_tests() {
    print_header "Running Integration Tests"
    
    # Database integration tests
    run_test "Database Integration Tests" "go test -v ./tests/integration/database_test.go"
    
    # API integration tests
    run_test "API Integration Tests" "go test -v ./tests/integration/api_test.go"
    
    # WebSocket integration tests
    run_test "WebSocket Integration Tests" "go test -v ./tests/integration/websocket_test.go"
    
    # AI integration tests
    run_test "AI Integration Tests" "go test -v ./tests/integration/ai_test.go"
    
    # Email integration tests
    run_test "Email Integration Tests" "go test -v ./tests/integration/email_test.go"
}

# Performance Tests
run_performance_tests() {
    print_header "Running Performance Tests"
    
    # Go benchmark tests
    run_test "Go Benchmark Tests" "go test -bench=. -benchmem ./internal/..."
    
    # Memory usage tests
    run_test "Memory Usage Tests" "go test -v ./tests/performance/memory_test.go"
    
    # Response time tests
    run_test "Response Time Tests" "go test -v ./tests/performance/response_time_test.go"
}

# Load Tests (if k6 is available)
run_load_tests() {
    if ! command -v k6 &> /dev/null; then
        print_warning "Skipping load tests - k6 not installed"
        return
    fi
    
    print_header "Running Load Tests"
    
    # Ensure the system is running
    if ! curl -s http://localhost:8080/health > /dev/null; then
        print_warning "System not running - starting Docker containers"
        docker-compose up -d
        sleep 30
    fi
    
    # API load tests
    run_test "API Load Tests (100 VUs, 2min)" "k6 run --vus 100 --duration 2m tests/load/api_load_test.js"
    
    # Analytics dashboard load tests
    run_test "Analytics Load Tests (50 VUs, 1min)" "k6 run --vus 50 --duration 1m tests/load/analytics_load_test.js"
    
    # WebSocket load tests
    run_test "WebSocket Load Tests (30 VUs, 1min)" "k6 run --vus 30 --duration 1m tests/load/websocket_load_test.js"
    
    # AI service load tests
    run_test "AI Service Load Tests (10 VUs, 1min)" "k6 run --vus 10 --duration 1m tests/load/ai_load_test.js"
}

# Security Tests
run_security_tests() {
    print_header "Running Security Tests"
    
    # Authentication tests
    run_test "Authentication Security Tests" "go test -v ./tests/security/auth_test.go"
    
    # Input validation tests
    run_test "Input Validation Tests" "go test -v ./tests/security/validation_test.go"
    
    # SQL injection tests
    run_test "SQL Injection Tests" "go test -v ./tests/security/sql_injection_test.go"
    
    # Rate limiting tests
    run_test "Rate Limiting Tests" "go test -v ./tests/security/rate_limit_test.go"
}

# System Health Tests
run_health_tests() {
    print_header "Running System Health Tests"
    
    # Docker container health
    run_test "Docker Container Health" "docker-compose ps | grep -q 'Up'"
    
    # Database connectivity
    run_test "Database Connectivity" "go run tests/health/db_health_test.go"
    
    # API endpoints health
    run_test "API Endpoints Health" "curl -f http://localhost:8080/health"
    
    # WebSocket connectivity
    run_test "WebSocket Connectivity" "go run tests/health/websocket_health_test.go"
    
    # AI service health
    run_test "AI Service Health" "curl -f http://localhost:1234/v1/models"
}

# Code Quality Tests
run_code_quality_tests() {
    print_header "Running Code Quality Tests"
    
    # Go vet
    run_test "Go Vet Analysis" "go vet ./..."
    
    # Go fmt check
    run_test "Go Format Check" "test -z \$(gofmt -l .)"
    
    # Go mod tidy check
    run_test "Go Mod Tidy Check" "go mod tidy && git diff --exit-code go.mod go.sum"
    
    # Cyclomatic complexity (if gocyclo is available)
    if command -v gocyclo &> /dev/null; then
        run_test "Cyclomatic Complexity Check" "gocyclo -over 15 ."
    fi
    
    # Test coverage
    run_test "Test Coverage Analysis" "go test -coverprofile=coverage.out ./... && go tool cover -func=coverage.out"
}

# Create test directories and files if they don't exist
setup_test_structure() {
    print_header "Setting up test structure"
    
    # Create test directories
    mkdir -p tests/{integration,performance,security,health,load}
    
    # Create basic test files if they don't exist
    create_basic_test_files
    
    print_status "Test structure setup completed"
}

# Create basic test files
create_basic_test_files() {
    # Database integration test
    if [ ! -f "tests/integration/database_test.go" ]; then
        cat > tests/integration/database_test.go << 'EOF'
package integration

import (
    "testing"
    "context"
    "gobackend-hvac-kratos/internal/data"
)

func TestDatabaseConnection(t *testing.T) {
    // Test database connectivity
    db, err := data.NewData(nil, nil)
    if err != nil {
        t.Fatalf("Failed to connect to database: %v", err)
    }
    
    // Test basic query
    var count int64
    err = db.DB().WithContext(context.Background()).Raw("SELECT 1").Count(&count).Error
    if err != nil {
        t.Fatalf("Failed to execute test query: %v", err)
    }
    
    if count != 1 {
        t.Fatalf("Expected count 1, got %d", count)
    }
}
EOF
    fi
    
    # API integration test
    if [ ! -f "tests/integration/api_test.go" ]; then
        cat > tests/integration/api_test.go << 'EOF'
package integration

import (
    "testing"
    "net/http"
    "time"
)

func TestAPIHealth(t *testing.T) {
    client := &http.Client{Timeout: 10 * time.Second}
    
    resp, err := client.Get("http://localhost:8080/health")
    if err != nil {
        t.Fatalf("Failed to call health endpoint: %v", err)
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        t.Fatalf("Expected status 200, got %d", resp.StatusCode)
    }
}
EOF
    fi
    
    # Load test script
    if [ ! -f "tests/load/api_load_test.js" ]; then
        cat > tests/load/api_load_test.js << 'EOF'
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  vus: 100,
  duration: '2m',
};

export default function() {
  let response = http.get('http://localhost:8080/health');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
  sleep(1);
}
EOF
    fi
}

# Generate test report
generate_test_report() {
    print_header "Generating Test Report"
    
    local report_file="test_results/test_report_$(date +%Y%m%d_%H%M%S).md"
    mkdir -p test_results
    
    cat > "$report_file" << EOF
# 🧪 GoBackend-Kratos Test Report
*Generated: $(date)*

## 📊 Test Summary
- **Total Tests**: $TOTAL_TESTS
- **Passed**: $PASSED_TESTS
- **Failed**: $FAILED_TESTS
- **Success Rate**: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%

## 📋 Test Results
EOF
    
    for result in "${TEST_RESULTS[@]}"; do
        echo "- $result" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## 🎯 System Status
- **Overall Health**: $([ $FAILED_TESTS -eq 0 ] && echo "✅ HEALTHY" || echo "⚠️ ISSUES DETECTED")
- **Performance**: $([ $PASSED_TESTS -gt $(( TOTAL_TESTS * 80 / 100 )) ] && echo "✅ GOOD" || echo "⚠️ NEEDS ATTENTION")
- **Security**: $([ $FAILED_TESTS -eq 0 ] && echo "✅ SECURE" || echo "⚠️ VULNERABILITIES DETECTED")

## 📈 Recommendations
$([ $FAILED_TESTS -gt 0 ] && echo "- Review failed tests and fix issues" || echo "- All tests passing - system ready for production")
$([ $PASSED_TESTS -lt $(( TOTAL_TESTS * 90 / 100 )) ] && echo "- Improve test coverage" || echo "- Excellent test coverage")
- Continue monitoring system performance
- Regular security audits recommended

---
*Report generated by GoBackend-Kratos Testing Suite*
EOF
    
    print_status "Test report generated: $report_file"
}

# Main execution function
main() {
    echo "🧪 GoBackend-Kratos Comprehensive Testing Suite"
    echo "=============================================="
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Setup test structure
    setup_test_structure
    
    # Run all test suites
    run_unit_tests
    run_integration_tests
    run_performance_tests
    run_load_tests
    run_security_tests
    run_health_tests
    run_code_quality_tests
    
    # Generate report
    generate_test_report
    
    # Final summary
    echo ""
    echo "🎯 Testing Complete!"
    echo "==================="
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"
    echo "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_status "All tests passed! System is ready for production 🚀"
        exit 0
    else
        print_error "$FAILED_TESTS tests failed. Please review and fix issues."
        exit 1
    fi
}

# Run main function
main "$@"
