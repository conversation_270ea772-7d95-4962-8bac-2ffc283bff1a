/* 🐙 Morphic Octopus Interface - Custom Styles */

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Connection Status Animations */
#status-indicator {
    transition: all 0.3s ease;
}

#status-indicator.connected {
    background-color: #10b981; /* green-500 */
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

#status-indicator.connecting {
    background-color: #f59e0b; /* yellow-500 */
    animation: pulse 2s infinite;
}

#status-indicator.disconnected {
    background-color: #ef4444; /* red-500 */
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Card Hover Effects */
.bg-gray-800:hover {
    background-color: rgb(55, 65, 81); /* gray-700 */
    transition: background-color 0.2s ease;
}

/* Service Status Indicators */
.service-status .w-3.h-3 {
    transition: all 0.3s ease;
}

.service-status.healthy .w-3.h-3 {
    background-color: #10b981; /* green-500 */
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.service-status.warning .w-3.h-3 {
    background-color: #f59e0b; /* yellow-500 */
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.service-status.error .w-3.h-3 {
    background-color: #ef4444; /* red-500 */
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

/* Alert Styles */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 4px solid;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.alert.critical {
    background-color: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
    color: #fca5a5;
}

.alert.warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
    color: #fcd34d;
}

.alert.info {
    background-color: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
    color: #93c5fd;
}

/* Chart Container Styles */
canvas {
    max-width: 100%;
    height: auto;
}

/* Button Hover Effects */
button {
    transition: all 0.2s ease;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Real-time Data Animation */
.data-update {
    animation: dataFlash 0.5s ease;
}

@keyframes dataFlash {
    0% { background-color: rgba(59, 130, 246, 0.2); }
    100% { background-color: transparent; }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .text-3xl {
        font-size: 1.5rem;
    }
    
    .text-2xl {
        font-size: 1.25rem;
    }
}

/* Dark Theme Enhancements */
.dark {
    color-scheme: dark;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #374151;
}

::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}