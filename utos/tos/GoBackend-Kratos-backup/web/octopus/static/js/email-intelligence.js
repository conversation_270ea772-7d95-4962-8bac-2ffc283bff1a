// 📧 Email Intelligence Dashboard - AI Email Management
class EmailIntelligenceManager {
    constructor() {
        this.apiBaseUrl = window.OCTOPUS_CONFIG?.apiBaseUrl || 'http://localhost:8083/api';
        this.emailData = {
            stats: {},
            categories: {},
            sentiment: {},
            mailboxes: {},
            recentAnalysis: []
        };
        this.charts = {};
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        console.log('📧 Initializing Email Intelligence Manager...');
        this.setupEventListeners();
        this.createCharts();
        this.loadInitialData();
        this.startPeriodicUpdates();
    }
    
    setupEventListeners() {
        // Listen for WebSocket updates
        if (window.octopusWS) {
            window.octopusWS.onMessage('email_intelligence', (data) => {
                this.updateEmailData(data);
            });
        }
        
        // Email action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[onclick*="syncAllMailboxes"]')) {
                e.preventDefault();
                this.syncAllMailboxes();
            } else if (e.target.matches('[onclick*="processQueue"]')) {
                e.preventDefault();
                this.processQueue();
            } else if (e.target.matches('[onclick*="retrainModel"]')) {
                e.preventDefault();
                this.retrainModel();
            } else if (e.target.matches('[onclick*="exportAnalytics"]')) {
                e.preventDefault();
                this.exportAnalytics();
            }
        });
    }
    
    async loadInitialData() {
        try {
            console.log('📊 Loading email intelligence data...');
            
            // Load email statistics
            const statsResponse = await fetch(`${this.apiBaseUrl}/email/intelligence`);
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateEmailStats(stats);
            }
            
            // Load mailbox status
            const mailboxResponse = await fetch(`${this.apiBaseUrl}/email/mailboxes`);
            if (mailboxResponse.ok) {
                const mailboxes = await mailboxResponse.json();
                this.updateMailboxStatus(mailboxes);
            }
            
            // Load recent analysis
            const analysisResponse = await fetch(`${this.apiBaseUrl}/email/analytics`);
            if (analysisResponse.ok) {
                const analysis = await analysisResponse.json();
                this.updateRecentAnalysis(analysis);
            }
            
        } catch (error) {
            console.error('❌ Error loading email intelligence data:', error);
            this.showMockData();
        }
    }
    
    updateEmailStats(stats) {
        // Update email counts
        this.updateElement('total-emails-count', stats.total_emails || 0);
        this.updateElement('emails-today-count', stats.emails_today || 0);
        this.updateElement('unprocessed-emails', stats.unprocessed_emails || 0);
        
        // Update AI analysis stats
        this.updateElement('ai-analyzed-count', stats.ai_analyzed || 0);
        this.updateElement('ai-accuracy', `${(stats.ai_accuracy * 100).toFixed(1)}%`);
        this.updateElement('ai-processing-time', `${stats.avg_processing_time}ms`);
        
        // Update sentiment stats
        this.updateElement('avg-sentiment-score', stats.avg_sentiment_score?.toFixed(2) || '0.00');
        this.updateElement('positive-emails', stats.positive_emails || 0);
        this.updateElement('negative-emails', stats.negative_emails || 0);
        
        // Update response time stats
        this.updateElement('avg-response-time', this.formatDuration(stats.avg_response_time));
        this.updateElement('sla-compliance', `${(stats.sla_compliance * 100).toFixed(1)}%`);
        this.updateElement('overdue-emails', stats.overdue_emails || 0);
        
        // Update AI model performance
        this.updateElement('gemma-accuracy', `${(stats.gemma_accuracy * 100).toFixed(1)}%`);
        this.updateElement('classification-rate', `${(stats.classification_rate * 100).toFixed(1)}%`);
        this.updateElement('processing-speed', `${stats.processing_speed}ms`);
        this.updateElement('model-uptime', `${(stats.model_uptime * 100).toFixed(1)}%`);
        
        // Update timestamps
        this.updateElement('last-email-sync', new Date(stats.last_sync).toLocaleTimeString());
        this.updateElement('email-queue-count', stats.queue_count || 0);
        
        // Update charts
        this.updateEmailCategoriesChart(stats.categories);
        this.updateSentimentTrendsChart(stats.sentiment_trends);
    }
    
    updateMailboxStatus(mailboxes) {
        const mailboxElements = ['primary', 'support', 'billing'];
        
        mailboxElements.forEach((type, index) => {
            const mailbox = mailboxes[index] || {};
            const element = document.getElementById(`mailbox-${type}`);
            
            if (element) {
                const indicator = element.querySelector('.w-3.h-3');
                const statusText = element.querySelector('.text-xs');
                
                // Remove existing status classes
                element.classList.remove('healthy', 'warning', 'error');
                
                // Update status
                if (mailbox.status === 'connected') {
                    element.classList.add('healthy');
                    if (statusText) statusText.textContent = `✅ ${mailbox.email_count || 0} emails`;
                } else if (mailbox.status === 'syncing') {
                    element.classList.add('warning');
                    if (statusText) statusText.textContent = '🔄 Syncing...';
                } else {
                    element.classList.add('error');
                    if (statusText) statusText.textContent = '❌ Disconnected';
                }
            }
        });
    }
    
    updateRecentAnalysis(analysis) {
        const container = document.getElementById('recent-analysis-container');
        if (!container) return;
        
        if (!analysis || analysis.length === 0) {
            container.innerHTML = '<div class="text-gray-400 text-sm">No recent analysis available</div>';
            return;
        }
        
        container.innerHTML = analysis.slice(0, 5).map(item => `
            <div class="bg-gray-700 rounded p-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-medium text-white">${item.subject || 'No Subject'}</div>
                    <div class="text-xs text-gray-400">${this.formatTime(item.analyzed_at)}</div>
                </div>
                <div class="text-sm text-gray-300 mb-2">${item.from || 'Unknown Sender'}</div>
                <div class="flex space-x-4 text-xs">
                    <span class="px-2 py-1 rounded ${this.getCategoryColor(item.category)}">${item.category}</span>
                    <span class="px-2 py-1 rounded ${this.getSentimentColor(item.sentiment)}">${item.sentiment}</span>
                    <span class="px-2 py-1 rounded ${this.getPriorityColor(item.priority)}">${item.priority}</span>
                </div>
                ${item.ai_summary ? `<div class="text-xs text-gray-400 mt-2">${item.ai_summary}</div>` : ''}
            </div>
        `).join('');
    }
    
    createCharts() {
        this.createEmailCategoriesChart();
        this.createSentimentTrendsChart();
    }
    
    createEmailCategoriesChart() {
        const ctx = document.getElementById('email-categories-chart');
        if (!ctx) return;
        
        this.charts.categories = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Service Request', 'Billing', 'Complaint', 'Appointment', 'Emergency', 'Other'],
                datasets: [{
                    data: [0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#3b82f6', // blue
                        '#10b981', // green
                        '#ef4444', // red
                        '#f59e0b', // yellow
                        '#8b5cf6', // purple
                        '#6b7280'  // gray
                    ],
                    borderWidth: 2,
                    borderColor: '#374151'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#d1d5db',
                            padding: 15
                        }
                    }
                }
            }
        });
    }
    
    createSentimentTrendsChart() {
        const ctx = document.getElementById('sentiment-trends-chart');
        if (!ctx) return;
        
        this.charts.sentiment = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Positive',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Neutral',
                        data: [],
                        borderColor: '#6b7280',
                        backgroundColor: 'rgba(107, 114, 128, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Negative',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#d1d5db'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)'
                        }
                    }
                }
            }
        });
    }
    
    updateEmailCategoriesChart(categories) {
        if (!this.charts.categories || !categories) return;
        
        const data = [
            categories.service_request || 0,
            categories.billing || 0,
            categories.complaint || 0,
            categories.appointment || 0,
            categories.emergency || 0,
            categories.other || 0
        ];
        
        this.charts.categories.data.datasets[0].data = data;
        this.charts.categories.update('none');
    }
    
    updateSentimentTrendsChart(trends) {
        if (!this.charts.sentiment || !trends) return;
        
        const labels = trends.map(t => new Date(t.date).toLocaleDateString());
        const positive = trends.map(t => t.positive || 0);
        const neutral = trends.map(t => t.neutral || 0);
        const negative = trends.map(t => t.negative || 0);
        
        this.charts.sentiment.data.labels = labels;
        this.charts.sentiment.data.datasets[0].data = positive;
        this.charts.sentiment.data.datasets[1].data = neutral;
        this.charts.sentiment.data.datasets[2].data = negative;
        this.charts.sentiment.update('none');
    }
    
    // Email management actions
    async syncAllMailboxes() {
        try {
            console.log('🔄 Syncing all mailboxes...');
            this.showNotification('Syncing all mailboxes...', 'info');
            
            const response = await fetch(`${this.apiBaseUrl}/email/sync-all`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showNotification('Mailbox sync initiated successfully', 'success');
                this.loadInitialData(); // Refresh data
            } else {
                throw new Error('Sync failed');
            }
        } catch (error) {
            console.error('❌ Mailbox sync error:', error);
            this.showNotification('Failed to sync mailboxes', 'error');
        }
    }
    
    async processQueue() {
        try {
            console.log('⚡ Processing email queue...');
            this.showNotification('Processing email queue...', 'info');
            
            const response = await fetch(`${this.apiBaseUrl}/email/process-queue`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showNotification('Email queue processing started', 'success');
                this.loadInitialData(); // Refresh data
            } else {
                throw new Error('Queue processing failed');
            }
        } catch (error) {
            console.error('❌ Queue processing error:', error);
            this.showNotification('Failed to process email queue', 'error');
        }
    }
    
    async retrainModel() {
        try {
            console.log('🧠 Retraining AI model...');
            this.showNotification('Initiating AI model retraining...', 'info');
            
            const response = await fetch(`${this.apiBaseUrl}/ai/retrain`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'gemma-3-4b',
                    type: 'email_classification'
                })
            });
            
            if (response.ok) {
                this.showNotification('AI model retraining started', 'success');
            } else {
                throw new Error('Model retraining failed');
            }
        } catch (error) {
            console.error('❌ Model retraining error:', error);
            this.showNotification('Failed to retrain AI model', 'error');
        }
    }
    
    async exportAnalytics() {
        try {
            console.log('📊 Exporting analytics data...');
            this.showNotification('Preparing analytics export...', 'info');
            
            const response = await fetch(`${this.apiBaseUrl}/email/export-analytics`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `email-analytics-${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showNotification('Analytics data exported successfully', 'success');
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            console.error('❌ Analytics export error:', error);
            this.showNotification('Failed to export analytics data', 'error');
        }
    }
    
    // Utility methods
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.add('data-update');
            setTimeout(() => element.classList.remove('data-update'), 500);
        }
    }
    
    formatDuration(seconds) {
        if (seconds < 60) return `${seconds}s`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
        return `${Math.floor(seconds / 3600)}h`;
    }
    
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString();
    }
    
    getCategoryColor(category) {
        const colors = {
            'service_request': 'bg-blue-600',
            'billing': 'bg-green-600',
            'complaint': 'bg-red-600',
            'appointment': 'bg-yellow-600',
            'emergency': 'bg-purple-600',
            'other': 'bg-gray-600'
        };
        return colors[category] || 'bg-gray-600';
    }
    
    getSentimentColor(sentiment) {
        const colors = {
            'positive': 'bg-green-600',
            'neutral': 'bg-gray-600',
            'negative': 'bg-red-600'
        };
        return colors[sentiment] || 'bg-gray-600';
    }
    
    getPriorityColor(priority) {
        const colors = {
            'high': 'bg-red-600',
            'medium': 'bg-yellow-600',
            'low': 'bg-green-600'
        };
        return colors[priority] || 'bg-gray-600';
    }
    
    showNotification(message, type = 'info') {
        if (window.octopusInterface) {
            window.octopusInterface.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    showMockData() {
        console.log('📊 Showing mock email intelligence data...');
        
        // Mock email stats
        this.updateEmailStats({
            total_emails: 1247,
            emails_today: 23,
            unprocessed_emails: 5,
            ai_analyzed: 1189,
            ai_accuracy: 0.94,
            avg_processing_time: 150,
            avg_sentiment_score: 0.72,
            positive_emails: 892,
            negative_emails: 89,
            avg_response_time: 3600,
            sla_compliance: 0.89,
            overdue_emails: 12,
            gemma_accuracy: 0.96,
            classification_rate: 0.98,
            processing_speed: 120,
            model_uptime: 0.995,
            last_sync: new Date().toISOString(),
            queue_count: 3,
            categories: {
                service_request: 45,
                billing: 20,
                complaint: 15,
                appointment: 10,
                emergency: 5,
                other: 5
            },
            sentiment_trends: [
                { date: '2024-01-01', positive: 15, neutral: 8, negative: 2 },
                { date: '2024-01-02', positive: 18, neutral: 6, negative: 1 },
                { date: '2024-01-03', positive: 12, neutral: 10, negative: 3 }
            ]
        });
        
        // Mock mailbox status
        this.updateMailboxStatus([
            { status: 'connected', email_count: 45 },
            { status: 'syncing', email_count: 0 },
            { status: 'connected', email_count: 12 }
        ]);
        
        // Mock recent analysis
        this.updateRecentAnalysis([
            {
                subject: 'HVAC System Not Cooling Properly',
                from: '<EMAIL>',
                analyzed_at: new Date().toISOString(),
                category: 'service_request',
                sentiment: 'negative',
                priority: 'high',
                ai_summary: 'Customer reports AC not cooling, requires urgent service call'
            },
            {
                subject: 'Thank you for excellent service',
                from: '<EMAIL>',
                analyzed_at: new Date(Date.now() - 300000).toISOString(),
                category: 'other',
                sentiment: 'positive',
                priority: 'low',
                ai_summary: 'Customer satisfaction feedback, no action required'
            }
        ]);
    }
    
    startPeriodicUpdates() {
        this.refreshInterval = setInterval(() => {
            this.loadInitialData();
        }, 30000); // Update every 30 seconds
    }
    
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    }
}

// Initialize Email Intelligence Manager when page loads
let emailIntelligenceManager;
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('total-emails-count')) {
        emailIntelligenceManager = new EmailIntelligenceManager();
        console.log('📧 Email Intelligence Manager initialized successfully!');
    }
});

// Export for global access
window.EmailIntelligenceManager = EmailIntelligenceManager;