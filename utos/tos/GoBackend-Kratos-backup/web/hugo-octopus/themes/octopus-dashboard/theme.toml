# Theme configuration for Octopus Dashboard

name = "Octopus Dashboard"
license = "MIT"
licenselink = "https://github.com/gohugoio/hugo/blob/master/LICENSE"
description = "Modern HVAC CRM Dashboard Theme with Real-time Analytics"
homepage = "http://localhost:8083"
tags = ["dashboard", "admin", "real-time", "hvac", "crm"]
features = ["responsive", "real-time", "websocket", "charts", "dark-theme"]
min_version = "0.121.0"

[author]
  name = "HVAC GoBackend-Kratos Team"
  homepage = "http://localhost:8083"

# Original template
[original]
  name = "Morphic Octopus Interface"
  homepage = "http://localhost:8083"
  repo = "https://github.com/hvac-company/gobackend-kratos"