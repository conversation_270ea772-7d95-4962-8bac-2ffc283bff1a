{{ define "main" }}
<div class="px-4 py-6 sm:px-0">
    <!-- Email Intelligence Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">📧 Email Intelligence Dashboard</h1>
        <p class="mt-2 text-gray-400">AI-powered email management with Gemma-3-4b integration</p>
        <div class="mt-4 flex items-center space-x-4">
            <div class="text-sm text-gray-400">
                Last Sync: <span id="last-email-sync" class="text-blue-400">Loading...</span>
            </div>
            <div class="text-sm text-gray-400">
                Processing Queue: <span id="email-queue-count" class="text-yellow-400">0</span>
            </div>
        </div>
    </div>

    <!-- Email Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Emails -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">Total Emails</p>
                    <p id="total-emails-count" class="text-2xl font-bold text-purple-400">Loading...</p>
                </div>
                <div class="text-3xl">📧</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Today</span>
                    <span id="emails-today-count" class="text-green-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Unprocessed</span>
                    <span id="unprocessed-emails" class="text-red-400">0</span>
                </div>
            </div>
        </div>

        <!-- AI Analysis -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">AI Analyzed</p>
                    <p id="ai-analyzed-count" class="text-2xl font-bold text-blue-400">Loading...</p>
                </div>
                <div class="text-3xl">🤖</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Accuracy</span>
                    <span id="ai-accuracy" class="text-green-400">0%</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Avg Time</span>
                    <span id="ai-processing-time" class="text-blue-400">0ms</span>
                </div>
            </div>
        </div>

        <!-- Sentiment Analysis -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">Sentiment Score</p>
                    <p id="avg-sentiment-score" class="text-2xl font-bold text-green-400">Loading...</p>
                </div>
                <div class="text-3xl">😊</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Positive</span>
                    <span id="positive-emails" class="text-green-400">0</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Negative</span>
                    <span id="negative-emails" class="text-red-400">0</span>
                </div>
            </div>
        </div>

        <!-- Response Time -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-400">Avg Response</p>
                    <p id="avg-response-time" class="text-2xl font-bold text-yellow-400">Loading...</p>
                </div>
                <div class="text-3xl">⏱️</div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">SLA Met</span>
                    <span id="sla-compliance" class="text-green-400">0%</span>
                </div>
                <div class="flex justify-between text-sm mt-1">
                    <span class="text-gray-400">Overdue</span>
                    <span id="overdue-emails" class="text-red-400">0</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Categories Chart -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Email Categories -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">Email Categories</h3>
            <canvas id="email-categories-chart" width="400" height="200"></canvas>
        </div>

        <!-- Sentiment Trends -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">Sentiment Trends</h3>
            <canvas id="sentiment-trends-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Mailbox Status -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Mailbox Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div id="mailbox-primary" class="mailbox-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Primary Mailbox</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
            <div id="mailbox-support" class="mailbox-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Support Mailbox</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
            <div id="mailbox-billing" class="mailbox-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm">Billing Mailbox</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">Loading...</div>
            </div>
        </div>
    </div>

    <!-- Recent Email Analysis -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Recent AI Analysis</h3>
        <div id="recent-analysis-container" class="space-y-4">
            <div class="text-gray-400 text-sm">Loading recent email analysis...</div>
        </div>
    </div>

    <!-- AI Model Performance -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">AI Model Performance</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-400" id="gemma-accuracy">0%</div>
                <div class="text-sm text-gray-400">Gemma-3-4b Accuracy</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-400" id="classification-rate">0%</div>
                <div class="text-sm text-gray-400">Classification Rate</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-400" id="processing-speed">0ms</div>
                <div class="text-sm text-gray-400">Avg Processing</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-400" id="model-uptime">0%</div>
                <div class="text-sm text-gray-400">Model Uptime</div>
            </div>
        </div>
    </div>

    <!-- Email Actions -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-4">Email Management Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm" onclick="syncAllMailboxes()">
                🔄 Sync All
            </button>
            <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm" onclick="processQueue()">
                ⚡ Process Queue
            </button>
            <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm" onclick="retrainModel()">
                🧠 Retrain AI
            </button>
            <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm" onclick="exportAnalytics()">
                📊 Export Data
            </button>
        </div>
    </div>
</div>

<script>
// Email Intelligence specific JavaScript
function syncAllMailboxes() {
    console.log('🔄 Syncing all mailboxes...');
    // Implementation for mailbox sync
}

function processQueue() {
    console.log('⚡ Processing email queue...');
    // Implementation for queue processing
}

function retrainModel() {
    console.log('🧠 Retraining AI model...');
    // Implementation for model retraining
}

function exportAnalytics() {
    console.log('📊 Exporting analytics data...');
    // Implementation for data export
}
</script>
{{ end }}