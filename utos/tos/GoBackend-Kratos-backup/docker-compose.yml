version: '3.8'

services:
  # External PostgreSQL Database (**************:5432)
  # Using external database - no local postgres service needed

  # Redis Cache (Shared by HVAC and BillionMail)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Ory Kratos Identity Server
  kratos:
    image: oryd/kratos:latest
    ports:
      - "4433:4433" # Public API
      - "4434:4434" # Admin API
    environment:
      - DSN=************************************************************************
      - KRATOS_PUBLIC_URL=http://localhost:4433
      - KRATOS_ADMIN_URL=http://localhost:4434
      - LOG_LEVEL=debug
      - LOG_LEAK_SENSITIVE_VALUES=true
      - SECURE_COOKIES=false
    volumes:
      - ./kratos_config:/etc/config/kratos:ro
      - kratos_data:/var/lib/kratos
    command: serve -c /etc/config/kratos/kratos.yml --dev --watch-courier
    restart: unless-stopped
    depends_on:
      - redis # Kratos can use Redis for some features, though not strictly required for basic setup
  
  # 📧 Email Client Configuration (External SMTP/IMAP servers)
  # Using external email servers from email_servers.md
  # No local email server needed - just client functionality

  # HVAC Kratos Backend
  hvac-backend:
    image: gobackend-hvac-kratos:latest
    ports:
      - "8080:8080"  # HTTP
      - "9000:9000"  # gRPC
      - "8081:8081"  # MCP
    environment:
      - DATABASE_URL=************************************************************************
      - REDIS_URL=redis://redis:6379
      - LM_STUDIO_URL=http://************:1234
      # External Email Configuration (from email_servers.md)
      - SMTP_HOST=serwer2440139.home.pl
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASSWORD=Blaeritipol1
      - IMAP_HOST=serwer2440139.home.pl
      - IMAP_PORT=993
      - IMAP_USER=<EMAIL>
      - IMAP_PASSWORD=Blaeritipol1
      # Audio Email Account
      - AUDIO_EMAIL_HOST=serwer2440139.home.pl
      - AUDIO_EMAIL_USER=<EMAIL>
      - AUDIO_EMAIL_PASSWORD=Blaeritipol1
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./configs:/root/configs
    restart: unless-stopped

  # Bytebase Database Management
  bytebase:
    image: bytebase/bytebase:latest
    hostname: bytebase
    ports:
      - "8092:8080"  # Bytebase Web UI
    environment:
      - BB_DATA_DIR=/var/opt/bytebase
      - BB_EXTERNAL_URL=http://localhost:8092
      - BB_PG_URL=************************************************************************
      - BB_PORT=8080
      - BB_READONLY=false
    volumes:
      - bytebase_data:/var/opt/bytebase
      - ./migrations:/migrations
    restart: unless-stopped

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # 🐙 Morphic Octopus Interface - Ultimate Backend Management
  octopus-interface:
    build:
      context: .
      dockerfile: Dockerfile.octopus
    ports:
      - "8083:8083"
    environment:
      - DATABASE_URL=************************************************************************
      - REDIS_URL=redis://redis:6379
      - LM_STUDIO_URL=http://************:1234
      - EMAIL_SERVICE_URL=http://email-intelligence:8082
      - TRANSCRIPTION_SERVICE_URL=http://hvac-backend:8084
    depends_on:
      - redis
      - hvac-backend
    volumes:
      - ./logs:/app/logs
      - ./configs:/app/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/api/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
  bytebase_data:
  kratos_config:
  kratos_data: