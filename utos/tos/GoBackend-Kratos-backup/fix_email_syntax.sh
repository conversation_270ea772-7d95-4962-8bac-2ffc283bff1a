#!/bin/bash

# Fix email.go syntax errors
echo "🔧 Fixing email.go syntax errors..."

# Fix all the syntax errors with extra parentheses
sed -i 's/t\.logger\.Error("Failed to analyze email"))/t.logger.Error("Failed to analyze email")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Info("Email insights requested"))/t.logger.Info("Email insights requested")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to extract customer info"))/t.logger.Error("Failed to extract customer info")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to classify email intent"))/t.logger.Error("Failed to classify email intent")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to generate email response"))/t.logger.Error("Failed to generate email response")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to analyze sentiment"))/t.logger.Error("Failed to analyze sentiment")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to extract entities"))/t.logger.Error("Failed to extract entities")/g' internal/mcp/tools/email.go
sed -i 's/t\.logger\.Error("Failed to process HVAC keywords"))/t.logger.Error("Failed to process HVAC keywords")/g' internal/mcp/tools/email.go

echo "✅ email.go syntax errors fixed!"
