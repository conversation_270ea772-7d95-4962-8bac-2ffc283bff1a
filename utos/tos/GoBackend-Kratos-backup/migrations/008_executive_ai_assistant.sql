-- 🧠 Executive AI Assistant Database Schema
-- Comprehensive email automation and AI-powered management system

-- ==========================================
-- EMAIL TRIAGE SYSTEM
-- ==========================================

-- Email Triage Results - AI-powered email classification
CREATE TABLE IF NOT EXISTS email_triage (
    id BIGSERIAL PRIMARY KEY,
    email_id BIGINT NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
    
    -- Triage Decision
    triage_action VARCHAR(20) NOT NULL CHECK (triage_action IN ('respond', 'notify', 'ignore', 'escalate')),
    triage_reason TEXT NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    
    -- Priority Classification
    priority_level VARCHAR(20) NOT NULL DEFAULT 'normal' CHECK (priority_level IN ('urgent', 'high', 'normal', 'low')),
    urgency_score DECIMAL(3,2) CHECK (urgency_score >= 0 AND urgency_score <= 1),
    
    -- Category Classification
    email_category VARCHAR(50) NOT NULL DEFAULT 'general',
    subcategory VARCHAR(100),
    
    -- HVAC-Specific Classification
    hvac_service_type VARCHAR(100), -- 'installation', 'repair', 'maintenance', 'emergency', 'quote'
    equipment_type VARCHAR(100),    -- 'ac', 'heating', 'ventilation', 'ductwork'
    customer_type VARCHAR(50),      -- 'residential', 'commercial', 'industrial'
    
    -- AI Analysis Results
    detected_intent VARCHAR(200),
    detected_entities JSONB DEFAULT '{}',
    key_phrases TEXT[],
    sentiment VARCHAR(20) CHECK (sentiment IN ('positive', 'neutral', 'negative', 'angry')),
    sentiment_score DECIMAL(3,2),
    
    -- Processing Metadata
    ai_model_used VARCHAR(100) NOT NULL DEFAULT 'gemma-3-4b',
    processing_time_ms INTEGER,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Audit Trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    UNIQUE(email_id)
);

-- ==========================================
-- EMAIL RESPONSE DRAFTING SYSTEM
-- ==========================================

-- Email Response Drafts - AI-generated response drafts
CREATE TABLE IF NOT EXISTS email_response_drafts (
    id BIGSERIAL PRIMARY KEY,
    email_id BIGINT NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
    triage_id BIGINT REFERENCES email_triage(id) ON DELETE CASCADE,
    
    -- Draft Content
    draft_subject TEXT,
    draft_body TEXT NOT NULL,
    draft_html_body TEXT,
    
    -- Response Type
    response_type VARCHAR(50) NOT NULL CHECK (response_type IN ('reply', 'forward', 'new_thread')),
    response_tone VARCHAR(30) DEFAULT 'professional' CHECK (response_tone IN ('professional', 'friendly', 'formal', 'casual', 'urgent')),
    
    -- Recipients
    to_addresses TEXT[] NOT NULL,
    cc_addresses TEXT[],
    bcc_addresses TEXT[],
    
    -- AI Generation Details
    ai_model_used VARCHAR(100) NOT NULL DEFAULT 'gemma-3-4b',
    generation_prompt TEXT,
    generation_context JSONB DEFAULT '{}',
    
    -- Quality Metrics
    quality_score DECIMAL(3,2) CHECK (quality_score >= 0 AND quality_score <= 1),
    relevance_score DECIMAL(3,2) CHECK (relevance_score >= 0 AND relevance_score <= 1),
    tone_match_score DECIMAL(3,2) CHECK (tone_match_score >= 0 AND tone_match_score <= 1),
    
    -- Status and Approval
    status VARCHAR(30) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'pending_review', 'approved', 'rejected', 'sent', 'failed')),
    approved_by VARCHAR(100),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    
    -- Sending Details
    sent_at TIMESTAMP WITH TIME ZONE,
    sent_message_id VARCHAR(255),
    delivery_status VARCHAR(30) CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    
    -- Audit Trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- EXECUTIVE MEMORY BANK
-- ==========================================

-- Executive Memory Bank - Long-term context and learning
CREATE TABLE IF NOT EXISTS executive_memory_bank (
    id BIGSERIAL PRIMARY KEY,
    
    -- Memory Classification
    memory_type VARCHAR(50) NOT NULL CHECK (memory_type IN ('customer_preference', 'communication_pattern', 'business_rule', 'contact_info', 'recurring_issue', 'important_detail')),
    memory_category VARCHAR(100) NOT NULL,
    
    -- Memory Content
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    
    -- Context and References
    entity_type VARCHAR(50), -- 'customer', 'vendor', 'employee', 'equipment'
    entity_id VARCHAR(100),  -- Reference to specific entity
    entity_name VARCHAR(200),
    
    -- Source Information
    source_type VARCHAR(50) NOT NULL CHECK (source_type IN ('email', 'call', 'meeting', 'document', 'manual')),
    source_id BIGINT,        -- Reference to source (email_id, call_id, etc.)
    source_reference TEXT,
    
    -- AI Analysis
    extracted_entities JSONB DEFAULT '{}',
    key_concepts TEXT[],
    related_memories BIGINT[], -- Array of related memory IDs
    
    -- Quality and Verification
    confidence_score DECIMAL(3,2) NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by VARCHAR(100),
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Usage Tracking
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    relevance_score DECIMAL(3,2) DEFAULT 0.5,
    
    -- Lifecycle Management
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Audit Trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- WORKFLOW AUTOMATION
-- ==========================================

-- Email Workflow Rules - Automated action rules
CREATE TABLE IF NOT EXISTS email_workflow_rules (
    id BIGSERIAL PRIMARY KEY,
    
    -- Rule Definition
    rule_name VARCHAR(200) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Trigger Conditions
    trigger_conditions JSONB NOT NULL, -- Complex conditions in JSON format
    
    -- Actions to Execute
    actions JSONB NOT NULL, -- Array of actions to execute
    
    -- Priority and Execution
    priority INTEGER DEFAULT 100, -- Lower number = higher priority
    execution_order INTEGER DEFAULT 1,
    
    -- Performance Metrics
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_executed_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit Trail
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Workflow Executions - Track workflow execution history
CREATE TABLE IF NOT EXISTS email_workflow_executions (
    id BIGSERIAL PRIMARY KEY,
    
    -- Workflow Reference
    rule_id BIGINT NOT NULL REFERENCES email_workflow_rules(id) ON DELETE CASCADE,
    email_id BIGINT NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
    
    -- Execution Details
    execution_status VARCHAR(30) NOT NULL CHECK (execution_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    execution_result JSONB DEFAULT '{}',
    
    -- Actions Executed
    actions_executed JSONB DEFAULT '[]',
    actions_failed JSONB DEFAULT '[]',
    
    -- Performance
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,
    
    -- Error Handling
    error_message TEXT,
    error_details JSONB,
    retry_count INTEGER DEFAULT 0,
    
    -- Audit Trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- CALENDAR INTEGRATION
-- ==========================================

-- Calendar Events - Meeting scheduling and management
CREATE TABLE IF NOT EXISTS calendar_events (
    id BIGSERIAL PRIMARY KEY,
    
    -- Event Details
    event_id VARCHAR(255) UNIQUE, -- External calendar system ID
    title VARCHAR(500) NOT NULL,
    description TEXT,
    location VARCHAR(500),
    
    -- Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    timezone VARCHAR(100) DEFAULT 'UTC',
    is_all_day BOOLEAN DEFAULT FALSE,
    
    -- Participants
    organizer_email VARCHAR(255) NOT NULL,
    attendees JSONB DEFAULT '[]', -- Array of attendee objects
    
    -- Meeting Type
    meeting_type VARCHAR(50) DEFAULT 'general' CHECK (meeting_type IN ('general', 'hvac_consultation', 'site_visit', 'follow_up', 'emergency')),
    customer_id BIGINT, -- Reference to customer if applicable
    job_id BIGINT,      -- Reference to job if applicable
    
    -- Status
    status VARCHAR(30) DEFAULT 'confirmed' CHECK (status IN ('tentative', 'confirmed', 'cancelled')),
    
    -- AI Integration
    created_by_ai BOOLEAN DEFAULT FALSE,
    ai_confidence DECIMAL(3,2),
    ai_reasoning TEXT,
    
    -- Source Information
    source_email_id BIGINT REFERENCES emails(id),
    source_type VARCHAR(50) DEFAULT 'manual' CHECK (source_type IN ('manual', 'email_ai', 'customer_portal', 'mobile_app')),
    
    -- Audit Trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- PERFORMANCE ANALYTICS
-- ==========================================

-- Executive AI Performance Metrics
CREATE TABLE IF NOT EXISTS executive_ai_metrics (
    id BIGSERIAL PRIMARY KEY,
    
    -- Metric Details
    metric_name VARCHAR(100) NOT NULL,
    metric_category VARCHAR(50) NOT NULL, -- 'triage', 'response', 'automation', 'calendar'
    metric_value DECIMAL(10,4) NOT NULL,
    metric_unit VARCHAR(20), -- 'percentage', 'count', 'seconds', 'score'
    
    -- Context
    context_data JSONB DEFAULT '{}',
    
    -- Time Period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Audit Trail
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- INDEXES FOR PERFORMANCE
-- ==========================================

-- Email Triage Indexes
CREATE INDEX IF NOT EXISTS idx_email_triage_email_id ON email_triage(email_id);
CREATE INDEX IF NOT EXISTS idx_email_triage_action ON email_triage(triage_action);
CREATE INDEX IF NOT EXISTS idx_email_triage_priority ON email_triage(priority_level);
CREATE INDEX IF NOT EXISTS idx_email_triage_category ON email_triage(email_category);
CREATE INDEX IF NOT EXISTS idx_email_triage_processed_at ON email_triage(processed_at);

-- Email Response Drafts Indexes
CREATE INDEX IF NOT EXISTS idx_email_response_drafts_email_id ON email_response_drafts(email_id);
CREATE INDEX IF NOT EXISTS idx_email_response_drafts_status ON email_response_drafts(status);
CREATE INDEX IF NOT EXISTS idx_email_response_drafts_created_at ON email_response_drafts(created_at);

-- Executive Memory Bank Indexes
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_type ON executive_memory_bank(memory_type);
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_category ON executive_memory_bank(memory_category);
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_entity ON executive_memory_bank(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_source ON executive_memory_bank(source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_active ON executive_memory_bank(is_active);
CREATE INDEX IF NOT EXISTS idx_executive_memory_bank_search ON executive_memory_bank USING gin(to_tsvector('english', title || ' ' || content));

-- Workflow Rules Indexes
CREATE INDEX IF NOT EXISTS idx_email_workflow_rules_active ON email_workflow_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_email_workflow_rules_priority ON email_workflow_rules(priority);

-- Workflow Executions Indexes
CREATE INDEX IF NOT EXISTS idx_email_workflow_executions_rule_id ON email_workflow_executions(rule_id);
CREATE INDEX IF NOT EXISTS idx_email_workflow_executions_email_id ON email_workflow_executions(email_id);
CREATE INDEX IF NOT EXISTS idx_email_workflow_executions_status ON email_workflow_executions(execution_status);
CREATE INDEX IF NOT EXISTS idx_email_workflow_executions_started_at ON email_workflow_executions(started_at);

-- Calendar Events Indexes
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_calendar_events_organizer ON calendar_events(organizer_email);
CREATE INDEX IF NOT EXISTS idx_calendar_events_customer_id ON calendar_events(customer_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_source_email ON calendar_events(source_email_id);

-- Performance Metrics Indexes
CREATE INDEX IF NOT EXISTS idx_executive_ai_metrics_name ON executive_ai_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_executive_ai_metrics_category ON executive_ai_metrics(metric_category);
CREATE INDEX IF NOT EXISTS idx_executive_ai_metrics_period ON executive_ai_metrics(period_start, period_end);

-- ==========================================
-- FUNCTIONS AND TRIGGERS
-- ==========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_email_triage_updated_at BEFORE UPDATE ON email_triage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_response_drafts_updated_at BEFORE UPDATE ON email_response_drafts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_executive_memory_bank_updated_at BEFORE UPDATE ON executive_memory_bank FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_workflow_rules_updated_at BEFORE UPDATE ON email_workflow_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_calendar_events_updated_at BEFORE UPDATE ON calendar_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to increment memory access count
CREATE OR REPLACE FUNCTION increment_memory_access(memory_id BIGINT)
RETURNS VOID AS $$
BEGIN
    UPDATE executive_memory_bank 
    SET access_count = access_count + 1,
        last_accessed_at = NOW()
    WHERE id = memory_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update workflow execution metrics
CREATE OR REPLACE FUNCTION update_workflow_metrics(rule_id BIGINT, success BOOLEAN)
RETURNS VOID AS $$
BEGIN
    UPDATE email_workflow_rules 
    SET execution_count = execution_count + 1,
        success_count = CASE WHEN success THEN success_count + 1 ELSE success_count END,
        failure_count = CASE WHEN NOT success THEN failure_count + 1 ELSE failure_count END,
        last_executed_at = NOW()
    WHERE id = rule_id;
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- INITIAL DATA
-- ==========================================

-- Default Workflow Rules
INSERT INTO email_workflow_rules (rule_name, description, trigger_conditions, actions, created_by) VALUES
('Emergency HVAC Response', 'Auto-escalate emergency HVAC issues', 
 '{"conditions": [{"field": "hvac_service_type", "operator": "equals", "value": "emergency"}, {"field": "urgency_score", "operator": "greater_than", "value": 0.8}]}',
 '[{"type": "escalate", "target": "emergency_team"}, {"type": "notify", "recipients": ["<EMAIL>"]}, {"type": "priority", "level": "urgent"}]',
 'system'),

('Customer Complaint Auto-Response', 'Auto-respond to customer complaints with empathy', 
 '{"conditions": [{"field": "sentiment", "operator": "equals", "value": "negative"}, {"field": "email_category", "operator": "equals", "value": "complaint"}]}',
 '[{"type": "draft_response", "template": "complaint_acknowledgment"}, {"type": "escalate", "target": "customer_service"}, {"type": "schedule_follow_up", "hours": 24}]',
 'system'),

('Quote Request Processing', 'Auto-process quote requests', 
 '{"conditions": [{"field": "detected_intent", "operator": "contains", "value": "quote"}, {"field": "hvac_service_type", "operator": "in", "value": ["installation", "repair"]}]}',
 '[{"type": "create_lead", "source": "email"}, {"type": "draft_response", "template": "quote_request"}, {"type": "assign", "team": "sales"}]',
 'system'),

('Appointment Scheduling', 'Auto-schedule service appointments', 
 '{"conditions": [{"field": "detected_intent", "operator": "contains", "value": "schedule"}, {"field": "customer_type", "operator": "equals", "value": "existing"}]}',
 '[{"type": "check_calendar", "service": "calendar_api"}, {"type": "draft_response", "template": "appointment_confirmation"}, {"type": "create_calendar_event"}]',
 'system');

-- Performance Metrics Initial Setup
INSERT INTO executive_ai_metrics (metric_name, metric_category, metric_value, metric_unit, period_start, period_end) VALUES
('triage_accuracy', 'triage', 0.0, 'percentage', NOW() - INTERVAL '1 day', NOW()),
('response_quality', 'response', 0.0, 'score', NOW() - INTERVAL '1 day', NOW()),
('automation_rate', 'automation', 0.0, 'percentage', NOW() - INTERVAL '1 day', NOW()),
('calendar_success_rate', 'calendar', 0.0, 'percentage', NOW() - INTERVAL '1 day', NOW());

-- ==========================================
-- COMMENTS AND DOCUMENTATION
-- ==========================================

COMMENT ON TABLE email_triage IS 'AI-powered email classification and triage decisions';
COMMENT ON TABLE email_response_drafts IS 'AI-generated email response drafts with approval workflow';
COMMENT ON TABLE executive_memory_bank IS 'Long-term memory system for AI learning and context';
COMMENT ON TABLE email_workflow_rules IS 'Automated workflow rules for email processing';
COMMENT ON TABLE email_workflow_executions IS 'Execution history and results of workflow rules';
COMMENT ON TABLE calendar_events IS 'Calendar events and meeting scheduling';
COMMENT ON TABLE executive_ai_metrics IS 'Performance metrics and analytics for Executive AI';

COMMENT ON COLUMN email_triage.triage_action IS 'AI decision: respond, notify, ignore, or escalate';
COMMENT ON COLUMN email_triage.confidence_score IS 'AI confidence in triage decision (0.0-1.0)';
COMMENT ON COLUMN email_response_drafts.quality_score IS 'AI-assessed quality of generated response (0.0-1.0)';
COMMENT ON COLUMN executive_memory_bank.confidence_score IS 'Confidence in memory accuracy (0.0-1.0)';
COMMENT ON COLUMN calendar_events.created_by_ai IS 'Whether this event was created by AI assistant';