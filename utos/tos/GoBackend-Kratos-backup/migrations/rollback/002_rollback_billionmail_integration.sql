-- Rollback: 002_rollback_billionmail_integration.sql
-- Description: Rollback BillionMail email system integration
-- Author: GoBackend HVAC Kratos
-- Date: 2024-12-19

-- Drop triggers
DROP TRIGGER IF EXISTS update_domains_updated_at ON billionmail.domains;
DROP TRIGGER IF EXISTS update_mailboxes_updated_at ON billionmail.mailboxes;
DROP TRIGGER IF EXISTS update_email_messages_updated_at ON billionmail.email_messages;
DROP TRIGGER IF EXISTS update_email_campaigns_updated_at ON billionmail.email_campaigns;
DROP TRIGGER IF EXISTS update_email_templates_updated_at ON billionmail.email_templates;

-- Drop indexes
DROP INDEX IF EXISTS billionmail.idx_domains_domain;
DROP INDEX IF EXISTS billionmail.idx_mailboxes_email;
DROP INDEX IF EXISTS billionmail.idx_mailboxes_domain_id;
DROP INDEX IF EXISTS billionmail.idx_aliases_source;
DROP INDEX IF EXISTS billionmail.idx_aliases_domain_id;
DROP INDEX IF EXISTS billionmail.idx_email_messages_status;
DROP INDEX IF EXISTS billionmail.idx_email_messages_from;
DROP INDEX IF EXISTS billionmail.idx_email_messages_created;
DROP INDEX IF EXISTS billionmail.idx_campaigns_status;
DROP INDEX IF EXISTS billionmail.idx_campaigns_scheduled;
DROP INDEX IF EXISTS billionmail.idx_templates_type;
DROP INDEX IF EXISTS billionmail.idx_hvac_integration_customer;
DROP INDEX IF EXISTS billionmail.idx_hvac_integration_job;
DROP INDEX IF EXISTS billionmail.idx_hvac_integration_type;

-- Drop tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS billionmail.hvac_email_integration;
DROP TABLE IF EXISTS billionmail.email_templates;
DROP TABLE IF EXISTS billionmail.email_campaigns;
DROP TABLE IF EXISTS billionmail.email_messages;
DROP TABLE IF EXISTS billionmail.aliases;
DROP TABLE IF EXISTS billionmail.mailboxes;
DROP TABLE IF EXISTS billionmail.domains;

-- Drop schema
DROP SCHEMA IF EXISTS billionmail;
