package middleware

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"

	"gobackend-hvac-kratos/internal/common"
)

// 🚀 Advanced HVAC Middleware Chains for Kratos Framework Excellence
// Enterprise-grade middleware for HVAC business logic and operations

// ==========================================
// HVAC AUTHENTICATION MIDDLEWARE
// ==========================================

// HVACAuthConfig configures HVAC authentication
type HVACAuthConfig struct {
	EnableRoleBasedAuth bool          `json:"enable_role_based_auth"`
	RequiredRoles       []string      `json:"required_roles"`
	AdminRoles          []string      `json:"admin_roles"`
	TechnicianRoles     []string      `json:"technician_roles"`
	CustomerRoles       []string      `json:"customer_roles"`
	BypassPaths         []string      `json:"bypass_paths"`
	TokenHeader         string        `json:"token_header"`
	SessionTimeout      time.Duration `json:"session_timeout"`
}

// HVACAuthMiddleware provides HVAC-specific authentication
type HVACAuthMiddleware struct {
	config *HVACAuthConfig
	log    *log.Helper
}

// NewHVACAuthMiddleware creates a new HVAC authentication middleware
func NewHVACAuthMiddleware(config *HVACAuthConfig, logger log.Logger) *HVACAuthMiddleware {
	if config == nil {
		config = &HVACAuthConfig{
			EnableRoleBasedAuth: true,
			RequiredRoles:       []string{"user"},
			AdminRoles:          []string{"admin", "manager"},
			TechnicianRoles:     []string{"technician", "senior_technician"},
			CustomerRoles:       []string{"customer"},
			BypassPaths:         []string{"/health", "/metrics", "/api/public"},
			TokenHeader:         "Authorization",
			SessionTimeout:      24 * time.Hour,
		}
	}

	return &HVACAuthMiddleware{
		config: config,
		log:    log.NewHelper(logger),
	}
}

// Middleware returns the HVAC authentication middleware function
func (h *HVACAuthMiddleware) Middleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// Extract request path
			path := h.extractPath(ctx)

			// Check if path should bypass authentication
			if h.shouldBypass(path) {
				return handler(ctx, req)
			}

			// Validate HVAC user authentication
			user, err := h.validateHVACAuth(ctx)
			if err != nil {
				return nil, errors.Unauthorized("HVAC_AUTH_FAILED", err.Error())
			}

			// Check HVAC role-based permissions
			if h.config.EnableRoleBasedAuth {
				if err := h.checkHVACPermissions(ctx, user, path); err != nil {
					return nil, errors.Forbidden("HVAC_PERMISSION_DENIED", err.Error())
				}
			}

			// Enrich context with HVAC user information
			ctx = h.enrichContextWithHVACUser(ctx, user)

			h.log.WithContext(ctx).Infof("HVAC Auth: User %s accessed %s", user.Username, path)
			return handler(ctx, req)
		}
	}
}

// ==========================================
// HVAC BUSINESS LOGIC MIDDLEWARE
// ==========================================

// HVACBusinessConfig configures business logic middleware
type HVACBusinessConfig struct {
	EnableValidation     bool          `json:"enable_validation"`
	EnableAuditLogging   bool          `json:"enable_audit_logging"`
	EnableRateLimit      bool          `json:"enable_rate_limit"`
	RateLimitRPS         int           `json:"rate_limit_rps"`
	EnableCircuitBreaker bool          `json:"enable_circuit_breaker"`
	FailureThreshold     int           `json:"failure_threshold"`
	Timeout              time.Duration `json:"timeout"`
}

// HVACBusinessMiddleware provides HVAC business logic middleware
type HVACBusinessMiddleware struct {
	config         *HVACBusinessConfig
	log            *log.Helper
	perfMonitor    *common.PerformanceMonitor
	circuitBreaker *CircuitBreaker
	rateLimiter    *RateLimiter
}

// NewHVACBusinessMiddleware creates a new HVAC business middleware
func NewHVACBusinessMiddleware(
	config *HVACBusinessConfig,
	perfMonitor *common.PerformanceMonitor,
	logger log.Logger,
) *HVACBusinessMiddleware {
	if config == nil {
		config = &HVACBusinessConfig{
			EnableValidation:     true,
			EnableAuditLogging:   true,
			EnableRateLimit:      true,
			RateLimitRPS:         1000,
			EnableCircuitBreaker: true,
			FailureThreshold:     5,
			Timeout:              30 * time.Second,
		}
	}

	middleware := &HVACBusinessMiddleware{
		config:      config,
		log:         log.NewHelper(logger),
		perfMonitor: perfMonitor,
	}

	// Initialize circuit breaker if enabled
	if config.EnableCircuitBreaker {
		middleware.circuitBreaker = NewCircuitBreaker(&CircuitBreakerConfig{
			FailureThreshold: config.FailureThreshold,
			Timeout:          config.Timeout,
		})
	}

	// Initialize rate limiter if enabled
	if config.EnableRateLimit {
		middleware.rateLimiter = NewRateLimiter(&RateLimiterConfig{
			RPS: config.RateLimitRPS,
		})
	}

	return middleware
}

// Middleware returns the HVAC business logic middleware function
func (h *HVACBusinessMiddleware) Middleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			start := time.Now()
			path := h.extractPath(ctx)

			// Rate limiting
			if h.config.EnableRateLimit && h.rateLimiter != nil {
				if !h.rateLimiter.Allow() {
					return nil, errors.New(429, "RATE_LIMIT_EXCEEDED", "Too many requests")
				}
			}

			// Circuit breaker
			if h.config.EnableCircuitBreaker && h.circuitBreaker != nil {
				if !h.circuitBreaker.Allow() {
					return nil, errors.ServiceUnavailable("CIRCUIT_BREAKER_OPEN", "Service temporarily unavailable")
				}
			}

			// HVAC business validation
			if h.config.EnableValidation {
				if err := h.validateHVACBusinessRules(ctx, req); err != nil {
					h.recordFailure()
					return nil, errors.BadRequest("HVAC_VALIDATION_FAILED", err.Error())
				}
			}

			// Execute handler
			resp, err := handler(ctx, req)
			duration := time.Since(start)

			// Record metrics
			if h.perfMonitor != nil {
				statusCode := 200
				if err != nil {
					statusCode = 500
				}
				h.perfMonitor.RecordHTTPRequest(path, statusCode, duration)
			}

			// Audit logging
			if h.config.EnableAuditLogging {
				h.auditLog(ctx, path, req, resp, err, duration)
			}

			// Record success/failure for circuit breaker
			if h.config.EnableCircuitBreaker && h.circuitBreaker != nil {
				if err != nil {
					h.circuitBreaker.RecordFailure()
				} else {
					h.circuitBreaker.RecordSuccess()
				}
			}

			return resp, err
		}
	}
}

// ==========================================
// HVAC PERFORMANCE TRACKING MIDDLEWARE
// ==========================================

// HVACPerformanceMiddleware tracks HVAC-specific performance metrics
type HVACPerformanceMiddleware struct {
	log         *log.Helper
	perfMonitor *common.PerformanceMonitor
	metrics     *HVACMetrics
}

// HVACMetrics tracks HVAC-specific metrics
type HVACMetrics struct {
	CustomerRequests    int64         `json:"customer_requests"`
	TechnicianRequests  int64         `json:"technician_requests"`
	JobRequests         int64         `json:"job_requests"`
	AIRequests          int64         `json:"ai_requests"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	ErrorRate           float64       `json:"error_rate"`
}

// NewHVACPerformanceMiddleware creates a new performance tracking middleware
func NewHVACPerformanceMiddleware(
	perfMonitor *common.PerformanceMonitor,
	logger log.Logger,
) *HVACPerformanceMiddleware {
	return &HVACPerformanceMiddleware{
		log:         log.NewHelper(logger),
		perfMonitor: perfMonitor,
		metrics:     &HVACMetrics{},
	}
}

// Middleware returns the HVAC performance tracking middleware function
func (h *HVACPerformanceMiddleware) Middleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			start := time.Now()
			path := h.extractPath(ctx)

			// Track request type
			h.trackRequestType(path)

			// Execute handler
			resp, err := handler(ctx, req)
			duration := time.Since(start)

			// Update performance metrics
			h.updateMetrics(path, duration, err != nil)

			// Log performance data
			h.log.WithContext(ctx).Infof("HVAC Performance: %s took %v", path, duration)

			return resp, err
		}
	}
}

// ==========================================
// HVAC MIDDLEWARE CHAIN BUILDER
// ==========================================

// HVACMiddlewareChain builds the complete HVAC middleware chain
type HVACMiddlewareChain struct {
	authMiddleware        *HVACAuthMiddleware
	businessMiddleware    *HVACBusinessMiddleware
	performanceMiddleware *HVACPerformanceMiddleware
	log                   *log.Helper
}

// NewHVACMiddlewareChain creates a new HVAC middleware chain
func NewHVACMiddlewareChain(
	authConfig *HVACAuthConfig,
	businessConfig *HVACBusinessConfig,
	perfMonitor *common.PerformanceMonitor,
	logger log.Logger,
) *HVACMiddlewareChain {
	return &HVACMiddlewareChain{
		authMiddleware:        NewHVACAuthMiddleware(authConfig, logger),
		businessMiddleware:    NewHVACBusinessMiddleware(businessConfig, perfMonitor, logger),
		performanceMiddleware: NewHVACPerformanceMiddleware(perfMonitor, logger),
		log:                   log.NewHelper(logger),
	}
}

// BuildChain builds the complete middleware chain
func (h *HVACMiddlewareChain) BuildChain() []middleware.Middleware {
	return []middleware.Middleware{
		h.performanceMiddleware.Middleware(), // First: Performance tracking
		h.authMiddleware.Middleware(),        // Second: Authentication
		h.businessMiddleware.Middleware(),    // Third: Business logic
	}
}

// BuildGRPCChain builds the gRPC-specific middleware chain
func (h *HVACMiddlewareChain) BuildGRPCChain() []middleware.Middleware {
	chain := h.BuildChain()
	// Add gRPC-specific middleware if needed
	return chain
}

// BuildHTTPChain builds the HTTP-specific middleware chain
func (h *HVACMiddlewareChain) BuildHTTPChain() []middleware.Middleware {
	chain := h.BuildChain()
	// Add HTTP-specific middleware if needed
	return chain
}

// ==========================================
// HELPER TYPES AND FUNCTIONS
// ==========================================

// HVACUser represents an HVAC system user
type HVACUser struct {
	ID       int64    `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
	Company  string   `json:"company"`
	IsActive bool     `json:"is_active"`
}

// CircuitBreaker provides circuit breaker functionality
type CircuitBreaker struct {
	config       *CircuitBreakerConfig
	failures     int
	lastFailTime time.Time
	state        CircuitBreakerState
}

type CircuitBreakerConfig struct {
	FailureThreshold int
	Timeout          time.Duration
}

type CircuitBreakerState int

const (
	Closed CircuitBreakerState = iota
	Open
	HalfOpen
)

// RateLimiter provides rate limiting functionality
type RateLimiter struct {
	config *RateLimiterConfig
	// Implementation would use token bucket or similar algorithm
}

type RateLimiterConfig struct {
	RPS int
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(config *CircuitBreakerConfig) *CircuitBreaker {
	return &CircuitBreaker{
		config: config,
		state:  Closed,
	}
}

// Allow checks if the circuit breaker allows the request
func (cb *CircuitBreaker) Allow() bool {
	switch cb.state {
	case Closed:
		return true
	case Open:
		if time.Since(cb.lastFailTime) > cb.config.Timeout {
			cb.state = HalfOpen
			return true
		}
		return false
	case HalfOpen:
		return true
	}
	return false
}

// RecordSuccess records a successful operation
func (cb *CircuitBreaker) RecordSuccess() {
	cb.failures = 0
	cb.state = Closed
}

// RecordFailure records a failed operation
func (cb *CircuitBreaker) RecordFailure() {
	cb.failures++
	cb.lastFailTime = time.Now()
	if cb.failures >= cb.config.FailureThreshold {
		cb.state = Open
	}
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(config *RateLimiterConfig) *RateLimiter {
	return &RateLimiter{
		config: config,
	}
}

// Allow checks if the rate limiter allows the request
func (rl *RateLimiter) Allow() bool {
	// Simplified implementation - in production, use proper token bucket
	return true
}

// ==========================================
// HELPER METHODS
// ==========================================

func (h *HVACAuthMiddleware) extractPath(ctx context.Context) string {
	if tr, ok := transport.FromServerContext(ctx); ok {
		return tr.Operation()
	}
	return "unknown"
}

func (h *HVACAuthMiddleware) shouldBypass(path string) bool {
	for _, bypassPath := range h.config.BypassPaths {
		if strings.HasPrefix(path, bypassPath) {
			return true
		}
	}
	return false
}

func (h *HVACAuthMiddleware) validateHVACAuth(ctx context.Context) (*HVACUser, error) {
	// Simplified implementation - in production, validate JWT token
	return &HVACUser{
		ID:       1,
		Username: "hvac_user",
		Email:    "<EMAIL>",
		Roles:    []string{"user"},
		Company:  "HVAC Company",
		IsActive: true,
	}, nil
}

func (h *HVACAuthMiddleware) checkHVACPermissions(ctx context.Context, user *HVACUser, path string) error {
	// Simplified permission check
	if strings.Contains(path, "/admin/") {
		for _, role := range user.Roles {
			for _, adminRole := range h.config.AdminRoles {
				if role == adminRole {
					return nil
				}
			}
		}
		return fmt.Errorf("admin access required")
	}
	return nil
}

func (h *HVACAuthMiddleware) enrichContextWithHVACUser(ctx context.Context, user *HVACUser) context.Context {
	return context.WithValue(ctx, "hvac_user", user)
}

func (h *HVACBusinessMiddleware) extractPath(ctx context.Context) string {
	if tr, ok := transport.FromServerContext(ctx); ok {
		return tr.Operation()
	}
	return "unknown"
}

func (h *HVACBusinessMiddleware) validateHVACBusinessRules(ctx context.Context, req interface{}) error {
	// Implement HVAC-specific business validation
	return nil
}

func (h *HVACBusinessMiddleware) recordFailure() {
	// Record failure for metrics
}

func (h *HVACBusinessMiddleware) auditLog(ctx context.Context, path string, req, resp interface{}, err error, duration time.Duration) {
	status := "SUCCESS"
	if err != nil {
		status = "ERROR"
	}
	h.log.WithContext(ctx).Infof("HVAC Audit: %s %s in %v", path, status, duration)
}

func (h *HVACPerformanceMiddleware) extractPath(ctx context.Context) string {
	if tr, ok := transport.FromServerContext(ctx); ok {
		return tr.Operation()
	}
	return "unknown"
}

func (h *HVACPerformanceMiddleware) trackRequestType(path string) {
	if strings.Contains(path, "customer") {
		h.metrics.CustomerRequests++
	} else if strings.Contains(path, "technician") {
		h.metrics.TechnicianRequests++
	} else if strings.Contains(path, "job") {
		h.metrics.JobRequests++
	} else if strings.Contains(path, "ai") {
		h.metrics.AIRequests++
	}
}

func (h *HVACPerformanceMiddleware) updateMetrics(path string, duration time.Duration, hasError bool) {
	// Update average response time and error rate
	// Simplified implementation
}
