package server

import (
	"context"
	"io"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gobackend-hvac-kratos/internal/biz"
)

// Test structures for validation
type TestCustomerRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"required,min=10"`
	Type     string `json:"type" validate:"required,customer_type"`
	Priority string `json:"priority" validate:"omitempty,hvac_priority"`
}

type TestJobRequest struct {
	Title       string `json:"title" validate:"required,min=5,max=200"`
	Description string `json:"description" validate:"required,min=10"`
	Priority    string `json:"priority" validate:"required,hvac_priority"`
	Status      string `json:"status" validate:"required,hvac_status"`
	ServiceType string `json:"service_type" validate:"required,service_type"`
	CustomerID  int    `json:"customer_id" validate:"required,min=1"`
}

func TestValidationService_ValidateStruct(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	validator := NewValidationService(logger)
	ctx := context.Background()

	t.Run("Valid_Customer_Request", func(t *testing.T) {
		req := &TestCustomerRequest{
			Name:     "John Doe",
			Email:    "<EMAIL>",
			Phone:    "1234567890",
			Type:     "residential",
			Priority: "medium",
		}

		err := validator.ValidateStruct(ctx, req)
		assert.Nil(t, err)
	})

	t.Run("Invalid_Customer_Request_Missing_Required", func(t *testing.T) {
		req := &TestCustomerRequest{
			Name:  "",              // Missing required field
			Email: "invalid-email", // Invalid email format
			Phone: "123",           // Too short
			Type:  "invalid_type",  // Invalid customer type
		}

		err := validator.ValidateStruct(ctx, req)
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeValidationFailed, err.Code)
		assert.Contains(t, err.Message, "Validation failed")

		// Check that error data contains field errors
		data, ok := err.Data.(map[string]interface{})
		require.True(t, ok)

		fieldErrors, ok := data["field_errors"].([]map[string]string)
		require.True(t, ok)
		assert.Greater(t, len(fieldErrors), 0)
	})

	t.Run("Valid_Job_Request", func(t *testing.T) {
		req := &TestJobRequest{
			Title:       "HVAC System Installation",
			Description: "Install new HVAC system in residential building",
			Priority:    "high",
			Status:      "pending",
			ServiceType: "installation",
			CustomerID:  123,
		}

		err := validator.ValidateStruct(ctx, req)
		assert.Nil(t, err)
	})

	t.Run("Invalid_Job_Request", func(t *testing.T) {
		req := &TestJobRequest{
			Title:       "Bad",     // Too short
			Description: "Short",   // Too short
			Priority:    "invalid", // Invalid priority
			Status:      "invalid", // Invalid status
			ServiceType: "invalid", // Invalid service type
			CustomerID:  0,         // Invalid customer ID
		}

		err := validator.ValidateStruct(ctx, req)
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeValidationFailed, err.Code)
	})

	t.Run("Nil_Input", func(t *testing.T) {
		err := validator.ValidateStruct(ctx, nil)
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeInvalidParams, err.Code)
		assert.Contains(t, err.Message, "Input cannot be nil")
	})
}

func TestValidationService_ValidateVar(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	validator := NewValidationService(logger)
	ctx := context.Background()

	t.Run("Valid_Email", func(t *testing.T) {
		email := "<EMAIL>"
		err := validator.ValidateVar(ctx, email, "email")
		assert.Nil(t, err)
	})

	t.Run("Invalid_Email", func(t *testing.T) {
		email := "invalid-email"
		err := validator.ValidateVar(ctx, email, "email")
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeValidationFailed, err.Code)
	})

	t.Run("Valid_HVAC_Priority", func(t *testing.T) {
		priority := "urgent"
		err := validator.ValidateVar(ctx, priority, "hvac_priority")
		assert.Nil(t, err)
	})

	t.Run("Invalid_HVAC_Priority", func(t *testing.T) {
		priority := "invalid"
		err := validator.ValidateVar(ctx, priority, "hvac_priority")
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeValidationFailed, err.Code)
	})
}

func TestValidationService_BatchValidate(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	validator := NewValidationService(logger)
	ctx := context.Background()

	t.Run("Mixed_Valid_Invalid", func(t *testing.T) {
		validReq := &TestCustomerRequest{
			Name:  "John Doe",
			Email: "<EMAIL>",
			Phone: "1234567890",
			Type:  "residential",
		}

		invalidReq := &TestCustomerRequest{
			Name:  "", // Missing required
			Email: "invalid",
			Phone: "123",
			Type:  "invalid",
		}

		errors := validator.BatchValidate(ctx, validReq, invalidReq)
		assert.Len(t, errors, 1) // Only one error (for invalid request)

		// Check that the error has batch_index context
		err := errors[0]
		assert.NotNil(t, err.Context)
		assert.Equal(t, 1, err.Context["batch_index"]) // Second item (index 1)
	})

	t.Run("All_Valid", func(t *testing.T) {
		req1 := &TestCustomerRequest{
			Name:  "John Doe",
			Email: "<EMAIL>",
			Phone: "1234567890",
			Type:  "residential",
		}

		req2 := &TestCustomerRequest{
			Name:  "Jane Smith",
			Email: "<EMAIL>",
			Phone: "0987654321",
			Type:  "commercial",
		}

		errors := validator.BatchValidate(ctx, req1, req2)
		assert.Len(t, errors, 0) // No errors
	})
}

func TestCustomValidationRules(t *testing.T) {
	t.Run("HVAC_Priority_Validation", func(t *testing.T) {
		validPriorities := []string{"low", "medium", "high", "urgent"}
		invalidPriorities := []string{"", "invalid", "URGENT", "Low"}

		for _, priority := range validPriorities {
			t.Run("Valid_"+priority, func(t *testing.T) {
				// This would test the validation function directly
				// In a real implementation, you'd test the validator function
				assert.Contains(t, []string{"low", "medium", "high", "urgent"}, priority)
			})
		}

		for _, priority := range invalidPriorities {
			t.Run("Invalid_"+priority, func(t *testing.T) {
				assert.NotContains(t, []string{"low", "medium", "high", "urgent"}, priority)
			})
		}
	})

	t.Run("Customer_Type_Validation", func(t *testing.T) {
		validTypes := []string{"residential", "commercial", "industrial"}
		invalidTypes := []string{"", "invalid", "RESIDENTIAL", "home"}

		for _, customerType := range validTypes {
			t.Run("Valid_"+customerType, func(t *testing.T) {
				assert.Contains(t, []string{"residential", "commercial", "industrial"}, customerType)
			})
		}

		for _, customerType := range invalidTypes {
			t.Run("Invalid_"+customerType, func(t *testing.T) {
				assert.NotContains(t, []string{"residential", "commercial", "industrial"}, customerType)
			})
		}
	})
}

func TestValidationService_ErrorMessages(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	validator := NewValidationService(logger)

	// Test that error messages are human-readable
	t.Run("Human_Readable_Messages", func(t *testing.T) {
		req := &TestCustomerRequest{
			Name:  "A", // Too short (min=2)
			Email: "invalid-email",
			Phone: "123", // Too short (min=10)
			Type:  "invalid_type",
		}

		err := validator.ValidateStruct(context.Background(), req)
		require.NotNil(t, err)

		data, ok := err.Data.(map[string]interface{})
		require.True(t, ok)

		fieldErrors, ok := data["field_errors"].([]map[string]string)
		require.True(t, ok)

		// Check that error messages are descriptive
		for _, fieldError := range fieldErrors {
			message := fieldError["message"]
			assert.NotEmpty(t, message)
			assert.NotContains(t, message, "Key:") // Should not contain raw validator output
		}
	})
}
