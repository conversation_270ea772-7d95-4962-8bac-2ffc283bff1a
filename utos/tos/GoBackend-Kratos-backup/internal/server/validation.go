package server

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"

	"gobackend-hvac-kratos/internal/biz"
)

// 🔍 Enhanced Validation Service - Comprehensive input validation with custom rules
type ValidationService struct {
	validator *validator.Validate
	logger    *log.Helper
}

// NewValidationService creates a new validation service with custom rules
func NewValidationService(logger log.Logger) *ValidationService {
	validate := validator.New()

	// Register custom validation rules
	validate.RegisterValidation("hvac_priority", validateHVACPriority)
	validate.RegisterValidation("hvac_status", validateHVACStatus)
	validate.RegisterValidation("customer_type", validateCustomerType)
	validate.RegisterValidation("service_type", validateServiceType)

	// Use JSON tag names for field names in error messages
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	return &ValidationService{
		validator: validate,
		logger:    log.NewHelper(logger),
	}
}

// ValidateStruct validates a struct and returns detailed error information
func (v *ValidationService) ValidateStruct(ctx context.Context, s interface{}) *biz.CustomError {
	if s == nil {
		return biz.NewCustomError(biz.ErrCodeInvalidParams, "Input cannot be nil", nil)
	}

	err := v.validator.Struct(s)
	if err == nil {
		return nil
	}

	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		v.logger.WithContext(ctx).Errorf("Unexpected validation error type: %T", err)
		return biz.NewCustomError(biz.ErrCodeInternalError, "Validation error", err.Error())
	}

	// Convert validation errors to detailed error response
	errorDetails := make(map[string]interface{})
	fieldErrors := make([]map[string]string, 0)

	for _, fieldError := range validationErrors {
		fieldErr := map[string]string{
			"field":   fieldError.Field(),
			"tag":     fieldError.Tag(),
			"value":   fmt.Sprintf("%v", fieldError.Value()),
			"message": v.getErrorMessage(fieldError),
		}
		fieldErrors = append(fieldErrors, fieldErr)

		// Also add to errorDetails for easy access
		errorDetails[fieldError.Field()] = fieldErr["message"]
	}

	customErr := biz.NewCustomError(biz.ErrCodeValidationFailed, "Validation failed", map[string]interface{}{
		"field_errors": fieldErrors,
		"error_count":  len(fieldErrors),
	})

	v.logger.WithContext(ctx).Warnf("Validation failed for %T: %d errors", s, len(fieldErrors))

	return customErr
}

// ValidateVar validates a single variable
func (v *ValidationService) ValidateVar(ctx context.Context, field interface{}, tag string) *biz.CustomError {
	err := v.validator.Var(field, tag)
	if err == nil {
		return nil
	}

	return biz.NewCustomError(biz.ErrCodeValidationFailed, "Field validation failed", err.Error())
}

// getErrorMessage returns a human-readable error message for validation errors
func (v *ValidationService) getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fe.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fe.Field(), fe.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", fe.Field(), fe.Param())
	case "numeric":
		return fmt.Sprintf("%s must be numeric", fe.Field())
	case "alpha":
		return fmt.Sprintf("%s must contain only letters", fe.Field())
	case "alphanum":
		return fmt.Sprintf("%s must contain only letters and numbers", fe.Field())
	case "url":
		return fmt.Sprintf("%s must be a valid URL", fe.Field())
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", fe.Field())
	case "hvac_priority":
		return fmt.Sprintf("%s must be one of: low, medium, high, urgent", fe.Field())
	case "hvac_status":
		return fmt.Sprintf("%s must be one of: pending, in_progress, completed, cancelled", fe.Field())
	case "customer_type":
		return fmt.Sprintf("%s must be one of: residential, commercial, industrial", fe.Field())
	case "service_type":
		return fmt.Sprintf("%s must be one of: installation, repair, maintenance, inspection", fe.Field())
	default:
		return fmt.Sprintf("%s is invalid", fe.Field())
	}
}

// Custom validation functions for HVAC-specific fields
func validateHVACPriority(fl validator.FieldLevel) bool {
	priority := fl.Field().String()
	validPriorities := map[string]bool{
		"low":    true,
		"medium": true,
		"high":   true,
		"urgent": true,
	}
	return validPriorities[priority]
}

func validateHVACStatus(fl validator.FieldLevel) bool {
	status := fl.Field().String()
	validStatuses := map[string]bool{
		"pending":     true,
		"in_progress": true,
		"completed":   true,
		"cancelled":   true,
	}
	return validStatuses[status]
}

func validateCustomerType(fl validator.FieldLevel) bool {
	customerType := fl.Field().String()
	validTypes := map[string]bool{
		"residential": true,
		"commercial":  true,
		"industrial":  true,
	}
	return validTypes[customerType]
}

func validateServiceType(fl validator.FieldLevel) bool {
	serviceType := fl.Field().String()
	validTypes := map[string]bool{
		"installation": true,
		"repair":       true,
		"maintenance":  true,
		"inspection":   true,
	}
	return validTypes[serviceType]
}

// ValidationMiddleware provides middleware for request validation
func (v *ValidationService) ValidationMiddleware() func(interface{}) *biz.CustomError {
	return func(req interface{}) *biz.CustomError {
		return v.ValidateStruct(context.Background(), req)
	}
}

// BatchValidate validates multiple structs and returns all errors
func (v *ValidationService) BatchValidate(ctx context.Context, structs ...interface{}) []*biz.CustomError {
	var errors []*biz.CustomError

	for i, s := range structs {
		if err := v.ValidateStruct(ctx, s); err != nil {
			err.WithContext("batch_index", i)
			errors = append(errors, err)
		}
	}

	return errors
}
