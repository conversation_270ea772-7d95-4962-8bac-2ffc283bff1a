package customer

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
)

// 👤 Customer Intelligence Service - Complete Customer Profile Management
type CustomerIntelligenceService struct {
	log     *log.Helper
	db      *gorm.DB
	gemma3  *ai.Gemma3Service
	matcher *CustomerMatcher
	config  *CustomerConfig
}

// ⚙️ Customer Service Configuration
type CustomerConfig struct {
	AutoCreateCustomers    bool    `yaml:"auto_create_customers"`
	MatchingThreshold     float64 `yaml:"matching_threshold"`
	EnableAIEnrichment    bool    `yaml:"enable_ai_enrichment"`
	DefaultSatisfaction   float64 `yaml:"default_satisfaction"`
	AnalyticsUpdateFreq   string  `yaml:"analytics_update_frequency"`
}

// 🔍 Customer Matching Criteria
type MatchCriteria struct {
	Email       string
	Phone       string
	Name        string
	Company     string
	Address     string
	Content     string // email/call content for context
}

// 📊 Customer Profile with Analytics
type CustomerProfile struct {
	Customer     *Customer              `json:"customer"`
	Analytics    *CustomerAnalytics     `json:"analytics"`
	Interactions []*CustomerInteraction `json:"recent_interactions"`
	Timeline     []*InteractionSummary  `json:"timeline"`
	Insights     *CustomerInsights      `json:"insights"`
	Predictions  *CustomerPredictions   `json:"predictions"`
}

// 📈 Customer Insights
type CustomerInsights struct {
	PreferredContactTime    string    `json:"preferred_contact_time"`
	CommunicationStyle      string    `json:"communication_style"`
	TechnicalKnowledge      string    `json:"technical_knowledge"`
	ServiceHistory          []string  `json:"service_history"`
	PainPoints             []string  `json:"pain_points"`
	SatisfactionTrends     []float64 `json:"satisfaction_trends"`
	BusinessPotential      string    `json:"business_potential"`
	ChurnRisk              float64   `json:"churn_risk"`
	NextBestAction         string    `json:"next_best_action"`
}

// 🔮 Customer Predictions
type CustomerPredictions struct {
	ChurnProbability       float64   `json:"churn_probability"`
	LifetimeValuePredicted float64   `json:"lifetime_value_predicted"`
	NextContactPredicted   time.Time `json:"next_contact_predicted"`
	ServiceNeedsPredicted  []string  `json:"service_needs_predicted"`
	UpsellOpportunities    []string  `json:"upsell_opportunities"`
}

// 📝 Interaction Summary for Timeline
type InteractionSummary struct {
	ID              uuid.UUID `json:"id"`
	Type            string    `json:"type"`
	Direction       string    `json:"direction"`
	Timestamp       time.Time `json:"timestamp"`
	Subject         string    `json:"subject"`
	Summary         string    `json:"summary"`
	Sentiment       string    `json:"sentiment"`
	Priority        string    `json:"priority"`
	ResolutionTime  *string   `json:"resolution_time,omitempty"`
	Satisfaction    *int      `json:"satisfaction,omitempty"`
}

// NewCustomerIntelligenceService creates a new customer intelligence service
func NewCustomerIntelligenceService(
	db *gorm.DB,
	gemma3 *ai.Gemma3Service,
	config *CustomerConfig,
	logger log.Logger,
) *CustomerIntelligenceService {
	log := log.NewHelper(logger)
	
	matcher := NewCustomerMatcher(db, gemma3, logger)
	
	return &CustomerIntelligenceService{
		log:     log,
		db:      db,
		gemma3:  gemma3,
		matcher: matcher,
		config:  config,
	}
}

// 🔍 Find or Create Customer from Email/Phone
func (s *CustomerIntelligenceService) FindOrCreateCustomer(
	ctx context.Context,
	criteria *MatchCriteria,
) (*Customer, error) {
	s.log.WithContext(ctx).Infof("Finding or creating customer: %s / %s", criteria.Email, criteria.Phone)
	
	// Try to find existing customer
	customer, confidence, err := s.matcher.FindCustomer(ctx, criteria)
	if err != nil {
		return nil, fmt.Errorf("failed to search for customer: %w", err)
	}
	
	// If found with high confidence, return existing
	if customer != nil && confidence >= s.config.MatchingThreshold {
		s.log.WithContext(ctx).Infof("Found existing customer: %s (confidence: %.2f)", customer.Name, confidence)
		return customer, nil
	}
	
	// Create new customer if auto-creation is enabled
	if s.config.AutoCreateCustomers {
		newCustomer := &Customer{
			ID:                      uuid.New(),
			Email:                   criteria.Email,
			PrimaryPhone:           criteria.Phone,
			Name:                   criteria.Name,
			Company:                criteria.Company,
			CustomerType:           s.inferCustomerType(criteria),
			CommunicationPreference: "email",
			KnowledgeLevel:         "basic",
			SatisfactionScore:      s.config.DefaultSatisfaction,
			Status:                 "active",
		}
		
		// Parse address if provided
		if criteria.Address != "" {
			newCustomer.Address = map[string]interface{}{
				"raw": criteria.Address,
			}
		}
		
		// Save to database
		if err := s.db.Create(newCustomer).Error; err != nil {
			return nil, fmt.Errorf("failed to create customer: %w", err)
		}
		
		// Create phone and email records
		if criteria.Phone != "" {
			phone := &CustomerPhone{
				ID:          uuid.New(),
				CustomerID:  newCustomer.ID,
				PhoneNumber: criteria.Phone,
				PhoneType:   "mobile",
				IsPrimary:   true,
			}
			s.db.Create(phone)
		}
		
		if criteria.Email != "" {
			email := &CustomerEmail{
				ID:           uuid.New(),
				CustomerID:   newCustomer.ID,
				EmailAddress: criteria.Email,
				EmailType:    "primary",
				IsPrimary:    true,
			}
			s.db.Create(email)
		}
		
		// Initialize analytics
		analytics := &CustomerAnalytics{
			CustomerID: newCustomer.ID,
		}
		s.db.Create(analytics)
		
		s.log.WithContext(ctx).Infof("Created new customer: %s", newCustomer.Name)
		return newCustomer, nil
	}
	
	return nil, fmt.Errorf("customer not found and auto-creation disabled")
}

// 🔗 Link Interaction to Customer
func (s *CustomerIntelligenceService) LinkInteraction(
	ctx context.Context,
	customerID uuid.UUID,
	interaction *CustomerInteraction,
) error {
	s.log.WithContext(ctx).Infof("Linking interaction to customer: %s", customerID)
	
	// Set customer ID
	interaction.CustomerID = customerID
	
	// Save interaction
	if err := s.db.Create(interaction).Error; err != nil {
		return fmt.Errorf("failed to save interaction: %w", err)
	}
	
	// Update customer last contact date
	if err := s.db.Model(&Customer{}).Where("id = ?", customerID).
		Update("last_contact_date", interaction.Timestamp).Error; err != nil {
		s.log.WithContext(ctx).Warnf("Failed to update last contact date: %v", err)
	}
	
	// Update analytics asynchronously
	go s.updateCustomerAnalytics(context.Background(), customerID)
	
	return nil
}

// 📊 Build Comprehensive Customer Profile
func (s *CustomerIntelligenceService) BuildCustomerProfile(
	ctx context.Context,
	customerID uuid.UUID,
) (*CustomerProfile, error) {
	s.log.WithContext(ctx).Infof("Building customer profile: %s", customerID)
	
	// Load customer with relations
	var customer Customer
	if err := s.db.Preload("Phones").Preload("Emails").Preload("Analytics").
		First(&customer, customerID).Error; err != nil {
		return nil, fmt.Errorf("failed to load customer: %w", err)
	}
	
	// Load recent interactions (last 30 days)
	var interactions []*CustomerInteraction
	if err := s.db.Where("customer_id = ? AND timestamp >= ?", customerID, 
		time.Now().AddDate(0, 0, -30)).
		Order("timestamp DESC").Limit(50).Find(&interactions).Error; err != nil {
		return nil, fmt.Errorf("failed to load interactions: %w", err)
	}
	
	// Build timeline
	timeline := s.buildInteractionTimeline(interactions)
	
	// Generate insights using AI
	insights, err := s.generateCustomerInsights(ctx, &customer, interactions)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to generate insights: %v", err)
		insights = &CustomerInsights{} // Empty insights on error
	}
	
	// Generate predictions
	predictions, err := s.generateCustomerPredictions(ctx, &customer, interactions)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to generate predictions: %v", err)
		predictions = &CustomerPredictions{} // Empty predictions on error
	}
	
	profile := &CustomerProfile{
		Customer:     &customer,
		Analytics:    customer.Analytics,
		Interactions: interactions,
		Timeline:     timeline,
		Insights:     insights,
		Predictions:  predictions,
	}
	
	return profile, nil
}

// 🧠 Generate Customer Insights using Gemma 3
func (s *CustomerIntelligenceService) generateCustomerInsights(
	ctx context.Context,
	customer *Customer,
	interactions []*CustomerInteraction,
) (*CustomerInsights, error) {
	if !s.config.EnableAIEnrichment {
		return &CustomerInsights{}, nil
	}
	
	// Build context for AI analysis
	var contentBuilder strings.Builder
	contentBuilder.WriteString(fmt.Sprintf("Customer Profile Analysis for: %s\n", customer.Name))
	contentBuilder.WriteString(fmt.Sprintf("Customer Type: %s\n", customer.CustomerType))
	contentBuilder.WriteString(fmt.Sprintf("Company: %s\n", customer.Company))
	contentBuilder.WriteString(fmt.Sprintf("Current Satisfaction: %.2f\n\n", customer.SatisfactionScore))
	
	contentBuilder.WriteString("Recent Interactions:\n")
	for _, interaction := range interactions {
		contentBuilder.WriteString(fmt.Sprintf("- %s (%s): %s\n", 
			interaction.InteractionType, interaction.Timestamp.Format("2006-01-02"), interaction.Subject))
		if interaction.Summary != "" {
			contentBuilder.WriteString(fmt.Sprintf("  Summary: %s\n", interaction.Summary))
		}
	}
	
	// Prepare Gemma 3 request for customer insights
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: contentBuilder.String(),
		Subject:      "Customer Profile Analysis",
		AnalysisType: "customer_insights",
		HVACContext: &ai.HVACContextData{
			CustomerHistory: []string{customer.Company, customer.CustomerType},
		},
	}
	
	// Get AI analysis
	response, err := s.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze customer profile: %w", err)
	}
	
	// Extract insights from AI response
	insights := &CustomerInsights{
		CommunicationStyle:  customer.CommunicationPreference,
		TechnicalKnowledge:  customer.KnowledgeLevel,
		BusinessPotential:   "medium", // Default
		ChurnRisk:          0.3,       // Default
	}
	
	// Map AI analysis to insights
	if response.CustomerAnalysis != nil {
		insights.CommunicationStyle = response.CustomerAnalysis.CommunicationStyle
		insights.TechnicalKnowledge = response.CustomerAnalysis.KnowledgeLevel
	}
	
	if response.BusinessInsights != nil {
		insights.BusinessPotential = response.BusinessInsights.RevenueOpportunity
		insights.NextBestAction = strings.Join(response.ActionPlan.ImmediateActions, "; ")
	}
	
	return insights, nil
}

// 🔮 Generate Customer Predictions
func (s *CustomerIntelligenceService) generateCustomerPredictions(
	ctx context.Context,
	customer *Customer,
	interactions []*CustomerInteraction,
) (*CustomerPredictions, error) {
	// Simple prediction logic - can be enhanced with ML models
	predictions := &CustomerPredictions{
		ChurnProbability:       s.calculateChurnProbability(customer, interactions),
		LifetimeValuePredicted: customer.LifetimeValue * 1.2, // Simple growth prediction
		NextContactPredicted:   s.predictNextContact(interactions),
	}
	
	// Predict service needs based on interaction patterns
	serviceNeeds := s.predictServiceNeeds(interactions)
	predictions.ServiceNeedsPredicted = serviceNeeds
	
	// Predict upsell opportunities
	upsellOpps := s.predictUpsellOpportunities(customer, interactions)
	predictions.UpsellOpportunities = upsellOpps
	
	return predictions, nil
}

// 📈 Build Interaction Timeline
func (s *CustomerIntelligenceService) buildInteractionTimeline(
	interactions []*CustomerInteraction,
) []*InteractionSummary {
	timeline := make([]*InteractionSummary, len(interactions))
	
	for i, interaction := range interactions {
		summary := &InteractionSummary{
			ID:        interaction.ID,
			Type:      interaction.InteractionType,
			Direction: interaction.Direction,
			Timestamp: interaction.Timestamp,
			Subject:   interaction.Subject,
			Summary:   interaction.Summary,
			Sentiment: interaction.Sentiment,
			Priority:  interaction.PriorityLevel,
		}
		
		if interaction.ResolutionTime != nil {
			resTime := interaction.ResolutionTime.String()
			summary.ResolutionTime = &resTime
		}
		
		summary.Satisfaction = interaction.CustomerSatisfaction
		timeline[i] = summary
	}
	
	return timeline
}

// 🔄 Update Customer Analytics
func (s *CustomerIntelligenceService) updateCustomerAnalytics(
	ctx context.Context,
	customerID uuid.UUID,
) {
	s.log.WithContext(ctx).Infof("Updating analytics for customer: %s", customerID)
	
	// Calculate interaction counts
	var counts struct {
		Total       int64
		Email       int64
		PhoneCall   int64
		ServiceVisit int64
	}
	
	s.db.Model(&CustomerInteraction{}).Where("customer_id = ?", customerID).Count(&counts.Total)
	s.db.Model(&CustomerInteraction{}).Where("customer_id = ? AND interaction_type = ?", customerID, "email").Count(&counts.Email)
	s.db.Model(&CustomerInteraction{}).Where("customer_id = ? AND interaction_type = ?", customerID, "phone_call").Count(&counts.PhoneCall)
	s.db.Model(&CustomerInteraction{}).Where("customer_id = ? AND interaction_type = ?", customerID, "service_visit").Count(&counts.ServiceVisit)
	
	// Calculate satisfaction metrics
	var avgSatisfaction float64
	s.db.Model(&CustomerInteraction{}).Where("customer_id = ? AND customer_satisfaction IS NOT NULL", customerID).
		Select("AVG(customer_satisfaction)").Scan(&avgSatisfaction)
	
	// Update analytics record
	analytics := &CustomerAnalytics{
		CustomerID:        customerID,
		TotalInteractions: int(counts.Total),
		EmailCount:       int(counts.Email),
		PhoneCallCount:   int(counts.PhoneCall),
		ServiceVisitCount: int(counts.ServiceVisit),
		AvgSatisfaction:  avgSatisfaction,
		LastCalculated:   time.Now(),
	}
	
	// Upsert analytics
	s.db.Save(analytics)
}

// 🎯 Helper Methods for Predictions

func (s *CustomerIntelligenceService) inferCustomerType(criteria *MatchCriteria) string {
	if criteria.Company != "" {
		return "commercial"
	}
	return "residential"
}

func (s *CustomerIntelligenceService) calculateChurnProbability(
	customer *Customer,
	interactions []*CustomerInteraction,
) float64 {
	// Simple churn calculation based on recency and satisfaction
	if len(interactions) == 0 {
		return 0.5 // Unknown
	}
	
	lastInteraction := interactions[0].Timestamp
	daysSinceLastContact := time.Since(lastInteraction).Hours() / 24
	
	churnRisk := daysSinceLastContact / 90.0 // Risk increases over 90 days
	if customer.SatisfactionScore < 3.0 {
		churnRisk += 0.3 // Higher risk for unsatisfied customers
	}
	
	if churnRisk > 1.0 {
		churnRisk = 1.0
	}
	
	return churnRisk
}

func (s *CustomerIntelligenceService) predictNextContact(
	interactions []*CustomerInteraction,
) time.Time {
	if len(interactions) < 2 {
		return time.Now().AddDate(0, 0, 30) // Default 30 days
	}
	
	// Calculate average time between interactions
	var totalDays float64
	for i := 1; i < len(interactions); i++ {
		days := interactions[i-1].Timestamp.Sub(interactions[i].Timestamp).Hours() / 24
		totalDays += days
	}
	
	avgDays := totalDays / float64(len(interactions)-1)
	return time.Now().AddDate(0, 0, int(avgDays))
}

func (s *CustomerIntelligenceService) predictServiceNeeds(
	interactions []*CustomerInteraction,
) []string {
	needs := []string{}
	
	// Analyze interaction patterns
	for _, interaction := range interactions {
		if interaction.HVACRelevance && interaction.ServiceCategory != "" {
			needs = append(needs, interaction.ServiceCategory)
		}
	}
	
	// Remove duplicates and return unique needs
	uniqueNeeds := make(map[string]bool)
	result := []string{}
	for _, need := range needs {
		if !uniqueNeeds[need] {
			uniqueNeeds[need] = true
			result = append(result, need)
		}
	}
	
	return result
}

func (s *CustomerIntelligenceService) predictUpsellOpportunities(
	customer *Customer,
	interactions []*CustomerInteraction,
) []string {
	opportunities := []string{}
	
	// Based on customer type and interaction history
	if customer.CustomerType == "commercial" {
		opportunities = append(opportunities, "maintenance_contract", "system_upgrade")
	}
	
	if customer.SatisfactionScore >= 4.0 {
		opportunities = append(opportunities, "referral_program", "premium_service")
	}
	
	return opportunities
}
