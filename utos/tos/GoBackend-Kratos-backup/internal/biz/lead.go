package biz

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/entity" // Import the new entity package

	"github.com/go-kratos/kratos/v2/log"
)

// LeadRepo defines the interface for lead data operations
type LeadRepo interface {
	CreateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error)
	GetLead(ctx context.Context, id int64) (*entity.Lead, error)
	ListLeads(ctx context.Context, page, pageSize int32, status, source string) ([]*entity.Lead, int32, error)
	UpdateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error)
	DeleteLead(ctx context.Context, id int64) error
	// Additional methods for import/export, deduplication
	SaveLeads(ctx context.Context, leads []*entity.Lead) ([]*entity.Lead, error)       // For bulk import
	FindDuplicateLeads(ctx context.Context, criteria []string) ([]*entity.Lead, error) // For deduplication
}

// LeadUsecase encapsulates lead business logic
type LeadUsecase struct {
	repo LeadRepo
	log  *log.Helper
}

// NewLeadUsecase creates a new lead usecase
func NewLeadUsecase(repo LeadRepo, logger log.Logger) *LeadUsecase {
	return &LeadUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateLead creates a new lead with validation
func (uc *LeadUsecase) CreateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Creating lead: %s", lead.Name)

	// Business logic validation
	if lead.Name == "" {
		return nil, ErrLeadNameRequired
	}
	if lead.Email == "" && lead.Phone == "" {
		return nil, ErrLeadContactRequired
	}

	// Set timestamps
	now := time.Now()
	lead.CreatedAt = now
	lead.UpdatedAt = now

	return uc.repo.CreateLead(ctx, lead)
}

// GetLead retrieves a lead by ID
func (uc *LeadUsecase) GetLead(ctx context.Context, id int64) (*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Getting lead: %d", id)

	if id <= 0 {
		return nil, ErrInvalidLeadID
	}

	return uc.repo.GetLead(ctx, id)
}

// ListLeads retrieves leads with pagination and filters
func (uc *LeadUsecase) ListLeads(ctx context.Context, page, pageSize int32, status, source string) ([]*entity.Lead, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing leads: page=%d, size=%d, status=%s, source=%s", page, pageSize, status, source)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListLeads(ctx, page, pageSize, status, source)
}

// UpdateLead updates an existing lead
func (uc *LeadUsecase) UpdateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Updating lead: %d", lead.ID)

	if lead.ID <= 0 {
		return nil, ErrInvalidLeadID
	}

	// Update timestamp
	lead.UpdatedAt = time.Now()

	return uc.repo.UpdateLead(ctx, lead)
}

// DeleteLead deletes a lead by ID
func (uc *LeadUsecase) DeleteLead(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting lead: %d", id)

	if id <= 0 {
		return ErrInvalidLeadID
	}

	return uc.repo.DeleteLead(ctx, id)
}

// ImportLeads handles bulk import of leads
func (uc *LeadUsecase) ImportLeads(ctx context.Context, leads []*entity.Lead) (importedCount, failedCount int32, errors []string, err error) {
	uc.log.WithContext(ctx).Infof("Importing %d leads", len(leads))

	// TODO: Implement actual parsing and validation logic here
	// For now, a simple save
	savedLeads, saveErr := uc.repo.SaveLeads(ctx, leads)
	if saveErr != nil {
		return 0, int32(len(leads)), []string{saveErr.Error()}, saveErr
	}
	importedCount = int32(len(savedLeads))
	failedCount = int32(len(leads)) - importedCount
	// Populate errors if any failed

	return importedCount, failedCount, errors, nil
}

// ExportLeads handles bulk export of leads
func (uc *LeadUsecase) ExportLeads(ctx context.Context, status, source string) ([]*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Exporting leads with status=%s, source=%s", status, source)
	// TODO: Implement actual export logic, e.g., fetching all leads and formatting
	leads, _, err := uc.repo.ListLeads(ctx, 1, 99999, status, source) // Fetch all matching leads
	if err != nil {
		return nil, err
	}
	return leads, nil
}

// DetectDuplicateLeads detects duplicate leads based on criteria
func (uc *LeadUsecase) DetectDuplicateLeads(ctx context.Context, criteria []string) ([]*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Detecting duplicate leads with criteria: %v", criteria)
	// TODO: Implement actual deduplication logic
	return uc.repo.FindDuplicateLeads(ctx, criteria)
}

// MergeLeads merges multiple leads into a primary lead
func (uc *LeadUsecase) MergeLeads(ctx context.Context, leadIDsToMerge []int64, primaryLeadID int64) (*entity.Lead, error) {
	uc.log.WithContext(ctx).Infof("Merging leads %v into primary lead %d", leadIDsToMerge, primaryLeadID)
	// TODO: Implement actual merge logic:
	// 1. Get primary lead
	// 2. Transfer related data (jobs, interactions, etc.) from leadsToMerge to primaryLead
	// 3. Delete leadsToMerge
	// 4. Update primaryLead
	return nil, nil
}
