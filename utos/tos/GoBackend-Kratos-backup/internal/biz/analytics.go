package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 📊 Analytics Business Logic Layer
// GoBackend-Kratos HVAC CRM System

// AnalyticsRepo defines the analytics repository interface
type AnalyticsRepo interface {
	// Dashboard methods
	GetExecutiveDashboard(ctx context.Context) (*ExecutiveDashboardSummary, error)
	GetCustomerInsights(ctx context.Context) ([]*CustomerInsight, error)
	GetOperationalAnalytics(ctx context.Context, date time.Time) (*OperationalAnalytics, error)
	GetPerformanceTrends(ctx context.Context, weeks int) ([]*PerformanceTrend, error)
	
	// KPI methods
	GetKPIs(ctx context.Context, category string) ([]*KPI, error)
	UpdateKPI(ctx context.Context, kpi *KPI) error
	
	// Real-time metrics
	GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error)
	
	// Widget management
	GetDashboardWidgets(ctx context.Context, category string) ([]*DashboardWidget, error)
	CreateDashboardWidget(ctx context.Context, widget *DashboardWidget) error
	
	// Analytics calculations
	CalculateCustomerAnalytics(ctx context.Context, customerID uint) error
	UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error
	UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *OperationalAnalytics) error
}

// AnalyticsUsecase handles analytics business logic
type AnalyticsUsecase struct {
	repo AnalyticsRepo
	log  *log.Helper
}

// NewAnalyticsUsecase creates a new analytics usecase
func NewAnalyticsUsecase(repo AnalyticsRepo, logger log.Logger) *AnalyticsUsecase {
	return &AnalyticsUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// 📊 BUSINESS ENTITIES
// ============================================================================

// ExecutiveDashboardSummary represents executive dashboard data
type ExecutiveDashboardSummary struct {
	Period           string  `json:"period"`
	TodayRevenue     float64 `json:"today_revenue"`
	TodayJobs        int     `json:"today_jobs"`
	TodaySatisfaction float64 `json:"today_satisfaction"`
	TodayEfficiency  float64 `json:"today_efficiency"`
	WeekRevenue      float64 `json:"week_revenue"`
	WeekJobs         int     `json:"week_jobs"`
	MonthRevenue     float64 `json:"month_revenue"`
	MonthJobs        int     `json:"month_jobs"`
}

// CustomerInsight represents customer insights data
type CustomerInsight struct {
	LoyaltyTier        string  `json:"loyalty_tier"`
	CustomerCount      int     `json:"customer_count"`
	AvgLifetimeValue   float64 `json:"avg_lifetime_value"`
	AvgSatisfaction    float64 `json:"avg_satisfaction"`
	AvgChurnRisk       float64 `json:"avg_churn_risk"`
	TierTotalRevenue   float64 `json:"tier_total_revenue"`
}

// OperationalAnalytics represents operational analytics data
type OperationalAnalytics struct {
	ID                     uint           `json:"id"`
	AnalysisDate           time.Time      `json:"analysis_date"`
	TotalActiveJobs        int            `json:"total_active_jobs"`
	CompletedJobs          int            `json:"completed_jobs"`
	CancelledJobs          int            `json:"cancelled_jobs"`
	EmergencyJobs          int            `json:"emergency_jobs"`
	AverageResponseTime    *time.Duration `json:"average_response_time"`
	AverageCompletionTime  *time.Duration `json:"average_completion_time"`
	TechnicianEfficiency   *float64       `json:"technician_efficiency"`
	EquipmentUtilization   *float64       `json:"equipment_utilization"`
	CustomerSatisfaction   *float64       `json:"customer_satisfaction"`
	FirstTimeFixRate       *float64       `json:"first_time_fix_rate"`
	CallbackRate           *float64       `json:"callback_rate"`
	PartsAvailability      *float64       `json:"parts_availability"`
	FuelCosts              float64        `json:"fuel_costs"`
	OvertimeHours          float64        `json:"overtime_hours"`
}

// PerformanceTrend represents performance trends data
type PerformanceTrend struct {
	WeekStart           time.Time `json:"week_start"`
	AvgEfficiency       float64   `json:"avg_efficiency"`
	AvgSatisfaction     float64   `json:"avg_satisfaction"`
	AvgFirstTimeFix     float64   `json:"avg_first_time_fix"`
	TotalJobs           int       `json:"total_jobs"`
	AvgResponseHours    float64   `json:"avg_response_hours"`
}

// KPI represents a Key Performance Indicator
type KPI struct {
	ID                  uint      `json:"id"`
	KPIName             string    `json:"kpi_name"`
	KPICategory         string    `json:"kpi_category"`
	CurrentValue        float64   `json:"current_value"`
	TargetValue         *float64  `json:"target_value"`
	PreviousValue       *float64  `json:"previous_value"`
	TrendDirection      string    `json:"trend_direction"`
	TrendPercentage     *float64  `json:"trend_percentage"`
	MeasurementDate     time.Time `json:"measurement_date"`
	MeasurementTime     time.Time `json:"measurement_time"`
	AlertThresholdMin   *float64  `json:"alert_threshold_min"`
	AlertThresholdMax   *float64  `json:"alert_threshold_max"`
	IsAlertTriggered    bool      `json:"is_alert_triggered"`
	Notes               string    `json:"notes"`
}

// DashboardWidget represents a dashboard widget configuration
type DashboardWidget struct {
	ID                uint                   `json:"id"`
	WidgetName        string                 `json:"widget_name"`
	WidgetType        string                 `json:"widget_type"`
	DashboardCategory string                 `json:"dashboard_category"`
	DataSource        string                 `json:"data_source"`
	RefreshInterval   int                    `json:"refresh_interval"`
	WidgetConfig      map[string]interface{} `json:"widget_config"`
	IsActive          bool                   `json:"is_active"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
}

// ============================================================================
// 📊 BUSINESS LOGIC METHODS
// ============================================================================

// GetExecutiveDashboard returns executive dashboard data
func (uc *AnalyticsUsecase) GetExecutiveDashboard(ctx context.Context) (*ExecutiveDashboardSummary, error) {
	uc.log.WithContext(ctx).Info("📊 Getting executive dashboard data")
	
	summary, err := uc.repo.GetExecutiveDashboard(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get executive dashboard: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Info("✅ Executive dashboard data retrieved successfully")
	return summary, nil
}

// GetCustomerInsights returns customer insights dashboard data
func (uc *AnalyticsUsecase) GetCustomerInsights(ctx context.Context) ([]*CustomerInsight, error) {
	uc.log.WithContext(ctx).Info("📊 Getting customer insights data")
	
	insights, err := uc.repo.GetCustomerInsights(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get customer insights: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Customer insights retrieved: %d tiers", len(insights))
	return insights, nil
}

// GetOperationalDashboard returns operational dashboard data
func (uc *AnalyticsUsecase) GetOperationalDashboard(ctx context.Context) (*OperationalAnalytics, error) {
	uc.log.WithContext(ctx).Info("📊 Getting operational dashboard data")
	
	today := time.Now()
	operational, err := uc.repo.GetOperationalAnalytics(ctx, today)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get operational analytics: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Info("✅ Operational dashboard data retrieved successfully")
	return operational, nil
}

// GetPerformanceTrends returns performance trends data
func (uc *AnalyticsUsecase) GetPerformanceTrends(ctx context.Context, weeks int) ([]*PerformanceTrend, error) {
	uc.log.WithContext(ctx).Infof("📊 Getting performance trends for %d weeks", weeks)
	
	trends, err := uc.repo.GetPerformanceTrends(ctx, weeks)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get performance trends: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Performance trends retrieved: %d data points", len(trends))
	return trends, nil
}

// GetKPIs returns KPI data
func (uc *AnalyticsUsecase) GetKPIs(ctx context.Context, category string) ([]*KPI, error) {
	uc.log.WithContext(ctx).Infof("📊 Getting KPIs for category: %s", category)
	
	kpis, err := uc.repo.GetKPIs(ctx, category)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get KPIs: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ KPIs retrieved: %d items", len(kpis))
	return kpis, nil
}

// UpdateKPI updates or creates a KPI
func (uc *AnalyticsUsecase) UpdateKPI(ctx context.Context, kpiName, category string, value float64, target *float64) error {
	uc.log.WithContext(ctx).Infof("📊 Updating KPI: %s = %.2f", kpiName, value)
	
	kpi := &KPI{
		KPIName:         kpiName,
		KPICategory:     category,
		CurrentValue:    value,
		TargetValue:     target,
		MeasurementDate: time.Now(),
		MeasurementTime: time.Now(),
	}
	
	err := uc.repo.UpdateKPI(ctx, kpi)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to update KPI: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Infof("✅ KPI updated successfully: %s", kpiName)
	return nil
}

// GetRealTimeMetrics returns real-time system metrics
func (uc *AnalyticsUsecase) GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error) {
	uc.log.WithContext(ctx).Info("📊 Getting real-time metrics")
	
	metrics, err := uc.repo.GetRealTimeMetrics(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get real-time metrics: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Info("✅ Real-time metrics retrieved successfully")
	return metrics, nil
}

// GetDashboardWidgets returns dashboard widgets
func (uc *AnalyticsUsecase) GetDashboardWidgets(ctx context.Context, category string) ([]*DashboardWidget, error) {
	uc.log.WithContext(ctx).Infof("📊 Getting dashboard widgets for: %s", category)
	
	widgets, err := uc.repo.GetDashboardWidgets(ctx, category)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get dashboard widgets: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Dashboard widgets retrieved: %d items", len(widgets))
	return widgets, nil
}

// CreateDashboardWidget creates a new dashboard widget
func (uc *AnalyticsUsecase) CreateDashboardWidget(ctx context.Context, widget *DashboardWidget) error {
	uc.log.WithContext(ctx).Infof("📊 Creating dashboard widget: %s", widget.WidgetName)
	
	widget.CreatedAt = time.Now()
	widget.UpdatedAt = time.Now()
	
	err := uc.repo.CreateDashboardWidget(ctx, widget)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to create dashboard widget: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Dashboard widget created successfully: %s", widget.WidgetName)
	return nil
}

// CalculateCustomerAnalytics calculates and updates customer analytics
func (uc *AnalyticsUsecase) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	uc.log.WithContext(ctx).Infof("📊 Calculating customer analytics for ID: %d", customerID)
	
	err := uc.repo.CalculateCustomerAnalytics(ctx, customerID)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to calculate customer analytics: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Customer analytics calculated successfully for ID: %d", customerID)
	return nil
}

// UpdateRevenueAnalytics updates revenue analytics
func (uc *AnalyticsUsecase) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	uc.log.WithContext(ctx).Infof("📊 Updating revenue analytics: %.2f for %s", revenue, date.Format("2006-01-02"))
	
	err := uc.repo.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to update revenue analytics: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Info("✅ Revenue analytics updated successfully")
	return nil
}

// UpdateOperationalAnalytics updates operational analytics
func (uc *AnalyticsUsecase) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *OperationalAnalytics) error {
	uc.log.WithContext(ctx).Infof("📊 Updating operational analytics for %s", date.Format("2006-01-02"))
	
	data.AnalysisDate = date
	
	err := uc.repo.UpdateOperationalAnalytics(ctx, date, data)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to update operational analytics: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Info("✅ Operational analytics updated successfully")
	return nil
}