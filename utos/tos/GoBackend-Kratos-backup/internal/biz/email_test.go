package biz

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockEmailRepo is a mock implementation of EmailRepo
type MockEmailRepo struct {
	mock.Mock
}

func (m *MockEmailRepo) CreateEmail(ctx context.Context, email *Email) (*Email, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Email), args.Error(1)
}

func (m *MockEmailRepo) GetEmail(ctx context.Context, id int64) (*Email, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Email), args.Error(1)
}

func (m *MockEmailRepo) GetEmailByMessageID(ctx context.Context, messageID string) (*Email, error) {
	args := m.Called(ctx, messageID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Email), args.Error(1)
}

func (m *MockEmailRepo) ListEmails(ctx context.Context, page, pageSize int32) ([]*Email, int64, error) {
	args := m.Called(ctx, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*Email), args.Get(1).(int64), args.Error(2)
}

func (m *MockEmailRepo) UpdateEmail(ctx context.Context, email *Email) (*Email, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Email), args.Error(1)
}

func (m *MockEmailRepo) DeleteEmail(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockEmailRepo) CreateEmailAnalysis(ctx context.Context, analysis *EmailAnalysis) (*EmailAnalysis, error) {
	args := m.Called(ctx, analysis)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAnalysis), args.Error(1)
}

func (m *MockEmailRepo) GetEmailAnalysis(ctx context.Context, emailID int64) (*EmailAnalysis, error) {
	args := m.Called(ctx, emailID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAnalysis), args.Error(1)
}

func (m *MockEmailRepo) UpdateEmailAnalysis(ctx context.Context, analysis *EmailAnalysis) (*EmailAnalysis, error) {
	args := m.Called(ctx, analysis)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAnalysis), args.Error(1)
}

func (m *MockEmailRepo) DeleteEmailAnalysis(ctx context.Context, emailID int64) error {
	args := m.Called(ctx, emailID)
	return args.Error(0)
}

func (m *MockEmailRepo) CreateEmailAttachment(ctx context.Context, attachment *EmailAttachment) (*EmailAttachment, error) {
	args := m.Called(ctx, attachment)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAttachment), args.Error(1)
}

func (m *MockEmailRepo) GetEmailAttachments(ctx context.Context, emailID int64) ([]*EmailAttachment, error) {
	args := m.Called(ctx, emailID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*EmailAttachment), args.Error(1)
}

func (m *MockEmailRepo) UpdateEmailAttachment(ctx context.Context, attachment *EmailAttachment) (*EmailAttachment, error) {
	args := m.Called(ctx, attachment)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAttachment), args.Error(1)
}

func (m *MockEmailRepo) DeleteEmailAttachment(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockEmailRepo) GetEmailAnalysisResult(ctx context.Context, emailID int64) (*EmailAnalysisResult, error) {
	args := m.Called(ctx, emailID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailAnalysisResult), args.Error(1)
}

func (m *MockEmailRepo) SearchEmails(ctx context.Context, req *EmailSearchRequest) (*EmailSearchResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*EmailSearchResponse), args.Error(1)
}

func (m *MockEmailRepo) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*DashboardStats), args.Error(1)
}

func (m *MockEmailRepo) StoreEmailAnalysisResult(ctx context.Context, result *EmailAnalysisResult) error {
	args := m.Called(ctx, result)
	return args.Error(0)
}

func TestNewEmailUsecase(t *testing.T) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestStoreEmailAnalysisResult(t *testing.T) {
	tests := []struct {
		name           string
		input          *EmailAnalysisResult
		mockReturnErr  error
		expectedErr    error
		expectRepoCall bool
	}{
		{
			name: "Success case",
			input: &EmailAnalysisResult{
				Email:       &Email{Subject: "Test Subject", From: "<EMAIL>"},
				Analysis:    &EmailAnalysis{},
				Attachments: []*EmailAttachment{{}},
			},
			mockReturnErr:  nil,
			expectedErr:    nil,
			expectRepoCall: true,
		},
		{
			name:           "Error case: Email is nil",
			input:          &EmailAnalysisResult{Email: nil},
			mockReturnErr:  nil,
			expectedErr:    ErrEmailRequired,
			expectRepoCall: false,
		},
		{
			name: "Error case: Empty Subject",
			input: &EmailAnalysisResult{
				Email:       &Email{Subject: "", From: "<EMAIL>"},
				Analysis:    &EmailAnalysis{},
				Attachments: []*EmailAttachment{{}},
			},
			mockReturnErr:  nil,
			expectedErr:    ErrEmailSubjectRequired,
			expectRepoCall: false,
		},
		{
			name: "Error case: Empty From",
			input: &EmailAnalysisResult{
				Email:       &Email{Subject: "Test Subject", From: ""},
				Analysis:    &EmailAnalysis{},
				Attachments: []*EmailAttachment{{}},
			},
			mockReturnErr:  nil,
			expectedErr:    ErrEmailFromRequired,
			expectRepoCall: false,
		},
		{
			name: "Error case: Repo returns error",
			input: &EmailAnalysisResult{
				Email:       &Email{Subject: "Test Subject", From: "<EMAIL>"},
				Analysis:    &EmailAnalysis{},
				Attachments: []*EmailAttachment{{}},
			},
			mockReturnErr:  errors.New("db error"),
			expectedErr:    errors.New("db error"),
			expectRepoCall: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(MockEmailRepo)
			uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
			ctx := context.Background()

			if tt.expectRepoCall {
				mockRepo.On("StoreEmailAnalysisResult", ctx, mock.AnythingOfType("*biz.EmailAnalysisResult")).Return(tt.mockReturnErr).Once()
			}

			err := uc.StoreEmailAnalysisResult(ctx, tt.input)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				if tt.expectedErr.Error() == "db error" {
					assert.Contains(t, err.Error(), tt.expectedErr.Error())
				} else {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.NotZero(t, tt.input.Email.CreatedAt)
				assert.NotZero(t, tt.input.Email.UpdatedAt)
				if tt.input.Analysis != nil {
					assert.NotZero(t, tt.input.Analysis.CreatedAt)
					assert.NotZero(t, tt.input.Analysis.UpdatedAt)
				}
				for _, att := range tt.input.Attachments {
					assert.NotZero(t, att.CreatedAt)
					assert.NotZero(t, att.UpdatedAt)
				}
			}
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestGetEmailAnalysisResult(t *testing.T) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedResult := &EmailAnalysisResult{Email: &Email{ID: 1, Subject: "Test"}}
	mockRepo.On("GetEmailAnalysisResult", ctx, int64(1)).Return(expectedResult, nil).Once()
	result, err := uc.GetEmailAnalysisResult(ctx, 1)
	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	result, err = uc.GetEmailAnalysisResult(ctx, 0)
	assert.ErrorIs(t, err, ErrInvalidEmailID)
	assert.Nil(t, result)
	mockRepo.AssertNotCalled(t, "GetEmailAnalysisResult")

	// Error case: Repo returns error
	mockRepo.On("GetEmailAnalysisResult", ctx, int64(2)).Return(nil, errors.New("not found")).Once()
	result, err = uc.GetEmailAnalysisResult(ctx, 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
	assert.Nil(t, result)
	mockRepo.AssertExpectations(t)
}

func TestSearchEmails(t *testing.T) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	req := &EmailSearchRequest{Limit: 10, Offset: 0}
	expectedResp := &EmailSearchResponse{Results: []*EmailAnalysisResult{{Email: &Email{ID: 1}}}, Total: 1, Page: 1, PageSize: 10, TotalPages: 1}
	mockRepo.On("SearchEmails", ctx, req).Return(expectedResp, nil).Once()
	resp, err := uc.SearchEmails(ctx, req)
	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
	mockRepo.AssertExpectations(t)

	// Test case: Limit <= 0, Offset < 0
	req = &EmailSearchRequest{Limit: 0, Offset: -1}
	expectedResp = &EmailSearchResponse{Results: []*EmailAnalysisResult{{Email: &Email{ID: 1}}}, Total: 1, Page: 1, PageSize: 50, TotalPages: 1}
	mockRepo.On("SearchEmails", ctx, &EmailSearchRequest{Limit: 50, Offset: 0}).Return(expectedResp, nil).Once()
	resp, err = uc.SearchEmails(ctx, req)
	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
	mockRepo.AssertExpectations(t)

	// Test case: Limit > 100
	req = &EmailSearchRequest{Limit: 101, Offset: 0}
	expectedResp = &EmailSearchResponse{Results: []*EmailAnalysisResult{{Email: &Email{ID: 1}}}, Total: 1, Page: 1, PageSize: 50, TotalPages: 1}
	mockRepo.On("SearchEmails", ctx, &EmailSearchRequest{Limit: 50, Offset: 0}).Return(expectedResp, nil).Once()
	resp, err = uc.SearchEmails(ctx, req)
	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	req = &EmailSearchRequest{Limit: 10, Offset: 0}
	mockRepo.On("SearchEmails", ctx, req).Return(nil, errors.New("db error")).Once()
	resp, err = uc.SearchEmails(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, resp)
	mockRepo.AssertExpectations(t)
}

func TestGetDashboardStats(t *testing.T) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedStats := &DashboardStats{TotalEmails: 100, TodayEmails: 10}
	mockRepo.On("GetDashboardStats", ctx).Return(expectedStats, nil).Once()
	stats, err := uc.GetDashboardStats(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedStats, stats)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("GetDashboardStats", ctx).Return(nil, errors.New("db error")).Once()
	stats, err = uc.GetDashboardStats(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, stats)
	mockRepo.AssertExpectations(t)
}

// Benchmarks
func BenchmarkStoreEmailAnalysisResult(b *testing.B) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	email := &Email{Subject: "Benchmark Subject", From: "<EMAIL>"}
	analysis := &EmailAnalysis{}
	attachment := &EmailAttachment{}
	result := &EmailAnalysisResult{Email: email, Analysis: analysis, Attachments: []*EmailAttachment{attachment}}

	mockRepo.On("StoreEmailAnalysisResult", ctx, mock.AnythingOfType("*biz.EmailAnalysisResult")).Return(nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = uc.StoreEmailAnalysisResult(ctx, result)
	}
}

func BenchmarkSearchEmails(b *testing.B) {
	mockRepo := new(MockEmailRepo)
	uc := NewEmailUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	req := &EmailSearchRequest{Limit: 10, Offset: 0}
	expectedResp := &EmailSearchResponse{Results: []*EmailAnalysisResult{{Email: &Email{ID: 1}}}, Total: 1, Page: 1, PageSize: 10, TotalPages: 1}

	mockRepo.On("SearchEmails", ctx, req).Return(expectedResp, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.SearchEmails(ctx, req)
	}
}
