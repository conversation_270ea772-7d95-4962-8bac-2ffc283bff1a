package biz

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
)

// LeadCampaignInteractionRepo defines the interface for lead campaign interaction data operations
type LeadCampaignInteractionRepo interface {
	CreateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error)
	GetLeadCampaignInteraction(ctx context.Context, id int64) (*entity.LeadCampaignInteraction, error)
	ListLeadCampaignInteractions(ctx context.Context, page, pageSize int32, leadID, campaignID int64, eventType string) ([]*entity.LeadCampaignInteraction, int32, error)
	UpdateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error)
	DeleteLeadCampaignInteraction(ctx context.Context, id int64) error
}

// LeadCampaignInteractionUsecase encapsulates lead campaign interaction business logic
type LeadCampaignInteractionUsecase struct {
	repo LeadCampaignInteractionRepo
	log  *log.Helper
}

// NewLeadCampaignInteractionUsecase creates a new lead campaign interaction usecase
func NewLeadCampaignInteractionUsecase(repo LeadCampaignInteractionRepo, logger log.Logger) *LeadCampaignInteractionUsecase {
	return &LeadCampaignInteractionUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateLeadCampaignInteraction creates a new lead campaign interaction with validation
func (uc *LeadCampaignInteractionUsecase) CreateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error) {
	uc.log.WithContext(ctx).Infof("Creating lead campaign interaction: LeadID=%d, CampaignID=%d", interaction.LeadID, interaction.CampaignID)

	// Business logic validation
	if interaction.LeadID <= 0 || interaction.CampaignID <= 0 || interaction.EventType == "" {
		return nil, ErrCampaignAttributionFailed // Or a more specific error
	}

	// Set timestamps
	now := time.Now()
	interaction.CreatedAt = now

	return uc.repo.CreateLeadCampaignInteraction(ctx, interaction)
}

// GetLeadCampaignInteraction retrieves a lead campaign interaction by ID
func (uc *LeadCampaignInteractionUsecase) GetLeadCampaignInteraction(ctx context.Context, id int64) (*entity.LeadCampaignInteraction, error) {
	uc.log.WithContext(ctx).Infof("Getting lead campaign interaction: %d", id)

	if id <= 0 {
		return nil, ErrInvalidCampaignID // Reusing campaign error for now
	}

	return uc.repo.GetLeadCampaignInteraction(ctx, id)
}

// ListLeadCampaignInteractions retrieves lead campaign interactions with pagination and filters
func (uc *LeadCampaignInteractionUsecase) ListLeadCampaignInteractions(ctx context.Context, page, pageSize int32, leadID, campaignID int64, eventType string) ([]*entity.LeadCampaignInteraction, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing lead campaign interactions: page=%d, size=%d, leadID=%d, campaignID=%d, eventType=%s", page, pageSize, leadID, campaignID, eventType)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListLeadCampaignInteractions(ctx, page, pageSize, leadID, campaignID, eventType)
}

// UpdateLeadCampaignInteraction updates an existing lead campaign interaction
func (uc *LeadCampaignInteractionUsecase) UpdateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error) {
	uc.log.WithContext(ctx).Infof("Updating lead campaign interaction: %d", interaction.ID)

	if interaction.ID <= 0 {
		return nil, ErrInvalidCampaignID // Reusing campaign error for now
	}

	return uc.repo.UpdateLeadCampaignInteraction(ctx, interaction)
}

// DeleteLeadCampaignInteraction deletes a lead campaign interaction by ID
func (uc *LeadCampaignInteractionUsecase) DeleteLeadCampaignInteraction(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting lead campaign interaction: %d", id)

	if id <= 0 {
		return ErrInvalidCampaignID // Reusing campaign error for now
	}

	return uc.repo.DeleteLeadCampaignInteraction(ctx, id)
}
