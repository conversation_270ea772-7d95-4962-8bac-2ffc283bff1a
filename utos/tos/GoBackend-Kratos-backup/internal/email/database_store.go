package email

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/biz"
)

// 📧 DatabaseEmailStore implements persistent email storage using database
type DatabaseEmailStore struct {
	emailUsecase *biz.EmailUsecase
	log          *log.Helper
}

// NewDatabaseEmailStore creates a new database-backed email store
func NewDatabaseEmailStore(emailUsecase *biz.EmailUsecase, logger log.Logger) *DatabaseEmailStore {
	return &DatabaseEmailStore{
		emailUsecase: emailUsecase,
		log:          log.NewHelper(logger),
	}
}

// 💾 Store email analysis result in database
func (s *DatabaseEmailStore) Store(result *EmailAnalysisResult) error {
	ctx := context.Background()

	// Convert EmailAnalysisResult to biz.EmailAnalysisResult
	bizResult := s.convertToBizResult(result)

	// Store using email usecase
	err := s.emailUsecase.StoreEmailAnalysisResult(ctx, bizResult)
	if err != nil {
		s.log.Errorf("Failed to store email analysis result: %v", err)
		return err
	}

	s.log.Infof("Stored email analysis result in database: %s", result.Subject)
	return nil
}

// 🔍 Get email analysis result by ID
func (s *DatabaseEmailStore) Get(emailID string) (*EmailAnalysisResult, bool) {
	ctx := context.Background()

	// Parse email ID
	var id int64
	if _, err := fmt.Sscanf(emailID, "%d", &id); err != nil {
		s.log.Warnf("Invalid email ID format: %s", emailID)
		return nil, false
	}

	// Get from database
	bizResult, err := s.emailUsecase.GetEmailAnalysisResult(ctx, id)
	if err != nil {
		s.log.Warnf("Failed to get email analysis result: %v", err)
		return nil, false
	}

	// Convert back to EmailAnalysisResult
	result := s.convertFromBizResult(bizResult)
	return result, true
}

// 📋 List all email analysis results
func (s *DatabaseEmailStore) List() []*EmailAnalysisResult {
	ctx := context.Background()

	// Search all emails with default criteria
	searchReq := &biz.EmailSearchRequest{
		Limit:  1000, // Get up to 1000 recent emails
		Offset: 0,
	}

	searchResp, err := s.emailUsecase.SearchEmails(ctx, searchReq)
	if err != nil {
		s.log.Errorf("Failed to list emails: %v", err)
		return []*EmailAnalysisResult{}
	}

	// Convert results
	results := make([]*EmailAnalysisResult, len(searchResp.Results))
	for i, bizResult := range searchResp.Results {
		results[i] = s.convertFromBizResult(bizResult)
	}

	return results
}

// 🔍 Search emails with criteria
func (s *DatabaseEmailStore) Search(req *EmailSearchRequest) []*EmailAnalysisResult {
	ctx := context.Background()

	// Convert search request
	bizReq := s.convertSearchRequest(req)

	// Search in database
	searchResp, err := s.emailUsecase.SearchEmails(ctx, bizReq)
	if err != nil {
		s.log.Errorf("Failed to search emails: %v", err)
		return []*EmailAnalysisResult{}
	}

	// Convert results
	results := make([]*EmailAnalysisResult, len(searchResp.Results))
	for i, bizResult := range searchResp.Results {
		results[i] = s.convertFromBizResult(bizResult)
	}

	return results
}

// 🎯 Check if email matches search criteria (for compatibility)
func (s *DatabaseEmailStore) matchesSearchCriteria(email *EmailAnalysisResult, req *EmailSearchRequest) bool {
	// This method is kept for compatibility but not used in database implementation
	// The actual filtering is done in the database query
	return true
}

// Conversion methods between email package types and biz types

// convertToBizResult converts EmailAnalysisResult to biz.EmailAnalysisResult
func (s *DatabaseEmailStore) convertToBizResult(result *EmailAnalysisResult) *biz.EmailAnalysisResult {
	bizResult := &biz.EmailAnalysisResult{}

	// Convert email
	bizResult.Email = &biz.Email{
		MessageID:  result.EmailID, // Use EmailID as MessageID for now
		From:       result.From,
		To:         result.To,
		Subject:    result.Subject,
		Body:       "",
		Priority:   result.Priority,
		Status:     "processed",
		ReceivedAt: &result.Timestamp,
		CreatedAt:  result.Timestamp,
		UpdatedAt:  time.Now(),
	}

	// Convert analysis
	if result.BodyAnalysis != nil || result.Sentiment != "" {
		bizResult.Analysis = &biz.EmailAnalysis{
			EmailID:          0, // Will be set by repository
			SentimentScore:   &result.SentimentScore,
			UrgencyLevel:     s.mapPriorityToUrgency(result.Priority),
			DetectedIntent:   s.mapCategoryToIntent(result.Category),
			DetectedEntities: make(map[string]interface{}),
			KeyPhrases:       []string{},
			LanguageCode:     "en",
			IsSpam:           false,
			ConfidenceScore:  &result.SentimentScore, // Use sentiment score as confidence
			HVACRelevance:    s.convertHVACRelevanceToFloat(result.HVACRelevance),
			Category:         result.Category,
			Priority:         result.Priority,
			ActionItems:      result.ActionItems,
			CreatedAt:        result.Timestamp,
			UpdatedAt:        time.Now(),
		}

		// Add body analysis content if available
		if result.BodyAnalysis != nil {
			bizResult.Email.Body = result.BodyAnalysis.Content
			bizResult.Analysis.KeyPhrases = result.BodyAnalysis.KeyPhrases
		}
	}

	// Convert attachments
	if len(result.Attachments) > 0 {
		bizResult.Attachments = make([]*biz.EmailAttachment, len(result.Attachments))
		for i, attachment := range result.Attachments {
			bizResult.Attachments[i] = &biz.EmailAttachment{
				EmailID:     0, // Will be set by repository
				Filename:    attachment.Filename,
				ContentType: attachment.ContentType,
				Size:        attachment.Size,
				TextContent: attachment.TextContent,
				IsProcessed: attachment.IsProcessed,
				CreatedAt:   result.Timestamp,
				UpdatedAt:   time.Now(),
			}
		}
	}

	return bizResult
}

// convertFromBizResult converts biz.EmailAnalysisResult to EmailAnalysisResult
func (s *DatabaseEmailStore) convertFromBizResult(bizResult *biz.EmailAnalysisResult) *EmailAnalysisResult {
	result := &EmailAnalysisResult{
		EmailID:   fmt.Sprintf("%d", bizResult.Email.ID),
		Subject:   bizResult.Email.Subject,
		From:      bizResult.Email.From,
		To:        bizResult.Email.To,
		Timestamp: bizResult.Email.CreatedAt,
	}

	// Convert analysis
	if bizResult.Analysis != nil {
		result.Sentiment = s.getSentimentFromScore(bizResult.Analysis.SentimentScore)
		if bizResult.Analysis.SentimentScore != nil {
			result.SentimentScore = *bizResult.Analysis.SentimentScore
		}
		result.Category = bizResult.Analysis.Category
		result.Priority = bizResult.Analysis.Priority
		result.ActionItems = bizResult.Analysis.ActionItems
		if bizResult.Analysis.HVACRelevance != nil {
			result.HVACRelevance = s.convertFloatToHVACRelevance(*bizResult.Analysis.HVACRelevance)
		}

		// Create body analysis
		result.BodyAnalysis = &TextAnalysis{
			Content:    bizResult.Email.Body,
			WordCount:  len(bizResult.Email.Body),
			KeyPhrases: bizResult.Analysis.KeyPhrases,
		}
	}

	// Convert attachments
	if len(bizResult.Attachments) > 0 {
		result.Attachments = make([]*AttachmentAnalysis, len(bizResult.Attachments))
		result.AttachmentCount = len(bizResult.Attachments)

		for i, bizAttachment := range bizResult.Attachments {
			result.Attachments[i] = &AttachmentAnalysis{
				Filename:    bizAttachment.Filename,
				ContentType: bizAttachment.ContentType,
				Size:        bizAttachment.Size,
				TextContent: bizAttachment.TextContent,
				IsProcessed: bizAttachment.IsProcessed,
			}
		}
	}

	return result
}

// convertSearchRequest converts EmailSearchRequest to biz.EmailSearchRequest
func (s *DatabaseEmailStore) convertSearchRequest(req *EmailSearchRequest) *biz.EmailSearchRequest {
	bizReq := &biz.EmailSearchRequest{
		Query:     req.Query,
		StartDate: &req.StartDate,
		EndDate:   &req.EndDate,
		Category:  req.Category,
		Sentiment: req.Sentiment,
		Priority:  req.Priority,
		Limit:     req.Limit,
		Offset:    0,
	}

	if req.HasHVAC != nil {
		bizReq.HasHVAC = req.HasHVAC
	}

	return bizReq
}

// Helper methods

// mapPriorityToUrgency maps priority to urgency level
func (s *DatabaseEmailStore) mapPriorityToUrgency(priority string) string {
	switch priority {
	case "urgent", "high":
		return "high"
	case "medium":
		return "medium"
	case "low":
		return "low"
	default:
		return "low"
	}
}

// mapCategoryToIntent maps category to detected intent
func (s *DatabaseEmailStore) mapCategoryToIntent(category string) string {
	switch category {
	case "service":
		return "support"
	case "sales":
		return "sales"
	case "support":
		return "support"
	case "emergency":
		return "support"
	default:
		return "other"
	}
}

// getSentimentFromScore converts sentiment score to sentiment string
func (s *DatabaseEmailStore) getSentimentFromScore(score *float64) string {
	if score == nil {
		return "neutral"
	}

	if *score > 0.1 {
		return "positive"
	} else if *score < -0.1 {
		return "negative"
	}
	return "neutral"
}

// convertHVACRelevanceToFloat converts HVACRelevanceAnalysis to float64 for database storage
func (s *DatabaseEmailStore) convertHVACRelevanceToFloat(hvac *HVACRelevanceAnalysis) *float64 {
	if hvac == nil {
		return nil
	}

	// Use confidence as the relevance score
	return &hvac.Confidence
}

// convertFloatToHVACRelevance converts float64 to HVACRelevanceAnalysis for display
func (s *DatabaseEmailStore) convertFloatToHVACRelevance(score float64) *HVACRelevanceAnalysis {
	isRelevant := score > 0.5

	return &HVACRelevanceAnalysis{
		IsHVACRelated:     isRelevant,
		Confidence:        score,
		HVACKeywords:      []string{}, // Would need to be stored separately for full reconstruction
		ServiceType:       "unknown",
		Urgency:           s.mapScoreToUrgency(score),
		RecommendedAction: s.getRecommendedAction(isRelevant),
	}
}

// mapScoreToUrgency maps relevance score to urgency level
func (s *DatabaseEmailStore) mapScoreToUrgency(score float64) string {
	if score > 0.8 {
		return "high"
	} else if score > 0.5 {
		return "medium"
	}
	return "low"
}

// getRecommendedAction returns recommended action based on relevance
func (s *DatabaseEmailStore) getRecommendedAction(isRelevant bool) string {
	if isRelevant {
		return "Review and assign to HVAC technician"
	}
	return "Standard email processing"
}
