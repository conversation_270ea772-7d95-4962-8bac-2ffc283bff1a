package email

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
)

// 🚀 Comprehensive Email Intelligence Service
type EmailIntelligenceService struct {
	log         *log.Helper
	analysis    *EmailAnalysisService
	dashboard   *EmailDashboardService
	retrieval   *EmailRetrievalService
	config      *EmailIntelligenceConfig
	httpServer  *http.Server
}

// ⚙️ Email Intelligence Configuration
type EmailIntelligenceConfig struct {
	// Analysis Configuration
	Analysis *EmailAnalysisConfig `json:"analysis"`

	// Mailbox Configurations
	Mailboxes []*MailboxConfig `json:"mailboxes"`

	// Server Configuration
	HTTPPort     int    `json:"http_port"`
	EnableCORS   bool   `json:"enable_cors"`
	LogLevel     string `json:"log_level"`

	// AI Configuration
	OllamaURL    string `json:"ollama_url"`
	VectorDBPath string `json:"vector_db_path"`

	// Processing Configuration
	MaxConcurrentAnalysis int           `json:"max_concurrent_analysis"`
	ProcessingTimeout     time.Duration `json:"processing_timeout"`
	RetryAttempts        int           `json:"retry_attempts"`
}

// 📊 Service Status
type ServiceStatus struct {
	Status           string                 `json:"status"`
	Version          string                 `json:"version"`
	Uptime           time.Duration          `json:"uptime"`
	ActiveMailboxes  int                    `json:"active_mailboxes"`
	ProcessedEmails  int                    `json:"processed_emails"`
	LastProcessed    time.Time              `json:"last_processed"`
	AnalysisService  *AnalysisServiceStatus `json:"analysis_service"`
	RetrievalService *RetrievalServiceStatus `json:"retrieval_service"`
	Errors           []string               `json:"errors,omitempty"`
}

// 🔍 Analysis Service Status
type AnalysisServiceStatus struct {
	Status         string    `json:"status"`
	ProcessedCount int       `json:"processed_count"`
	ErrorCount     int       `json:"error_count"`
	LastAnalysis   time.Time `json:"last_analysis"`
	VectorDBSize   int       `json:"vector_db_size"`
}

// 📧 Retrieval Service Status
type RetrievalServiceStatus struct {
	Status           string            `json:"status"`
	ActiveMailboxes  int               `json:"active_mailboxes"`
	RetrievedCount   int               `json:"retrieved_count"`
	LastRetrieval    time.Time         `json:"last_retrieval"`
	MailboxStatuses  map[string]string `json:"mailbox_statuses"`
}

// NewEmailIntelligenceService creates a comprehensive email intelligence service with in-memory storage (legacy)
func NewEmailIntelligenceService(config *EmailIntelligenceConfig, logger log.Logger) (*EmailIntelligenceService, error) {
	log := log.NewHelper(logger)

	// Initialize Analysis Service
	analysisService, err := NewEmailAnalysisService(config.Analysis, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize analysis service: %w", err)
	}

	// Initialize Dashboard Service with in-memory store
	dashboardService := NewEmailDashboardServiceWithMemoryStore(analysisService, logger)

	// Initialize Retrieval Service
	retrievalService := NewEmailRetrievalService(
		config.Mailboxes,
		analysisService,
		dashboardService,
		logger,
	)

	service := &EmailIntelligenceService{
		log:       log,
		analysis:  analysisService,
		dashboard: dashboardService,
		retrieval: retrievalService,
		config:    config,
	}

	// Setup HTTP server
	err = service.setupHTTPServer()
	if err != nil {
		return nil, fmt.Errorf("failed to setup HTTP server: %w", err)
	}

	return service, nil
}

// NewEmailIntelligenceServiceWithDatabase creates a comprehensive email intelligence service with database storage
func NewEmailIntelligenceServiceWithDatabase(config *EmailIntelligenceConfig, emailStore EmailStoreInterface, logger log.Logger) (*EmailIntelligenceService, error) {
	log := log.NewHelper(logger)

	// Initialize Analysis Service
	analysisService, err := NewEmailAnalysisService(config.Analysis, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize analysis service: %w", err)
	}

	// Initialize Dashboard Service with database store
	dashboardService := NewEmailDashboardService(analysisService, emailStore, logger)

	// Initialize Retrieval Service
	retrievalService := NewEmailRetrievalService(
		config.Mailboxes,
		analysisService,
		dashboardService,
		logger,
	)

	service := &EmailIntelligenceService{
		log:       log,
		analysis:  analysisService,
		dashboard: dashboardService,
		retrieval: retrievalService,
		config:    config,
	}

	// Setup HTTP server
	err = service.setupHTTPServer()
	if err != nil {
		return nil, fmt.Errorf("failed to setup HTTP server: %w", err)
	}

	return service, nil
}

// 🌐 Setup HTTP server with all routes
func (s *EmailIntelligenceService) setupHTTPServer() error {
	router := mux.NewRouter()

	// Enable CORS if configured
	if s.config.EnableCORS {
		router.Use(s.corsMiddleware)
	}

	// Add logging middleware
	router.Use(s.loggingMiddleware)

	// Health check endpoint
	router.HandleFunc("/health", s.handleHealth).Methods("GET")
	router.HandleFunc("/status", s.handleStatus).Methods("GET")

	// Dashboard routes
	s.dashboard.SetupRoutes(router)

	// Email retrieval routes
	s.setupRetrievalRoutes(router)

	// Analysis routes
	s.setupAnalysisRoutes(router)

	// Static files for web interface (if needed)
	router.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("./web/static/"))))

	// Create HTTP server
	s.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", s.config.HTTPPort),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	s.log.Infof("HTTP server configured on port %d", s.config.HTTPPort)
	return nil
}

// 🔧 Setup retrieval routes
func (s *EmailIntelligenceService) setupRetrievalRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/v1/retrieval").Subrouter()

	api.HandleFunc("/start", s.handleStartRetrieval).Methods("POST")
	api.HandleFunc("/stop", s.handleStopRetrieval).Methods("POST")
	api.HandleFunc("/status", s.handleRetrievalStatus).Methods("GET")
	api.HandleFunc("/mailboxes", s.handleListMailboxes).Methods("GET")
	api.HandleFunc("/mailboxes", s.handleAddMailbox).Methods("POST")
	api.HandleFunc("/mailboxes/{name}", s.handleUpdateMailbox).Methods("PUT")
	api.HandleFunc("/mailboxes/{name}", s.handleRemoveMailbox).Methods("DELETE")
	api.HandleFunc("/mailboxes/{name}/test", s.handleTestMailbox).Methods("POST")
	api.HandleFunc("/mailboxes/{name}/retrieve", s.handleRetrieveFromMailbox).Methods("POST")
}

// 🔧 Setup analysis routes
func (s *EmailIntelligenceService) setupAnalysisRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/v1/analysis").Subrouter()

	api.HandleFunc("/analyze", s.handleAnalyzeEmail).Methods("POST")
	api.HandleFunc("/batch-analyze", s.handleBatchAnalyze).Methods("POST")
	api.HandleFunc("/reanalyze/{id}", s.handleReanalyzeEmail).Methods("POST")
}

// 🚀 Start the email intelligence service
func (s *EmailIntelligenceService) Start(ctx context.Context) error {
	s.log.WithContext(ctx).Info("Starting Email Intelligence Service")

	// Start email retrieval
	err := s.retrieval.StartRetrieval(ctx)
	if err != nil {
		return fmt.Errorf("failed to start email retrieval: %w", err)
	}

	// Start HTTP server
	go func() {
		s.log.Infof("Starting HTTP server on port %d", s.config.HTTPPort)
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.log.Errorf("HTTP server error: %v", err)
		}
	}()

	s.log.WithContext(ctx).Info("Email Intelligence Service started successfully")
	return nil
}

// 🛑 Stop the email intelligence service
func (s *EmailIntelligenceService) Stop(ctx context.Context) error {
	s.log.WithContext(ctx).Info("Stopping Email Intelligence Service")

	// Stop HTTP server
	if s.httpServer != nil {
		ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		if err := s.httpServer.Shutdown(ctx); err != nil {
			s.log.WithContext(ctx).Errorf("HTTP server shutdown error: %v", err)
		}
	}

	s.log.WithContext(ctx).Info("Email Intelligence Service stopped")
	return nil
}

// 🏥 Health check handler
func (s *EmailIntelligenceService) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"healthy","service":"email-intelligence"}`))
}

// 📊 Status handler
func (s *EmailIntelligenceService) handleStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	status := &ServiceStatus{
		Status:  "running",
		Version: "1.0.0",
		Uptime:  time.Since(time.Now()), // Placeholder
	}

	// Get retrieval stats
	retrievalStats, err := s.retrieval.GetRetrievalStats(ctx)
	if err != nil {
		status.Errors = append(status.Errors, fmt.Sprintf("Retrieval stats error: %v", err))
	} else {
		status.ActiveMailboxes = retrievalStats.ActiveMailboxes
		status.ProcessedEmails = retrievalStats.TotalRetrieved
		status.LastProcessed = retrievalStats.LastRetrieval
	}

	// Get dashboard stats
	dashStats, err := s.dashboard.GetDashboardStats(ctx)
	if err != nil {
		status.Errors = append(status.Errors, fmt.Sprintf("Dashboard stats error: %v", err))
	} else {
		status.AnalysisService = &AnalysisServiceStatus{
			Status:         "running",
			ProcessedCount: dashStats.TotalEmails,
			LastAnalysis:   dashStats.ProcessingMetrics.LastProcessed,
		}
	}

	status.RetrievalService = &RetrievalServiceStatus{
		Status:          "running",
		ActiveMailboxes: status.ActiveMailboxes,
		RetrievedCount:  status.ProcessedEmails,
		LastRetrieval:   status.LastProcessed,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := writeJSON(w, status); err != nil {
		http.Error(w, "Failed to encode status", http.StatusInternalServerError)
	}
}

// 🔄 Start retrieval handler
func (s *EmailIntelligenceService) handleStartRetrieval(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	err := s.retrieval.StartRetrieval(ctx)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to start retrieval: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"started","message":"Email retrieval started"}`))
}

// ⏹️ Stop retrieval handler
func (s *EmailIntelligenceService) handleStopRetrieval(w http.ResponseWriter, r *http.Request) {
	// Implementation would stop retrieval goroutines
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"stopped","message":"Email retrieval stopped"}`))
}

// 📊 Retrieval status handler
func (s *EmailIntelligenceService) handleRetrievalStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	stats, err := s.retrieval.GetRetrievalStats(ctx)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get retrieval stats: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := writeJSON(w, stats); err != nil {
		http.Error(w, "Failed to encode stats", http.StatusInternalServerError)
	}
}

// 📮 List mailboxes handler
func (s *EmailIntelligenceService) handleListMailboxes(w http.ResponseWriter, r *http.Request) {
	mailboxes := s.retrieval.ListMailboxes()

	w.Header().Set("Content-Type", "application/json")
	if err := writeJSON(w, mailboxes); err != nil {
		http.Error(w, "Failed to encode mailboxes", http.StatusInternalServerError)
	}
}

// ➕ Add mailbox handler
func (s *EmailIntelligenceService) handleAddMailbox(w http.ResponseWriter, r *http.Request) {
	var config MailboxConfig
	if err := readJSON(r, &config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := s.retrieval.AddMailbox(&config)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to add mailbox: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"added","message":"Mailbox added successfully"}`))
}

// 🔄 Update mailbox handler
func (s *EmailIntelligenceService) handleUpdateMailbox(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	name := vars["name"]

	var config MailboxConfig
	if err := readJSON(r, &config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := s.retrieval.UpdateMailbox(name, &config)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to update mailbox: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"updated","message":"Mailbox updated successfully"}`))
}

// 🗑️ Remove mailbox handler
func (s *EmailIntelligenceService) handleRemoveMailbox(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	name := vars["name"]

	err := s.retrieval.RemoveMailbox(name)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to remove mailbox: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"removed","message":"Mailbox removed successfully"}`))
}

// 🧪 Test mailbox handler
func (s *EmailIntelligenceService) handleTestMailbox(w http.ResponseWriter, r *http.Request) {
	var config MailboxConfig
	if err := readJSON(r, &config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()
	err := s.retrieval.TestMailboxConnection(ctx, &config)
	if err != nil {
		http.Error(w, fmt.Sprintf("Mailbox test failed: %v", err), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"success","message":"Mailbox connection test successful"}`))
}

// 📥 Retrieve from mailbox handler
func (s *EmailIntelligenceService) handleRetrieveFromMailbox(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	name := vars["name"]

	ctx := r.Context()
	err := s.retrieval.RetrieveFromMailbox(ctx, name)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to retrieve from mailbox: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"success","message":"Email retrieval initiated"}`))
}

// 🔍 Analyze email handler
func (s *EmailIntelligenceService) handleAnalyzeEmail(w http.ResponseWriter, r *http.Request) {
	// This is handled by dashboard service
	s.dashboard.HandleAnalyzeEmail(w, r)
}

// 📦 Batch analyze handler
func (s *EmailIntelligenceService) handleBatchAnalyze(w http.ResponseWriter, r *http.Request) {
	// Implementation for batch analysis
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"not_implemented","message":"Batch analysis not yet implemented"}`))
}

// 🔄 Reanalyze email handler
func (s *EmailIntelligenceService) handleReanalyzeEmail(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	emailID := vars["id"]

	// Get email from store
	email, exists := s.dashboard.emailStore.Get(emailID)
	if !exists {
		http.Error(w, "Email not found", http.StatusNotFound)
		return
	}

	// Reanalyze
	ctx := r.Context()
	result, err := s.analysis.AnalyzeEmail(ctx, []byte(email.BodyAnalysis.Content))
	if err != nil {
		http.Error(w, fmt.Sprintf("Reanalysis failed: %v", err), http.StatusInternalServerError)
		return
	}

	// Update store
	s.dashboard.emailStore.Store(result)

	w.Header().Set("Content-Type", "application/json")
	if err := writeJSON(w, result); err != nil {
		http.Error(w, "Failed to encode result", http.StatusInternalServerError)
	}
}

// 🔧 Middleware functions
func (s *EmailIntelligenceService) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func (s *EmailIntelligenceService) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		next.ServeHTTP(w, r)
		s.log.Infof("%s %s %v", r.Method, r.URL.Path, time.Since(start))
	})
}

// 🔧 Helper functions
func writeJSON(w http.ResponseWriter, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	return json.NewEncoder(w).Encode(data)
}

func readJSON(r *http.Request, data interface{}) error {
	return json.NewDecoder(r.Body).Decode(data)
}
