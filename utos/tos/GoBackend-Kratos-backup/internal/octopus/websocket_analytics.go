// GoBackend-Kratos/internal/octopus/websocket_analytics.go
package octopus

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"gobackend-hvac-kratos/internal/service"
	pb "gobackend-hvac-kratos/api/analytics/v1"
)

// 📊 Real-time Analytics WebSocket Handler
// Provides live streaming of analytics data to frontend dashboards
type AnalyticsWebSocket struct {
	log         *log.Helper
	analytics   *service.AnalyticsService
	connections map[string]*websocket.Conn
	mutex       sync.RWMutex
	ticker      *time.Ticker
	stopChan    chan bool
}

// WebSocket message types
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// Analytics data structure for WebSocket
type AnalyticsUpdate struct {
	RealTimeMetrics    map[string]interface{} `json:"real_time_metrics"`
	ExecutiveSummary   interface{}            `json:"executive_summary"`
	CustomerInsights   interface{}            `json:"customer_insights"`
	PerformanceTrends  interface{}            `json:"performance_trends"`
	SystemAlerts       []interface{}          `json:"system_alerts"`
}

// NewAnalyticsWebSocket creates a new analytics WebSocket handler
func NewAnalyticsWebSocket(analytics *service.AnalyticsService, logger *log.Helper) *AnalyticsWebSocket {
	return &AnalyticsWebSocket{
		log:         logger,
		analytics:   analytics,
		connections: make(map[string]*websocket.Conn),
		stopChan:    make(chan bool),
	}
}

// Start begins the real-time analytics streaming
func (aws *AnalyticsWebSocket) Start() {
	aws.log.Info("🚀 Starting Analytics WebSocket streaming")

	// Start real-time metrics streaming every 5 seconds
	aws.ticker = time.NewTicker(5 * time.Second)

	go func() {
		for {
			select {
			case <-aws.ticker.C:
				aws.streamAnalyticsUpdate()
			case <-aws.stopChan:
				aws.log.Info("🛑 Stopping Analytics WebSocket streaming")
				return
			}
		}
	}()
}

// Stop stops the analytics streaming
func (aws *AnalyticsWebSocket) Stop() {
	if aws.ticker != nil {
		aws.ticker.Stop()
	}
	aws.stopChan <- true

	// Close all connections
	aws.mutex.Lock()
	defer aws.mutex.Unlock()

	for connID, conn := range aws.connections {
		conn.Close()
		delete(aws.connections, connID)
	}
}

// AddConnection adds a new WebSocket connection
func (aws *AnalyticsWebSocket) AddConnection(connID string, conn *websocket.Conn) {
	aws.mutex.Lock()
	defer aws.mutex.Unlock()

	aws.connections[connID] = conn
	aws.log.Infof("📱 New analytics connection added: %s (Total: %d)", connID, len(aws.connections))

	// Send initial data to new connection
	go aws.sendInitialData(conn)
}

// RemoveConnection removes a WebSocket connection
func (aws *AnalyticsWebSocket) RemoveConnection(connID string) {
	aws.mutex.Lock()
	defer aws.mutex.Unlock()

	if conn, exists := aws.connections[connID]; exists {
		conn.Close()
		delete(aws.connections, connID)
		aws.log.Infof("📱 Analytics connection removed: %s (Remaining: %d)", connID, len(aws.connections))
	}
}

// streamAnalyticsUpdate streams real-time analytics data to all connected clients
func (aws *AnalyticsWebSocket) streamAnalyticsUpdate() {
	ctx := context.Background()

	// Gather all analytics data
	update, err := aws.gatherAnalyticsData(ctx)
	if err != nil {
		aws.log.Errorf("❌ Failed to gather analytics data: %v", err)
		return
	}

	// Create WebSocket message
	message := WSMessage{
		Type:      "analytics_update",
		Data:      update,
		Timestamp: time.Now(),
	}

	// Broadcast to all connections
	aws.broadcastMessage(message)
}

// gatherAnalyticsData collects all analytics data from various sources
func (aws *AnalyticsWebSocket) gatherAnalyticsData(ctx context.Context) (*AnalyticsUpdate, error) {
	// Real-time metrics
	realTimeResp, err := aws.analytics.GetRealTimeMetrics(ctx, &pb.GetRealTimeMetricsRequest{})
	var realTimeMetrics map[string]interface{}
	if err != nil {
		aws.log.Warnf("⚠️ Failed to get real-time metrics: %v", err)
		realTimeMetrics = make(map[string]interface{})
	} else {
		realTimeMetrics = realTimeResp.Metrics.AsMap()
	}

	// Executive dashboard summary
	executiveResp, err := aws.analytics.GetExecutiveDashboard(ctx, &pb.GetExecutiveDashboardRequest{})
	var executiveSummary interface{}
	if err != nil {
		aws.log.Warnf("⚠️ Failed to get executive dashboard: %v", err)
		executiveSummary = nil
	} else {
		executiveSummary = executiveResp.Data
	}

	// Customer insights (placeholder - method doesn't exist in protobuf)
	var customerInsights interface{}
	customerInsights = []interface{}{} // Empty for now

	// Performance trends (last 4 weeks)
	trendsResp, err := aws.analytics.GetPerformanceTrends(ctx, &pb.GetPerformanceTrendsRequest{Weeks: 4})
	var performanceTrends interface{}
	if err != nil {
		aws.log.Warnf("⚠️ Failed to get performance trends: %v", err)
		performanceTrends = nil
	} else {
		performanceTrends = trendsResp.Trends
	}

	// System alerts (placeholder - would integrate with monitoring system)
	systemAlerts := aws.generateSystemAlerts(ctx)

	return &AnalyticsUpdate{
		RealTimeMetrics:   realTimeMetrics,
		ExecutiveSummary:  executiveSummary,
		CustomerInsights:  customerInsights,
		PerformanceTrends: performanceTrends,
		SystemAlerts:      systemAlerts,
	}, nil
}

// sendInitialData sends initial analytics data to a new connection
func (aws *AnalyticsWebSocket) sendInitialData(conn *websocket.Conn) {
	ctx := context.Background()

	// Gather initial data
	update, err := aws.gatherAnalyticsData(ctx)
	if err != nil {
		aws.log.Errorf("❌ Failed to gather initial analytics data: %v", err)
		return
	}

	// Create welcome message
	message := WSMessage{
		Type:      "initial_data",
		Data:      update,
		Timestamp: time.Now(),
	}

	// Send to specific connection
	if err := conn.WriteJSON(message); err != nil {
		aws.log.Errorf("❌ Failed to send initial data: %v", err)
	}
}

// broadcastMessage broadcasts a message to all connected clients
func (aws *AnalyticsWebSocket) broadcastMessage(message WSMessage) {
	aws.mutex.RLock()
	defer aws.mutex.RUnlock()

	if len(aws.connections) == 0 {
		return
	}

	// Convert message to JSON
	messageJSON, err := json.Marshal(message)
	if err != nil {
		aws.log.Errorf("❌ Failed to marshal WebSocket message: %v", err)
		return
	}

	// Send to all connections
	var failedConnections []string
	for connID, conn := range aws.connections {
		if err := conn.WriteMessage(websocket.TextMessage, messageJSON); err != nil {
			aws.log.Warnf("⚠️ Failed to send message to connection %s: %v", connID, err)
			failedConnections = append(failedConnections, connID)
		}
	}

	// Remove failed connections
	for _, connID := range failedConnections {
		aws.RemoveConnection(connID)
	}

	if len(failedConnections) == 0 {
		aws.log.Debugf("📡 Analytics update broadcasted to %d connections", len(aws.connections))
	}
}

// generateSystemAlerts generates system alerts for the dashboard
func (aws *AnalyticsWebSocket) generateSystemAlerts(ctx context.Context) []interface{} {
	alerts := []interface{}{}

	// Get real-time metrics for alert generation
	metricsResp, err := aws.analytics.GetRealTimeMetrics(ctx, &pb.GetRealTimeMetricsRequest{})
	if err != nil {
		return alerts
	}

	metrics := metricsResp.Metrics.AsMap()

	// Check for low revenue alert
	if todayRevenue, ok := metrics["today_revenue"].(float64); ok && todayRevenue < 1000 {
		alerts = append(alerts, map[string]interface{}{
			"id":       "low_revenue",
			"type":     "warning",
			"title":    "Low Revenue Alert",
			"message":  fmt.Sprintf("Today's revenue (%.2f) is below target", todayRevenue),
			"priority": "medium",
		})
	}

	// Check for low job count alert
	if todayJobs, ok := metrics["today_jobs"].(float64); ok && todayJobs < 5 {
		alerts = append(alerts, map[string]interface{}{
			"id":       "low_jobs",
			"type":     "info",
			"title":    "Low Job Count",
			"message":  fmt.Sprintf("Only %.0f jobs completed today", todayJobs),
			"priority": "low",
		})
	}

	// System health alert
	alerts = append(alerts, map[string]interface{}{
		"id":       "system_health",
		"type":     "success",
		"title":    "System Operational",
		"message":  "All systems running normally",
		"priority": "info",
	})

	return alerts
}

// GetConnectionCount returns the number of active WebSocket connections
func (aws *AnalyticsWebSocket) GetConnectionCount() int {
	aws.mutex.RLock()
	defer aws.mutex.RUnlock()
	return len(aws.connections)
}

// SendCustomMessage sends a custom message to all connected clients
func (aws *AnalyticsWebSocket) SendCustomMessage(messageType string, data interface{}) {
	message := WSMessage{
		Type:      messageType,
		Data:      data,
		Timestamp: time.Now(),
	}

	aws.broadcastMessage(message)
}
