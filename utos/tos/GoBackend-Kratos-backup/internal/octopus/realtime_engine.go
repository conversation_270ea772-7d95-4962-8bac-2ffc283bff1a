package octopus

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
)

// ⚡ Enhanced Real-time Engine for Octopus Interface
type RealtimeEngine struct {
	log                *log.Helper
	octopus           *MorphicOctopusInterface
	connections       map[string]*RealtimeConnection
	connectionsMutex  sync.RWMutex
	channels          map[string]*RealtimeChannel
	channelsMutex     sync.RWMutex
	eventBus          *EventBus
	streamProcessors  map[string]*RealtimeStreamProcessor
	subscriptions     map[string][]*Subscription
	subscriptionsMutex sync.RWMutex
}

// 🔌 Enhanced Real-time Connection
type RealtimeConnection struct {
	ID               string                 `json:"id"`
	UserID           string                 `json:"user_id"`
	Role             string                 `json:"role"`
	WebSocket        *websocket.Conn        `json:"-"`
	Channels         []string               `json:"channels"`
	Subscriptions    []string               `json:"subscriptions"`
	LastActivity     time.Time              `json:"last_activity"`
	ConnectedAt      time.Time              `json:"connected_at"`
	MessageCount     int64                  `json:"message_count"`
	BytesTransferred int64                  `json:"bytes_transferred"`
	Metadata         map[string]interface{} `json:"metadata"`
	IsActive         bool                   `json:"is_active"`
}

// 📡 Real-time Channel
type RealtimeChannel struct {
	Name             string                 `json:"name"`
	Type             string                 `json:"type"` // broadcast, targeted, private
	Subscribers      []string               `json:"subscribers"`
	MessageHistory   []*ChannelMessage      `json:"message_history"`
	MaxHistory       int                    `json:"max_history"`
	RateLimit        *RateLimit             `json:"rate_limit"`
	Permissions      *ChannelPermissions    `json:"permissions"`
	CreatedAt        time.Time              `json:"created_at"`
	LastActivity     time.Time              `json:"last_activity"`
	MessageCount     int64                  `json:"message_count"`
	IsActive         bool                   `json:"is_active"`
}

// 💬 Channel Message
type ChannelMessage struct {
	ID               string                 `json:"id"`
	Channel          string                 `json:"channel"`
	Type             string                 `json:"type"`
	Payload          interface{}            `json:"payload"`
	SenderID         string                 `json:"sender_id"`
	Recipients       []string               `json:"recipients"`
	Priority         int                    `json:"priority"`
	TTL              time.Duration          `json:"ttl"`
	Timestamp        time.Time              `json:"timestamp"`
	ExpiresAt        time.Time              `json:"expires_at"`
	Metadata         map[string]interface{} `json:"metadata"`
	DeliveryStatus   map[string]string      `json:"delivery_status"`
}

// 🚦 Rate Limit
type RateLimit struct {
	MaxMessages      int           `json:"max_messages"`
	TimeWindow       time.Duration `json:"time_window"`
	CurrentCount     int           `json:"current_count"`
	WindowStart      time.Time     `json:"window_start"`
	Enabled          bool          `json:"enabled"`
}

// 🔒 Channel Permissions
type ChannelPermissions struct {
	AllowedRoles     []string `json:"allowed_roles"`
	AllowedUsers     []string `json:"allowed_users"`
	RequireAuth      bool     `json:"require_auth"`
	ReadOnly         bool     `json:"read_only"`
	ModeratedChannel bool     `json:"moderated_channel"`
}

// 🚌 Event Bus
type EventBus struct {
	events           chan *Event
	subscribers      map[string][]EventHandler
	subscribersMutex sync.RWMutex
	isRunning        bool
}

// 📨 Event
type Event struct {
	Type             string                 `json:"type"`
	Source           string                 `json:"source"`
	Data             interface{}            `json:"data"`
	Priority         int                    `json:"priority"`
	Timestamp        time.Time              `json:"timestamp"`
	Metadata         map[string]interface{} `json:"metadata"`
	CorrelationID    string                 `json:"correlation_id"`
}

// 🎯 Event Handler
type EventHandler func(*Event) error

// 🌊 Realtime Stream Processor (specific for realtime engine)
type RealtimeStreamProcessor struct {
	Name           string                 `json:"name"`
	Type           string                 `json:"type"`
	Config         map[string]interface{} `json:"config"`
	InputChannels  []string               `json:"input_channels"`
	OutputChannels []string               `json:"output_channels"`
	ProcessingFunc ProcessingFunction     `json:"-"`
	IsActive       bool                   `json:"is_active"`
}

// ⚙️ Processing Function
type ProcessingFunction func(interface{}) (interface{}, error)

// 📋 Subscription
type Subscription struct {
	ID               string                 `json:"id"`
	ConnectionID     string                 `json:"connection_id"`
	Channel          string                 `json:"channel"`
	Filter           *SubscriptionFilter    `json:"filter"`
	Transform        *SubscriptionTransform `json:"transform"`
	CreatedAt        time.Time              `json:"created_at"`
	LastMessage      time.Time              `json:"last_message"`
	MessageCount     int64                  `json:"message_count"`
	IsActive         bool                   `json:"is_active"`
}

// 🔍 Subscription Filter
type SubscriptionFilter struct {
	Type             string                 `json:"type"`
	Conditions       map[string]interface{} `json:"conditions"`
	Enabled          bool                   `json:"enabled"`
}

// 🔄 Subscription Transform
type SubscriptionTransform struct {
	Type             string                 `json:"type"`
	Parameters       map[string]interface{} `json:"parameters"`
	Enabled          bool                   `json:"enabled"`
}

// NewRealtimeEngine creates a new Enhanced Real-time Engine
func NewRealtimeEngine(octopus *MorphicOctopusInterface, logger log.Logger) *RealtimeEngine {
	log := log.NewHelper(logger)

	engine := &RealtimeEngine{
		log:               log,
		octopus:           octopus,
		connections:       make(map[string]*RealtimeConnection),
		channels:          make(map[string]*RealtimeChannel),
		streamProcessors:  make(map[string]*RealtimeStreamProcessor),
		subscriptions:     make(map[string][]*Subscription),
		eventBus: &EventBus{
			events:      make(chan *Event, 1000),
			subscribers: make(map[string][]EventHandler),
		},
	}

	// Initialize default channels
	engine.initializeChannels()

	// Initialize stream processors
	engine.initializeStreamProcessors()

	// Start event bus
	go engine.startEventBus()

	// Start connection monitoring
	go engine.startConnectionMonitoring()

	// Start message cleanup
	go engine.startMessageCleanup()

	return engine
}

// 🚀 Initialize Channels
func (re *RealtimeEngine) initializeChannels() {
	channels := map[string]*RealtimeChannel{
		"system_alerts": {
			Name:         "system_alerts",
			Type:         "broadcast",
			Subscribers:  make([]string, 0),
			MessageHistory: make([]*ChannelMessage, 0),
			MaxHistory:   100,
			RateLimit: &RateLimit{
				MaxMessages: 10,
				TimeWindow:  time.Minute,
				Enabled:     true,
			},
			Permissions: &ChannelPermissions{
				AllowedRoles: []string{"admin", "manager"},
				RequireAuth:  true,
				ReadOnly:     false,
			},
			CreatedAt: time.Now(),
			IsActive:  true,
		},
		"dashboard_updates": {
			Name:         "dashboard_updates",
			Type:         "broadcast",
			Subscribers:  make([]string, 0),
			MessageHistory: make([]*ChannelMessage, 0),
			MaxHistory:   50,
			RateLimit: &RateLimit{
				MaxMessages: 30,
				TimeWindow:  time.Minute,
				Enabled:     true,
			},
			Permissions: &ChannelPermissions{
				AllowedRoles: []string{"admin", "manager", "technician", "analyst"},
				RequireAuth:  false,
				ReadOnly:     true,
			},
			CreatedAt: time.Now(),
			IsActive:  true,
		},
		"customer_events": {
			Name:         "customer_events",
			Type:         "targeted",
			Subscribers:  make([]string, 0),
			MessageHistory: make([]*ChannelMessage, 0),
			MaxHistory:   200,
			RateLimit: &RateLimit{
				MaxMessages: 50,
				TimeWindow:  time.Minute,
				Enabled:     true,
			},
			Permissions: &ChannelPermissions{
				AllowedRoles: []string{"admin", "manager", "technician"},
				RequireAuth:  true,
				ReadOnly:     false,
			},
			CreatedAt: time.Now(),
			IsActive:  true,
		},
		"ai_insights": {
			Name:         "ai_insights",
			Type:         "broadcast",
			Subscribers:  make([]string, 0),
			MessageHistory: make([]*ChannelMessage, 0),
			MaxHistory:   75,
			RateLimit: &RateLimit{
				MaxMessages: 20,
				TimeWindow:  time.Minute,
				Enabled:     true,
			},
			Permissions: &ChannelPermissions{
				AllowedRoles: []string{"admin", "manager", "analyst"},
				RequireAuth:  true,
				ReadOnly:     true,
			},
			CreatedAt: time.Now(),
			IsActive:  true,
		},
	}

	re.channelsMutex.Lock()
	re.channels = channels
	re.channelsMutex.Unlock()

	re.log.Infof("⚡ Initialized %d real-time channels", len(channels))
}

// 🌊 Initialize Stream Processors
func (re *RealtimeEngine) initializeStreamProcessors() {
	processors := map[string]*RealtimeStreamProcessor{
		"alert_filter": {
			Name:           "alert_filter",
			Type:           "filter",
			Config: map[string]interface{}{
				"min_severity": "warning",
				"max_age":      "1h",
			},
			InputChannels:  []string{"system_alerts"},
			OutputChannels: []string{"filtered_alerts"},
			ProcessingFunc: re.filterAlerts,
			IsActive:       true,
		},
		"dashboard_aggregator": {
			Name:           "dashboard_aggregator",
			Type:           "aggregate",
			Config: map[string]interface{}{
				"window_size": "5m",
				"metrics":     []string{"cpu", "memory", "connections"},
			},
			InputChannels:  []string{"system_metrics"},
			OutputChannels: []string{"dashboard_updates"},
			ProcessingFunc: re.aggregateDashboardData,
			IsActive:       true,
		},
		"ai_insight_router": {
			Name:           "ai_insight_router",
			Type:           "route",
			Config: map[string]interface{}{
				"routing_rules": map[string]string{
					"customer_insights": "customer_events",
					"system_insights":   "system_alerts",
					"general_insights":  "ai_insights",
				},
			},
			InputChannels:  []string{"ai_raw_insights"},
			OutputChannels: []string{"customer_events", "system_alerts", "ai_insights"},
			ProcessingFunc: re.routeAIInsights,
			IsActive:       true,
		},
	}

	re.streamProcessors = processors
	re.log.Infof("🌊 Initialized %d stream processors", len(processors))
}

// 🚌 Start Event Bus
func (re *RealtimeEngine) startEventBus() {
	re.eventBus.isRunning = true

	for event := range re.eventBus.events {
		re.processEvent(event)
	}
}

// 📨 Process Event
func (re *RealtimeEngine) processEvent(event *Event) {
	re.eventBus.subscribersMutex.RLock()
	handlers, exists := re.eventBus.subscribers[event.Type]
	re.eventBus.subscribersMutex.RUnlock()

	if !exists {
		return
	}

	for _, handler := range handlers {
		go func(h EventHandler) {
			if err := h(event); err != nil {
				re.log.Errorf("Error processing event %s: %v", event.Type, err)
			}
		}(handler)
	}
}

// 👁️ Start Connection Monitoring
func (re *RealtimeEngine) startConnectionMonitoring() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		re.monitorConnections()
	}
}

// 🔍 Monitor Connections
func (re *RealtimeEngine) monitorConnections() {
	re.connectionsMutex.Lock()
	defer re.connectionsMutex.Unlock()

	deadConnections := make([]string, 0)

	for id, conn := range re.connections {
		// Check if connection is still alive
		if time.Since(conn.LastActivity) > 5*time.Minute {
			// Send ping to check connection
			if err := conn.WebSocket.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				deadConnections = append(deadConnections, id)
				conn.IsActive = false
			}
		}
	}

	// Remove dead connections
	for _, id := range deadConnections {
		re.removeConnection(id)
	}

	if len(deadConnections) > 0 {
		re.log.Infof("🧹 Cleaned up %d dead connections", len(deadConnections))
	}
}

// 🧹 Start Message Cleanup
func (re *RealtimeEngine) startMessageCleanup() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		re.cleanupExpiredMessages()
	}
}

// 🗑️ Cleanup Expired Messages
func (re *RealtimeEngine) cleanupExpiredMessages() {
	re.channelsMutex.Lock()
	defer re.channelsMutex.Unlock()

	totalCleaned := 0

	for _, channel := range re.channels {
		cleaned := 0
		newHistory := make([]*ChannelMessage, 0)

		for _, msg := range channel.MessageHistory {
			if time.Now().Before(msg.ExpiresAt) {
				newHistory = append(newHistory, msg)
			} else {
				cleaned++
			}
		}

		channel.MessageHistory = newHistory
		totalCleaned += cleaned
	}

	if totalCleaned > 0 {
		re.log.Infof("🗑️ Cleaned up %d expired messages", totalCleaned)
	}
}

// 🔌 Add Connection
func (re *RealtimeEngine) AddConnection(id, userID, role string, ws *websocket.Conn) *RealtimeConnection {
	re.connectionsMutex.Lock()
	defer re.connectionsMutex.Unlock()

	conn := &RealtimeConnection{
		ID:               id,
		UserID:           userID,
		Role:             role,
		WebSocket:        ws,
		Channels:         make([]string, 0),
		Subscriptions:    make([]string, 0),
		LastActivity:     time.Now(),
		ConnectedAt:      time.Now(),
		MessageCount:     0,
		BytesTransferred: 0,
		Metadata:         make(map[string]interface{}),
		IsActive:         true,
	}

	re.connections[id] = conn
	re.log.Infof("🔌 Added real-time connection: %s (user: %s, role: %s)", id, userID, role)

	return conn
}

// 🔌 Remove Connection
func (re *RealtimeEngine) removeConnection(id string) {
	if conn, exists := re.connections[id]; exists {
		conn.WebSocket.Close()

		// Remove from all channels
		for _, channelName := range conn.Channels {
			re.unsubscribeFromChannel(id, channelName)
		}

		delete(re.connections, id)
		re.log.Infof("🔌 Removed connection: %s", id)
	}
}

// 📡 Subscribe to Channel
func (re *RealtimeEngine) SubscribeToChannel(connectionID, channelName string) error {
	re.connectionsMutex.Lock()
	conn, connExists := re.connections[connectionID]
	re.connectionsMutex.Unlock()

	if !connExists {
		return fmt.Errorf("connection not found: %s", connectionID)
	}

	re.channelsMutex.Lock()
	channel, channelExists := re.channels[channelName]
	re.channelsMutex.Unlock()

	if !channelExists {
		return fmt.Errorf("channel not found: %s", channelName)
	}

	// Check permissions
	if !re.hasChannelPermission(conn, channel) {
		return fmt.Errorf("permission denied for channel: %s", channelName)
	}

	// Add to channel subscribers
	re.channelsMutex.Lock()
	channel.Subscribers = append(channel.Subscribers, connectionID)
	re.channelsMutex.Unlock()

	// Add to connection channels
	re.connectionsMutex.Lock()
	conn.Channels = append(conn.Channels, channelName)
	re.connectionsMutex.Unlock()

	re.log.Infof("📡 Connection %s subscribed to channel %s", connectionID, channelName)
	return nil
}

// 📡 Unsubscribe from Channel
func (re *RealtimeEngine) unsubscribeFromChannel(connectionID, channelName string) {
	re.channelsMutex.Lock()
	if channel, exists := re.channels[channelName]; exists {
		// Remove from channel subscribers
		for i, sub := range channel.Subscribers {
			if sub == connectionID {
				channel.Subscribers = append(channel.Subscribers[:i], channel.Subscribers[i+1:]...)
				break
			}
		}
	}
	re.channelsMutex.Unlock()

	re.connectionsMutex.Lock()
	if conn, exists := re.connections[connectionID]; exists {
		// Remove from connection channels
		for i, ch := range conn.Channels {
			if ch == channelName {
				conn.Channels = append(conn.Channels[:i], conn.Channels[i+1:]...)
				break
			}
		}
	}
	re.connectionsMutex.Unlock()
}

// 🔒 Check Channel Permission
func (re *RealtimeEngine) hasChannelPermission(conn *RealtimeConnection, channel *RealtimeChannel) bool {
	if !channel.Permissions.RequireAuth {
		return true
	}

	// Check role permissions
	for _, allowedRole := range channel.Permissions.AllowedRoles {
		if conn.Role == allowedRole {
			return true
		}
	}

	// Check user permissions
	for _, allowedUser := range channel.Permissions.AllowedUsers {
		if conn.UserID == allowedUser {
			return true
		}
	}

	return false
}

// 📤 Broadcast Message
func (re *RealtimeEngine) BroadcastMessage(channelName string, messageType string, payload interface{}) error {
	re.channelsMutex.RLock()
	channel, exists := re.channels[channelName]
	re.channelsMutex.RUnlock()

	if !exists {
		return fmt.Errorf("channel not found: %s", channelName)
	}

	// Check rate limit
	if !re.checkRateLimit(channel) {
		return fmt.Errorf("rate limit exceeded for channel: %s", channelName)
	}

	message := &ChannelMessage{
		ID:             fmt.Sprintf("msg_%d", time.Now().UnixNano()),
		Channel:        channelName,
		Type:           messageType,
		Payload:        payload,
		SenderID:       "system",
		Recipients:     channel.Subscribers,
		Priority:       1,
		TTL:            1 * time.Hour,
		Timestamp:      time.Now(),
		ExpiresAt:      time.Now().Add(1 * time.Hour),
		Metadata:       make(map[string]interface{}),
		DeliveryStatus: make(map[string]string),
	}

	// Add to channel history
	re.channelsMutex.Lock()
	channel.MessageHistory = append(channel.MessageHistory, message)
	if len(channel.MessageHistory) > channel.MaxHistory {
		channel.MessageHistory = channel.MessageHistory[1:]
	}
	channel.MessageCount++
	channel.LastActivity = time.Now()
	re.channelsMutex.Unlock()

	// Send to all subscribers
	re.connectionsMutex.RLock()
	for _, subscriberID := range channel.Subscribers {
		if conn, exists := re.connections[subscriberID]; exists && conn.IsActive {
			go re.sendMessageToConnection(conn, message)
		}
	}
	re.connectionsMutex.RUnlock()

	return nil
}

// 📤 Send Message to Connection
func (re *RealtimeEngine) sendMessageToConnection(conn *RealtimeConnection, message *ChannelMessage) {
	data, err := json.Marshal(message)
	if err != nil {
		re.log.Errorf("Failed to marshal message: %v", err)
		return
	}

	if err := conn.WebSocket.WriteMessage(websocket.TextMessage, data); err != nil {
		re.log.Errorf("Failed to send message to connection %s: %v", conn.ID, err)
		conn.IsActive = false
		return
	}

	conn.MessageCount++
	conn.BytesTransferred += int64(len(data))
	conn.LastActivity = time.Now()
	message.DeliveryStatus[conn.ID] = "delivered"
}

// 🚦 Check Rate Limit
func (re *RealtimeEngine) checkRateLimit(channel *RealtimeChannel) bool {
	if !channel.RateLimit.Enabled {
		return true
	}

	now := time.Now()

	// Reset window if needed
	if now.Sub(channel.RateLimit.WindowStart) > channel.RateLimit.TimeWindow {
		channel.RateLimit.CurrentCount = 0
		channel.RateLimit.WindowStart = now
	}

	// Check limit
	if channel.RateLimit.CurrentCount >= channel.RateLimit.MaxMessages {
		return false
	}

	channel.RateLimit.CurrentCount++
	return true
}

// 🔍 Filter Alerts (Stream Processor)
func (re *RealtimeEngine) filterAlerts(data interface{}) (interface{}, error) {
	// Implementation for filtering alerts
	return data, nil
}

// 📊 Aggregate Dashboard Data (Stream Processor)
func (re *RealtimeEngine) aggregateDashboardData(data interface{}) (interface{}, error) {
	// Implementation for aggregating dashboard data
	return data, nil
}

// 🎯 Route AI Insights (Stream Processor)
func (re *RealtimeEngine) routeAIInsights(data interface{}) (interface{}, error) {
	// Implementation for routing AI insights
	return data, nil
}

// 📊 Get Real-time Engine Dashboard Data
func (re *RealtimeEngine) GetDashboardData() map[string]interface{} {
	re.connectionsMutex.RLock()
	activeConnections := len(re.connections)
	re.connectionsMutex.RUnlock()

	re.channelsMutex.RLock()
	activeChannels := len(re.channels)
	totalMessages := int64(0)
	for _, channel := range re.channels {
		totalMessages += channel.MessageCount
	}
	re.channelsMutex.RUnlock()

	return map[string]interface{}{
		"active_connections": activeConnections,
		"active_channels":    activeChannels,
		"total_messages":     totalMessages,
		"stream_processors":  len(re.streamProcessors),
		"event_bus_running":  re.eventBus.isRunning,
		"connections":        re.getConnectionsSummary(),
		"channels":           re.getChannelsSummary(),
	}
}

// 📊 Get Connections Summary
func (re *RealtimeEngine) getConnectionsSummary() []map[string]interface{} {
	re.connectionsMutex.RLock()
	defer re.connectionsMutex.RUnlock()

	summary := make([]map[string]interface{}, 0)
	for _, conn := range re.connections {
		summary = append(summary, map[string]interface{}{
			"id":                conn.ID,
			"user_id":           conn.UserID,
			"role":              conn.Role,
			"channels":          len(conn.Channels),
			"message_count":     conn.MessageCount,
			"bytes_transferred": conn.BytesTransferred,
			"connected_at":      conn.ConnectedAt,
			"last_activity":     conn.LastActivity,
			"is_active":         conn.IsActive,
		})
	}
	return summary
}

// 📊 Get Channels Summary
func (re *RealtimeEngine) getChannelsSummary() []map[string]interface{} {
	re.channelsMutex.RLock()
	defer re.channelsMutex.RUnlock()

	summary := make([]map[string]interface{}, 0)
	for _, channel := range re.channels {
		summary = append(summary, map[string]interface{}{
			"name":          channel.Name,
			"type":          channel.Type,
			"subscribers":   len(channel.Subscribers),
			"message_count": channel.MessageCount,
			"last_activity": channel.LastActivity,
			"is_active":     channel.IsActive,
		})
	}
	return summary
}
