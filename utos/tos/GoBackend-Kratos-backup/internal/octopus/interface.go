package octopus

import (
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/transcription"
)

// 🐙 MORPHIC OCTOPUS INTERFACE - Potężny system zarządzania backendem
type MorphicOctopusInterface struct {
	log                *log.Helper
	db                 *gorm.DB
	httpServer         *http.Server
	wsUpgrader         websocket.Upgrader

	// Service Tentacles (Macki Usług)
	emailService       *email.EmailIntelligenceService
	transcriptionService *transcription.TranscriptionParser
	customerService    *customer.CustomerIntelligenceService
	aiService          *ai.Gemma3Service
	aiEmailService     *AIEmailService

	// 🔒 Thread-safe WebSocket connections with proper cleanup
	wsConnections      map[string]*websocket.Conn
	wsConnectionsMutex sync.RWMutex
	wsCleanupTicker    *time.Ticker

	// 📊 Analytics WebSocket for real-time dashboard updates
	analyticsWS        *AnalyticsWebSocket

	// 📊 LangFuse-like Flow Tracking
	flowTracker        *FlowTracker
	requestMetrics     *RequestMetrics
	tokenUsage         *TokenUsageTracker

	// Configuration
	config             *OctopusConfig
}

// ⚙️ Octopus Configuration
type OctopusConfig struct {
	HTTPPort           int    `yaml:"http_port"`
	WebSocketEnabled   bool   `yaml:"websocket_enabled"`
	DashboardPath      string `yaml:"dashboard_path"`
	AuthEnabled        bool   `yaml:"auth_enabled"`
	AdminUsers         []string `yaml:"admin_users"`
	RefreshInterval    time.Duration `yaml:"refresh_interval"`
	MaxConnections     int    `yaml:"max_connections"`
}

// 📊 Octopus Dashboard Data
type OctopusDashboard struct {
	SystemStatus       *SystemStatus       `json:"system_status"`
	ServiceHealth      *ServiceHealth      `json:"service_health"`
	CustomerMetrics    *CustomerMetrics    `json:"customer_metrics"`
	TranscriptionStats *TranscriptionStats `json:"transcription_stats"`
	EmailIntelligence  *EmailIntelligence  `json:"email_intelligence"`
	AIPerformance      *AIPerformance      `json:"ai_performance"`
	LangChainMetrics   *LangChainMetrics  `json:"langchain_metrics"`
	WorkflowMetrics    *WorkflowMetrics   `json:"workflow_metrics"`
	RealtimeAlerts     []*Alert           `json:"realtime_alerts"`
	QuickActions       []*QuickAction     `json:"quick_actions"`
	Timestamp          time.Time          `json:"timestamp"`
}

// 🔧 System Status
type SystemStatus struct {
	Uptime             time.Duration `json:"uptime"`
	CPUUsage           float64       `json:"cpu_usage"`
	MemoryUsage        float64       `json:"memory_usage"`
	DatabaseConnections int          `json:"database_connections"`
	ActiveWebSockets   int           `json:"active_websockets"`
	TotalRequests      int64         `json:"total_requests"`
	ErrorRate          float64       `json:"error_rate"`
	ResponseTime       time.Duration `json:"avg_response_time"`
}

// 🏥 Service Health
type ServiceHealth struct {
	EmailService       *ServiceStatus `json:"email_service"`
	TranscriptionService *ServiceStatus `json:"transcription_service"`
	CustomerService    *ServiceStatus `json:"customer_service"`
	AIService          *ServiceStatus `json:"ai_service"`
	DatabaseService    *ServiceStatus `json:"database_service"`
	RedisService       *ServiceStatus `json:"redis_service"`
}

// 📈 Service Status
type ServiceStatus struct {
	Status             string        `json:"status"` // healthy, degraded, unhealthy
	LastCheck          time.Time     `json:"last_check"`
	ResponseTime       time.Duration `json:"response_time"`
	ErrorCount         int           `json:"error_count"`
	SuccessRate        float64       `json:"success_rate"`
	Message            string        `json:"message,omitempty"`
}

// 👥 Customer Metrics
type CustomerMetrics struct {
	TotalCustomers     int64   `json:"total_customers"`
	NewToday           int64   `json:"new_today"`
	NewThisWeek        int64   `json:"new_this_week"`
	ActiveCustomers    int64   `json:"active_customers"`
	HighValueCustomers int64   `json:"high_value_customers"`
	AtRiskCustomers    int64   `json:"at_risk_customers"`
	AvgSatisfaction    float64 `json:"avg_satisfaction"`
	ChurnRate          float64 `json:"churn_rate"`
	LifetimeValue      float64 `json:"avg_lifetime_value"`
}

// 📞 Transcription Statistics
type TranscriptionStats struct {
	TotalCalls         int64   `json:"total_calls"`
	CallsToday         int64   `json:"calls_today"`
	CallsThisWeek      int64   `json:"calls_this_week"`
	HVACRelevantCalls  int64   `json:"hvac_relevant_calls"`
	EmergencyCalls     int64   `json:"emergency_calls"`
	AvgCallDuration    time.Duration `json:"avg_call_duration"`
	AvgConfidence      float64 `json:"avg_confidence"`
	ProcessingBacklog  int64   `json:"processing_backlog"`
	TopCallerCompanies []string `json:"top_caller_companies"`
}

// 📧 Email Intelligence
type EmailIntelligence struct {
	TotalEmails        int64   `json:"total_emails"`
	EmailsToday        int64   `json:"emails_today"`
	EmailsThisWeek     int64   `json:"emails_this_week"`
	HVACRelevantEmails int64   `json:"hvac_relevant_emails"`
	PositiveSentiment  int64   `json:"positive_sentiment"`
	NegativeSentiment  int64   `json:"negative_sentiment"`
	AvgProcessingTime  time.Duration `json:"avg_processing_time"`
	TopKeywords        []string `json:"top_keywords"`
}

// 🤖 AI Performance
type AIPerformance struct {
	TotalRequests      int64         `json:"total_requests"`
	RequestsToday      int64         `json:"requests_today"`
	AvgResponseTime    time.Duration `json:"avg_response_time"`
	SuccessRate        float64       `json:"success_rate"`
	ModelAccuracy      float64       `json:"model_accuracy"`
	TokensProcessed    int64         `json:"tokens_processed"`
	ActiveModels       []string      `json:"active_models"`
	QueueLength        int           `json:"queue_length"`
}

// 🧠 LangChain Metrics
type LangChainMetrics struct {
	TotalWorkflows     int64         `json:"total_workflows"`
	WorkflowsToday     int64         `json:"workflows_today"`
	AvgWorkflowTime    time.Duration `json:"avg_workflow_time"`
	SuccessRate        float64       `json:"success_rate"`
	ActiveChains       []string      `json:"active_chains"`
	ChainExecutions    int64         `json:"chain_executions"`
	TokensConsumed     int64         `json:"tokens_consumed"`
	CostToday          float64       `json:"cost_today"`
	TopWorkflowTypes   []string      `json:"top_workflow_types"`
}

// 🕸️ Workflow Metrics
type WorkflowMetrics struct {
	TotalExecutions    int64         `json:"total_executions"`
	ExecutionsToday    int64         `json:"executions_today"`
	RunningWorkflows   int64         `json:"running_workflows"`
	CompletedWorkflows int64         `json:"completed_workflows"`
	FailedWorkflows    int64         `json:"failed_workflows"`
	AvgExecutionTime   time.Duration `json:"avg_execution_time"`
	WorkflowTypes      []string      `json:"workflow_types"`
	SuccessRate        float64       `json:"success_rate"`
	QueuedWorkflows    int64         `json:"queued_workflows"`
}

// 🚨 Alert System
type Alert struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"` // info, warning, error, critical
	Title       string    `json:"title"`
	Message     string    `json:"message"`
	Source      string    `json:"source"`
	Timestamp   time.Time `json:"timestamp"`
	Acknowledged bool     `json:"acknowledged"`
	Actions     []string  `json:"actions,omitempty"`
}

// ⚡ Quick Actions
type QuickAction struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Endpoint    string `json:"endpoint"`
	Method      string `json:"method"`
	Category    string `json:"category"`
	Dangerous   bool   `json:"dangerous,omitempty"`
}

// 📊 LangFuse-like Flow Tracking Structures

// FlowTracker tracks AI request flows like LangFuse
type FlowTracker struct {
	flows       map[string]*Flow
	flowsMutex  sync.RWMutex
	log         *log.Helper
}

// Flow represents a complete AI interaction flow
type Flow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	UserID      string                 `json:"user_id,omitempty"`
	SessionID   string                 `json:"session_id,omitempty"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    *time.Duration         `json:"duration,omitempty"`
	Status      string                 `json:"status"` // running, completed, failed
	Traces      []*Trace               `json:"traces"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	TotalCost   float64                `json:"total_cost"`
	TokenUsage  *TokenUsage            `json:"token_usage"`
}

// Trace represents a single step in the flow
type Trace struct {
	ID          string                 `json:"id"`
	FlowID      string                 `json:"flow_id"`
	ParentID    string                 `json:"parent_id,omitempty"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"` // llm, embedding, retrieval, tool
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    *time.Duration         `json:"duration,omitempty"`
	Status      string                 `json:"status"` // running, completed, failed
	Input       interface{}            `json:"input,omitempty"`
	Output      interface{}            `json:"output,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	TokenUsage  *TokenUsage            `json:"token_usage,omitempty"`
	Cost        float64                `json:"cost"`
	Model       string                 `json:"model,omitempty"`
}

// TokenUsage tracks token consumption
type TokenUsage struct {
	InputTokens  int64 `json:"input_tokens"`
	OutputTokens int64 `json:"output_tokens"`
	TotalTokens  int64 `json:"total_tokens"`
}

// RequestMetrics tracks overall request metrics
type RequestMetrics struct {
	TotalRequests    int64                    `json:"total_requests"`
	SuccessfulRequests int64                  `json:"successful_requests"`
	FailedRequests   int64                    `json:"failed_requests"`
	AvgResponseTime  time.Duration            `json:"avg_response_time"`
	RequestsByModel  map[string]int64         `json:"requests_by_model"`
	RequestsByType   map[string]int64         `json:"requests_by_type"`
	LastUpdated      time.Time                `json:"last_updated"`
	mutex            sync.RWMutex
}

// TokenUsageTracker tracks token usage across all models
type TokenUsageTracker struct {
	TotalTokens      int64                    `json:"total_tokens"`
	TokensByModel    map[string]*TokenUsage   `json:"tokens_by_model"`
	TokensByUser     map[string]*TokenUsage   `json:"tokens_by_user"`
	TotalCost        float64                  `json:"total_cost"`
	CostByModel      map[string]float64       `json:"cost_by_model"`
	LastUpdated      time.Time                `json:"last_updated"`
	mutex            sync.RWMutex
}

// NewMorphicOctopusInterface creates the ultimate backend management interface
func NewMorphicOctopusInterface(
	db *gorm.DB,
	emailService *email.EmailIntelligenceService,
	transcriptionService *transcription.TranscriptionParser,
	customerService *customer.CustomerIntelligenceService,
	aiService *ai.Gemma3Service,
	config *OctopusConfig,
	logger log.Logger,
) *MorphicOctopusInterface {
	log := log.NewHelper(logger)

	octopus := &MorphicOctopusInterface{
		log:                  log,
		db:                   db,
		emailService:         emailService,
		transcriptionService: transcriptionService,
		customerService:      customerService,
		aiService:            aiService,
		config:               config,
		wsConnections:        make(map[string]*websocket.Conn),
		wsCleanupTicker:      time.NewTicker(30 * time.Second), // Cleanup every 30 seconds

		// 📊 Initialize LangFuse-like tracking
		flowTracker:          NewFlowTracker(logger),
		requestMetrics:       NewRequestMetrics(),
		tokenUsage:           NewTokenUsageTracker(),

		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins in development
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 🧹 Start WebSocket cleanup routine
	go octopus.startWebSocketCleanup()

	// 🌐 Setup HTTP server and routes
	octopus.setupHTTPServer()

	return octopus
}

// 🧹 WebSocket cleanup routine to prevent memory leaks
func (o *MorphicOctopusInterface) startWebSocketCleanup() {
	for range o.wsCleanupTicker.C {
		o.cleanupDeadConnections()
	}
}

// 🔍 Clean up dead WebSocket connections
func (o *MorphicOctopusInterface) cleanupDeadConnections() {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	deadConnections := make([]string, 0)

	for connID, conn := range o.wsConnections {
		// Send ping to check if connection is alive
		if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
			deadConnections = append(deadConnections, connID)
			conn.Close()
		}
	}

	// Remove dead connections
	for _, connID := range deadConnections {
		delete(o.wsConnections, connID)
		o.log.Infof("🧹 Cleaned up dead WebSocket connection: %s", connID)
	}

	if len(deadConnections) > 0 {
		o.log.Infof("🧹 Cleaned up %d dead WebSocket connections", len(deadConnections))
	}
}

// 🔌 Add WebSocket connection with thread safety
func (o *MorphicOctopusInterface) addWebSocketConnection(connID string, conn *websocket.Conn) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	o.wsConnections[connID] = conn
	o.log.Infof("🔌 Added WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
}

// 🔌 Remove WebSocket connection with thread safety
func (o *MorphicOctopusInterface) removeWebSocketConnection(connID string) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	if conn, exists := o.wsConnections[connID]; exists {
		conn.Close()
		delete(o.wsConnections, connID)
		o.log.Infof("🔌 Removed WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
	}
}

// 📊 Get WebSocket connection count
func (o *MorphicOctopusInterface) getWebSocketConnectionCount() int {
	o.wsConnectionsMutex.RLock()
	defer o.wsConnectionsMutex.RUnlock()

	return len(o.wsConnections)
}

// Start method moved to handlers.go

// Stop method moved to handlers.go

// startFlowCleanup starts the flow cleanup routine
func (o *MorphicOctopusInterface) startFlowCleanup() {
	ticker := time.NewTicker(1 * time.Hour) // Cleanup every hour
	defer ticker.Stop()

	for range ticker.C {
		// Cleanup flows older than 24 hours
		removed := o.flowTracker.CleanupOldFlows(24 * time.Hour)
		if removed > 0 {
			o.log.Infof("🧹 Cleaned up %d old flows", removed)
		}
	}
}

// setupHTTPServer method moved to handlers.go

// 🔧 Build System Status
func (o *MorphicOctopusInterface) buildSystemStatus(ctx context.Context) (*SystemStatus, error) {
	status := &SystemStatus{
		Uptime:              time.Since(time.Now().Add(-time.Hour)), // Placeholder
		CPUUsage:            50.0,  // Placeholder
		MemoryUsage:         65.0,  // Placeholder
		DatabaseConnections: 10,    // Placeholder
		ActiveWebSockets:    o.getWebSocketConnectionCount(),
		TotalRequests:       o.requestMetrics.TotalRequests,
		ErrorRate:           0.05,  // Placeholder
		ResponseTime:        o.requestMetrics.AvgResponseTime,
	}

	return status, nil
}

// 🏥 Build Service Health
func (o *MorphicOctopusInterface) buildServiceHealth(ctx context.Context) (*ServiceHealth, error) {
	health := &ServiceHealth{
		EmailService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 50 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.5,
			Message:      "Email service operational",
		},
		TranscriptionService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 100 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  98.0,
			Message:      "Transcription service operational",
		},
		CustomerService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 30 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.8,
			Message:      "Customer service operational",
		},
		AIService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 200 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  97.5,
			Message:      "AI service operational",
		},
		DatabaseService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 10 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.9,
			Message:      "Database operational",
		},
		RedisService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 5 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.9,
			Message:      "Redis operational",
		},
	}

	return health, nil
}
