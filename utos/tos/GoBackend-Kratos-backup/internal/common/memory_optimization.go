package common

import (
	"bytes"
	"context"
	"encoding/json"
	"runtime"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Advanced Memory Optimization Patterns for Go 1.23
// Object pooling, buffer reuse, and memory profiling integration

// ==========================================
// OBJECT POOLS
// ==========================================

// ObjectPool provides generic object pooling
type ObjectPool[T any] struct {
	pool sync.Pool
	new  func() T
}

// NewObjectPool creates a new generic object pool
func NewObjectPool[T any](newFunc func() T) *ObjectPool[T] {
	return &ObjectPool[T]{
		pool: sync.Pool{
			New: func() interface{} {
				return newFunc()
			},
		},
		new: newFunc,
	}
}

// Get retrieves an object from the pool
func (p *ObjectPool[T]) Get() T {
	return p.pool.Get().(T)
}

// Put returns an object to the pool
func (p *ObjectPool[T]) Put(obj T) {
	p.pool.Put(obj)
}

// ==========================================
// BUFFER POOLS FOR HIGH-FREQUENCY OPERATIONS
// ==========================================

// BufferPool manages byte buffers for JSON serialization
type BufferPool struct {
	pool sync.Pool
}

// NewBufferPool creates a new buffer pool
func NewBufferPool(initialSize int) *BufferPool {
	return &BufferPool{
		pool: sync.Pool{
			New: func() interface{} {
				return bytes.NewBuffer(make([]byte, 0, initialSize))
			},
		},
	}
}

// Get retrieves a buffer from the pool
func (bp *BufferPool) Get() *bytes.Buffer {
	return bp.pool.Get().(*bytes.Buffer)
}

// Put returns a buffer to the pool after resetting it
func (bp *BufferPool) Put(buf *bytes.Buffer) {
	buf.Reset()
	bp.pool.Put(buf)
}

// ==========================================
// HVAC-SPECIFIC OBJECT POOLS
// ==========================================

// HVACObjectPools contains all object pools for HVAC entities
type HVACObjectPools struct {
	EmailPool     *ObjectPool[*EmailBuffer]
	CustomerPool  *ObjectPool[*CustomerBuffer]
	JobPool       *ObjectPool[*JobBuffer]
	BufferPool    *BufferPool
	JSONPool      *ObjectPool[*JSONProcessor]
	log           *log.Helper
}

// EmailBuffer represents a reusable email processing buffer
type EmailBuffer struct {
	Subject     string
	Body        string
	From        string
	To          []string
	Attachments []string
	Metadata    map[string]interface{}
}

// Reset clears the email buffer for reuse
func (eb *EmailBuffer) Reset() {
	eb.Subject = ""
	eb.Body = ""
	eb.From = ""
	eb.To = eb.To[:0]
	eb.Attachments = eb.Attachments[:0]
	for k := range eb.Metadata {
		delete(eb.Metadata, k)
	}
}

// CustomerBuffer represents a reusable customer processing buffer
type CustomerBuffer struct {
	ID       int64
	Name     string
	Email    string
	Phone    string
	Address  string
	Metadata map[string]interface{}
}

// Reset clears the customer buffer for reuse
func (cb *CustomerBuffer) Reset() {
	cb.ID = 0
	cb.Name = ""
	cb.Email = ""
	cb.Phone = ""
	cb.Address = ""
	for k := range cb.Metadata {
		delete(cb.Metadata, k)
	}
}

// JobBuffer represents a reusable job processing buffer
type JobBuffer struct {
	ID          int64
	CustomerID  int64
	Title       string
	Description string
	Status      string
	Priority    string
	Metadata    map[string]interface{}
}

// Reset clears the job buffer for reuse
func (jb *JobBuffer) Reset() {
	jb.ID = 0
	jb.CustomerID = 0
	jb.Title = ""
	jb.Description = ""
	jb.Status = ""
	jb.Priority = ""
	for k := range jb.Metadata {
		delete(jb.Metadata, k)
	}
}

// JSONProcessor provides reusable JSON processing
type JSONProcessor struct {
	encoder *json.Encoder
	decoder *json.Decoder
	buffer  *bytes.Buffer
}

// Reset prepares the JSON processor for reuse
func (jp *JSONProcessor) Reset() {
	jp.buffer.Reset()
	jp.encoder = json.NewEncoder(jp.buffer)
	jp.decoder = json.NewDecoder(jp.buffer)
}

// NewHVACObjectPools creates all HVAC object pools
func NewHVACObjectPools(logger log.Logger) *HVACObjectPools {
	return &HVACObjectPools{
		EmailPool: NewObjectPool(func() *EmailBuffer {
			return &EmailBuffer{
				To:          make([]string, 0, 5),
				Attachments: make([]string, 0, 3),
				Metadata:    make(map[string]interface{}),
			}
		}),
		CustomerPool: NewObjectPool(func() *CustomerBuffer {
			return &CustomerBuffer{
				Metadata: make(map[string]interface{}),
			}
		}),
		JobPool: NewObjectPool(func() *JobBuffer {
			return &JobBuffer{
				Metadata: make(map[string]interface{}),
			}
		}),
		BufferPool: NewBufferPool(1024), // 1KB initial buffer
		JSONPool: NewObjectPool(func() *JSONProcessor {
			buf := bytes.NewBuffer(make([]byte, 0, 512))
			return &JSONProcessor{
				buffer:  buf,
				encoder: json.NewEncoder(buf),
				decoder: json.NewDecoder(buf),
			}
		}),
		log: log.NewHelper(logger),
	}
}

// ==========================================
// MEMORY MONITORING
// ==========================================

// MemoryMonitor tracks memory usage and triggers optimization
type MemoryMonitor struct {
	log              *log.Helper
	gcThreshold      uint64 // Memory threshold to trigger GC
	monitorInterval  time.Duration
	lastGCTime       time.Time
	stats            *MemoryStats
	stopChan         chan struct{}
	running          bool
	mu               sync.RWMutex
}

// MemoryStats holds memory usage statistics
type MemoryStats struct {
	Alloc        uint64    `json:"alloc"`         // Current allocated memory
	TotalAlloc   uint64    `json:"total_alloc"`   // Total allocated memory
	Sys          uint64    `json:"sys"`           // System memory
	NumGC        uint32    `json:"num_gc"`        // Number of GC cycles
	LastGC       time.Time `json:"last_gc"`       // Last GC time
	GCPauseTotal uint64    `json:"gc_pause_total"` // Total GC pause time
	Goroutines   int       `json:"goroutines"`    // Number of goroutines
}

// NewMemoryMonitor creates a new memory monitor
func NewMemoryMonitor(logger log.Logger, gcThreshold uint64, monitorInterval time.Duration) *MemoryMonitor {
	return &MemoryMonitor{
		log:             log.NewHelper(logger),
		gcThreshold:     gcThreshold,
		monitorInterval: monitorInterval,
		stats:           &MemoryStats{},
		stopChan:        make(chan struct{}),
	}
}

// Start begins memory monitoring
func (mm *MemoryMonitor) Start(ctx context.Context) {
	mm.mu.Lock()
	if mm.running {
		mm.mu.Unlock()
		return
	}
	mm.running = true
	mm.mu.Unlock()

	go mm.monitorLoop(ctx)
}

// Stop stops memory monitoring
func (mm *MemoryMonitor) Stop() {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	
	if !mm.running {
		return
	}
	
	mm.running = false
	close(mm.stopChan)
}

// GetStats returns current memory statistics
func (mm *MemoryMonitor) GetStats() *MemoryStats {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	// Create a copy to avoid race conditions
	return &MemoryStats{
		Alloc:        mm.stats.Alloc,
		TotalAlloc:   mm.stats.TotalAlloc,
		Sys:          mm.stats.Sys,
		NumGC:        mm.stats.NumGC,
		LastGC:       mm.stats.LastGC,
		GCPauseTotal: mm.stats.GCPauseTotal,
		Goroutines:   mm.stats.Goroutines,
	}
}

// monitorLoop runs the memory monitoring loop
func (mm *MemoryMonitor) monitorLoop(ctx context.Context) {
	ticker := time.NewTicker(mm.monitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-mm.stopChan:
			return
		case <-ticker.C:
			mm.updateStats()
			mm.checkGCThreshold()
		}
	}
}

// updateStats updates memory statistics
func (mm *MemoryMonitor) updateStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mm.mu.Lock()
	mm.stats.Alloc = m.Alloc
	mm.stats.TotalAlloc = m.TotalAlloc
	mm.stats.Sys = m.Sys
	mm.stats.NumGC = m.NumGC
	mm.stats.GCPauseTotal = m.PauseTotalNs
	mm.stats.Goroutines = runtime.NumGoroutine()
	
	if m.NumGC > 0 {
		mm.stats.LastGC = time.Unix(0, int64(m.LastGC))
	}
	mm.mu.Unlock()

	// Log memory stats periodically
	mm.log.Infof("Memory Stats - Alloc: %d MB, Sys: %d MB, NumGC: %d, Goroutines: %d",
		m.Alloc/1024/1024, m.Sys/1024/1024, m.NumGC, runtime.NumGoroutine())
}

// checkGCThreshold checks if GC should be triggered
func (mm *MemoryMonitor) checkGCThreshold() {
	mm.mu.RLock()
	alloc := mm.stats.Alloc
	mm.mu.RUnlock()

	if alloc > mm.gcThreshold && time.Since(mm.lastGCTime) > time.Minute {
		mm.log.Infof("Memory threshold exceeded (%d MB), triggering GC", alloc/1024/1024)
		runtime.GC()
		mm.lastGCTime = time.Now()
	}
}

// ==========================================
// MEMORY OPTIMIZATION UTILITIES
// ==========================================

// OptimizeMemoryUsage performs various memory optimizations
func OptimizeMemoryUsage(logger log.Logger) {
	log := log.NewHelper(logger)
	
	// Force garbage collection
	runtime.GC()
	
	// Get memory stats before optimization
	var before runtime.MemStats
	runtime.ReadMemStats(&before)
	
	// Set GC target percentage (lower = more frequent GC)
	runtime.GOMAXPROCS(runtime.NumCPU())
	
	// Force another GC after settings
	runtime.GC()
	
	// Get memory stats after optimization
	var after runtime.MemStats
	runtime.ReadMemStats(&after)
	
	log.Infof("Memory optimization completed - Before: %d MB, After: %d MB, Saved: %d MB",
		before.Alloc/1024/1024, after.Alloc/1024/1024, (before.Alloc-after.Alloc)/1024/1024)
}

// GetMemoryUsage returns current memory usage in MB
func GetMemoryUsage() (alloc, sys uint64) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return m.Alloc / 1024 / 1024, m.Sys / 1024 / 1024
}
