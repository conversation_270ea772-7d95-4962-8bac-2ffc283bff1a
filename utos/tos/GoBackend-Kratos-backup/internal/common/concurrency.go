package common

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Advanced Concurrency Patterns for Go 1.23
// Worker pools, pipeline patterns, and optimized channel operations

// ==========================================
// WORKER POOL IMPLEMENTATION
// ==========================================

// Job represents a unit of work
type Job[T any, R any] struct {
	ID       string
	Data     T
	ResultCh chan JobResult[R]
	Context  context.Context
}

// JobResult represents the result of a job
type JobResult[R any] struct {
	Result R
	Error  error
}

// WorkerFunc defines the function signature for worker operations
type WorkerFunc[T any, R any] func(ctx context.Context, data T) (R, error)

// WorkerPool manages a pool of workers for concurrent processing
type WorkerPool[T any, R any] struct {
	workers     int
	jobQueue    chan Job[T, R]
	resultQueue chan <PERSON>ult[R]
	workerFunc  WorkerFunc[T, R]
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	log         *log.Helper
	metrics     *WorkerPoolMetrics
	started     bool
	mu          sync.RWMutex
}

// WorkerPoolMetrics tracks worker pool performance
type WorkerPoolMetrics struct {
	JobsProcessed   int64         `json:"jobs_processed"`
	JobsSuccessful  int64         `json:"jobs_successful"`
	JobsFailed      int64         `json:"jobs_failed"`
	AverageLatency  time.Duration `json:"average_latency"`
	ActiveWorkers   int           `json:"active_workers"`
	QueueLength     int           `json:"queue_length"`
	TotalLatency    time.Duration `json:"total_latency"`
	mu              sync.RWMutex
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool[T any, R any](
	workers int,
	queueSize int,
	workerFunc WorkerFunc[T, R],
	logger log.Logger,
) *WorkerPool[T, R] {
	if workers <= 0 {
		workers = runtime.NumCPU()
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WorkerPool[T, R]{
		workers:     workers,
		jobQueue:    make(chan Job[T, R], queueSize),
		resultQueue: make(chan JobResult[R], queueSize),
		workerFunc:  workerFunc,
		ctx:         ctx,
		cancel:      cancel,
		log:         log.NewHelper(logger),
		metrics:     &WorkerPoolMetrics{},
	}
}

// Start starts the worker pool
func (wp *WorkerPool[T, R]) Start() {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	
	if wp.started {
		return
	}
	
	wp.started = true
	
	// Start workers
	for i := 0; i < wp.workers; i++ {
		wp.wg.Add(1)
		go wp.worker(i)
	}
	
	wp.log.Infof("Worker pool started with %d workers", wp.workers)
}

// Stop stops the worker pool gracefully
func (wp *WorkerPool[T, R]) Stop() {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	
	if !wp.started {
		return
	}
	
	wp.cancel()
	close(wp.jobQueue)
	wp.wg.Wait()
	wp.started = false
	
	wp.log.Info("Worker pool stopped")
}

// Submit submits a job to the worker pool
func (wp *WorkerPool[T, R]) Submit(ctx context.Context, jobID string, data T) <-chan JobResult[R] {
	resultCh := make(chan JobResult[R], 1)
	
	job := Job[T, R]{
		ID:       jobID,
		Data:     data,
		ResultCh: resultCh,
		Context:  ctx,
	}
	
	select {
	case wp.jobQueue <- job:
		return resultCh
	case <-ctx.Done():
		close(resultCh)
		return resultCh
	}
}

// worker processes jobs from the job queue
func (wp *WorkerPool[T, R]) worker(id int) {
	defer wp.wg.Done()
	
	wp.log.Infof("Worker %d started", id)
	
	for {
		select {
		case <-wp.ctx.Done():
			wp.log.Infof("Worker %d stopped", id)
			return
		case job, ok := <-wp.jobQueue:
			if !ok {
				wp.log.Infof("Worker %d stopped - job queue closed", id)
				return
			}
			
			wp.processJob(job)
		}
	}
}

// processJob processes a single job
func (wp *WorkerPool[T, R]) processJob(job Job[T, R]) {
	start := time.Now()
	
	result, err := wp.workerFunc(job.Context, job.Data)
	
	jobResult := JobResult[R]{
		Result: result,
		Error:  err,
	}
	
	// Send result
	select {
	case job.ResultCh <- jobResult:
	case <-job.Context.Done():
	}
	close(job.ResultCh)
	
	// Update metrics
	wp.updateMetrics(time.Since(start), err == nil)
}

// updateMetrics updates worker pool metrics
func (wp *WorkerPool[T, R]) updateMetrics(latency time.Duration, success bool) {
	wp.metrics.mu.Lock()
	defer wp.metrics.mu.Unlock()
	
	wp.metrics.JobsProcessed++
	wp.metrics.TotalLatency += latency
	wp.metrics.AverageLatency = wp.metrics.TotalLatency / time.Duration(wp.metrics.JobsProcessed)
	wp.metrics.QueueLength = len(wp.jobQueue)
	
	if success {
		wp.metrics.JobsSuccessful++
	} else {
		wp.metrics.JobsFailed++
	}
}

// GetMetrics returns current worker pool metrics
func (wp *WorkerPool[T, R]) GetMetrics() WorkerPoolMetrics {
	wp.metrics.mu.RLock()
	defer wp.metrics.mu.RUnlock()
	
	return WorkerPoolMetrics{
		JobsProcessed:  wp.metrics.JobsProcessed,
		JobsSuccessful: wp.metrics.JobsSuccessful,
		JobsFailed:     wp.metrics.JobsFailed,
		AverageLatency: wp.metrics.AverageLatency,
		ActiveWorkers:  wp.workers,
		QueueLength:    len(wp.jobQueue),
		TotalLatency:   wp.metrics.TotalLatency,
	}
}

// ==========================================
// PIPELINE PATTERN IMPLEMENTATION
// ==========================================

// Stage represents a stage in a processing pipeline
type Stage[T any, R any] func(ctx context.Context, input <-chan T) <-chan R

// Pipeline represents a processing pipeline
type Pipeline[T any] struct {
	stages []func(ctx context.Context, input <-chan T) <-chan T
	log    *log.Helper
}

// NewPipeline creates a new processing pipeline
func NewPipeline[T any](logger log.Logger) *Pipeline[T] {
	return &Pipeline[T]{
		stages: make([]func(ctx context.Context, input <-chan T) <-chan T, 0),
		log:    log.NewHelper(logger),
	}
}

// AddStage adds a stage to the pipeline
func (p *Pipeline[T]) AddStage(stage func(ctx context.Context, input <-chan T) <-chan T) *Pipeline[T] {
	p.stages = append(p.stages, stage)
	return p
}

// Process processes data through the pipeline
func (p *Pipeline[T]) Process(ctx context.Context, input <-chan T) <-chan T {
	current := input
	
	for i, stage := range p.stages {
		p.log.Infof("Processing pipeline stage %d", i+1)
		current = stage(ctx, current)
	}
	
	return current
}

// ==========================================
// HVAC-SPECIFIC WORKER POOLS
// ==========================================

// HVACWorkerPools contains specialized worker pools for HVAC operations
type HVACWorkerPools struct {
	EmailProcessor    *WorkerPool[EmailProcessingJob, EmailProcessingResult]
	CustomerProcessor *WorkerPool[CustomerProcessingJob, CustomerProcessingResult]
	JobProcessor      *WorkerPool[JobProcessingJob, JobProcessingResult]
	AIProcessor       *WorkerPool[AIProcessingJob, AIProcessingResult]
	log               *log.Helper
}

// EmailProcessingJob represents an email processing job
type EmailProcessingJob struct {
	EmailID   int64
	Subject   string
	Body      string
	From      string
	To        []string
	Priority  string
}

// EmailProcessingResult represents the result of email processing
type EmailProcessingResult struct {
	EmailID     int64
	Analysis    map[string]interface{}
	Actions     []string
	Priority    string
	ProcessedAt time.Time
}

// CustomerProcessingJob represents a customer processing job
type CustomerProcessingJob struct {
	CustomerID int64
	Action     string
	Data       map[string]interface{}
}

// CustomerProcessingResult represents the result of customer processing
type CustomerProcessingResult struct {
	CustomerID  int64
	Success     bool
	UpdatedData map[string]interface{}
	ProcessedAt time.Time
}

// JobProcessingJob represents a job processing job
type JobProcessingJob struct {
	JobID      int64
	Action     string
	Parameters map[string]interface{}
}

// JobProcessingResult represents the result of job processing
type JobProcessingResult struct {
	JobID       int64
	Success     bool
	Result      map[string]interface{}
	ProcessedAt time.Time
}

// AIProcessingJob represents an AI processing job
type AIProcessingJob struct {
	RequestID string
	Model     string
	Input     string
	Context   map[string]interface{}
}

// AIProcessingResult represents the result of AI processing
type AIProcessingResult struct {
	RequestID   string
	Output      string
	Confidence  float64
	Metadata    map[string]interface{}
	ProcessedAt time.Time
}

// NewHVACWorkerPools creates all HVAC worker pools
func NewHVACWorkerPools(logger log.Logger) *HVACWorkerPools {
	return &HVACWorkerPools{
		EmailProcessor: NewWorkerPool(
			runtime.NumCPU(),
			100,
			processEmail,
			logger,
		),
		CustomerProcessor: NewWorkerPool(
			runtime.NumCPU()/2,
			50,
			processCustomer,
			logger,
		),
		JobProcessor: NewWorkerPool(
			runtime.NumCPU(),
			100,
			processJob,
			logger,
		),
		AIProcessor: NewWorkerPool(
			2, // Limit AI processing workers
			20,
			processAI,
			logger,
		),
		log: log.NewHelper(logger),
	}
}

// Start starts all worker pools
func (hwp *HVACWorkerPools) Start() {
	hwp.EmailProcessor.Start()
	hwp.CustomerProcessor.Start()
	hwp.JobProcessor.Start()
	hwp.AIProcessor.Start()
	hwp.log.Info("All HVAC worker pools started")
}

// Stop stops all worker pools
func (hwp *HVACWorkerPools) Stop() {
	hwp.EmailProcessor.Stop()
	hwp.CustomerProcessor.Stop()
	hwp.JobProcessor.Stop()
	hwp.AIProcessor.Stop()
	hwp.log.Info("All HVAC worker pools stopped")
}

// ==========================================
// WORKER FUNCTIONS
// ==========================================

// processEmail processes an email job
func processEmail(ctx context.Context, job EmailProcessingJob) (EmailProcessingResult, error) {
	// Simulate email processing
	time.Sleep(10 * time.Millisecond)
	
	return EmailProcessingResult{
		EmailID: job.EmailID,
		Analysis: map[string]interface{}{
			"sentiment": "positive",
			"urgency":   "medium",
			"category":  "service_request",
		},
		Actions:     []string{"create_ticket", "notify_technician"},
		Priority:    job.Priority,
		ProcessedAt: time.Now(),
	}, nil
}

// processCustomer processes a customer job
func processCustomer(ctx context.Context, job CustomerProcessingJob) (CustomerProcessingResult, error) {
	// Simulate customer processing
	time.Sleep(5 * time.Millisecond)
	
	return CustomerProcessingResult{
		CustomerID:  job.CustomerID,
		Success:     true,
		UpdatedData: job.Data,
		ProcessedAt: time.Now(),
	}, nil
}

// processJob processes a job processing job
func processJob(ctx context.Context, job JobProcessingJob) (JobProcessingResult, error) {
	// Simulate job processing
	time.Sleep(15 * time.Millisecond)
	
	return JobProcessingResult{
		JobID:       job.JobID,
		Success:     true,
		Result:      job.Parameters,
		ProcessedAt: time.Now(),
	}, nil
}

// processAI processes an AI job
func processAI(ctx context.Context, job AIProcessingJob) (AIProcessingResult, error) {
	// Simulate AI processing (longer duration)
	time.Sleep(100 * time.Millisecond)
	
	return AIProcessingResult{
		RequestID:   job.RequestID,
		Output:      fmt.Sprintf("AI processed: %s", job.Input),
		Confidence:  0.95,
		Metadata:    job.Context,
		ProcessedAt: time.Now(),
	}, nil
}
