package data

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
)

// AIRepo implements the AI repository
type AIRepo struct {
	data        *Data
	config      *conf.AI
	gemmaService *ai.GemmaGGUFService
	log         *log.Helper
}

// NewAIRepo creates a new AI repository
func NewAIRepo(data *Data, config *conf.AI, logger log.Logger) biz.AIRepo {
	gemmaService := ai.NewGemmaGGUFService(config.Gemma, logger)

	return &AIRepo{
		data:         data,
		config:       config,
		gemmaService: gemmaService,
		log:          log.NewHelper(logger),
	}
}

// Chat processes a chat request using AI models
func (r *AIRepo) Chat(ctx context.Context, req *biz.ChatRequest) (*biz.ChatResponse, error) {
	r.log.WithContext(ctx).Infof("Processing chat with model: %s", req.Model)

	// Route to appropriate model service
	switch req.Model {
	case "gemma-3-4b-it", "gemma-3-4b-it-qat-q4_0-gguf":
		return r.gemmaService.Chat(ctx, req)
	case "bielik-v3":
		// For now, fallback to mock implementation for Bielik
		return r.mockBielikChat(ctx, req)
	default:
		return nil, biz.ErrAIModelNotFound
	}
}// Analyze processes an analysis request
func (r *AIRepo) Analyze(ctx context.Context, req *biz.AnalyzeRequest) (*biz.AnalyzeResponse, error) {
	r.log.WithContext(ctx).Infof("Processing analysis: %s", req.AnalysisType)

	// Route to appropriate model service
	switch req.Model {
	case "gemma-3-4b-it", "gemma-3-4b-it-qat-q4_0-gguf", "":
		// Use Gemma for analysis (default if no model specified)
		return r.gemmaService.Analyze(ctx, req)
	case "bielik-v3":
		// For now, fallback to mock implementation for Bielik
		return r.mockBielikAnalyze(ctx, req)
	default:
		return nil, biz.ErrAIModelNotFound
	}
}

// ListModels returns available AI models
func (r *AIRepo) ListModels(ctx context.Context) ([]*biz.AIModel, error) {
	models := []*biz.AIModel{}

	// Get Gemma model info
	if gemmaModel, err := r.gemmaService.GetModelInfo(ctx); err == nil {
		models = append(models, gemmaModel)
	}

	// Add Bielik model (mock for now)
	models = append(models, &biz.AIModel{
		Name:      "bielik-v3",
		Type:      "analysis",
		Available: false, // Not implemented yet
		Endpoint:  r.config.Bielik.Endpoint,
	})

	return models, nil
}

// IsModelAvailable checks if a model is available
func (r *AIRepo) IsModelAvailable(ctx context.Context, modelName string) (bool, error) {
	models, err := r.ListModels(ctx)
	if err != nil {
		return false, err
	}

	for _, model := range models {
		if model.Name == modelName && model.Available {
			return true, nil
		}
	}

	return false, nil
}// mockBielikChat provides mock implementation for Bielik chat
func (r *AIRepo) mockBielikChat(ctx context.Context, req *biz.ChatRequest) (*biz.ChatResponse, error) {
	r.log.WithContext(ctx).Info("Using mock Bielik chat implementation")

	return &biz.ChatResponse{
		Response:   "This is a mock response from Bielik V3 model. The actual model integration is pending.",
		ModelUsed:  "bielik-v3",
		TokensUsed: 120,
	}, nil
}

// mockBielikAnalyze provides mock implementation for Bielik analysis
func (r *AIRepo) mockBielikAnalyze(ctx context.Context, req *biz.AnalyzeRequest) (*biz.AnalyzeResponse, error) {
	r.log.WithContext(ctx).Info("Using mock Bielik analysis implementation")

	return &biz.AnalyzeResponse{
		Analysis:   fmt.Sprintf("Mock analysis of type '%s' using Bielik V3. Content analyzed: %d characters.", req.AnalysisType, len(req.Content)),
		Confidence: 0.75,
		Metadata: map[string]string{
			"model":         "bielik-v3",
			"analysis_type": req.AnalysisType,
			"status":        "mock_implementation",
		},
	}, nil
}