package data

import (
	"context"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity" // Import the new entity package

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// LeadRepo implements the lead repository
type LeadRepo struct {
	data *Data
	log  *log.Helper
}

// NewLeadRepo creates a new lead repository
func NewLeadRepo(data *Data, logger log.Logger) biz.LeadRepo {
	return &LeadRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateLead creates a new lead in the database
func (r *LeadRepo) CreateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error) {
	dbLead := &Lead{ // This Lead refers to data.Lead (GORM model)
		Name:   lead.Name,
		Email:  lead.Email,
		Phone:  lead.Phone,
		Status: lead.Status,
		Source: lead.Source,
	}

	if err := r.data.db.WithContext(ctx).Create(dbLead).Error; err != nil {
		return nil, err
	}

	return r.convertToBiz(dbLead), nil
}

// GetLead retrieves a lead by ID
func (r *LeadRepo) GetLead(ctx context.Context, id int64) (*entity.Lead, error) {
	var lead Lead // This Lead refers to data.Lead (GORM model)
	if err := r.data.db.WithContext(ctx).First(&lead, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrLeadNotFound
		}
		return nil, err
	}

	return r.convertToBiz(&lead), nil
}

// ListLeads retrieves leads with pagination and filters
func (r *LeadRepo) ListLeads(ctx context.Context, page, pageSize int32, status, source string) ([]*entity.Lead, int32, error) {
	var leads []Lead // This Lead refers to data.Lead (GORM model)
	var total int64
	query := r.data.db.WithContext(ctx).Model(&Lead{}) // This Lead refers to data.Lead (GORM model)

	if status != "" {
		query = query.Where("status = ?", status)
	}
	if source != "" {
		query = query.Where("source = ?", source)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := query.Offset(int(offset)).Limit(int(pageSize)).Find(&leads).Error; err != nil {
		return nil, 0, err
	}

	// Convert to business entities
	bizLeads := make([]*entity.Lead, len(leads))
	for i, lead := range leads {
		bizLeads[i] = r.convertToBiz(&lead)
	}

	return bizLeads, int32(total), nil
}

// UpdateLead updates an existing lead
func (r *LeadRepo) UpdateLead(ctx context.Context, lead *entity.Lead) (*entity.Lead, error) {
	dbLead := &Lead{ // This Lead refers to data.Lead (GORM model)
		ID:     lead.ID,
		Name:   lead.Name,
		Email:  lead.Email,
		Phone:  lead.Phone,
		Status: lead.Status,
		Source: lead.Source,
	}

	if err := r.data.db.WithContext(ctx).Save(dbLead).Error; err != nil {
		return nil, err
	}

	return r.convertToBiz(dbLead), nil
}

// DeleteLead deletes a lead by ID
func (r *LeadRepo) DeleteLead(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&Lead{}, id).Error // This Lead refers to data.Lead (GORM model)
}

// SaveLeads saves a slice of leads (for bulk import)
func (r *LeadRepo) SaveLeads(ctx context.Context, leads []*entity.Lead) ([]*entity.Lead, error) {
	var savedLeads []*entity.Lead
	for _, lead := range leads {
		dbLead := &Lead{ // This Lead refers to data.Lead (GORM model)
			Name:   lead.Name,
			Email:  lead.Email,
			Phone:  lead.Phone,
			Status: lead.Status,
			Source: lead.Source,
		}
		if err := r.data.db.WithContext(ctx).Create(dbLead).Error; err != nil {
			r.log.WithContext(ctx).Errorf("Failed to save lead %s: %v", lead.Name, err)
			// Continue to next lead even if one fails
			continue
		}
		savedLeads = append(savedLeads, r.convertToBiz(dbLead))
	}
	return savedLeads, nil
}

// FindDuplicateLeads finds duplicate leads based on criteria
func (r *LeadRepo) FindDuplicateLeads(ctx context.Context, criteria []string) ([]*entity.Lead, error) {
	var duplicateLeads []Lead                          // This Lead refers to data.Lead (GORM model)
	query := r.data.db.WithContext(ctx).Model(&Lead{}) // This Lead refers to data.Lead (GORM model)

	// Simple example: find duplicates by email or phone
	if len(criteria) == 0 || (len(criteria) == 1 && (criteria[0] == "email" || criteria[0] == "phone")) {
		// Find leads with duplicate emails
		if err := query.Raw(`
			SELECT l1.* FROM leads l1
			JOIN (
				SELECT email, COUNT(*) FROM leads WHERE email IS NOT NULL AND email != '' GROUP BY email HAVING COUNT(*) > 1
			) AS duplicates ON l1.email = duplicates.email
		`).Scan(&duplicateLeads).Error; err != nil {
			return nil, err
		}

		// Find leads with duplicate phones (if not already covered by email)
		if err := query.Raw(`
			SELECT l1.* FROM leads l1
			JOIN (
				SELECT phone, COUNT(*) FROM leads WHERE phone IS NOT NULL AND phone != '' GROUP BY phone HAVING COUNT(*) > 1
			) AS duplicates ON l1.phone = duplicates.phone
			WHERE l1.id NOT IN (?)
		`, func() []int64 {
			ids := make([]int64, len(duplicateLeads))
			for i, l := range duplicateLeads {
				ids[i] = l.ID
			}
			return ids
		}()).Scan(&duplicateLeads).Error; err != nil {
			return nil, err
		}
	} else {
		return nil, biz.ErrLeadDeduplicationFailed // Or a more specific error
	}

	bizLeads := make([]*entity.Lead, len(duplicateLeads))
	for i, lead := range duplicateLeads {
		bizLeads[i] = r.convertToBiz(&lead)
	}
	return bizLeads, nil
}

// convertToBiz converts database model to business entity
func (r *LeadRepo) convertToBiz(lead *Lead) *entity.Lead { // This Lead refers to data.Lead (GORM model)
	return &entity.Lead{
		ID:        lead.ID,
		Name:      lead.Name,
		Email:     lead.Email,
		Phone:     lead.Phone,
		Status:    lead.Status,
		Source:    lead.Source,
		CreatedAt: lead.CreatedAt,
		UpdatedAt: lead.UpdatedAt,
	}
}
