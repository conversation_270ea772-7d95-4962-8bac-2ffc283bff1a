package data

import (
	"time"
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// 📁 Enhanced File Storage Models for Dolores Email Intelligence
// Comprehensive file management with hybrid storage strategy

// FileStorage represents the enhanced file storage model
type FileStorage struct {
	ID              int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID         *int64                 `gorm:"index" json:"email_id,omitempty"`
	OriginalName    string                 `gorm:"not null" json:"original_name"`
	StorageName     string                 `gorm:"not null;uniqueIndex" json:"storage_name"`
	ContentType     string                 `gorm:"not null" json:"content_type"`
	Size            int64                  `gorm:"not null" json:"size"`
	ChecksumMD5     string                 `gorm:"not null" json:"checksum_md5"`
	ChecksumSHA256  string                 `json:"checksum_sha256"`
	
	// Storage Strategy
	StorageType     StorageType            `gorm:"not null;default:auto" json:"storage_type"`
	StoragePath     string                 `json:"storage_path,omitempty"`     // MinIO/S3 object key
	StorageBucket   string                 `json:"storage_bucket,omitempty"`   // MinIO/S3 bucket
	BinaryData      []byte                 `gorm:"type:bytea" json:"-"`        // For small files in DB
	
	// Content Extraction
	ExtractedText   string                 `gorm:"type:text" json:"extracted_text,omitempty"`
	ExtractionMeta  ExtractionMetadata     `gorm:"type:jsonb" json:"extraction_meta,omitempty"`
	ProcessingStatus ProcessingStatus      `gorm:"default:pending" json:"processing_status"`
	ProcessingError string                 `json:"processing_error,omitempty"`
	ProcessedAt     *time.Time             `json:"processed_at,omitempty"`
	
	// Security & Access
	EncryptionKey   string                 `json:"-"`                          // Encryption key reference
	AccessLevel     AccessLevel            `gorm:"default:private" json:"access_level"`
	VirusScanStatus VirusScanStatus        `gorm:"default:pending" json:"virus_scan_status"`
	VirusScanResult string                 `json:"virus_scan_result,omitempty"`
	
	// Metadata
	Tags            StringArray            `gorm:"type:text[]" json:"tags,omitempty"`
	Metadata        JSONMap                `gorm:"type:jsonb" json:"metadata,omitempty"`
	
	// Timestamps
	CreatedAt       time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	AccessedAt      *time.Time             `json:"accessed_at,omitempty"`
	ExpiresAt       *time.Time             `json:"expires_at,omitempty"`
	
	// Relationships
	Email           *Email                 `gorm:"foreignKey:EmailID;constraint:OnDelete:SET NULL" json:"email,omitempty"`
	ExtractionJobs  []*ContentExtractionJob `gorm:"foreignKey:FileID;constraint:OnDelete:CASCADE" json:"extraction_jobs,omitempty"`
}

// ContentExtractionJob represents content extraction processing job
type ContentExtractionJob struct {
	ID              int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	FileID          int64                  `gorm:"not null;index" json:"file_id"`
	JobType         ExtractionJobType      `gorm:"not null" json:"job_type"`
	Status          ProcessingStatus       `gorm:"default:pending" json:"status"`
	Priority        JobPriority            `gorm:"default:normal" json:"priority"`
	
	// Processing Details
	StartedAt       *time.Time             `json:"started_at,omitempty"`
	CompletedAt     *time.Time             `json:"completed_at,omitempty"`
	ProcessingTime  *int                   `json:"processing_time_ms,omitempty"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	RetryCount      int                    `gorm:"default:0" json:"retry_count"`
	MaxRetries      int                    `gorm:"default:3" json:"max_retries"`
	
	// Results
	ExtractedText   string                 `gorm:"type:text" json:"extracted_text,omitempty"`
	ExtractedMeta   ExtractionMetadata     `gorm:"type:jsonb" json:"extracted_meta,omitempty"`
	WordCount       *int                   `json:"word_count,omitempty"`
	PageCount       *int                   `json:"page_count,omitempty"`
	
	// Queue Management
	QueuedAt        time.Time              `gorm:"autoCreateTime" json:"queued_at"`
	WorkerID        string                 `json:"worker_id,omitempty"`
	
	// Relationships
	File            *FileStorage           `gorm:"foreignKey:FileID;constraint:OnDelete:CASCADE" json:"file,omitempty"`
}

// Enhanced EmailAttachment with file storage integration
type EnhancedEmailAttachment struct {
	ID              int64                  `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID         int64                  `gorm:"not null;index" json:"email_id"`
	FileStorageID   *int64                 `gorm:"index" json:"file_storage_id,omitempty"`
	
	// Legacy fields for backward compatibility
	Filename        string                 `gorm:"not null" json:"filename"`
	ContentType     string                 `json:"content_type"`
	Size            int64                  `json:"size"`
	TextContent     string                 `gorm:"type:text" json:"text_content"`
	IsProcessed     bool                   `gorm:"default:false" json:"is_processed"`
	
	// Enhanced fields
	AttachmentIndex int                    `gorm:"default:0" json:"attachment_index"`
	IsInline        bool                   `gorm:"default:false" json:"is_inline"`
	ContentID       string                 `json:"content_id,omitempty"`
	
	CreatedAt       time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	
	// Relationships
	Email           *Email                 `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
	FileStorage     *FileStorage           `gorm:"foreignKey:FileStorageID;constraint:OnDelete:SET NULL" json:"file_storage,omitempty"`
}

// Enums and Types

type StorageType string
const (
	StorageTypeAuto     StorageType = "auto"      // Automatic selection based on size
	StorageTypeDatabase StorageType = "database"  // Store in PostgreSQL
	StorageTypeMinIO    StorageType = "minio"     // Store in MinIO
	StorageTypeS3       StorageType = "s3"        // Store in AWS S3
	StorageTypeLocal    StorageType = "local"     // Store in local filesystem
)

type ProcessingStatus string
const (
	ProcessingStatusPending    ProcessingStatus = "pending"
	ProcessingStatusProcessing ProcessingStatus = "processing"
	ProcessingStatusCompleted  ProcessingStatus = "completed"
	ProcessingStatusFailed     ProcessingStatus = "failed"
	ProcessingStatusSkipped    ProcessingStatus = "skipped"
)

type AccessLevel string
const (
	AccessLevelPrivate   AccessLevel = "private"
	AccessLevelInternal  AccessLevel = "internal"
	AccessLevelPublic    AccessLevel = "public"
	AccessLevelRestricted AccessLevel = "restricted"
)

type VirusScanStatus string
const (
	VirusScanStatusPending VirusScanStatus = "pending"
	VirusScanStatusScanning VirusScanStatus = "scanning"
	VirusScanStatusClean   VirusScanStatus = "clean"
	VirusScanStatusInfected VirusScanStatus = "infected"
	VirusScanStatusError   VirusScanStatus = "error"
	VirusScanStatusSkipped VirusScanStatus = "skipped"
)

type ExtractionJobType string
const (
	ExtractionJobTypeText     ExtractionJobType = "text"
	ExtractionJobTypeMetadata ExtractionJobType = "metadata"
	ExtractionJobTypeImages   ExtractionJobType = "images"
	ExtractionJobTypeFull     ExtractionJobType = "full"
)

type JobPriority string
const (
	JobPriorityLow    JobPriority = "low"
	JobPriorityNormal JobPriority = "normal"
	JobPriorityHigh   JobPriority = "high"
	JobPriorityUrgent JobPriority = "urgent"
)

// ExtractionMetadata contains detailed extraction metadata
type ExtractionMetadata struct {
	FileType        string                 `json:"file_type"`
	FileVersion     string                 `json:"file_version,omitempty"`
	PageCount       int                    `json:"page_count,omitempty"`
	WordCount       int                    `json:"word_count,omitempty"`
	CharCount       int                    `json:"char_count,omitempty"`
	Language        string                 `json:"language,omitempty"`
	Author          string                 `json:"author,omitempty"`
	Title           string                 `json:"title,omitempty"`
	Subject         string                 `json:"subject,omitempty"`
	Keywords        []string               `json:"keywords,omitempty"`
	CreatedDate     *time.Time             `json:"created_date,omitempty"`
	ModifiedDate    *time.Time             `json:"modified_date,omitempty"`
	Application     string                 `json:"application,omitempty"`
	
	// PDF specific
	PDFVersion      string                 `json:"pdf_version,omitempty"`
	IsEncrypted     bool                   `json:"is_encrypted,omitempty"`
	HasForms        bool                   `json:"has_forms,omitempty"`
	
	// Office specific
	SheetNames      []string               `json:"sheet_names,omitempty"`
	SlideCount      int                    `json:"slide_count,omitempty"`
	
	// Processing info
	ExtractionTime  int                    `json:"extraction_time_ms"`
	ProcessorUsed   string                 `json:"processor_used"`
	ProcessorVersion string                `json:"processor_version"`
	Confidence      float64                `json:"confidence,omitempty"`
	
	// Custom metadata
	Custom          map[string]interface{} `json:"custom,omitempty"`
}

// Implement Valuer and Scanner for GORM JSON support
func (em ExtractionMetadata) Value() (driver.Value, error) {
	return json.Marshal(em)
}

func (em *ExtractionMetadata) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ExtractionMetadata", value)
	}
	
	return json.Unmarshal(bytes, em)
}

// Table names
func (FileStorage) TableName() string {
	return "file_storage"
}

func (ContentExtractionJob) TableName() string {
	return "content_extraction_jobs"
}

func (EnhancedEmailAttachment) TableName() string {
	return "email_attachments_enhanced"
}

// Helper methods

// IsSmallFile determines if file should be stored in database
func (fs *FileStorage) IsSmallFile() bool {
	return fs.Size <= 1024*1024 // 1MB threshold
}

// ShouldStoreInDatabase determines storage strategy
func (fs *FileStorage) ShouldStoreInDatabase() bool {
	if fs.StorageType == StorageTypeDatabase {
		return true
	}
	if fs.StorageType == StorageTypeAuto {
		return fs.IsSmallFile()
	}
	return false
}

// GetStorageLocation returns the full storage location
func (fs *FileStorage) GetStorageLocation() string {
	if fs.ShouldStoreInDatabase() {
		return "database"
	}
	if fs.StorageBucket != "" && fs.StoragePath != "" {
		return fmt.Sprintf("%s/%s", fs.StorageBucket, fs.StoragePath)
	}
	return fs.StoragePath
}

// IsProcessingComplete checks if all extraction jobs are complete
func (fs *FileStorage) IsProcessingComplete() bool {
	return fs.ProcessingStatus == ProcessingStatusCompleted
}

// CanRetry checks if extraction job can be retried
func (cej *ContentExtractionJob) CanRetry() bool {
	return cej.Status == ProcessingStatusFailed && cej.RetryCount < cej.MaxRetries
}

// GetProcessingDuration returns processing duration
func (cej *ContentExtractionJob) GetProcessingDuration() *time.Duration {
	if cej.StartedAt != nil && cej.CompletedAt != nil {
		duration := cej.CompletedAt.Sub(*cej.StartedAt)
		return &duration
	}
	return nil
}
