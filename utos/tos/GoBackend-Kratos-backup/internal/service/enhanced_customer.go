package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"

	"gobackend-hvac-kratos/internal/common"
	"gobackend-hvac-kratos/internal/data"
)

// 🚀 Enhanced Customer Service with Go 1.23 Generics
// Type-safe, high-performance customer business logic

// ==========================================
// ENHANCED CUSTOMER SERVICE
// ==========================================

// EnhancedCustomerService provides advanced customer operations using generics
type EnhancedCustomerService struct {
	*common.Service[*data.EnhancedCustomer, common.Repository[*data.EnhancedCustomer]]
	workerPools   *common.HVACWorkerPools
	memoryMonitor *common.MemoryMonitor
	perfMonitor   *common.PerformanceMonitor
	objectPools   *common.HVACObjectPools
	log           *log.Helper
}

// EnhancedCustomerServiceConfig provides configuration for the enhanced service
type EnhancedCustomerServiceConfig struct {
	*common.ServiceConfig
	EnableWorkerPools   bool `json:"enable_worker_pools"`
	EnableMemoryMonitor bool `json:"enable_memory_monitor"`
	EnablePerfMonitor   bool `json:"enable_perf_monitor"`
	WorkerPoolSize      int  `json:"worker_pool_size"`
}

// NewEnhancedCustomerService creates a new enhanced customer service
func NewEnhancedCustomerService(
	repo common.Repository[*data.EnhancedCustomer],
	cache common.Cache[*data.EnhancedCustomer],
	logger log.Logger,
	config *EnhancedCustomerServiceConfig,
) *EnhancedCustomerService {
	if config == nil {
		config = &EnhancedCustomerServiceConfig{
			ServiceConfig: &common.ServiceConfig{
				EnableCache:      true,
				CacheTTL:         10 * time.Minute,
				EnableMetrics:    true,
				EnableValidation: true,
				MaxBatchSize:     100,
				Timeout:          30 * time.Second,
			},
			EnableWorkerPools:   true,
			EnableMemoryMonitor: true,
			EnablePerfMonitor:   true,
			WorkerPoolSize:      4,
		}
	}

	// Create metrics implementation
	metrics := &CustomerServiceMetrics{
		operations: make(map[string]*OperationMetrics),
		log:        log.NewHelper(logger),
	}

	// Create base generic service
	baseService := common.NewService(
		repo,
		cache,
		validator.New(),
		metrics,
		logger,
		config.ServiceConfig,
	)

	service := &EnhancedCustomerService{
		Service: baseService,
		log:     log.NewHelper(logger),
	}

	// Initialize worker pools if enabled
	if config.EnableWorkerPools {
		service.workerPools = common.NewHVACWorkerPools(logger)
		service.workerPools.Start()
	}

	// Initialize memory monitor if enabled
	if config.EnableMemoryMonitor {
		service.memoryMonitor = common.NewMemoryMonitor(
			logger,
			512*1024*1024,  // 512MB threshold
			30*time.Second, // Monitor every 30 seconds
		)
	}

	// Initialize performance monitor if enabled
	if config.EnablePerfMonitor {
		service.perfMonitor = common.NewPerformanceMonitor(logger, &common.MonitorConfig{
			EnableProfiling: true,
			EnableMetrics:   true,
			EnableAlerts:    true,
			ProfilerPort:    6061, // Different port for customer service
		})
	}

	// Initialize object pools
	service.objectPools = common.NewHVACObjectPools(logger)

	return service
}

// Start starts all monitoring and worker pools
func (s *EnhancedCustomerService) Start(ctx context.Context) error {
	if s.memoryMonitor != nil {
		s.memoryMonitor.Start(ctx)
	}

	if s.perfMonitor != nil {
		if err := s.perfMonitor.Start(ctx); err != nil {
			return fmt.Errorf("failed to start performance monitor: %w", err)
		}
	}

	s.log.Info("Enhanced customer service started")
	return nil
}

// Stop stops all monitoring and worker pools
func (s *EnhancedCustomerService) Stop() {
	if s.workerPools != nil {
		s.workerPools.Stop()
	}

	if s.memoryMonitor != nil {
		s.memoryMonitor.Stop()
	}

	if s.perfMonitor != nil {
		s.perfMonitor.Stop()
	}

	s.log.Info("Enhanced customer service stopped")
}

// ==========================================
// ADVANCED CUSTOMER OPERATIONS
// ==========================================

// CreateCustomerAsync creates a customer asynchronously using worker pools
func (s *EnhancedCustomerService) CreateCustomerAsync(ctx context.Context, customer *data.EnhancedCustomer) (<-chan common.JobResult[*data.EnhancedCustomer], error) {
	if s.workerPools == nil {
		// Fallback to synchronous creation
		result, err := s.Create(ctx, customer)
		resultCh := make(chan common.JobResult[*data.EnhancedCustomer], 1)
		resultCh <- common.JobResult[*data.EnhancedCustomer]{
			Result: result,
			Error:  err,
		}
		close(resultCh)
		return resultCh, nil
	}

	// Use worker pool for async processing
	jobID := fmt.Sprintf("create_customer_%d", time.Now().UnixNano())

	// Convert to worker pool job format
	job := common.CustomerProcessingJob{
		CustomerID: customer.ID,
		Action:     "create",
		Data: map[string]interface{}{
			"customer": customer,
		},
	}

	workerResult := s.workerPools.CustomerProcessor.Submit(ctx, jobID, job)

	// Convert worker result to customer result
	resultCh := make(chan common.JobResult[*data.EnhancedCustomer], 1)
	go func() {
		defer close(resultCh)

		select {
		case wr := <-workerResult:
			if wr.Error != nil {
				resultCh <- common.JobResult[*data.EnhancedCustomer]{
					Error: wr.Error,
				}
				return
			}

			// Create the customer synchronously in the worker
			result, err := s.Create(ctx, customer)
			resultCh <- common.JobResult[*data.EnhancedCustomer]{
				Result: result,
				Error:  err,
			}
		case <-ctx.Done():
			resultCh <- common.JobResult[*data.EnhancedCustomer]{
				Error: ctx.Err(),
			}
		}
	}()

	return resultCh, nil
}

// SearchCustomersWithAnalytics performs search with performance analytics
func (s *EnhancedCustomerService) SearchCustomersWithAnalytics(ctx context.Context, criteria common.SearchCriteria[*data.EnhancedCustomer]) (*common.SearchResult[*data.EnhancedCustomer], *SearchAnalytics, error) {
	start := time.Now()

	// Perform search using base service
	result, err := s.Search(ctx, criteria)

	searchDuration := time.Since(start)

	// Record performance metrics
	if s.perfMonitor != nil {
		s.perfMonitor.RecordHTTPRequest("search_customers", 200, searchDuration)
	}

	// Create analytics
	analytics := &SearchAnalytics{
		QueryTime:    searchDuration,
		ResultCount:  len(result.Items),
		TotalMatches: result.Total,
		CacheHit:     false,                                                 // Would need to track this in cache implementation
		IndexesUsed:  []string{"customers_name_idx", "customers_email_idx"}, // Example
		QueryPlan:    "Sequential scan with filters",                        // Example
	}

	return result, analytics, err
}

// BulkProcessCustomers processes multiple customers with optimized memory usage
func (s *EnhancedCustomerService) BulkProcessCustomers(ctx context.Context, customers []*data.EnhancedCustomer, operation string) (*BulkProcessResult, error) {
	start := time.Now()

	// Use object pools for memory optimization
	results := make([]*data.EnhancedCustomer, 0, len(customers))
	errors := make([]error, 0)

	// Process in batches to optimize memory usage
	batchSize := 50
	for i := 0; i < len(customers); i += batchSize {
		end := i + batchSize
		if end > len(customers) {
			end = len(customers)
		}

		batch := customers[i:end]

		switch operation {
		case "create":
			// Access the repository through the embedded Service
			repo := s.Service.GetRepository()
			batchResults, err := repo.CreateBatch(ctx, batch)
			if err != nil {
				errors = append(errors, err)
			} else {
				results = append(results, batchResults...)
			}
		case "update":
			// Access the repository through the embedded Service
			repo := s.Service.GetRepository()
			batchResults, err := repo.UpdateBatch(ctx, batch)
			if err != nil {
				errors = append(errors, err)
			} else {
				results = append(results, batchResults...)
			}
		default:
			return nil, fmt.Errorf("unsupported bulk operation: %s", operation)
		}

		// Trigger memory optimization between batches
		if i%200 == 0 && s.memoryMonitor != nil {
			// Use the existing logger from the service
			common.OptimizeMemoryUsage(log.DefaultLogger)
		}
	}

	processingTime := time.Since(start)

	return &BulkProcessResult{
		ProcessedCount: len(results),
		ErrorCount:     len(errors),
		ProcessingTime: processingTime,
		Results:        results,
		Errors:         errors,
	}, nil
}

// GetCustomerWithCaching retrieves a customer with advanced caching
func (s *EnhancedCustomerService) GetCustomerWithCaching(ctx context.Context, id int64) (*data.EnhancedCustomer, *CacheMetrics, error) {
	start := time.Now()

	// Use base service GetByID which includes caching
	customer, err := s.GetByID(ctx, id)

	retrievalTime := time.Since(start)

	// Create cache metrics (simplified - would need actual cache implementation)
	cacheMetrics := &CacheMetrics{
		CacheHit:      false, // Would be determined by actual cache implementation
		RetrievalTime: retrievalTime,
		CacheKey:      fmt.Sprintf("customer:%d", id),
		TTL:           10 * time.Minute,
	}

	return customer, cacheMetrics, err
}

// ==========================================
// ANALYTICS AND METRICS STRUCTURES
// ==========================================

// SearchAnalytics provides detailed search performance analytics
type SearchAnalytics struct {
	QueryTime    time.Duration `json:"query_time"`
	ResultCount  int           `json:"result_count"`
	TotalMatches int64         `json:"total_matches"`
	CacheHit     bool          `json:"cache_hit"`
	IndexesUsed  []string      `json:"indexes_used"`
	QueryPlan    string        `json:"query_plan"`
}

// BulkProcessResult provides results of bulk operations
type BulkProcessResult struct {
	ProcessedCount int                      `json:"processed_count"`
	ErrorCount     int                      `json:"error_count"`
	ProcessingTime time.Duration            `json:"processing_time"`
	Results        []*data.EnhancedCustomer `json:"results"`
	Errors         []error                  `json:"errors"`
}

// CacheMetrics provides cache performance metrics
type CacheMetrics struct {
	CacheHit      bool          `json:"cache_hit"`
	RetrievalTime time.Duration `json:"retrieval_time"`
	CacheKey      string        `json:"cache_key"`
	TTL           time.Duration `json:"ttl"`
}

// ==========================================
// SERVICE METRICS IMPLEMENTATION
// ==========================================

// CustomerServiceMetrics implements ServiceMetrics interface
type CustomerServiceMetrics struct {
	operations map[string]*OperationMetrics
	mu         sync.RWMutex
	log        *log.Helper
}

// OperationMetrics tracks metrics for a specific operation
type OperationMetrics struct {
	Count         int64         `json:"count"`
	SuccessCount  int64         `json:"success_count"`
	ErrorCount    int64         `json:"error_count"`
	TotalDuration time.Duration `json:"total_duration"`
	AvgDuration   time.Duration `json:"avg_duration"`
	MaxDuration   time.Duration `json:"max_duration"`
	MinDuration   time.Duration `json:"min_duration"`
}

func (m *CustomerServiceMetrics) RecordOperation(operation string, duration time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.operations[operation] == nil {
		m.operations[operation] = &OperationMetrics{
			MinDuration: duration,
			MaxDuration: duration,
		}
	}

	metrics := m.operations[operation]
	metrics.Count++
	metrics.TotalDuration += duration
	metrics.AvgDuration = metrics.TotalDuration / time.Duration(metrics.Count)

	if duration > metrics.MaxDuration {
		metrics.MaxDuration = duration
	}
	if duration < metrics.MinDuration {
		metrics.MinDuration = duration
	}

	if success {
		metrics.SuccessCount++
	} else {
		metrics.ErrorCount++
	}
}

func (m *CustomerServiceMetrics) RecordEntityCount(entityType string, count int64) {
	m.log.Infof("Entity count for %s: %d", entityType, count)
}

func (m *CustomerServiceMetrics) RecordCacheHit(entityType string, hit bool) {
	status := "miss"
	if hit {
		status = "hit"
	}
	m.log.Infof("Cache %s for %s", status, entityType)
}

func (m *CustomerServiceMetrics) RecordValidationError(entityType string, field string) {
	m.log.Warnf("Validation error for %s.%s", entityType, field)
}

func (m *CustomerServiceMetrics) GetMetrics() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]interface{})
	for operation, metrics := range m.operations {
		result[operation] = metrics
	}
	return result
}
