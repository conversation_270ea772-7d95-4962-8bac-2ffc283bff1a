package vectordb

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/philippgille/chromem-go"
	"github.com/tmc/langchaingo/embeddings"
	"gobackend-hvac-kratos/internal/conf"
)

// 🗄️ Chromem-Go Vector Database Service
// Zero-dependency embeddable vector database with Chroma-like interface

type ChromemService struct {
	db                  *chromem.DB
	hvacCollection      *chromem.Collection
	emailCollection     *chromem.Collection
	knowledgeCollection *chromem.Collection
	customerCollection  *chromem.Collection
	embedder            embeddings.Embedder
	config              *conf.Data
	log                 *log.Helper
	metrics             *VectorDBMetrics
}

// VectorDBMetrics tracks vector database performance
type VectorDBMetrics struct {
	DocumentsStored    int64
	QueriesExecuted    int64
	EmbeddingsGenerated int64
	AvgQueryTime       float64
	AvgEmbeddingTime   float64
	ErrorCount         int64
	CacheHits          int64
	CacheMisses        int64
}

// VectorDocument represents a document in the vector database
type VectorDocument struct {
	ID       string                 `json:"id"`
	Content  string                 `json:"content"`
	Metadata map[string]interface{} `json:"metadata"`
	Vector   []float32              `json:"vector,omitempty"`
}

// SearchResult represents a vector search result
type SearchResult struct {
	ID         string                 `json:"id"`
	Content    string                 `json:"content"`
	Similarity float64                `json:"similarity"`
	Metadata   map[string]interface{} `json:"metadata"`
	Score      float64                `json:"score"`
}

// SearchOptions defines search parameters
type SearchOptions struct {
	TopK      int                    `json:"top_k"`
	Threshold float64                `json:"threshold"`
	Filter    map[string]interface{} `json:"filter,omitempty"`
	Include   []string               `json:"include,omitempty"`
}

// NewChromemService creates a new Chromem vector database service
func NewChromemService(config *conf.Data, embedder embeddings.Embedder, logger log.Logger) (*ChromemService, error) {
	helper := log.NewHelper(logger)
	helper.Info("🗄️ Initializing Chromem Vector Database Service...")

	// Initialize Chromem database
	db := chromem.NewDB()

	service := &ChromemService{
		db:       db,
		embedder: embedder,
		config:   config,
		log:      helper,
		metrics:  &VectorDBMetrics{},
	}

	// Initialize collections
	if err := service.initializeCollections(); err != nil {
		return nil, fmt.Errorf("failed to initialize collections: %w", err)
	}

	helper.Info("✅ Chromem Vector Database Service initialized successfully!")
	return service, nil
}

// initializeCollections creates and configures vector collections
func (s *ChromemService) initializeCollections() error {
	var err error

	// HVAC Knowledge Collection
	s.hvacCollection, err = s.db.CreateCollection("hvac-knowledge", nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create HVAC collection: %w", err)
	}

	// Email Collection
	s.emailCollection, err = s.db.CreateCollection("customer-emails", nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create email collection: %w", err)
	}

	// Knowledge Base Collection
	s.knowledgeCollection, err = s.db.CreateCollection("knowledge-base", nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create knowledge collection: %w", err)
	}

	// Customer Data Collection
	s.customerCollection, err = s.db.CreateCollection("customer-data", nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create customer collection: %w", err)
	}

	s.log.Info("✅ All vector collections initialized successfully!")
	return nil
}

// AddHVACDocument adds an HVAC-related document to the vector database
func (s *ChromemService) AddHVACDocument(ctx context.Context, doc VectorDocument) error {
	s.log.WithContext(ctx).Infof("📝 Adding HVAC document: %s", doc.ID)
	s.metrics.DocumentsStored++

	// Convert metadata to map[string]string for chromem
	metadata := make(map[string]string)
	for k, v := range doc.Metadata {
		metadata[k] = fmt.Sprintf("%v", v)
	}

	chromemDoc := chromem.Document{
		ID:       doc.ID,
		Content:  doc.Content,
		Metadata: metadata,
	}

	err := s.hvacCollection.AddDocuments(ctx, []chromem.Document{chromemDoc}, runtime.NumCPU())
	if err != nil {
		s.metrics.ErrorCount++
		return fmt.Errorf("failed to add HVAC document: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ HVAC document added successfully: %s", doc.ID)
	return nil
}

// AddEmailDocument adds an email document to the vector database
func (s *ChromemService) AddEmailDocument(ctx context.Context, doc VectorDocument) error {
	s.log.WithContext(ctx).Infof("📧 Adding email document: %s", doc.ID)
	s.metrics.DocumentsStored++

	// Convert metadata to map[string]string for chromem
	metadata := make(map[string]string)
	for k, v := range doc.Metadata {
		metadata[k] = fmt.Sprintf("%v", v)
	}

	chromemDoc := chromem.Document{
		ID:       doc.ID,
		Content:  doc.Content,
		Metadata: metadata,
	}

	err := s.emailCollection.AddDocuments(ctx, []chromem.Document{chromemDoc}, runtime.NumCPU())
	if err != nil {
		s.metrics.ErrorCount++
		return fmt.Errorf("failed to add email document: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Email document added successfully: %s", doc.ID)
	return nil
}

// AddKnowledgeDocument adds a knowledge base document
func (s *ChromemService) AddKnowledgeDocument(ctx context.Context, doc VectorDocument) error {
	s.log.WithContext(ctx).Infof("🧠 Adding knowledge document: %s", doc.ID)
	s.metrics.DocumentsStored++

	// Convert metadata to map[string]string for chromem
	metadata := make(map[string]string)
	for k, v := range doc.Metadata {
		metadata[k] = fmt.Sprintf("%v", v)
	}

	chromemDoc := chromem.Document{
		ID:       doc.ID,
		Content:  doc.Content,
		Metadata: metadata,
	}

	err := s.knowledgeCollection.AddDocuments(ctx, []chromem.Document{chromemDoc}, runtime.NumCPU())
	if err != nil {
		s.metrics.ErrorCount++
		return fmt.Errorf("failed to add knowledge document: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Knowledge document added successfully: %s", doc.ID)
	return nil
}

// SearchHVACKnowledge searches for similar HVAC knowledge
func (s *ChromemService) SearchHVACKnowledge(ctx context.Context, query string, options SearchOptions) ([]SearchResult, error) {
	s.log.WithContext(ctx).Infof("🔍 Searching HVAC knowledge: %s", query)
	s.metrics.QueriesExecuted++

	startTime := time.Now()
	results, err := s.hvacCollection.Query(ctx, query, options.TopK, nil, nil)
	queryTime := time.Since(startTime).Seconds()
	s.updateAvgQueryTime(queryTime)

	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("HVAC knowledge search failed: %w", err)
	}

	return s.convertToSearchResults(results, options.Threshold), nil
}

// SearchSimilarEmails searches for similar emails
func (s *ChromemService) SearchSimilarEmails(ctx context.Context, query string, options SearchOptions) ([]SearchResult, error) {
	s.log.WithContext(ctx).Infof("📧 Searching similar emails: %s", query)
	s.metrics.QueriesExecuted++

	startTime := time.Now()
	results, err := s.emailCollection.Query(ctx, query, options.TopK, nil, nil)
	queryTime := time.Since(startTime).Seconds()
	s.updateAvgQueryTime(queryTime)

	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("email search failed: %w", err)
	}

	return s.convertToSearchResults(results, options.Threshold), nil
}

// SearchKnowledgeBase searches the general knowledge base
func (s *ChromemService) SearchKnowledgeBase(ctx context.Context, query string, options SearchOptions) ([]SearchResult, error) {
	s.log.WithContext(ctx).Infof("🧠 Searching knowledge base: %s", query)
	s.metrics.QueriesExecuted++

	startTime := time.Now()
	results, err := s.knowledgeCollection.Query(ctx, query, options.TopK, nil, nil)
	queryTime := time.Since(startTime).Seconds()
	s.updateAvgQueryTime(queryTime)

	if err != nil {
		s.metrics.ErrorCount++
		return nil, fmt.Errorf("knowledge base search failed: %w", err)
	}

	return s.convertToSearchResults(results, options.Threshold), nil
}

// SearchAllCollections searches across all collections
func (s *ChromemService) SearchAllCollections(ctx context.Context, query string, options SearchOptions) (map[string][]SearchResult, error) {
	s.log.WithContext(ctx).Infof("🌐 Searching all collections: %s", query)

	results := make(map[string][]SearchResult)

	// Search HVAC knowledge
	hvacResults, err := s.SearchHVACKnowledge(ctx, query, options)
	if err != nil {
		s.log.WithContext(ctx).Warnf("HVAC search failed: %v", err)
	} else {
		results["hvac"] = hvacResults
	}

	// Search emails
	emailResults, err := s.SearchSimilarEmails(ctx, query, options)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Email search failed: %v", err)
	} else {
		results["emails"] = emailResults
	}

	// Search knowledge base
	knowledgeResults, err := s.SearchKnowledgeBase(ctx, query, options)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Knowledge search failed: %v", err)
	} else {
		results["knowledge"] = knowledgeResults
	}

	return results, nil
}

// GetDocumentByID retrieves a document by its ID
func (s *ChromemService) GetDocumentByID(ctx context.Context, collectionName, docID string) (*VectorDocument, error) {
	s.log.WithContext(ctx).Infof("📄 Getting document by ID: %s from %s", docID, collectionName)

	var collection *chromem.Collection
	switch collectionName {
	case "hvac":
		collection = s.hvacCollection
	case "emails":
		collection = s.emailCollection
	case "knowledge":
		collection = s.knowledgeCollection
	case "customers":
		collection = s.customerCollection
	default:
		return nil, fmt.Errorf("unknown collection: %s", collectionName)
	}

	// Note: Chromem-go doesn't have a direct GetByID method
	// We'll use a query with the exact ID
	results, err := collection.Query(ctx, docID, 1, map[string]string{"id": docID}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("document not found: %s", docID)
	}

	result := results[0]
	// Convert metadata from map[string]string to map[string]interface{}
	metadata := make(map[string]interface{})
	for k, v := range result.Metadata {
		metadata[k] = v
	}

	return &VectorDocument{
		ID:       result.ID,
		Content:  result.Content,
		Metadata: metadata,
	}, nil
}

// DeleteDocument removes a document from the specified collection
func (s *ChromemService) DeleteDocument(ctx context.Context, collectionName, docID string) error {
	s.log.WithContext(ctx).Infof("🗑️ Deleting document: %s from %s", docID, collectionName)

	// Note: Chromem-go doesn't have a direct delete method in the current version
	// This would need to be implemented when the feature becomes available
	s.log.WithContext(ctx).Warn("⚠️ Document deletion not yet supported by chromem-go")
	return fmt.Errorf("document deletion not yet supported")
}

// UpdateDocument updates an existing document
func (s *ChromemService) UpdateDocument(ctx context.Context, collectionName string, doc VectorDocument) error {
	s.log.WithContext(ctx).Infof("📝 Updating document: %s in %s", doc.ID, collectionName)

	// For now, we'll delete and re-add (when delete becomes available)
	// Currently, we'll just add the updated version
	switch collectionName {
	case "hvac":
		return s.AddHVACDocument(ctx, doc)
	case "emails":
		return s.AddEmailDocument(ctx, doc)
	case "knowledge":
		return s.AddKnowledgeDocument(ctx, doc)
	default:
		return fmt.Errorf("unknown collection: %s", collectionName)
	}
}

// GetCollectionStats returns statistics for a collection
func (s *ChromemService) GetCollectionStats(ctx context.Context, collectionName string) (map[string]interface{}, error) {
	s.log.WithContext(ctx).Infof("📊 Getting stats for collection: %s", collectionName)

	// Note: Chromem-go doesn't expose collection stats directly
	// We'll return basic metrics we track
	stats := map[string]interface{}{
		"collection_name":     collectionName,
		"documents_stored":    s.metrics.DocumentsStored,
		"queries_executed":    s.metrics.QueriesExecuted,
		"avg_query_time":      s.metrics.AvgQueryTime,
		"error_count":         s.metrics.ErrorCount,
		"last_updated":        time.Now(),
	}

	return stats, nil
}

// GetMetrics returns current vector database metrics
func (s *ChromemService) GetMetrics() *VectorDBMetrics {
	return s.metrics
}

// HealthCheck verifies vector database health
func (s *ChromemService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🏥 Performing vector database health check")

	// Test each collection with a simple query
	collections := map[string]*chromem.Collection{
		"hvac":      s.hvacCollection,
		"emails":    s.emailCollection,
		"knowledge": s.knowledgeCollection,
		"customers": s.customerCollection,
	}

	for name, collection := range collections {
		_, err := collection.Query(ctx, "health check", 1, nil, nil)
		if err != nil {
			return fmt.Errorf("health check failed for collection %s: %w", name, err)
		}
	}

	return nil
}

// Helper methods

func (s *ChromemService) convertToSearchResults(results []chromem.Result, threshold float64) []SearchResult {
	var searchResults []SearchResult

	for _, result := range results {
		if float64(result.Similarity) >= threshold {
			// Convert metadata from map[string]string to map[string]interface{}
			metadata := make(map[string]interface{})
			for k, v := range result.Metadata {
				metadata[k] = v
			}

			searchResults = append(searchResults, SearchResult{
				ID:         result.ID,
				Content:    result.Content,
				Similarity: float64(result.Similarity),
				Metadata:   metadata,
				Score:      float64(result.Similarity),
			})
		}
	}

	return searchResults
}

func (s *ChromemService) updateAvgQueryTime(queryTime float64) {
	if s.metrics.AvgQueryTime == 0 {
		s.metrics.AvgQueryTime = queryTime
	} else {
		s.metrics.AvgQueryTime = (s.metrics.AvgQueryTime + queryTime) / 2
	}
}

// BatchAddDocuments adds multiple documents efficiently
func (s *ChromemService) BatchAddDocuments(ctx context.Context, collectionName string, docs []VectorDocument) error {
	s.log.WithContext(ctx).Infof("📦 Batch adding %d documents to %s", len(docs), collectionName)

	var collection *chromem.Collection
	switch collectionName {
	case "hvac":
		collection = s.hvacCollection
	case "emails":
		collection = s.emailCollection
	case "knowledge":
		collection = s.knowledgeCollection
	case "customers":
		collection = s.customerCollection
	default:
		return fmt.Errorf("unknown collection: %s", collectionName)
	}

	// Convert to chromem documents
	chromemDocs := make([]chromem.Document, len(docs))
	for i, doc := range docs {
		// Convert metadata to map[string]string for chromem
		metadata := make(map[string]string)
		for k, v := range doc.Metadata {
			metadata[k] = fmt.Sprintf("%v", v)
		}

		chromemDocs[i] = chromem.Document{
			ID:       doc.ID,
			Content:  doc.Content,
			Metadata: metadata,
		}
	}

	err := collection.AddDocuments(ctx, chromemDocs, runtime.NumCPU())
	if err != nil {
		s.metrics.ErrorCount++
		return fmt.Errorf("batch add failed: %w", err)
	}

	s.metrics.DocumentsStored += int64(len(docs))
	s.log.WithContext(ctx).Infof("✅ Batch added %d documents successfully", len(docs))
	return nil
}
