package middleware

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/foundations/auth"
	"gobackend-hvac-kratos/internal/foundations/monitoring"
)

// MiddlewareStack combines all middleware functionality
type MiddlewareStack struct {
	logger         *zap.Logger
	metrics        *monitoring.MetricsService
	jwtService     *auth.JWTService
	circuitBreaker *gobreaker.CircuitBreaker
}

// NewMiddlewareStack creates a new middleware stack
func NewMiddlewareStack(logger *zap.Logger, metrics *monitoring.MetricsService, jwtService *auth.JWTService) *MiddlewareStack {
	// Configure circuit breaker
	cbSettings := gobreaker.Settings{
		Name:        "hvac-crm-api",
		MaxRequests: 3,
		Interval:    60 * time.Second,
		Timeout:     30 * time.Second,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 3 && failureRatio >= 0.6
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			logger.Info("Circuit breaker state changed",
				zap.String("name", name),
				zap.String("from", from.String()),
				zap.String("to", to.String()),
			)
		},
	}

	return &MiddlewareStack{
		logger:         logger,
		metrics:        metrics,
		jwtService:     jwtService,
		circuitBreaker: gobreaker.NewCircuitBreaker(cbSettings),
	}
}

// RequestLogging logs HTTP requests
func (m *MiddlewareStack) RequestLogging() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get status
		status := c.Writer.Status()

		// Log request
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", raw),
			zap.Int("status", status),
			zap.Duration("latency", latency),
			zap.String("ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
		}

		if len(c.Errors) > 0 {
			fields = append(fields, zap.String("errors", c.Errors.String()))
		}

		if status >= 500 {
			m.logger.Error("HTTP request", fields...)
		} else if status >= 400 {
			m.logger.Warn("HTTP request", fields...)
		} else {
			m.logger.Info("HTTP request", fields...)
		}
	}
}

// MetricsMiddleware records metrics for HTTP requests
func (m *MiddlewareStack) MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Increment in-flight requests
		m.metrics.IncHTTPRequestsInFlight()
		defer m.metrics.DecHTTPRequestsInFlight()

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start)
		status := strconv.Itoa(c.Writer.Status())

		m.metrics.RecordHTTPRequest(
			c.Request.Method,
			c.FullPath(),
			status,
			duration,
		)
	}
}

// JWTAuthMiddleware validates JWT tokens
func (m *MiddlewareStack) JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Extract token from "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// Validate token
		claims, err := m.jwtService.ValidateToken(tokenString)
		if err != nil {
			m.logger.Warn("Invalid JWT token", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("tenant_id", claims.TenantID)

		c.Next()
	}
}

// OptionalJWTAuthMiddleware validates JWT tokens but doesn't require them
func (m *MiddlewareStack) OptionalJWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Extract token from "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := tokenParts[1]

		// Validate token
		claims, err := m.jwtService.ValidateToken(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("tenant_id", claims.TenantID)

		c.Next()
	}
}

// RoleAuthMiddleware checks user roles
func (m *MiddlewareStack) RoleAuthMiddleware(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found"})
			c.Abort()
			return
		}

		role := userRole.(string)
		for _, allowedRole := range allowedRoles {
			if role == allowedRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
		c.Abort()
	}
}

// CircuitBreakerMiddleware implements circuit breaker pattern
func (m *MiddlewareStack) CircuitBreakerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		result, err := m.circuitBreaker.Execute(func() (interface{}, error) {
			c.Next()

			// Consider 5xx status codes as failures
			if c.Writer.Status() >= 500 {
				return nil, gin.Error{Err: http.ErrAbortHandler}
			}

			return nil, nil
		})

		if err != nil {
			if err == gobreaker.ErrOpenState {
				m.logger.Warn("Circuit breaker is open")
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Service temporarily unavailable",
				})
				c.Abort()
				return
			}

			m.logger.Error("Circuit breaker execution failed", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			c.Abort()
			return
		}

		_ = result // Unused in this case
	}
}

// RateLimitMiddleware implements rate limiting
func (m *MiddlewareStack) RateLimitMiddleware(requestsPerMinute int) gin.HandlerFunc {
	// Simple in-memory rate limiter (use Redis for production)
	clients := make(map[string][]time.Time)

	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// Clean old requests
		if requests, exists := clients[clientIP]; exists {
			var validRequests []time.Time
			for _, reqTime := range requests {
				if now.Sub(reqTime) < time.Minute {
					validRequests = append(validRequests, reqTime)
				}
			}
			clients[clientIP] = validRequests
		}

		// Check rate limit
		if len(clients[clientIP]) >= requestsPerMinute {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
			})
			c.Abort()
			return
		}

		// Add current request
		clients[clientIP] = append(clients[clientIP], now)

		c.Next()
	}
}

// ErrorHandlingMiddleware handles errors and panics
func (m *MiddlewareStack) ErrorHandlingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				m.logger.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("path", c.Request.URL.Path),
					zap.String("method", c.Request.Method),
				)

				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
				})
				c.Abort()
			}
		}()

		c.Next()

		// Handle errors from handlers
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			m.logger.Error("Handler error",
				zap.Error(err),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
			)

			if !c.Writer.Written() {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
				})
			}
		}
	}
}

// RequestIDMiddleware adds request ID to context
func (m *MiddlewareStack) RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// generateRequestID generates a simple request ID
func generateRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// GetUserID extracts user ID from context
func GetUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	return userID.(string), true
}

// GetUserRole extracts user role from context
func GetUserRole(c *gin.Context) (string, bool) {
	userRole, exists := c.Get("user_role")
	if !exists {
		return "", false
	}
	return userRole.(string), true
}

// GetTenantID extracts tenant ID from context
func GetTenantID(c *gin.Context) (string, bool) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		return "", false
	}
	return tenantID.(string), true
}

// MiddlewareFoundation combines all middleware functionality
type MiddlewareFoundation struct {
	Stack  *MiddlewareStack
	Logger *zap.Logger
}

// NewMiddlewareFoundation creates a complete middleware foundation
func NewMiddlewareFoundation(logger *zap.Logger, metrics *monitoring.MetricsService, jwtService *auth.JWTService) *MiddlewareFoundation {
	stack := NewMiddlewareStack(logger, metrics, jwtService)

	return &MiddlewareFoundation{
		Stack:  stack,
		Logger: logger,
	}
}
