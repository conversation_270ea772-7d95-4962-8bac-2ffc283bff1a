package foundations

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/foundations/auth"
	"gobackend-hvac-kratos/internal/foundations/documents"
	"gobackend-hvac-kratos/internal/foundations/email"
	"gobackend-hvac-kratos/internal/foundations/middleware"
	"gobackend-hvac-kratos/internal/foundations/monitoring"
	"gobackend-hvac-kratos/internal/foundations/storage"
	"gobackend-hvac-kratos/internal/foundations/validation"
	"gobackend-hvac-kratos/internal/foundations/web"
	"gobackend-hvac-kratos/internal/foundations/workflow"
)

// FoundationManager manages all foundation services
type FoundationManager struct {
	// Core foundations
	Auth       *auth.AuthFoundation
	Web        *web.WebFoundation
	Storage    *storage.StorageFoundation
	Documents  *documents.DocumentFoundation
	Workflow   *workflow.WorkflowFoundation
	Monitoring *monitoring.MonitoringFoundation
	Email      *email.EmailFoundation
	Validation *validation.ValidationFoundation
	Middleware *middleware.MiddlewareFoundation

	// Configuration
	Config *FoundationConfig
	Logger *zap.Logger
}

// FoundationConfig represents complete foundation configuration
type FoundationConfig struct {
	Auth       *auth.AuthConfig             `yaml:"auth"`
	Web        *web.WebConfig               `yaml:"web"`
	Storage    *storage.StorageConfig       `yaml:"storage"`
	Documents  *documents.DocumentConfig    `yaml:"documents"`
	Workflow   *workflow.WorkflowConfig     `yaml:"workflow"`
	Monitoring *monitoring.MonitoringConfig `yaml:"monitoring"`
	Email      *email.EmailConfig           `yaml:"email"`
}

// DefaultFoundationConfig returns default configuration for all foundations
func DefaultFoundationConfig() *FoundationConfig {
	return &FoundationConfig{
		Auth:       auth.DefaultAuthConfig(),
		Web:        web.DefaultWebConfig(),
		Storage:    storage.DefaultStorageConfig(),
		Documents:  documents.DefaultDocumentConfig(),
		Workflow:   workflow.DefaultWorkflowConfig(),
		Monitoring: monitoring.DefaultMonitoringConfig(),
		Email:      email.DefaultEmailConfig(),
	}
}

// NewFoundationManager creates and initializes all foundations
func NewFoundationManager(config *FoundationConfig) (*FoundationManager, error) {
	if config == nil {
		config = DefaultFoundationConfig()
	}

	// Initialize monitoring first (needed by other services)
	monitoring, err := monitoring.NewMonitoringFoundation(config.Monitoring)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize monitoring foundation: %w", err)
	}

	logger := monitoring.Logger.Logger()

	// Initialize auth foundation
	authFoundation := auth.NewAuthFoundation(config.Auth)

	// Initialize web foundation
	webFoundation := web.NewWebFoundation(config.Web, logger)

	// Initialize storage foundation
	storageFoundation, err := storage.NewStorageFoundation(config.Storage, logger)
	if err != nil {
		logger.Warn("Failed to initialize storage foundation", zap.Error(err))
		storageFoundation = nil // Continue without storage if not available
	}

	// Initialize documents foundation
	documentsFoundation, err := documents.NewDocumentFoundation(config.Documents, logger)
	if err != nil {
		logger.Warn("Failed to initialize documents foundation", zap.Error(err))
		documentsFoundation = nil // Continue without documents if not available
	}

	// Initialize workflow foundation
	workflowFoundation, err := workflow.NewWorkflowFoundation(config.Workflow, logger)
	if err != nil {
		logger.Warn("Failed to initialize workflow foundation", zap.Error(err))
		workflowFoundation = nil // Continue without workflow if not available
	}

	// Initialize email foundation
	emailFoundation, err := email.NewEmailFoundation(config.Email, logger)
	if err != nil {
		logger.Warn("Failed to initialize email foundation", zap.Error(err))
		emailFoundation = nil // Continue without email if not available
	}

	// Initialize validation foundation
	validationFoundation := validation.NewValidationFoundation(logger)

	// Initialize middleware foundation
	middlewareFoundation := middleware.NewMiddlewareFoundation(
		logger,
		monitoring.Metrics,
		authFoundation.JWT,
	)

	manager := &FoundationManager{
		Auth:       authFoundation,
		Web:        webFoundation,
		Storage:    storageFoundation,
		Documents:  documentsFoundation,
		Workflow:   workflowFoundation,
		Monitoring: monitoring,
		Email:      emailFoundation,
		Validation: validationFoundation,
		Middleware: middlewareFoundation,
		Config:     config,
		Logger:     logger,
	}

	logger.Info("Foundation manager initialized successfully",
		zap.Bool("auth", authFoundation != nil),
		zap.Bool("web", webFoundation != nil),
		zap.Bool("storage", storageFoundation != nil),
		zap.Bool("documents", documentsFoundation != nil),
		zap.Bool("workflow", workflowFoundation != nil),
		zap.Bool("monitoring", monitoring != nil),
		zap.Bool("email", emailFoundation != nil),
		zap.Bool("validation", validationFoundation != nil),
		zap.Bool("middleware", middlewareFoundation != nil),
	)

	return manager, nil
}

// SetupWebServer configures the web server with all middleware and routes
func (fm *FoundationManager) SetupWebServer() error {
	if fm.Web == nil {
		return fmt.Errorf("web foundation not initialized")
	}

	engine := fm.Web.Server.Engine()

	// Apply global middleware
	if fm.Middleware != nil {
		engine.Use(fm.Middleware.Stack.RequestIDMiddleware())
		engine.Use(fm.Middleware.Stack.RequestLogging())
		engine.Use(fm.Middleware.Stack.MetricsMiddleware())
		engine.Use(fm.Middleware.Stack.ErrorHandlingMiddleware())
		engine.Use(fm.Middleware.Stack.RateLimitMiddleware(100)) // 100 requests per minute
	}

	// Setup API routes
	fm.setupAPIRoutes(engine)

	fm.Logger.Info("Web server configured successfully")
	return nil
}

// setupAPIRoutes sets up all API routes
func (fm *FoundationManager) setupAPIRoutes(engine *gin.Engine) {
	// API v1 group
	v1 := engine.Group("/api/v1")

	// Public routes (no auth required)
	public := v1.Group("/public")
	{
		public.POST("/auth/login", fm.handleLogin)
		public.POST("/auth/register", fm.handleRegister)
		public.GET("/health", fm.handleHealth)
	}

	// Protected routes (auth required)
	if fm.Middleware != nil {
		protected := v1.Group("/")
		protected.Use(fm.Middleware.Stack.JWTAuthMiddleware())
		{
			// Customer routes
			customers := protected.Group("/customers")
			{
				customers.GET("", fm.handleGetCustomers)
				customers.POST("", fm.handleCreateCustomer)
				customers.GET("/:id", fm.handleGetCustomer)
				customers.PUT("/:id", fm.handleUpdateCustomer)
				customers.DELETE("/:id", fm.handleDeleteCustomer)
			}

			// Service ticket routes
			tickets := protected.Group("/tickets")
			{
				tickets.GET("", fm.handleGetTickets)
				tickets.POST("", fm.handleCreateTicket)
				tickets.GET("/:id", fm.handleGetTicket)
				tickets.PUT("/:id", fm.handleUpdateTicket)
				tickets.DELETE("/:id", fm.handleDeleteTicket)
			}

			// File upload routes
			if fm.Storage != nil {
				files := protected.Group("/files")
				{
					files.POST("/upload", fm.handleFileUpload)
					files.GET("/:id", fm.handleFileDownload)
					files.DELETE("/:id", fm.handleFileDelete)
				}
			}

			// Email routes
			if fm.Email != nil {
				emails := protected.Group("/emails")
				{
					emails.POST("/send", fm.handleSendEmail)
					emails.GET("/inbox", fm.handleGetInbox)
				}
			}

			// Admin routes (admin role required)
			admin := protected.Group("/admin")
			if fm.Middleware != nil {
				admin.Use(fm.Middleware.Stack.RoleAuthMiddleware("admin"))
			}
			{
				admin.GET("/metrics", fm.handleAdminMetrics)
				admin.GET("/users", fm.handleGetUsers)
				admin.POST("/users", fm.handleCreateUser)
			}
		}
	}
}

// StartServices starts all foundation services
func (fm *FoundationManager) StartServices(ctx context.Context) error {
	fm.Logger.Info("Starting foundation services...")

	// Start workflow services
	if fm.Workflow != nil && fm.Workflow.BackgroundJob != nil {
		go func() {
			if err := fm.Workflow.BackgroundJob.StartJobWorker(); err != nil {
				fm.Logger.Error("Failed to start background job worker", zap.Error(err))
			}
		}()
	}

	// Start email processing
	if fm.Email != nil {
		go func() {
			ticker := time.NewTicker(fm.Email.Config.PollInterval)
			defer ticker.Stop()

			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if err := fm.Email.Service.Connect(ctx); err != nil {
						fm.Logger.Error("Failed to connect to email server", zap.Error(err))
						continue
					}

					emails, err := fm.Email.Service.FetchUnreadEmails(ctx)
					if err != nil {
						fm.Logger.Error("Failed to fetch emails", zap.Error(err))
					} else {
						fm.Logger.Info("Fetched emails", zap.Int("count", len(emails)))
					}

					fm.Email.Service.Disconnect()
				}
			}
		}()
	}

	fm.Logger.Info("Foundation services started successfully")
	return nil
}

// StopServices stops all foundation services
func (fm *FoundationManager) StopServices(ctx context.Context) error {
	fm.Logger.Info("Stopping foundation services...")

	// Stop workflow services
	if fm.Workflow != nil {
		if fm.Workflow.BackgroundJob != nil {
			fm.Workflow.BackgroundJob.StopJobWorker()
		}
		if fm.Workflow.Temporal != nil {
			fm.Workflow.Temporal.StopWorker()
		}
	}

	// Stop web server
	if fm.Web != nil {
		if err := fm.Web.StopServer(ctx); err != nil {
			fm.Logger.Error("Failed to stop web server", zap.Error(err))
		}
	}

	// Sync logger
	if fm.Monitoring != nil && fm.Monitoring.Logger != nil {
		fm.Monitoring.Logger.Sync()
	}

	fm.Logger.Info("Foundation services stopped successfully")
	return nil
}

// GetStatus returns status of all foundations
func (fm *FoundationManager) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"auth":       fm.Auth != nil,
		"web":        fm.Web != nil,
		"storage":    fm.Storage != nil,
		"documents":  fm.Documents != nil,
		"workflow":   fm.Workflow != nil,
		"monitoring": fm.Monitoring != nil,
		"email":      fm.Email != nil,
		"validation": fm.Validation != nil,
		"middleware": fm.Middleware != nil,
		"timestamp":  time.Now().Unix(),
	}
}

// Placeholder handlers - to be implemented in detail later
func (fm *FoundationManager) handleLogin(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Login endpoint - to be implemented"})
}

func (fm *FoundationManager) handleRegister(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Register endpoint - to be implemented"})
}

func (fm *FoundationManager) handleHealth(c *gin.Context) {
	c.JSON(200, fm.GetStatus())
}

func (fm *FoundationManager) handleGetCustomers(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get customers - to be implemented"})
}

func (fm *FoundationManager) handleCreateCustomer(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Create customer - to be implemented"})
}

func (fm *FoundationManager) handleGetCustomer(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get customer - to be implemented"})
}

func (fm *FoundationManager) handleUpdateCustomer(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Update customer - to be implemented"})
}

func (fm *FoundationManager) handleDeleteCustomer(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Delete customer - to be implemented"})
}

func (fm *FoundationManager) handleGetTickets(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get tickets - to be implemented"})
}

func (fm *FoundationManager) handleCreateTicket(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Create ticket - to be implemented"})
}

func (fm *FoundationManager) handleGetTicket(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get ticket - to be implemented"})
}

func (fm *FoundationManager) handleUpdateTicket(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Update ticket - to be implemented"})
}

func (fm *FoundationManager) handleDeleteTicket(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Delete ticket - to be implemented"})
}

func (fm *FoundationManager) handleFileUpload(c *gin.Context) {
	c.JSON(200, gin.H{"message": "File upload - to be implemented"})
}

func (fm *FoundationManager) handleFileDownload(c *gin.Context) {
	c.JSON(200, gin.H{"message": "File download - to be implemented"})
}

func (fm *FoundationManager) handleFileDelete(c *gin.Context) {
	c.JSON(200, gin.H{"message": "File delete - to be implemented"})
}

func (fm *FoundationManager) handleSendEmail(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Send email - to be implemented"})
}

func (fm *FoundationManager) handleGetInbox(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get inbox - to be implemented"})
}

func (fm *FoundationManager) handleAdminMetrics(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Admin metrics - to be implemented"})
}

func (fm *FoundationManager) handleGetUsers(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get users - to be implemented"})
}

func (fm *FoundationManager) handleCreateUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Create user - to be implemented"})
}
