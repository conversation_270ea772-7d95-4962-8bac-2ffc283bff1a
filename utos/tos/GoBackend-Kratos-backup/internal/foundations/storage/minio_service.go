package storage

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
)

// MinIOService handles MinIO object storage operations
type MinIOService struct {
	client *minio.Client
	config *StorageConfig
	logger *zap.Logger
}

// StorageConfig represents storage configuration
type StorageConfig struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyID     string `yaml:"access_key_id"`
	SecretAccessKey string `yaml:"secret_access_key"`
	UseSSL          bool   `yaml:"use_ssl"`
	Region          string `yaml:"region"`
	DefaultBucket   string `yaml:"default_bucket"`
}

// DefaultStorageConfig returns default storage configuration
func DefaultStorageConfig() *StorageConfig {
	return &StorageConfig{
		Endpoint:        "**************:9000",
		AccessKeyID:     "koldbringer",
		SecretAccessKey: "Blaeritipol1",
		UseSSL:          false,
		Region:          "us-east-1",
		DefaultBucket:   "hvac-crm",
	}
}

// NewMinIOService creates a new MinIO service instance
func NewMinIOService(config *StorageConfig, logger *zap.Logger) (*MinIOService, error) {
	if config == nil {
		config = DefaultStorageConfig()
	}

	// Initialize MinIO client
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
		Region: config.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	service := &MinIOService{
		client: client,
		config: config,
		logger: logger,
	}

	// Ensure default bucket exists
	if err := service.ensureBucket(context.Background(), config.DefaultBucket); err != nil {
		return nil, fmt.Errorf("failed to ensure default bucket: %w", err)
	}

	return service, nil
}

// ensureBucket creates bucket if it doesn't exist
func (s *MinIOService) ensureBucket(ctx context.Context, bucketName string) error {
	exists, err := s.client.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check bucket existence: %w", err)
	}

	if !exists {
		err = s.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{
			Region: s.config.Region,
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		s.logger.Info("Created bucket", zap.String("bucket", bucketName))
	}

	return nil
}

// UploadFile uploads a file to MinIO
func (s *MinIOService) UploadFile(ctx context.Context, bucketName, objectName string, reader io.Reader, objectSize int64, contentType string) (*UploadResult, error) {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	// Ensure bucket exists
	if err := s.ensureBucket(ctx, bucketName); err != nil {
		return nil, err
	}

	// Upload options
	opts := minio.PutObjectOptions{
		ContentType: contentType,
	}

	// Upload file
	info, err := s.client.PutObject(ctx, bucketName, objectName, reader, objectSize, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	s.logger.Info("File uploaded successfully",
		zap.String("bucket", bucketName),
		zap.String("object", objectName),
		zap.Int64("size", info.Size),
	)

	return &UploadResult{
		Bucket:      bucketName,
		ObjectName:  objectName,
		Size:        info.Size,
		ETag:        info.ETag,
		ContentType: contentType,
		UploadedAt:  time.Now(),
	}, nil
}

// DownloadFile downloads a file from MinIO
func (s *MinIOService) DownloadFile(ctx context.Context, bucketName, objectName string) (io.ReadCloser, error) {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	object, err := s.client.GetObject(ctx, bucketName, objectName, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}

	return object, nil
}

// DeleteFile deletes a file from MinIO
func (s *MinIOService) DeleteFile(ctx context.Context, bucketName, objectName string) error {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	err := s.client.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	s.logger.Info("File deleted successfully",
		zap.String("bucket", bucketName),
		zap.String("object", objectName),
	)

	return nil
}

// GetFileInfo gets file information
func (s *MinIOService) GetFileInfo(ctx context.Context, bucketName, objectName string) (*FileInfo, error) {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	stat, err := s.client.StatObject(ctx, bucketName, objectName, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &FileInfo{
		Bucket:       bucketName,
		ObjectName:   objectName,
		Size:         stat.Size,
		ETag:         stat.ETag,
		ContentType:  stat.ContentType,
		LastModified: stat.LastModified,
	}, nil
}

// ListFiles lists files in a bucket
func (s *MinIOService) ListFiles(ctx context.Context, bucketName, prefix string) ([]*FileInfo, error) {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	var files []*FileInfo

	objectCh := s.client.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("failed to list objects: %w", object.Err)
		}

		files = append(files, &FileInfo{
			Bucket:       bucketName,
			ObjectName:   object.Key,
			Size:         object.Size,
			ETag:         object.ETag,
			LastModified: object.LastModified,
		})
	}

	return files, nil
}

// GeneratePresignedURL generates a presigned URL for file access
func (s *MinIOService) GeneratePresignedURL(ctx context.Context, bucketName, objectName string, expiry time.Duration) (string, error) {
	if bucketName == "" {
		bucketName = s.config.DefaultBucket
	}

	url, err := s.client.PresignedGetObject(ctx, bucketName, objectName, expiry, nil)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return url.String(), nil
}

// UploadResult represents upload operation result
type UploadResult struct {
	Bucket      string    `json:"bucket"`
	ObjectName  string    `json:"object_name"`
	Size        int64     `json:"size"`
	ETag        string    `json:"etag"`
	ContentType string    `json:"content_type"`
	UploadedAt  time.Time `json:"uploaded_at"`
}

// FileInfo represents file information
type FileInfo struct {
	Bucket       string    `json:"bucket"`
	ObjectName   string    `json:"object_name"`
	Size         int64     `json:"size"`
	ETag         string    `json:"etag"`
	ContentType  string    `json:"content_type"`
	LastModified time.Time `json:"last_modified"`
}

// StorageFoundation combines all storage functionality
type StorageFoundation struct {
	MinIO  *MinIOService
	Config *StorageConfig
	Logger *zap.Logger
}

// NewStorageFoundation creates a complete storage foundation
func NewStorageFoundation(config *StorageConfig, logger *zap.Logger) (*StorageFoundation, error) {
	if config == nil {
		config = DefaultStorageConfig()
	}

	minioService, err := NewMinIOService(config, logger)
	if err != nil {
		return nil, err
	}

	return &StorageFoundation{
		MinIO:  minioService,
		Config: config,
		Logger: logger,
	}, nil
}
