FROM golang:1.23-alpine AS builder

# Install dependencies including make
RUN apk add --no-cache git protobuf-dev protoc make

# Set working directory
WORKDIR /src

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Install protoc plugins
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
RUN go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
RUN go install github.com/google/wire/cmd/wire@latest

# Generate protobuf and wire
RUN make api config
RUN cd cmd/server && wire

# Fix go.sum issues
RUN go mod download github.com/go-redis/redis/v8
RUN go mod tidy

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o server ./cmd/server

FROM alpine:latest

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates tzdata

WORKDIR /root/

# Copy the binary from builder
COPY --from=builder /src/server .
COPY --from=builder /src/configs ./configs

# Expose ports
EXPOSE 8080 9000 8081

# Command to run
CMD ["./server"]