// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: analytics/v1/analytics.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAnalyticsServiceCreateDashboardWidget = "/api.analytics.v1.AnalyticsService/CreateDashboardWidget"
const OperationAnalyticsServiceGetCustomerInsightsDashboard = "/api.analytics.v1.AnalyticsService/GetCustomerInsightsDashboard"
const OperationAnalyticsServiceGetDashboardWidgets = "/api.analytics.v1.AnalyticsService/GetDashboardWidgets"
const OperationAnalyticsServiceGetExecutiveDashboard = "/api.analytics.v1.AnalyticsService/GetExecutiveDashboard"
const OperationAnalyticsServiceGetKPIs = "/api.analytics.v1.AnalyticsService/GetKPIs"
const OperationAnalyticsServiceGetOperationalDashboard = "/api.analytics.v1.AnalyticsService/GetOperationalDashboard"
const OperationAnalyticsServiceGetPerformanceTrends = "/api.analytics.v1.AnalyticsService/GetPerformanceTrends"
const OperationAnalyticsServiceGetRealTimeMetrics = "/api.analytics.v1.AnalyticsService/GetRealTimeMetrics"
const OperationAnalyticsServiceHealthCheck = "/api.analytics.v1.AnalyticsService/HealthCheck"
const OperationAnalyticsServiceUpdateKPI = "/api.analytics.v1.AnalyticsService/UpdateKPI"

type AnalyticsServiceHTTPServer interface {
	// CreateDashboardWidget Create dashboard widget
	CreateDashboardWidget(context.Context, *CreateDashboardWidgetRequest) (*CreateDashboardWidgetResponse, error)
	// GetCustomerInsightsDashboard Get customer insights dashboard
	GetCustomerInsightsDashboard(context.Context, *GetCustomerInsightsDashboardRequest) (*GetCustomerInsightsDashboardResponse, error)
	// GetDashboardWidgets Get dashboard widgets
	GetDashboardWidgets(context.Context, *GetDashboardWidgetsRequest) (*GetDashboardWidgetsResponse, error)
	// GetExecutiveDashboard Get executive dashboard data
	GetExecutiveDashboard(context.Context, *GetExecutiveDashboardRequest) (*GetExecutiveDashboardResponse, error)
	// GetKPIs Get KPIs
	GetKPIs(context.Context, *GetKPIsRequest) (*GetKPIsResponse, error)
	// GetOperationalDashboard Get operational dashboard
	GetOperationalDashboard(context.Context, *GetOperationalDashboardRequest) (*GetOperationalDashboardResponse, error)
	// GetPerformanceTrends Get performance trends
	GetPerformanceTrends(context.Context, *GetPerformanceTrendsRequest) (*GetPerformanceTrendsResponse, error)
	// GetRealTimeMetrics Get real-time metrics
	GetRealTimeMetrics(context.Context, *GetRealTimeMetricsRequest) (*GetRealTimeMetricsResponse, error)
	// HealthCheck Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	// UpdateKPI Update KPI
	UpdateKPI(context.Context, *UpdateKPIRequest) (*UpdateKPIResponse, error)
}

func RegisterAnalyticsServiceHTTPServer(s *http.Server, srv AnalyticsServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/api/v1/analytics/dashboard/executive", _AnalyticsService_GetExecutiveDashboard0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/dashboard/customer", _AnalyticsService_GetCustomerInsightsDashboard0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/dashboard/operations", _AnalyticsService_GetOperationalDashboard0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/trends/performance", _AnalyticsService_GetPerformanceTrends0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/kpis", _AnalyticsService_GetKPIs0_HTTP_Handler(srv))
	r.POST("/api/v1/analytics/kpis", _AnalyticsService_UpdateKPI0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/metrics/realtime", _AnalyticsService_GetRealTimeMetrics0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/widgets", _AnalyticsService_GetDashboardWidgets0_HTTP_Handler(srv))
	r.POST("/api/v1/analytics/widgets", _AnalyticsService_CreateDashboardWidget0_HTTP_Handler(srv))
	r.GET("/api/v1/analytics/health", _AnalyticsService_HealthCheck0_HTTP_Handler(srv))
}

func _AnalyticsService_GetExecutiveDashboard0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExecutiveDashboardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetExecutiveDashboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExecutiveDashboard(ctx, req.(*GetExecutiveDashboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExecutiveDashboardResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetCustomerInsightsDashboard0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCustomerInsightsDashboardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetCustomerInsightsDashboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCustomerInsightsDashboard(ctx, req.(*GetCustomerInsightsDashboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCustomerInsightsDashboardResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetOperationalDashboard0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOperationalDashboardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetOperationalDashboard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOperationalDashboard(ctx, req.(*GetOperationalDashboardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOperationalDashboardResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetPerformanceTrends0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPerformanceTrendsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetPerformanceTrends)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPerformanceTrends(ctx, req.(*GetPerformanceTrendsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPerformanceTrendsResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetKPIs0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetKPIsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetKPIs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetKPIs(ctx, req.(*GetKPIsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetKPIsResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_UpdateKPI0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateKPIRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceUpdateKPI)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateKPI(ctx, req.(*UpdateKPIRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateKPIResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetRealTimeMetrics0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRealTimeMetricsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetRealTimeMetrics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRealTimeMetrics(ctx, req.(*GetRealTimeMetricsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRealTimeMetricsResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_GetDashboardWidgets0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDashboardWidgetsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceGetDashboardWidgets)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDashboardWidgets(ctx, req.(*GetDashboardWidgetsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDashboardWidgetsResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_CreateDashboardWidget0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDashboardWidgetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceCreateDashboardWidget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDashboardWidget(ctx, req.(*CreateDashboardWidgetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDashboardWidgetResponse)
		return ctx.Result(200, reply)
	}
}

func _AnalyticsService_HealthCheck0_HTTP_Handler(srv AnalyticsServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HealthCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnalyticsServiceHealthCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HealthCheck(ctx, req.(*HealthCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HealthCheckResponse)
		return ctx.Result(200, reply)
	}
}

type AnalyticsServiceHTTPClient interface {
	CreateDashboardWidget(ctx context.Context, req *CreateDashboardWidgetRequest, opts ...http.CallOption) (rsp *CreateDashboardWidgetResponse, err error)
	GetCustomerInsightsDashboard(ctx context.Context, req *GetCustomerInsightsDashboardRequest, opts ...http.CallOption) (rsp *GetCustomerInsightsDashboardResponse, err error)
	GetDashboardWidgets(ctx context.Context, req *GetDashboardWidgetsRequest, opts ...http.CallOption) (rsp *GetDashboardWidgetsResponse, err error)
	GetExecutiveDashboard(ctx context.Context, req *GetExecutiveDashboardRequest, opts ...http.CallOption) (rsp *GetExecutiveDashboardResponse, err error)
	GetKPIs(ctx context.Context, req *GetKPIsRequest, opts ...http.CallOption) (rsp *GetKPIsResponse, err error)
	GetOperationalDashboard(ctx context.Context, req *GetOperationalDashboardRequest, opts ...http.CallOption) (rsp *GetOperationalDashboardResponse, err error)
	GetPerformanceTrends(ctx context.Context, req *GetPerformanceTrendsRequest, opts ...http.CallOption) (rsp *GetPerformanceTrendsResponse, err error)
	GetRealTimeMetrics(ctx context.Context, req *GetRealTimeMetricsRequest, opts ...http.CallOption) (rsp *GetRealTimeMetricsResponse, err error)
	HealthCheck(ctx context.Context, req *HealthCheckRequest, opts ...http.CallOption) (rsp *HealthCheckResponse, err error)
	UpdateKPI(ctx context.Context, req *UpdateKPIRequest, opts ...http.CallOption) (rsp *UpdateKPIResponse, err error)
}

type AnalyticsServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAnalyticsServiceHTTPClient(client *http.Client) AnalyticsServiceHTTPClient {
	return &AnalyticsServiceHTTPClientImpl{client}
}

func (c *AnalyticsServiceHTTPClientImpl) CreateDashboardWidget(ctx context.Context, in *CreateDashboardWidgetRequest, opts ...http.CallOption) (*CreateDashboardWidgetResponse, error) {
	var out CreateDashboardWidgetResponse
	pattern := "/api/v1/analytics/widgets"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAnalyticsServiceCreateDashboardWidget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetCustomerInsightsDashboard(ctx context.Context, in *GetCustomerInsightsDashboardRequest, opts ...http.CallOption) (*GetCustomerInsightsDashboardResponse, error) {
	var out GetCustomerInsightsDashboardResponse
	pattern := "/api/v1/analytics/dashboard/customer"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetCustomerInsightsDashboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetDashboardWidgets(ctx context.Context, in *GetDashboardWidgetsRequest, opts ...http.CallOption) (*GetDashboardWidgetsResponse, error) {
	var out GetDashboardWidgetsResponse
	pattern := "/api/v1/analytics/widgets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetDashboardWidgets))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetExecutiveDashboard(ctx context.Context, in *GetExecutiveDashboardRequest, opts ...http.CallOption) (*GetExecutiveDashboardResponse, error) {
	var out GetExecutiveDashboardResponse
	pattern := "/api/v1/analytics/dashboard/executive"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetExecutiveDashboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetKPIs(ctx context.Context, in *GetKPIsRequest, opts ...http.CallOption) (*GetKPIsResponse, error) {
	var out GetKPIsResponse
	pattern := "/api/v1/analytics/kpis"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetKPIs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetOperationalDashboard(ctx context.Context, in *GetOperationalDashboardRequest, opts ...http.CallOption) (*GetOperationalDashboardResponse, error) {
	var out GetOperationalDashboardResponse
	pattern := "/api/v1/analytics/dashboard/operations"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetOperationalDashboard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetPerformanceTrends(ctx context.Context, in *GetPerformanceTrendsRequest, opts ...http.CallOption) (*GetPerformanceTrendsResponse, error) {
	var out GetPerformanceTrendsResponse
	pattern := "/api/v1/analytics/trends/performance"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetPerformanceTrends))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) GetRealTimeMetrics(ctx context.Context, in *GetRealTimeMetricsRequest, opts ...http.CallOption) (*GetRealTimeMetricsResponse, error) {
	var out GetRealTimeMetricsResponse
	pattern := "/api/v1/analytics/metrics/realtime"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceGetRealTimeMetrics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...http.CallOption) (*HealthCheckResponse, error) {
	var out HealthCheckResponse
	pattern := "/api/v1/analytics/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnalyticsServiceHealthCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnalyticsServiceHTTPClientImpl) UpdateKPI(ctx context.Context, in *UpdateKPIRequest, opts ...http.CallOption) (*UpdateKPIResponse, error) {
	var out UpdateKPIResponse
	pattern := "/api/v1/analytics/kpis"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAnalyticsServiceUpdateKPI))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
