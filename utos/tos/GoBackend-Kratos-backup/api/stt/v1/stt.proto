syntax = "proto3";

package api.stt.v1;

option go_package = "gobackend-hvac-kratos/api/stt/v1;v1";

import "google/api/annotations.proto";

// 🎤 NVIDIA STT Service - Polish FastConformer Hybrid Large PC
// Potężny model do rozpoznawania mowy polskiej z najwyższą dokładnością
service STTService {
  // 🎯 Transkrypcja audio na tekst (główna funkcja)
  rpc TranscribeAudio(TranscribeRequest) returns (TranscribeResponse) {
    option (google.api.http) = {
      post: "/api/v1/stt/transcribe"
      body: "*"
    };
  }

  // 🎤 Transkrypcja w czasie rzeczywistym (streaming)
  rpc TranscribeStream(stream StreamTranscribeRequest) returns (stream StreamTranscribeResponse);

  // 📞 Transkrypcja rozmów telefonicznych HVAC
  rpc TranscribePhoneCall(PhoneCallRequest) returns (PhoneCallResponse) {
    option (google.api.http) = {
      post: "/api/v1/stt/phone-call"
      body: "*"
    };
  }

  // 🔧 Analiza techniczna rozmów HVAC
  rpc AnalyzeHVACCall(HVACCallAnalysisRequest) returns (HVACCallAnalysisResponse) {
    option (google.api.http) = {
      post: "/api/v1/stt/analyze-hvac-call"
      body: "*"
    };
  }

  // 📊 Status modelu i metryki
  rpc GetModelStatus(ModelStatusRequest) returns (ModelStatusResponse) {
    option (google.api.http) = {
      get: "/api/v1/stt/model/status"
    };
  }

  // ⚙️ Konfiguracja modelu
  rpc ConfigureModel(ModelConfigRequest) returns (ModelConfigResponse) {
    option (google.api.http) = {
      post: "/api/v1/stt/model/configure"
      body: "*"
    };
  }
}

// 🎤 Podstawowe żądanie transkrypcji
message TranscribeRequest {
  bytes audio_data = 1;
  string audio_format = 2;
  int32 sample_rate = 3;
  string language = 4;
  TranscriptionMode mode = 5;
  HVACContext hvac_context = 6;
  TranscriptionOptions options = 7;
}

// 📝 Odpowiedź transkrypcji
message TranscribeResponse {
  string transcript = 1;
  float confidence = 2;
  repeated TranscriptSegment segments = 3;
  repeated string hvac_keywords = 4;
  SentimentAnalysis sentiment = 5;
  ProcessingMetadata metadata = 6;
  repeated TechnicalIssue technical_issues = 7;
}

// 🌊 Streaming transkrypcja - żądanie
message StreamTranscribeRequest {
  oneof request_type {
    StreamConfig config = 1;
    bytes audio_chunk = 2;
  }
}

// 🌊 Streaming transkrypcja - odpowiedź
message StreamTranscribeResponse {
  string partial_transcript = 1;
  string final_transcript = 2;
  bool is_final = 3;
  float confidence = 4;
  int64 timestamp_ms = 5;
  repeated string keywords = 6;
}

// 📞 Żądanie transkrypcji rozmowy telefonicznej
message PhoneCallRequest {
  bytes call_audio = 1;
  CallMetadata call_metadata = 2;
  CustomerContext customer_context = 3;
  CallAnalysisOptions analysis_options = 4;
}

// 📞 Odpowiedź transkrypcji rozmowy
message PhoneCallResponse {
  string full_transcript = 1;
  repeated SpeakerSegment speaker_segments = 2;
  CallSummary call_summary = 3;
  repeated CustomerNeed customer_needs = 4;
  repeated RecommendedAction recommended_actions = 5;
  ServiceQualityAnalysis service_quality = 6;
}

// 🔧 Żądanie analizy rozmowy HVAC
message HVACCallAnalysisRequest {
  string transcript = 1;
  CallMetadata call_metadata = 2;
  CustomerHistory customer_history = 3;
  repeated AnalysisType analysis_types = 4;
}

// 🔧 Odpowiedź analizy HVAC
message HVACCallAnalysisResponse {
  repeated TechnicalIssue technical_issues = 1;
  ServiceType required_service = 2;
  Priority priority = 3;
  EstimatedRepairTime estimated_time = 4;
  repeated RequiredPart required_parts = 5;
  CostAnalysis cost_analysis = 6;
  repeated TechnicianRecommendation technician_recommendations = 7;
}

// 📊 Status modelu
message ModelStatusRequest {}

message ModelStatusResponse {
  ModelStatus status = 1;
  string model_version = 2;
  PerformanceMetrics performance = 3;
  repeated string supported_languages = 4;
  ResourceUsage resource_usage = 5;
}

// ⚙️ Konfiguracja modelu
message ModelConfigRequest {
  ModelParameters parameters = 1;
  HVACDictionary hvac_dictionary = 2;
  QualitySettings quality_settings = 3;
}

message ModelConfigResponse {
  bool success = 1;
  string message = 2;
  ModelConfiguration current_config = 3;
}

// 🎯 Enums
enum TranscriptionMode {
  TRANSCRIPTION_MODE_UNSPECIFIED = 0;
  TRANSCRIPTION_MODE_STANDARD = 1;
  TRANSCRIPTION_MODE_HVAC_OPTIMIZED = 2;
  TRANSCRIPTION_MODE_PHONE_CALL = 3;
  TRANSCRIPTION_MODE_TECHNICAL = 4;
}

enum Priority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_MEDIUM = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_URGENT = 4;
  PRIORITY_EMERGENCY = 5;
}

enum ServiceType {
  SERVICE_TYPE_UNSPECIFIED = 0;
  SERVICE_TYPE_MAINTENANCE = 1;
  SERVICE_TYPE_REPAIR = 2;
  SERVICE_TYPE_INSTALLATION = 3;
  SERVICE_TYPE_INSPECTION = 4;
  SERVICE_TYPE_EMERGENCY = 5;
  SERVICE_TYPE_CONSULTATION = 6;
}

enum AnalysisType {
  ANALYSIS_TYPE_UNSPECIFIED = 0;
  ANALYSIS_TYPE_TECHNICAL = 1;
  ANALYSIS_TYPE_SENTIMENT = 2;
  ANALYSIS_TYPE_URGENCY = 3;
  ANALYSIS_TYPE_COST = 4;
  ANALYSIS_TYPE_QUALITY = 5;
}

enum ModelStatus {
  MODEL_STATUS_UNSPECIFIED = 0;
  MODEL_STATUS_LOADING = 1;
  MODEL_STATUS_READY = 2;
  MODEL_STATUS_BUSY = 3;
  MODEL_STATUS_ERROR = 4;
  MODEL_STATUS_MAINTENANCE = 5;
}

// 🏗️ Struktury danych
message HVACContext {
  string system_type = 1;
  string equipment_brand = 2;
  string equipment_model = 3;
  int32 installation_age_years = 4;
  repeated string service_history = 5;
  string season = 6;
}

message TranscriptionOptions {
  bool enable_emotion_detection = 1;
  bool enable_keyword_detection = 2;
  bool enable_technical_analysis = 3;
  int32 timestamp_granularity_ms = 4;
  bool enable_noise_reduction = 5;
  bool enable_volume_normalization = 6;
}

message TranscriptSegment {
  string text = 1;
  int64 start_time_ms = 2;
  int64 end_time_ms = 3;
  float confidence = 4;
  string speaker_id = 5;
  repeated string emotions = 6;
}

message SentimentAnalysis {
  float overall_sentiment = 1;
  float frustration_level = 2;
  float satisfaction_level = 3;
  repeated EmotionScore emotions = 4;
  string conversation_tone = 5;
}

message EmotionScore {
  string emotion = 1;
  float score = 2;
}

message ProcessingMetadata {
  int64 processing_time_ms = 1;
  string model_version = 2;
  AudioQuality audio_quality = 3;
  ProcessingStats stats = 4;
}

message AudioQuality {
  float noise_level = 1;
  float signal_quality = 2;
  repeated string quality_issues = 3;
}

message ProcessingStats {
  int32 word_count = 1;
  float audio_duration_seconds = 2;
  float processing_speed_ratio = 3;
}

message TechnicalIssue {
  string issue_type = 1;
  string description = 2;
  float confidence = 3;
  string affected_component = 4;
  repeated string possible_causes = 5;
  repeated string recommended_solutions = 6;
}

message StreamConfig {
  int32 sample_rate = 1;
  string audio_format = 2;
  HVACContext hvac_context = 3;
  TranscriptionOptions options = 4;
}

message CallMetadata {
  string call_id = 1;
  int64 start_time = 2;
  int64 duration_seconds = 3;
  string customer_phone = 4;
  string operator_id = 5;
  string call_type = 6;
}

message CustomerContext {
  string customer_id = 1;
  string name = 2;
  string address = 3;
  repeated string service_history = 4;
  CustomerPreferences preferences = 5;
}

message CustomerPreferences {
  string preferred_contact_time = 1;
  string preferred_communication = 2;
  repeated string special_notes = 3;
}

message CallAnalysisOptions {
  bool analyze_sentiment = 1;
  bool detect_technical_issues = 2;
  bool analyze_service_quality = 3;
  bool generate_summary = 4;
}

message SpeakerSegment {
  string speaker_id = 1;
  string text = 2;
  int64 start_time_ms = 3;
  int64 end_time_ms = 4;
  SentimentAnalysis sentiment = 5;
}

message CallSummary {
  string summary = 1;
  repeated string key_points = 2;
  repeated string decisions_made = 3;
  repeated string next_steps = 4;
  float overall_rating = 5;
}

message CustomerNeed {
  string need_type = 1;
  string description = 2;
  Priority priority = 3;
  CostEstimate cost_estimate = 4;
}

message CostEstimate {
  float min_cost = 1;
  float max_cost = 2;
  string currency = 3;
  repeated string cost_factors = 4;
}

message RecommendedAction {
  string action_type = 1;
  string description = 2;
  Priority priority = 3;
  int32 estimated_duration_minutes = 4;
  repeated string required_resources = 5;
}

message ServiceQualityAnalysis {
  float overall_quality_score = 1;
  float professionalism_score = 2;
  float resolution_speed_score = 3;
  float customer_satisfaction_score = 4;
  repeated string improvement_areas = 5;
  repeated string positive_aspects = 6;
}

message CustomerHistory {
  repeated string previous_tickets = 1;
  PaymentHistory payment_history = 2;
  ServicePreferences service_preferences = 3;
  repeated ServiceRating previous_ratings = 4;
}

message PaymentHistory {
  string payment_status = 1;
  repeated string payment_delays = 2;
  string preferred_payment_method = 3;
}

message ServicePreferences {
  string preferred_technician = 1;
  repeated string preferred_service_hours = 2;
  repeated string special_requirements = 3;
}

message ServiceRating {
  string service_date = 1;
  int32 rating = 2;
  string comment = 3;
  string service_type = 4;
}

message EstimatedRepairTime {
  int32 min_hours = 1;
  int32 max_hours = 2;
  repeated string time_factors = 3;
  string technician_availability = 4;
}

message RequiredPart {
  string part_name = 1;
  string part_code = 2;
  int32 quantity = 3;
  float estimated_price = 4;
  string availability = 5;
  repeated string alternative_parts = 6;
}

message CostAnalysis {
  float parts_cost = 1;
  float labor_cost = 2;
  float additional_costs = 3;
  float total_cost = 4;
  string currency = 5;
  repeated CostBreakdown cost_breakdown = 6;
}

message CostBreakdown {
  string category = 1;
  float amount = 2;
  string description = 3;
}

message TechnicianRecommendation {
  string recommendation_type = 1;
  string description = 2;
  Priority priority = 3;
  repeated string required_tools = 4;
  repeated string safety_measures = 5;
}

message PerformanceMetrics {
  float average_response_time_ms = 1;
  float transcription_accuracy = 2;
  int64 processed_requests = 3;
  int64 error_count = 4;
  float cpu_usage = 5;
  float memory_usage = 6;
}

message ResourceUsage {
  float gpu_usage = 1;
  float gpu_memory_mb = 2;
  float cpu_usage = 3;
  float ram_usage_mb = 4;
  int32 active_sessions = 5;
}

message ModelParameters {
  int32 beam_size = 1;
  float temperature = 2;
  float confidence_threshold = 3;
  int32 max_segment_length = 4;
  bool enable_post_processing = 5;
}

message HVACDictionary {
  repeated string hvac_keywords = 1;
  repeated string brand_names = 2;
  repeated string model_names = 3;
  repeated string technical_terms = 4;
  repeated string error_codes = 5;
}

message QualitySettings {
  float min_audio_quality = 1;
  float max_noise_level = 2;
  bool enable_auto_enhancement = 3;
  int32 filtering_aggressiveness = 4;
}

message ModelConfiguration {
  ModelParameters parameters = 1;
  HVACDictionary dictionary = 2;
  QualitySettings quality = 3;
  int64 last_updated = 4;
}