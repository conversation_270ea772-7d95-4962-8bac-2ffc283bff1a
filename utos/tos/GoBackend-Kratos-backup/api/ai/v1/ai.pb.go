// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: ai/v1/ai.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message Definitions
type ChatRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Model         string                 `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	Context       []string               `protobuf:"bytes,3,rep,name=context,proto3" json:"context,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatRequest) Reset() {
	*x = ChatRequest{}
	mi := &file_ai_v1_ai_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatRequest) ProtoMessage() {}

func (x *ChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatRequest.ProtoReflect.Descriptor instead.
func (*ChatRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{0}
}

func (x *ChatRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ChatRequest) GetContext() []string {
	if x != nil {
		return x.Context
	}
	return nil
}

type ChatResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      string                 `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	ModelUsed     string                 `protobuf:"bytes,2,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	TokensUsed    int32                  `protobuf:"varint,3,opt,name=tokens_used,json=tokensUsed,proto3" json:"tokens_used,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatResponse) Reset() {
	*x = ChatResponse{}
	mi := &file_ai_v1_ai_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatResponse) ProtoMessage() {}

func (x *ChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatResponse.ProtoReflect.Descriptor instead.
func (*ChatResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{1}
}

func (x *ChatResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *ChatResponse) GetModelUsed() string {
	if x != nil {
		return x.ModelUsed
	}
	return ""
}

func (x *ChatResponse) GetTokensUsed() int32 {
	if x != nil {
		return x.TokensUsed
	}
	return 0
}

type AnalyzeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	AnalysisType  string                 `protobuf:"bytes,2,opt,name=analysis_type,json=analysisType,proto3" json:"analysis_type,omitempty"`
	Model         string                 `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzeRequest) Reset() {
	*x = AnalyzeRequest{}
	mi := &file_ai_v1_ai_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeRequest) ProtoMessage() {}

func (x *AnalyzeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeRequest.ProtoReflect.Descriptor instead.
func (*AnalyzeRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{2}
}

func (x *AnalyzeRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AnalyzeRequest) GetAnalysisType() string {
	if x != nil {
		return x.AnalysisType
	}
	return ""
}

func (x *AnalyzeRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

type AnalyzeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Analysis      string                 `protobuf:"bytes,1,opt,name=analysis,proto3" json:"analysis,omitempty"`
	Confidence    float32                `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzeResponse) Reset() {
	*x = AnalyzeResponse{}
	mi := &file_ai_v1_ai_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeResponse) ProtoMessage() {}

func (x *AnalyzeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeResponse.ProtoReflect.Descriptor instead.
func (*AnalyzeResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{3}
}

func (x *AnalyzeResponse) GetAnalysis() string {
	if x != nil {
		return x.Analysis
	}
	return ""
}

func (x *AnalyzeResponse) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *AnalyzeResponse) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type ListModelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListModelsRequest) Reset() {
	*x = ListModelsRequest{}
	mi := &file_ai_v1_ai_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsRequest) ProtoMessage() {}

func (x *ListModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsRequest.ProtoReflect.Descriptor instead.
func (*ListModelsRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{4}
}

type ListModelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Models        []*AIModel             `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListModelsResponse) Reset() {
	*x = ListModelsResponse{}
	mi := &file_ai_v1_ai_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListModelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsResponse) ProtoMessage() {}

func (x *ListModelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsResponse.ProtoReflect.Descriptor instead.
func (*ListModelsResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{5}
}

func (x *ListModelsResponse) GetModels() []*AIModel {
	if x != nil {
		return x.Models
	}
	return nil
}

type AIModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Available     bool                   `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	Endpoint      string                 `protobuf:"bytes,4,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AIModel) Reset() {
	*x = AIModel{}
	mi := &file_ai_v1_ai_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIModel) ProtoMessage() {}

func (x *AIModel) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIModel.ProtoReflect.Descriptor instead.
func (*AIModel) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{6}
}

func (x *AIModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AIModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AIModel) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *AIModel) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

var File_ai_v1_ai_proto protoreflect.FileDescriptor

const file_ai_v1_ai_proto_rawDesc = "" +
	"\n" +
	"\x0eai/v1/ai.proto\x12\tapi.ai.v1\x1a\x1cgoogle/api/annotations.proto\"W\n" +
	"\vChatRequest\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x14\n" +
	"\x05model\x18\x02 \x01(\tR\x05model\x12\x18\n" +
	"\acontext\x18\x03 \x03(\tR\acontext\"j\n" +
	"\fChatResponse\x12\x1a\n" +
	"\bresponse\x18\x01 \x01(\tR\bresponse\x12\x1d\n" +
	"\n" +
	"model_used\x18\x02 \x01(\tR\tmodelUsed\x12\x1f\n" +
	"\vtokens_used\x18\x03 \x01(\x05R\n" +
	"tokensUsed\"e\n" +
	"\x0eAnalyzeRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12#\n" +
	"\ranalysis_type\x18\x02 \x01(\tR\fanalysisType\x12\x14\n" +
	"\x05model\x18\x03 \x01(\tR\x05model\"\xd0\x01\n" +
	"\x0fAnalyzeResponse\x12\x1a\n" +
	"\banalysis\x18\x01 \x01(\tR\banalysis\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x02R\n" +
	"confidence\x12D\n" +
	"\bmetadata\x18\x03 \x03(\v2(.api.ai.v1.AnalyzeResponse.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x13\n" +
	"\x11ListModelsRequest\"@\n" +
	"\x12ListModelsResponse\x12*\n" +
	"\x06models\x18\x01 \x03(\v2\x12.api.ai.v1.AIModelR\x06models\"k\n" +
	"\aAIModel\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x1c\n" +
	"\tavailable\x18\x03 \x01(\bR\tavailable\x12\x1a\n" +
	"\bendpoint\x18\x04 \x01(\tR\bendpoint2\xa7\x02\n" +
	"\tAIService\x12S\n" +
	"\x04Chat\x12\x16.api.ai.v1.ChatRequest\x1a\x17.api.ai.v1.ChatResponse\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/api/v1/ai/chat\x12_\n" +
	"\aAnalyze\x12\x19.api.ai.v1.AnalyzeRequest\x1a\x1a.api.ai.v1.AnalyzeResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/ai/analyze\x12d\n" +
	"\n" +
	"ListModels\x12\x1c.api.ai.v1.ListModelsRequest\x1a\x1d.api.ai.v1.ListModelsResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/ai/modelsB$Z\"gobackend-hvac-kratos/api/ai/v1;v1b\x06proto3"

var (
	file_ai_v1_ai_proto_rawDescOnce sync.Once
	file_ai_v1_ai_proto_rawDescData []byte
)

func file_ai_v1_ai_proto_rawDescGZIP() []byte {
	file_ai_v1_ai_proto_rawDescOnce.Do(func() {
		file_ai_v1_ai_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ai_v1_ai_proto_rawDesc), len(file_ai_v1_ai_proto_rawDesc)))
	})
	return file_ai_v1_ai_proto_rawDescData
}

var file_ai_v1_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_ai_v1_ai_proto_goTypes = []any{
	(*ChatRequest)(nil),        // 0: api.ai.v1.ChatRequest
	(*ChatResponse)(nil),       // 1: api.ai.v1.ChatResponse
	(*AnalyzeRequest)(nil),     // 2: api.ai.v1.AnalyzeRequest
	(*AnalyzeResponse)(nil),    // 3: api.ai.v1.AnalyzeResponse
	(*ListModelsRequest)(nil),  // 4: api.ai.v1.ListModelsRequest
	(*ListModelsResponse)(nil), // 5: api.ai.v1.ListModelsResponse
	(*AIModel)(nil),            // 6: api.ai.v1.AIModel
	nil,                        // 7: api.ai.v1.AnalyzeResponse.MetadataEntry
}
var file_ai_v1_ai_proto_depIdxs = []int32{
	7, // 0: api.ai.v1.AnalyzeResponse.metadata:type_name -> api.ai.v1.AnalyzeResponse.MetadataEntry
	6, // 1: api.ai.v1.ListModelsResponse.models:type_name -> api.ai.v1.AIModel
	0, // 2: api.ai.v1.AIService.Chat:input_type -> api.ai.v1.ChatRequest
	2, // 3: api.ai.v1.AIService.Analyze:input_type -> api.ai.v1.AnalyzeRequest
	4, // 4: api.ai.v1.AIService.ListModels:input_type -> api.ai.v1.ListModelsRequest
	1, // 5: api.ai.v1.AIService.Chat:output_type -> api.ai.v1.ChatResponse
	3, // 6: api.ai.v1.AIService.Analyze:output_type -> api.ai.v1.AnalyzeResponse
	5, // 7: api.ai.v1.AIService.ListModels:output_type -> api.ai.v1.ListModelsResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_ai_v1_ai_proto_init() }
func file_ai_v1_ai_proto_init() {
	if File_ai_v1_ai_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ai_v1_ai_proto_rawDesc), len(file_ai_v1_ai_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_v1_ai_proto_goTypes,
		DependencyIndexes: file_ai_v1_ai_proto_depIdxs,
		MessageInfos:      file_ai_v1_ai_proto_msgTypes,
	}.Build()
	File_ai_v1_ai_proto = out.File
	file_ai_v1_ai_proto_goTypes = nil
	file_ai_v1_ai_proto_depIdxs = nil
}
