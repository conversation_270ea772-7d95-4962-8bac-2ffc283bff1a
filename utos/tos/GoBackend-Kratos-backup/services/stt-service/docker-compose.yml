# 🎤 HVAC STT Service - Docker Compose Configuration
# Dual-Engine Speech-to-Text Service for HVAC CRM

version: '3.8'

services:
  # STT Service
  stt-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hvac-stt-service
    restart: unless-stopped
    ports:
      - "8085:8085"
    environment:
      # Service Configuration
      - STT_SERVICE_HOST=0.0.0.0
      - STT_SERVICE_PORT=8085
      - LOG_LEVEL=INFO
      
      # NVIDIA NeMo Configuration
      - NEMO_MODEL_PATH=/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo
      - CUDA_VISIBLE_DEVICES=0
      
      # ElevenLabs Configuration (set your API key)
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY:-dummy_key}
      
      # Integration URLs
      - HVAC_BACKEND_URL=http://hvac-backend:8080
      - EMAIL_SERVICE_URL=http://email-intelligence:8082
    
    volumes:
      # Configuration
      - ./config:/app/config:ro
      
      # Logs
      - ./logs:/app/logs
      
      # Temporary files
      - ./temp:/tmp/stt
      
      # Model cache (optional - for faster startup)
      - stt_models:/app/models
    
    networks:
      - hvac-network
    
    # GPU support for NVIDIA NeMo
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 15s
      start_period: 60s
      retries: 3
    
    # Dependencies
    depends_on:
      - redis
    
    # Labels
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.stt-service.rule=Host(`stt.hvac.local`)"
      - "traefik.http.services.stt-service.loadbalancer.server.port=8085"

  # Redis for caching (shared with main HVAC system)
  redis:
    image: redis:7-alpine
    container_name: hvac-stt-redis
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prometheus for metrics (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-stt-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"  # Different port to avoid conflicts
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - hvac-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-stt-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"  # Different port to avoid conflicts
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - hvac-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

# Networks
networks:
  hvac-network:
    driver: bridge
    external: true

# Volumes
volumes:
  stt_models:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
