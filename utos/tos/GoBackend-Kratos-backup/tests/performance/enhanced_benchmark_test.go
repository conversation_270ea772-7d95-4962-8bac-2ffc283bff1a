package performance

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/common"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/examples"
)

// 🚀 Enhanced Performance Benchmarks for Go 1.23 Features
// Comprehensive benchmarking of advanced patterns and optimizations

// ==========================================
// MEMORY OPTIMIZATION BENCHMARKS
// ==========================================

// BenchmarkObjectPooling tests object pooling performance
func BenchmarkObjectPooling(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	objectPools := common.NewHVACObjectPools(logger)

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			// Get email buffer from pool
			emailBuf := objectPools.EmailPool.Get()
			emailBuf.Subject = "Test Email"
			emailBuf.Body = "This is a test email body"
			emailBuf.From = "<EMAIL>"
			emailBuf.To = append(emailBuf.To, "<EMAIL>")

			// Simulate processing
			_ = len(emailBuf.Subject) + len(emailBuf.Body)

			// Return to pool
			emailBuf.Reset()
			objectPools.EmailPool.Put(emailBuf)
		}
	})
}

// BenchmarkWithoutObjectPooling tests performance without object pooling
func BenchmarkWithoutObjectPooling(b *testing.B) {
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// Create new objects each time
			emailBuf := &common.EmailBuffer{
				To:          make([]string, 0, 5),
				Attachments: make([]string, 0, 3),
				Metadata:    make(map[string]interface{}),
			}
			emailBuf.Subject = "Test Email"
			emailBuf.Body = "This is a test email body"
			emailBuf.From = "<EMAIL>"
			emailBuf.To = append(emailBuf.To, "<EMAIL>")

			// Simulate processing
			_ = len(emailBuf.Subject) + len(emailBuf.Body)
		}
	})
}

// BenchmarkBufferPooling tests buffer pooling for JSON operations
func BenchmarkBufferPooling(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	objectPools := common.NewHVACObjectPools(logger)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// Get buffer from pool
			buf := objectPools.BufferPool.Get()
			buf.WriteString(`{"customer_id": 123, "name": "John Doe", "email": "<EMAIL>"}`)

			// Simulate JSON processing
			_ = buf.Len()

			// Return to pool
			objectPools.BufferPool.Put(buf)
		}
	})
}

// ==========================================
// CONCURRENCY BENCHMARKS
// ==========================================

// BenchmarkWorkerPool tests worker pool performance
func BenchmarkWorkerPool(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	workerPools := common.NewHVACWorkerPools(logger)
	workerPools.Start()
	defer workerPools.Stop()

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			job := common.EmailProcessingJob{
				EmailID:  int64(b.N),
				Subject:  "Benchmark Email",
				Body:     "This is a benchmark email",
				From:     "<EMAIL>",
				To:       []string{"<EMAIL>"},
				Priority: "normal",
			}

			resultCh := workerPools.EmailProcessor.Submit(ctx, fmt.Sprintf("bench_%d", b.N), job)
			<-resultCh // Wait for completion
		}
	})
}

// BenchmarkDirectProcessing tests direct processing without worker pools
func BenchmarkDirectProcessing(b *testing.B) {
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			job := common.EmailProcessingJob{
				EmailID:  int64(b.N),
				Subject:  "Benchmark Email",
				Body:     "This is a benchmark email",
				From:     "<EMAIL>",
				To:       []string{"<EMAIL>"},
				Priority: "normal",
			}

			// Process directly
			_, _ = processEmailDirect(ctx, job)
		}
	})
}

// processEmailDirect simulates direct email processing
func processEmailDirect(ctx context.Context, job common.EmailProcessingJob) (common.EmailProcessingResult, error) {
	// Simulate processing time
	time.Sleep(10 * time.Millisecond)

	return common.EmailProcessingResult{
		EmailID: job.EmailID,
		Analysis: map[string]interface{}{
			"sentiment": "positive",
			"urgency":   "medium",
		},
		Actions:     []string{"create_ticket"},
		Priority:    job.Priority,
		ProcessedAt: time.Now(),
	}, nil
}

// ==========================================
// GENERIC REPOSITORY BENCHMARKS
// ==========================================

// BenchmarkGenericRepository tests generic repository performance
func BenchmarkGenericRepository(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	repo := &examples.MockEnhancedCustomerRepo{
		customers: make(map[int64]*data.EnhancedCustomer),
		log:       log.NewHelper(logger),
	}

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			customer := &data.EnhancedCustomer{
				Name:     fmt.Sprintf("Customer %d", b.N),
				Email:    fmt.Sprintf("<EMAIL>", b.N),
				Phone:    "+1234567890",
				Address:  "123 Main St",
				Priority: "normal",
			}

			// Create
			created, err := repo.Create(ctx, customer)
			if err != nil {
				b.Fatalf("Failed to create customer: %v", err)
			}

			// Read
			_, err = repo.GetByID(ctx, created.ID)
			if err != nil {
				b.Fatalf("Failed to get customer: %v", err)
			}
		}
	})
}

// BenchmarkBatchOperations tests batch operation performance
func BenchmarkBatchOperations(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	repo := &examples.MockEnhancedCustomerRepo{
		customers: make(map[int64]*data.EnhancedCustomer),
		log:       log.NewHelper(logger),
	}

	ctx := context.Background()
	batchSize := 100

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		customers := make([]*data.EnhancedCustomer, batchSize)
		for j := 0; j < batchSize; j++ {
			customers[j] = &data.EnhancedCustomer{
				Name:     fmt.Sprintf("Customer %d-%d", i, j),
				Email:    fmt.Sprintf("<EMAIL>", i, j),
				Phone:    "+1234567890",
				Address:  "123 Main St",
				Priority: "normal",
			}
		}

		_, err := repo.CreateBatch(ctx, customers)
		if err != nil {
			b.Fatalf("Failed to create batch: %v", err)
		}
	}
}

// ==========================================
// MEMORY USAGE BENCHMARKS
// ==========================================

// BenchmarkMemoryUsage tests memory usage patterns
func BenchmarkMemoryUsage(b *testing.B) {
	var m1, m2 runtime.MemStats

	// Measure memory before
	runtime.GC()
	runtime.ReadMemStats(&m1)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Simulate memory-intensive operations
		data := make([]byte, 1024) // 1KB allocation
		_ = data
	}
	b.StopTimer()

	// Measure memory after
	runtime.GC()
	runtime.ReadMemStats(&m2)

	b.ReportMetric(float64(m2.Alloc-m1.Alloc)/float64(b.N), "bytes/op")
	b.ReportMetric(float64(m2.Mallocs-m1.Mallocs)/float64(b.N), "allocs/op")
}

// ==========================================
// PERFORMANCE MONITORING BENCHMARKS
// ==========================================

// BenchmarkPerformanceMonitoring tests performance monitoring overhead
func BenchmarkPerformanceMonitoring(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	perfMonitor := common.NewPerformanceMonitor(logger, &common.MonitorConfig{
		EnableProfiling: false, // Disable profiling for benchmark
		EnableMetrics:   true,
		EnableAlerts:    false,
	})

	ctx := context.Background()
	perfMonitor.Start(ctx)
	defer perfMonitor.Stop()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// Simulate HTTP request recording
			perfMonitor.RecordHTTPRequest("/api/test", 200, 25*time.Millisecond)
		}
	})
}

// ==========================================
// COMPREHENSIVE PERFORMANCE TEST
// ==========================================

// BenchmarkComprehensivePerformance tests overall system performance
func BenchmarkComprehensivePerformance(b *testing.B) {
	logger := log.NewStdLogger(log.DefaultLogger)
	showcase := examples.NewPerformanceShowcase(logger)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if err := showcase.RunShowcase(ctx); err != nil {
			b.Fatalf("Performance showcase failed: %v", err)
		}
	}
}

// ==========================================
// LOAD TESTING
// ==========================================

// TestLoadTesting performs load testing on the enhanced system
func TestLoadTesting(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	logger := log.NewStdLogger(log.DefaultLogger)
	workerPools := common.NewHVACWorkerPools(logger)
	workerPools.Start()
	defer workerPools.Stop()

	ctx := context.Background()
	const (
		concurrency = 100
		jobsPerWorker = 100
		totalJobs = concurrency * jobsPerWorker
	)

	start := time.Now()
	var wg sync.WaitGroup

	// Launch concurrent workers
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < jobsPerWorker; j++ {
				job := common.EmailProcessingJob{
					EmailID:  int64(workerID*jobsPerWorker + j),
					Subject:  fmt.Sprintf("Load Test Email %d-%d", workerID, j),
					Body:     "This is a load test email",
					From:     "<EMAIL>",
					To:       []string{"<EMAIL>"},
					Priority: "normal",
				}

				resultCh := workerPools.EmailProcessor.Submit(ctx, fmt.Sprintf("load_%d_%d", workerID, j), job)
				select {
				case result := <-resultCh:
					if result.Error != nil {
						t.Errorf("Job failed: %v", result.Error)
					}
				case <-time.After(5 * time.Second):
					t.Errorf("Job timeout")
				}
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	// Get metrics
	metrics := workerPools.EmailProcessor.GetMetrics()

	t.Logf("Load Test Results:")
	t.Logf("  Total Jobs: %d", totalJobs)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Throughput: %.2f jobs/sec", float64(totalJobs)/duration.Seconds())
	t.Logf("  Processed: %d", metrics.JobsProcessed)
	t.Logf("  Successful: %d", metrics.JobsSuccessful)
	t.Logf("  Failed: %d", metrics.JobsFailed)
	t.Logf("  Average Latency: %v", metrics.AverageLatency)

	// Verify results
	if metrics.JobsProcessed != int64(totalJobs) {
		t.Errorf("Expected %d jobs processed, got %d", totalJobs, metrics.JobsProcessed)
	}

	if metrics.JobsFailed > 0 {
		t.Errorf("Expected 0 failed jobs, got %d", metrics.JobsFailed)
	}

	// Performance assertions
	expectedThroughput := 1000.0 // jobs/sec
	actualThroughput := float64(totalJobs) / duration.Seconds()
	if actualThroughput < expectedThroughput {
		t.Logf("Warning: Throughput %.2f jobs/sec is below expected %.2f jobs/sec", 
			actualThroughput, expectedThroughput)
	}
}

// ==========================================
// BENCHMARK UTILITIES
// ==========================================

// BenchmarkResult holds benchmark results for comparison
type BenchmarkResult struct {
	Name           string
	OpsPerSecond   float64
	AllocsPerOp    int64
	BytesPerOp     int64
	Duration       time.Duration
	MemoryUsage    uint64
}

// RunComparativeBenchmarks runs comparative benchmarks
func RunComparativeBenchmarks(b *testing.B) {
	benchmarks := []struct {
		name string
		fn   func(*testing.B)
	}{
		{"ObjectPooling", BenchmarkObjectPooling},
		{"WithoutObjectPooling", BenchmarkWithoutObjectPooling},
		{"WorkerPool", BenchmarkWorkerPool},
		{"DirectProcessing", BenchmarkDirectProcessing},
		{"GenericRepository", BenchmarkGenericRepository},
		{"BatchOperations", BenchmarkBatchOperations},
	}

	results := make([]BenchmarkResult, len(benchmarks))

	for i, bench := range benchmarks {
		result := testing.Benchmark(bench.fn)
		
		results[i] = BenchmarkResult{
			Name:         bench.name,
			OpsPerSecond: float64(result.N) / result.T.Seconds(),
			AllocsPerOp:  result.AllocsPerOp(),
			BytesPerOp:   result.AllocedBytesPerOp(),
			Duration:     result.T,
			MemoryUsage:  uint64(result.MemBytes),
		}

		b.Logf("Benchmark %s: %.2f ops/sec, %d allocs/op, %d bytes/op",
			bench.name, results[i].OpsPerSecond, results[i].AllocsPerOp, results[i].BytesPerOp)
	}

	// Compare results
	b.Logf("\n=== Performance Comparison ===")
	for i := 0; i < len(results); i += 2 {
		if i+1 < len(results) {
			improvement := (results[i].OpsPerSecond - results[i+1].OpsPerSecond) / results[i+1].OpsPerSecond * 100
			b.Logf("%s vs %s: %.2f%% improvement in throughput",
				results[i].Name, results[i+1].Name, improvement)
		}
	}
}
