#!/bin/bash

# 🤖 Gemma-3-4b-it-qat-q4_0-gguf Setup Script

set -e

echo "🚀 Setting up Gemma-3-4b-it-qat-q4_0-gguf model..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[GEMMA]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Start Ollama container
print_status "Starting Ollama container..."
docker-compose -f docker-compose.ai.yml up -d ollama

# Wait for <PERSON><PERSON><PERSON> to be ready
print_status "Waiting for <PERSON><PERSON><PERSON> to be ready..."
sleep 10

# Check if <PERSON><PERSON><PERSON> is responsive
for i in {1..30}; do
    if curl -s http://localhost:11434/api/tags > /dev/null; then
        print_success "Ollama is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Ollama failed to start after 30 attempts"
        exit 1
    fi
    echo "Waiting... ($i/30)"
    sleep 2
done

# Pull Gemma model
print_status "Pulling Gemma-3-4b-it model (this may take a while)..."
docker exec hvac-ollama ollama pull gemma:3b-instruct-q4_0

# Verify model is available
print_status "Verifying model installation..."
if docker exec hvac-ollama ollama list | grep -q "gemma:3b-instruct-q4_0"; then
    print_success "Gemma model installed successfully! 🎉"
else
    print_error "Failed to install Gemma model"
    exit 1
fi

# Test model with a simple prompt
print_status "Testing model with HVAC prompt..."
TEST_RESPONSE=$(docker exec hvac-ollama ollama run gemma:3b-instruct-q4_0 "What are the main components of an HVAC system?")

if [ $? -eq 0 ]; then
    print_success "Model test successful! 🚀"
    echo "Test response preview:"
    echo "$TEST_RESPONSE" | head -3
else
    print_warning "Model test failed, but model is installed"
fi

print_success "Gemma-3-4b-it-qat-q4_0-gguf setup completed! 🔥"
print_status "Model available at: http://localhost:11434"
print_status "Web UI available at: http://localhost:3000"