#!/bin/bash

# 🗄️ GoBackend HVAC Kratos - Complete Deployment with Bytebase
# Phase 3: Database Management Integration

set -e

echo "🚀 Starting Complete GoBackend HVAC Deployment with Bytebase..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed. Please install it and try again."
    exit 1
fi

print_status "🔧 Building GoBackend HVAC application..."
./scripts/build.sh

print_status "🛑 Stopping existing containers..."
docker-compose down --remove-orphans

print_status "🧹 Cleaning up old volumes (optional)..."
read -p "Do you want to clean up old data volumes? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose down -v
    print_warning "All data volumes have been removed!"
fi

print_status "🐳 Starting core infrastructure..."
docker-compose up -d postgres redis

print_status "⏳ Waiting for database to be ready..."
sleep 10

# Wait for PostgreSQL to be ready
print_status "🔍 Checking PostgreSQL connection..."
until docker-compose exec -T postgres pg_isready -U hvac_user -d hvac_db; do
    print_status "Waiting for PostgreSQL..."
    sleep 2
done
print_success "PostgreSQL is ready!"

print_status "🗄️ Starting Bytebase database management..."
docker-compose up -d bytebase

print_status "⏳ Waiting for Bytebase to initialize..."
sleep 15

# Check Bytebase health
print_status "🔍 Checking Bytebase health..."
for i in {1..30}; do
    if curl -f http://localhost:8092/healthz > /dev/null 2>&1; then
        print_success "Bytebase is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "Bytebase health check timeout, but continuing..."
        break
    fi
    sleep 2
done

print_status "📧 Starting BillionMail services..."
docker-compose up -d billionmail-core billionmail-postfix billionmail-dovecot

print_status "⏳ Waiting for BillionMail services to initialize..."
sleep 15

# Check BillionMail Core health
print_status "🔍 Checking BillionMail Core health..."
for i in {1..30}; do
    if curl -f http://localhost:8090/health > /dev/null 2>&1; then
        print_success "BillionMail Core is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "BillionMail Core health check timeout, but continuing..."
        break
    fi
    sleep 2
done

print_status "🚀 Starting HVAC Backend..."
docker-compose up -d hvac-backend

print_status "⏳ Waiting for HVAC Backend to start..."
sleep 10

# Check HVAC Backend health
print_status "🔍 Checking HVAC Backend health..."
for i in {1..30}; do
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        print_success "HVAC Backend is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "HVAC Backend health check timeout, but continuing..."
        break
    fi
    sleep 2
done

print_status "📊 Starting monitoring services..."
docker-compose up -d jaeger

print_status "🧪 Running integration tests..."
sleep 5

# Test HVAC API
print_status "Testing HVAC API..."
if curl -f http://localhost:8080/api/v1/customers > /dev/null 2>&1; then
    print_success "HVAC API is responding!"
else
    print_warning "HVAC API test failed, but deployment continues..."
fi

# Test Email API
print_status "Testing Email API..."
if curl -f -X POST http://localhost:8080/api/v1/emails/send \
    -H "Content-Type: application/json" \
    -d '{"from":"<EMAIL>","to":["<EMAIL>"],"subject":"Test","body":"Test email"}' > /dev/null 2>&1; then
    print_success "Email API is responding!"
else
    print_warning "Email API test failed, but deployment continues..."
fi

# Test Bytebase API
print_status "Testing Bytebase API..."
if curl -f http://localhost:8092/healthz > /dev/null 2>&1; then
    print_success "Bytebase is responding!"
else
    print_warning "Bytebase test failed, but deployment continues..."
fi

echo ""
echo "🎉 =============================================="
echo "🚀 Complete HVAC CRM Deployment Successful!"
echo "🎉 =============================================="
echo ""
echo "📋 Service URLs:"
echo "   🏠 HVAC Backend:      http://localhost:8080"
echo "   📧 BillionMail UI:    http://localhost:8090"
echo "   🗄️  Bytebase UI:       http://localhost:8092"
echo "   🔧 gRPC Server:       localhost:9000"
echo "   🛠️  MCP Server:        localhost:8081"
echo "   📊 Jaeger Tracing:    http://localhost:16686"
echo ""
echo "📧 Email Services:"
echo "   📤 SMTP Server:       localhost:587"
echo "   📥 IMAP Server:       localhost:143"
echo "   🌐 Webmail:          http://localhost:8090/roundcube"
echo ""
echo "🗄️ Database Management:"
echo "   📊 Bytebase UI:       http://localhost:8092"
echo "   🔍 Schema Browser:    http://localhost:8092/db"
echo "   📝 Migration Center:  http://localhost:8092/migration"
echo ""
echo "🧪 Test Commands:"
echo "   ./scripts/test-billionmail.sh"
echo "   ./scripts/test-bytebase.sh"
echo "   ./scripts/test.sh"
echo ""
echo "📚 Quick Start:"
echo "   # Create customer"
echo "   curl -X POST http://localhost:8080/api/v1/customers \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"phone\":\"+1234567890\"}'"
echo ""
echo "   # Send email"
echo "   curl -X POST http://localhost:8080/api/v1/emails/send \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"from\":\"<EMAIL>\",\"to\":[\"<EMAIL>\"],\"subject\":\"Service Reminder\",\"body\":\"Your HVAC needs maintenance.\"}'"
echo ""
echo "🔥 HVAC CRM + BillionMail + Bytebase = ENTERPRISE POWERHOUSE! 🚀📧🗄️"

# Show running containers
echo ""
print_status "📋 Running containers:"
docker-compose ps

echo ""
print_success "🎯 Phase 3 Complete: Enterprise-grade HVAC CRM with professional database management! 🔥"
print_status "🚀 Ready for production deployment and advanced features!"

# Display next steps
echo ""
print_status "🔮 Next Steps:"
echo "   1. 🗄️  Configure Bytebase projects and environments"
echo "   2. 📧 Setup BillionMail email campaigns"
echo "   3. 🤖 Enhance AI integration with Gemma models"
echo "   4. 📊 Setup monitoring and alerting"
echo "   5. 🔒 Configure security and backup strategies"
