#!/bin/bash

# 🚀 Complete HVAC Kratos + AI Deployment Script

set -e

echo "🚀 Deploying HVAC Kratos with AI Models..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Make scripts executable
chmod +x scripts/*.sh

# Step 1: Deploy AI Infrastructure
print_status "Step 1: Deploying AI Infrastructure..."
docker-compose -f docker-compose.ai.yml up -d

# Step 2: Deploy Main Application
print_status "Step 2: Deploying Main Application..."
docker-compose up -d

# Step 3: Setup Gemma Model
print_status "Step 3: Setting up Gemma Model..."
./scripts/setup-gemma.sh

# Step 4: Wait for services
print_status "Step 4: Waiting for all services to be ready..."
sleep 30

# Step 5: Health checks
print_status "Step 5: Performing health checks..."

# Check main application
if curl -f http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    print_success "✅ Main application is healthy"
else
    print_warning "⚠️  Main application health check failed"
fi

# Check AI services
if curl -f http://localhost:11434/api/tags > /dev/null 2>&1; then
    print_success "✅ Ollama AI service is healthy"
else
    print_warning "⚠️  Ollama AI service health check failed"
fi

# Step 6: Test AI integration
print_status "Step 6: Testing AI integration..."
./scripts/test-ai.sh

print_success "🎉 Deployment completed successfully!"
print_status "🌐 Services available at:"
print_status "  - Main API: http://localhost:8080"
print_status "  - gRPC: localhost:9000"
print_status "  - MCP Server: localhost:8081"
print_status "  - Ollama AI: http://localhost:11434"
print_status "  - AI Web UI: http://localhost:3000"
print_status "  - Jaeger UI: http://localhost:16686"
print_status ""
print_status "🤖 AI Models:"
print_status "  - Gemma-3-4b-it-qat-q4_0-gguf: Ready for HVAC assistance"
print_status "  - Bielik V3: Mock implementation (pending)"
print_status ""
print_success "🔥 Your HVAC CRM with AI is ready to rock! 🚀"