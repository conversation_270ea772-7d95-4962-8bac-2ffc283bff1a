#!/bin/bash

# 🚀 GoBackend-Kratos Analytics Dashboard Frontend Setup
# This script sets up the React-based analytics dashboard with real-time capabilities

set -e

echo "🚀 Setting up Analytics Dashboard Frontend for GoBackend-Kratos"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_header "Checking Node.js installation..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_status "Node.js version: $(node --version) ✅"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_status "npm version: $(npm --version) ✅"
}

# Create frontend directory structure
create_directory_structure() {
    print_header "Creating frontend directory structure..."
    
    # Create main frontend directory
    mkdir -p frontend/analytics-dashboard
    cd frontend/analytics-dashboard
    
    print_status "Created frontend/analytics-dashboard directory"
}

# Initialize React TypeScript project
initialize_react_project() {
    print_header "Initializing React TypeScript project..."
    
    # Create package.json
    cat > package.json << 'EOF'
{
  "name": "gobackend-kratos-analytics-dashboard",
  "version": "1.0.0",
  "description": "Real-time Analytics Dashboard for GoBackend-Kratos HVAC CRM",
  "private": true,
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/material": "^5.14.20",
    "@mui/x-charts": "^6.18.1",
    "@mui/x-data-grid": "^6.18.1",
    "@types/node": "^20.10.0",
    "@types/react": "^18.2.39",
    "@types/react-dom": "^18.2.17",
    "axios": "^1.6.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-query": "^3.39.3",
    "react-router-dom": "^6.20.1",
    "react-scripts": "5.0.1",
    "recharts": "^2.8.0",
    "socket.io-client": "^4.7.4",
    "typescript": "^5.3.2",
    "web-vitals": "^3.5.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "lint": "eslint src --ext .ts,.tsx",
    "format": "prettier --write src/**/*.{ts,tsx,css,md}"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "@types/jest": "^29.5.8",
    "eslint": "^8.54.0",
    "prettier": "^3.1.0"
  },
  "proxy": "http://localhost:8083"
}
EOF
    
    print_status "Created package.json with all required dependencies"
}

# Install dependencies
install_dependencies() {
    print_header "Installing npm dependencies..."
    
    npm install
    
    print_status "All dependencies installed successfully ✅"
}

# Create TypeScript configuration
create_typescript_config() {
    print_header "Creating TypeScript configuration..."
    
    cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "es6"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": [
    "src"
  ]
}
EOF
    
    print_status "TypeScript configuration created"
}

# Create source directory structure
create_source_structure() {
    print_header "Creating source directory structure..."
    
    # Create directories
    mkdir -p src/{components,hooks,services,types,utils,pages}
    mkdir -p src/components/{Dashboard,Charts,Widgets,Layout}
    mkdir -p public
    
    # Create index.html
    cat > public/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="GoBackend-Kratos HVAC CRM Analytics Dashboard" />
    <title>HVAC CRM Analytics Dashboard</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
EOF
    
    print_status "Source directory structure created"
}

# Create main App component
create_app_component() {
    print_header "Creating main App component..."
    
    cat > src/App.tsx << 'EOF'
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from 'react-query';

import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Analytics from './pages/Analytics';
import Reports from './pages/Reports';

// Create theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Create query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/reports" element={<Reports />} />
            </Routes>
          </Layout>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
EOF
    
    # Create index.tsx
    cat > src/index.tsx << 'EOF'
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
EOF
    
    print_status "Main App component created"
}

# Create WebSocket hook
create_websocket_hook() {
    print_header "Creating WebSocket hook for real-time data..."
    
    cat > src/hooks/useWebSocket.ts << 'EOF'
import { useState, useEffect, useRef } from 'react';
import io, { Socket } from 'socket.io-client';

interface UseWebSocketReturn {
  data: any;
  isConnected: boolean;
  error: string | null;
  sendMessage: (message: any) => void;
}

export const useWebSocket = (url: string): UseWebSocketReturn => {
  const [data, setData] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    // Connect to WebSocket
    socketRef.current = io(url);

    socketRef.current.on('connect', () => {
      setIsConnected(true);
      setError(null);
    });

    socketRef.current.on('disconnect', () => {
      setIsConnected(false);
    });

    socketRef.current.on('analytics_update', (newData: any) => {
      setData(newData);
    });

    socketRef.current.on('initial_data', (initialData: any) => {
      setData(initialData);
    });

    socketRef.current.on('error', (err: any) => {
      setError(err.message);
    });

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [url]);

  const sendMessage = (message: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('message', message);
    }
  };

  return { data, isConnected, error, sendMessage };
};
EOF
    
    print_status "WebSocket hook created"
}

# Create development scripts
create_dev_scripts() {
    print_header "Creating development scripts..."
    
    # Create start script
    cat > start-dashboard.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting GoBackend-Kratos Analytics Dashboard"
echo "Dashboard will be available at: http://localhost:3000"
echo "Backend API: http://localhost:8083"
echo "================================================"

cd frontend/analytics-dashboard
npm start
EOF
    
    chmod +x start-dashboard.sh
    
    # Create build script
    cat > build-dashboard.sh << 'EOF'
#!/bin/bash
echo "🏗️ Building GoBackend-Kratos Analytics Dashboard for production"

cd frontend/analytics-dashboard
npm run build

echo "✅ Build completed! Files are in frontend/analytics-dashboard/build/"
EOF
    
    chmod +x build-dashboard.sh
    
    print_status "Development scripts created"
}

# Main execution
main() {
    print_header "Starting GoBackend-Kratos Analytics Dashboard Setup"
    
    # Check prerequisites
    check_nodejs
    check_npm
    
    # Setup project
    create_directory_structure
    initialize_react_project
    install_dependencies
    create_typescript_config
    create_source_structure
    create_app_component
    create_websocket_hook
    
    # Go back to root directory
    cd ../..
    
    create_dev_scripts
    
    echo ""
    echo "🎉 Analytics Dashboard Frontend Setup Complete!"
    echo "=============================================="
    echo ""
    echo "📁 Project structure created in: frontend/analytics-dashboard/"
    echo "🚀 To start development server: ./start-dashboard.sh"
    echo "🏗️ To build for production: ./build-dashboard.sh"
    echo ""
    echo "📊 Dashboard will connect to GoBackend-Kratos at: http://localhost:8083"
    echo "🌐 Frontend will be available at: http://localhost:3000"
    echo ""
    echo "Next steps:"
    echo "1. Start the GoBackend-Kratos backend: docker-compose up"
    echo "2. Start the analytics dashboard: ./start-dashboard.sh"
    echo "3. Open http://localhost:3000 in your browser"
    echo ""
    print_status "Setup completed successfully! 🎯"
}

# Run main function
main "$@"
