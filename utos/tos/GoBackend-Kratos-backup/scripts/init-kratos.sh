#!/bin/bash

# 🔐 Kratos Initialization Script
# This script initializes <PERSON>y <PERSON> with proper database migrations

set -e

echo "🔐 Initializing Ory Kratos..."

# Configuration
KRATOS_CONFIG_PATH="./kratos_config/kratos.yml"
DATABASE_URL="*************************************************/hvacdb?sslmode=disable"

# Check if config file exists
if [ ! -f "$KRATOS_CONFIG_PATH" ]; then
    echo "❌ Kratos config file not found: $KRATOS_CONFIG_PATH"
    exit 1
fi

echo "✅ Kratos config file found: $KRATOS_CONFIG_PATH"

# Run Kratos database migrations
echo "🗄️ Running Kratos database migrations..."
docker run --rm \
    -v "$(pwd)/kratos_config:/etc/config/kratos:ro" \
    oryd/kratos:latest \
    migrate sql -e --yes \
    --config /etc/config/kratos/kratos.yml

echo "✅ Kratos database migrations completed!"

# Validate configuration
echo "🔍 Validating Kratos configuration..."
docker run --rm \
    -v "$(pwd)/kratos_config:/etc/config/kratos:ro" \
    oryd/kratos:latest \
    validate config \
    --config /etc/config/kratos/kratos.yml

echo "✅ Kratos configuration is valid!"

echo "🚀 Kratos initialization completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Start services: docker-compose up -d"
echo "   2. Check Kratos status: curl http://localhost:4434/health/ready"
echo "   3. Access Kratos Admin API: http://localhost:4434"
echo "   4. Access Kratos Public API: http://localhost:4433"
echo ""