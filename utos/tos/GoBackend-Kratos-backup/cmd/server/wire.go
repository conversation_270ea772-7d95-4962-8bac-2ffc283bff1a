//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/server"
	"gobackend-hvac-kratos/internal/service"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, *conf.AI, *conf.Email, *conf.MCP, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		server.ProviderSet,
		data.ProviderSet,
		biz.ProviderSet,
		service.ProviderSet,
		newApp,
	))
}