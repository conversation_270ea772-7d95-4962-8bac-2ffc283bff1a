server:
  http:
    addr: 0.0.0.0:8080
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s

data:
  database:
    driver: postgres
    source: *************************************************/hvacdb?sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

ai:
  gemma:
    endpoint: "http://************:1234"
    model_name: "gemma-3-4b-it-qat"
    max_tokens: 100000
  bielik:
    endpoint: "http://************:1234"
    model_name: "gemma-3-12b-it-qat"
    max_tokens: 100000

email:
  smtp:
    host: "localhost"
    port: 587
    username: ""
    password: ""
    from: ""
    use_tls: true
  billionmail:
    api_url: ""
    web_ui_url: ""
    admin_email: ""
    admin_password: ""
  imap:
    host: "localhost"
    port: 993
    username: ""
    password: ""
    use_tls: true
  templates:
    service_reminder: ""
    quote_follow_up: ""
    invoice_notification: ""
    appointment_confirmation: ""

mcp:
  server:
    addr: "0.0.0.0:8081"
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true

logging:
  level: "info"
  format: "json"
  output: "stdout"

tracing:
  endpoint: "http://localhost:14268/api/traces"
  sampler: 1.0

metrics:
  addr: "0.0.0.0:9090"
  path: "/metrics"
