package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// Simple test server to verify tRPC communication
// This bypasses the Kratos configuration issues

type TRPCRequest struct {
	ID     string          `json:"id"`
	Method string          `json:"method"`
	Params json.RawMessage `json:"params"`
}

type TRPCResponse struct {
	ID     string      `json:"id"`
	Result interface{} `json:"result,omitempty"`
	Error  *TRPCError  `json:"error,omitempty"`
}

type TRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type Customer struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Job struct {
	ID          int64     `json:"id"`
	CustomerID  int64     `json:"customer_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	ScheduledAt time.Time `json:"scheduled_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func main() {
	log.Println("🌟 Starting HVAC CRM Test Server with tRPC Support...")
	
	router := mux.NewRouter()
	
	// Health check endpoint
	router.HandleFunc("/api/trpc/health", handleHealth).Methods("GET")
	
	// Main tRPC endpoint
	router.HandleFunc("/api/trpc", handleTRPC).Methods("POST", "OPTIONS")
	
	// WebSocket endpoint (placeholder)
	router.HandleFunc("/api/trpc/ws", handleWebSocket).Methods("GET")
	
	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})
	
	handler := c.Handler(router)
	
	log.Println("🚀 Server starting on :8080")
	log.Println("📡 tRPC endpoint: http://localhost:8080/api/trpc")
	log.Println("🏥 Health check: http://localhost:8080/api/trpc/health")
	log.Println("🌐 WebSocket: ws://localhost:8080/api/trpc/ws")
	
	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"service":   "hvac-crm-test-server",
		"timestamp": time.Now().Format(time.RFC3339),
		"version":   "1.0.0-test",
	})
}

func handleTRPC(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendError(w, req.ID, 400, "Invalid JSON")
		return
	}

	log.Printf("📨 tRPC Request: %s", req.Method)

	result, err := routeRequest(r.Context(), req.Method, req.Params)
	
	if err != nil {
		sendError(w, req.ID, 500, err.Error())
		return
	}

	response := TRPCResponse{
		ID:     req.ID,
		Result: result,
	}

	json.NewEncoder(w).Encode(response)
}

func routeRequest(ctx context.Context, method string, params json.RawMessage) (interface{}, error) {
	switch method {
	// Customer methods
	case "customer.list":
		return handleCustomerList(ctx, params)
	case "customer.get":
		return handleCustomerGet(ctx, params)
	case "customer.create":
		return handleCustomerCreate(ctx, params)
	
	// Job methods
	case "job.list":
		return handleJobList(ctx, params)
	case "job.get":
		return handleJobGet(ctx, params)
	case "job.create":
		return handleJobCreate(ctx, params)
	
	// AI methods
	case "ai.analyzeIssue":
		return handleAIAnalyzeIssue(ctx, params)
	case "ai.chat":
		return handleAIChat(ctx, params)
	
	// System methods
	case "system.health":
		return handleSystemHealth(ctx, params)
	
	default:
		return nil, fmt.Errorf("method not found: %s", method)
	}
}

// Mock customer handlers
func handleCustomerList(ctx context.Context, params json.RawMessage) (interface{}, error) {
	// Mock data
	customers := []Customer{
		{
			ID:        1,
			Name:      "Jan Kowalski",
			Email:     "<EMAIL>",
			Phone:     "+48 123 456 789",
			Address:   "ul. Testowa 1, 00-001 Warszawa",
			CreatedAt: time.Now().Add(-24 * time.Hour),
			UpdatedAt: time.Now(),
		},
		{
			ID:        2,
			Name:      "Anna Nowak",
			Email:     "<EMAIL>",
			Phone:     "+48 987 654 321",
			Address:   "ul. Przykładowa 2, 00-002 Kraków",
			CreatedAt: time.Now().Add(-48 * time.Hour),
			UpdatedAt: time.Now(),
		},
	}

	return map[string]interface{}{
		"data":    customers,
		"total":   len(customers),
		"hasNext": false,
		"hasPrev": false,
	}, nil
}

func handleCustomerGet(ctx context.Context, params json.RawMessage) (interface{}, error) {
	var req struct {
		ID int64 `json:"id"`
	}
	
	if err := json.Unmarshal(params, &req); err != nil {
		return nil, err
	}

	// Mock customer
	customer := Customer{
		ID:        req.ID,
		Name:      "Jan Kowalski",
		Email:     "<EMAIL>",
		Phone:     "+48 123 456 789",
		Address:   "ul. Testowa 1, 00-001 Warszawa",
		CreatedAt: time.Now().Add(-24 * time.Hour),
		UpdatedAt: time.Now(),
	}

	return customer, nil
}

func handleCustomerCreate(ctx context.Context, params json.RawMessage) (interface{}, error) {
	var req Customer
	
	if err := json.Unmarshal(params, &req); err != nil {
		return nil, err
	}

	// Mock creation
	req.ID = time.Now().Unix()
	req.CreatedAt = time.Now()
	req.UpdatedAt = time.Now()

	return req, nil
}

// Mock job handlers
func handleJobList(ctx context.Context, params json.RawMessage) (interface{}, error) {
	// Mock data
	jobs := []Job{
		{
			ID:          1,
			CustomerID:  1,
			Title:       "Naprawa klimatyzacji",
			Description: "Klimatyzacja nie chłodzi",
			Status:      "pending",
			Priority:    "high",
			ScheduledAt: time.Now().Add(24 * time.Hour),
			CreatedAt:   time.Now().Add(-2 * time.Hour),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          2,
			CustomerID:  2,
			Title:       "Przegląd systemu HVAC",
			Description: "Rutynowy przegląd systemu",
			Status:      "in_progress",
			Priority:    "medium",
			ScheduledAt: time.Now().Add(48 * time.Hour),
			CreatedAt:   time.Now().Add(-4 * time.Hour),
			UpdatedAt:   time.Now(),
		},
	}

	return map[string]interface{}{
		"data":    jobs,
		"total":   len(jobs),
		"hasNext": false,
		"hasPrev": false,
	}, nil
}

func handleJobGet(ctx context.Context, params json.RawMessage) (interface{}, error) {
	var req struct {
		ID int64 `json:"id"`
	}
	
	if err := json.Unmarshal(params, &req); err != nil {
		return nil, err
	}

	// Mock job
	job := Job{
		ID:          req.ID,
		CustomerID:  1,
		Title:       "Naprawa klimatyzacji",
		Description: "Klimatyzacja nie chłodzi",
		Status:      "pending",
		Priority:    "high",
		ScheduledAt: time.Now().Add(24 * time.Hour),
		CreatedAt:   time.Now().Add(-2 * time.Hour),
		UpdatedAt:   time.Now(),
	}

	return job, nil
}

func handleJobCreate(ctx context.Context, params json.RawMessage) (interface{}, error) {
	var req Job
	
	if err := json.Unmarshal(params, &req); err != nil {
		return nil, err
	}

	// Mock creation
	req.ID = time.Now().Unix()
	req.CreatedAt = time.Now()
	req.UpdatedAt = time.Now()

	return req, nil
}

// Mock AI handlers
func handleAIAnalyzeIssue(ctx context.Context, params json.RawMessage) (interface{}, error) {
	return map[string]interface{}{
		"analysis": "Prawdopodobnie problem z kompresorem. Zalecam sprawdzenie poziomu freonu.",
		"urgency":  "high",
		"estimated_time": "2-3 godziny",
		"recommended_parts": []string{"freon R410A", "filtr powietrza"},
	}, nil
}

func handleAIChat(ctx context.Context, params json.RawMessage) (interface{}, error) {
	return map[string]interface{}{
		"response": "Witaj! Jestem asystentem AI dla systemu HVAC. Jak mogę Ci pomóc?",
		"timestamp": time.Now().Format(time.RFC3339),
	}, nil
}

// System handlers
func handleSystemHealth(ctx context.Context, params json.RawMessage) (interface{}, error) {
	return map[string]interface{}{
		"status":   "healthy",
		"services": map[string]bool{
			"database": true,
			"ai":       true,
			"email":    true,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	}, nil
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(http.StatusNotImplemented)
	w.Write([]byte("WebSocket endpoint - not implemented in test server"))
}

func sendError(w http.ResponseWriter, id string, code int, message string) {
	response := TRPCResponse{
		ID: id,
		Error: &TRPCError{
			Code:    code,
			Message: message,
		},
	}
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(response)
}
