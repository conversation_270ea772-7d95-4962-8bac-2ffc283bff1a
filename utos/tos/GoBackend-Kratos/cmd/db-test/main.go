package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"gobackend-hvac-kratos/internal/data"
)

func main() {
	fmt.Println("🚀 GoBackend-Kratos Database Test")
	fmt.Println("==================================")

	// Database connection (external server with correct credentials)
	dsn := "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable TimeZone=UTC"

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Connected to PostgreSQL database")

	// Auto-migrate email tables
	err = db.AutoMigrate(
		&data.Email{},
		&data.EmailAnalysis{},
		&data.EmailAttachment{},
	)
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	fmt.Println("✅ Database migration completed")

	// Test creating an email
	testEmail := &data.Email{
		MessageID:  "<EMAIL>",
		From:       "<EMAIL>",
		To:         data.StringArray{"<EMAIL>"},
		Subject:    "HVAC Emergency - Heating System Down",
		Body:       "Our heating system has completely stopped working. It's very cold and we need immediate assistance.",
		Priority:   "high",
		Status:     "pending",
		ReceivedAt: &[]time.Time{time.Now()}[0],
	}

	// Create email
	result := db.Create(testEmail)
	if result.Error != nil {
		log.Fatalf("Failed to create email: %v", result.Error)
	}

	fmt.Printf("✅ Email created with ID: %d\n", testEmail.ID)

	// Test creating email analysis
	testAnalysis := &data.EmailAnalysis{
		EmailID:          testEmail.ID,
		SentimentScore:   &[]float64{-0.7}[0],
		UrgencyLevel:     "high",
		DetectedIntent:   "support",
		DetectedEntities: data.JSONMap{"location": "customer_site", "issue": "heating_failure"},
		KeyPhrases:       data.StringArray{"heating system", "stopped working", "emergency", "immediate assistance"},
		LanguageCode:     "en",
		IsSpam:           false,
		ConfidenceScore:  &[]float64{0.95}[0],
		HVACRelevance:    &[]float64{0.98}[0],
		Category:         "emergency",
		Priority:         "high",
		ActionItems:      data.StringArray{"Dispatch emergency technician", "Contact customer within 30 minutes"},
	}

	// Create analysis
	result = db.Create(testAnalysis)
	if result.Error != nil {
		log.Fatalf("Failed to create email analysis: %v", result.Error)
	}

	fmt.Printf("✅ Email analysis created with ID: %d\n", testAnalysis.ID)

	// Test creating attachment
	testAttachment := &data.EmailAttachment{
		EmailID:     testEmail.ID,
		Filename:    "system_error.log",
		ContentType: "text/plain",
		Size:        1024,
		TextContent: "Error: Heating system controller not responding",
		IsProcessed: true,
	}

	// Create attachment
	result = db.Create(testAttachment)
	if result.Error != nil {
		log.Fatalf("Failed to create email attachment: %v", result.Error)
	}

	fmt.Printf("✅ Email attachment created with ID: %d\n", testAttachment.ID)

	// Test retrieval with relationships
	fmt.Println("🔍 Testing email retrieval with relationships...")

	var retrievedEmail data.Email
	err = db.Preload("Analysis").Preload("Attachments").First(&retrievedEmail, testEmail.ID).Error
	if err != nil {
		log.Fatalf("Failed to retrieve email: %v", err)
	}

	fmt.Printf("📧 Retrieved Email:\n")
	fmt.Printf("  - ID: %d\n", retrievedEmail.ID)
	fmt.Printf("  - Subject: %s\n", retrievedEmail.Subject)
	fmt.Printf("  - From: %s\n", retrievedEmail.From)
	fmt.Printf("  - To: %v\n", []string(retrievedEmail.To))
	fmt.Printf("  - Priority: %s\n", retrievedEmail.Priority)

	if retrievedEmail.Analysis != nil {
		fmt.Printf("  🔍 Analysis:\n")
		fmt.Printf("    - Sentiment Score: %.2f\n", *retrievedEmail.Analysis.SentimentScore)
		fmt.Printf("    - Urgency Level: %s\n", retrievedEmail.Analysis.UrgencyLevel)
		fmt.Printf("    - Category: %s\n", retrievedEmail.Analysis.Category)
		fmt.Printf("    - HVAC Relevance: %.2f\n", *retrievedEmail.Analysis.HVACRelevance)
		fmt.Printf("    - Key Phrases: %v\n", []string(retrievedEmail.Analysis.KeyPhrases))
		fmt.Printf("    - Action Items: %v\n", []string(retrievedEmail.Analysis.ActionItems))
	}

	if len(retrievedEmail.Attachments) > 0 {
		fmt.Printf("  📎 Attachments:\n")
		for i, attachment := range retrievedEmail.Attachments {
			fmt.Printf("    %d. %s (%s, %d bytes)\n",
				i+1, attachment.Filename, attachment.ContentType, attachment.Size)
		}
	}

	// Test database statistics
	fmt.Println("📊 Testing database statistics...")

	var emailCount int64
	db.Model(&data.Email{}).Count(&emailCount)
	fmt.Printf("📧 Total emails: %d\n", emailCount)

	var analysisCount int64
	db.Model(&data.EmailAnalysis{}).Count(&analysisCount)
	fmt.Printf("🔍 Total analyses: %d\n", analysisCount)

	var attachmentCount int64
	db.Model(&data.EmailAttachment{}).Count(&attachmentCount)
	fmt.Printf("📎 Total attachments: %d\n", attachmentCount)

	// Test search queries
	fmt.Println("🔍 Testing search queries...")

	var hvacEmails []data.Email
	err = db.Joins("LEFT JOIN email_analysis ON emails.id = email_analysis.email_id").
		Where("email_analysis.hvac_relevance > ?", 0.5).
		Find(&hvacEmails).Error
	if err != nil {
		log.Printf("Failed to search HVAC emails: %v", err)
	} else {
		fmt.Printf("🏠 HVAC relevant emails: %d\n", len(hvacEmails))
	}

	var highPriorityEmails []data.Email
	err = db.Where("priority = ?", "high").Find(&highPriorityEmails).Error
	if err != nil {
		log.Printf("Failed to search high priority emails: %v", err)
	} else {
		fmt.Printf("🚨 High priority emails: %d\n", len(highPriorityEmails))
	}

	// Test complex query with sentiment analysis
	var sentimentStats []struct {
		Sentiment string
		Count     int64
	}

	err = db.Raw(`
		SELECT
			CASE
				WHEN sentiment_score > 0.1 THEN 'positive'
				WHEN sentiment_score < -0.1 THEN 'negative'
				ELSE 'neutral'
			END as sentiment,
			COUNT(*) as count
		FROM email_analysis
		WHERE sentiment_score IS NOT NULL
		GROUP BY sentiment
	`).Scan(&sentimentStats).Error

	if err != nil {
		log.Printf("Failed to get sentiment stats: %v", err)
	} else {
		fmt.Printf("😊 Sentiment Analysis:\n")
		for _, stat := range sentimentStats {
			fmt.Printf("  - %s: %d emails\n", stat.Sentiment, stat.Count)
		}
	}

	fmt.Println("\n🎉 Database Test Completed Successfully!")
	fmt.Println("✅ All database operations working correctly")
	fmt.Println("✅ Email tables created and populated")
	fmt.Println("✅ Relationships working properly")
	fmt.Println("✅ Search queries functioning")
	fmt.Println("✅ Database is ready for email analysis storage!")
}
