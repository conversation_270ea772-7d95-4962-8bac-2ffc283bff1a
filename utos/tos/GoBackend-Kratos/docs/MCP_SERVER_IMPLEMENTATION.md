# 🚀 Enhanced MCP Server Implementation for HVAC CRM

## Overview

This document describes the comprehensive Model Context Protocol (MCP) server implementation for the HVAC CRM system. The MCP server provides a standardized interface for AI agents (Gemma/Bielik) to interact with all CRM modules and Kratos framework components.

## Architecture

### Core Components

```
GoBackend-Kratos/internal/mcp/
├── server.go              # Enhanced MCP server with middleware
├── types/
│   └── requests.go        # Request/response type definitions
├── middleware/
│   └── auth.go           # Authentication and authorization
├── metrics/
│   └── collector.go      # Performance monitoring and metrics
└── tools/                # Tool implementations
    ├── customer.go       # Customer lifecycle management
    ├── job.go           # Job and service management
    ├── email.go         # Email processing and AI analysis
    ├── analytics.go     # Analytics and reporting
    ├── workflow.go      # Workflow automation
    ├── auth.go          # Authentication tools
    └── system.go        # System operations
```

### Key Features

- **80+ MCP Tools** across 10 categories
- **Enterprise-grade reliability** with rate limiting and validation
- **Real-time WebSocket integration** for frontend updates
- **Comprehensive authentication** with Kratos framework
- **Performance monitoring** with detailed metrics
- **Circuit breaker pattern** for fault tolerance
- **Structured logging** with Kratos log framework

## Tool Categories

### 1. Customer Lifecycle Management (8 tools)
- `create_customer` - Create new HVAC customers
- `get_customer` - Retrieve customer details
- `update_customer` - Update customer information
- `list_customers` - List customers with pagination
- `delete_customer` - Delete customers (admin only)
- `search_customers` - Search customers by criteria
- `get_customer_history` - Complete interaction history
- `get_customer_analytics` - Customer behavior analytics

### 2. Job & Service Management (12 tools)
- `create_job` - Create new service jobs
- `get_job` - Retrieve job details
- `update_job` - Update job information
- `list_jobs` - List jobs with filtering
- `delete_job` - Delete jobs (admin only)
- `update_job_status` - Change job status
- `assign_technician` - Assign technicians
- `schedule_job` - Schedule job dates
- `complete_job` - Mark jobs as completed
- `get_jobs_by_customer` - Customer-specific jobs
- `get_jobs_by_status` - Status-filtered jobs
- `get_urgent_jobs` - High-priority jobs

### 3. Email Processing & AI Analysis (10 tools)
- `send_email` - Send emails with AI optimization
- `analyze_email` - AI-powered email analysis
- `process_transcription` - Audio transcription processing
- `get_email_insights` - Actionable email insights
- `extract_customer_info` - Customer data extraction
- `classify_email_intent` - Intent classification
- `generate_email_response` - AI response generation
- `analyze_sentiment` - Sentiment analysis
- `extract_entities` - Named entity recognition
- `process_hvac_keywords` - HVAC-specific analysis

### 4. Analytics & Reporting (8 tools)
- `get_dashboard_stats` - Dashboard statistics
- `generate_report` - Detailed analytics reports
- `get_performance_metrics` - System performance
- `calculate_kpis` - Key performance indicators
- `get_revenue_analytics` - Revenue analysis
- `get_customer_analytics` - Customer behavior
- `get_job_analytics` - Job efficiency metrics
- `get_technician_performance` - Technician metrics

### 5. Workflow Automation (7 tools)
- `create_workflow` - Create automation rules
- `execute_workflow` - Execute workflows
- `get_workflow_status` - Workflow execution status
- `list_workflows` - Available workflows
- `update_workflow` - Modify workflows
- `delete_workflow` - Remove workflows
- `trigger_automation` - Manual automation triggers

### 6. Authentication & Authorization (8 tools)
- `authenticate_user` - User authentication
- `create_user` - Create user accounts
- `update_user_role` - Role management
- `check_permissions` - Permission validation
- `create_session` - Session management
- `validate_session` - Session validation
- `revoke_session` - Session termination
- `get_user_permissions` - Permission listing

### 7. System Operations (10 tools)
- `health_check` - System health monitoring
- `get_system_status` - Comprehensive status
- `get_metrics` - Performance metrics
- `upload_file` - File storage operations
- `get_file` - File retrieval
- `delete_file` - File deletion
- `list_files` - File listing
- `backup_data` - Data backup
- `monitor_performance` - Performance monitoring
- `reset_metrics` - Metrics reset

## Integration Points

### 1. Kratos Framework Integration
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Session Management**: Secure session handling
- **User Management**: Complete user lifecycle

### 2. Business Logic Integration
- **Customer Usecase**: Complete customer management
- **Job Usecase**: Service job lifecycle
- **AI Usecase**: AI model orchestration
- **Email Usecase**: Email processing pipeline
- **Analytics Usecase**: Data analysis and reporting
- **Workflow Usecase**: Automation engine

### 3. External Integrations
- **MinIO Storage**: File management operations
- **Email Processing**: dolores@koldbringers.<NAME_EMAIL>
- **Transcription Services**: NVIDIA NeMo/Whisper + ElevenLabs backup
- **AI Models**: Gemma and Bielik orchestration
- **Vector Database**: Chromem integration

## Performance & Reliability

### Middleware Stack
1. **Rate Limiting**: 100 requests per minute with burst capacity
2. **Input Validation**: go-playground/validator for all requests
3. **Metrics Collection**: Comprehensive performance tracking
4. **Error Handling**: Structured error responses
5. **Logging**: Detailed operation logging

### Metrics Collected
- Total requests and success rates
- Average response times
- Tool-specific performance
- Rate limiting statistics
- Validation error tracking
- Active connection monitoring

### Circuit Breaker Pattern
- Automatic failure detection
- Graceful degradation
- Recovery mechanisms
- Configurable thresholds

## Usage Examples

### Creating a Customer
```json
{
  "tool": "create_customer",
  "args": {
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St, City, State"
  }
}
```

### Analyzing Email Content
```json
{
  "tool": "analyze_email",
  "args": {
    "content": "My AC unit is making strange noises...",
    "model": "gemma-3-4b"
  }
}
```

### Scheduling a Job
```json
{
  "tool": "create_job",
  "args": {
    "customer_id": 123,
    "title": "AC Maintenance",
    "description": "Annual maintenance check",
    "priority": "medium",
    "scheduled_at": "2024-01-15T10:00:00Z"
  }
}
```

## Configuration

### MCP Server Configuration
```yaml
mcp:
  server:
    addr: ":8080"
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true
```

### Rate Limiting Configuration
- Default: 100 requests per minute
- Burst capacity: 10 requests
- Configurable per tool category

### Authentication Configuration
- JWT token validation
- Role-based permissions
- Session timeout: 24 hours
- Refresh token support

## Deployment

### Prerequisites
- Go 1.21+
- PostgreSQL database
- MinIO storage
- Kratos identity server

### Environment Variables
```bash
MCP_SERVER_ADDR=:8080
MCP_RATE_LIMIT=100
MCP_BURST_CAPACITY=10
MCP_LOG_LEVEL=info
```

### Docker Deployment
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o mcp-server ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/mcp-server .
CMD ["./mcp-server"]
```

## Monitoring & Observability

### Health Checks
- Database connectivity
- External service availability
- Memory and CPU usage
- Active connections

### Metrics Dashboard
- Request throughput
- Error rates
- Response times
- Tool usage statistics

### Alerting
- High error rates
- Performance degradation
- Service unavailability
- Resource exhaustion

## Security Considerations

### Authentication
- JWT token validation
- Role-based access control
- Session management
- Permission checking

### Input Validation
- Comprehensive request validation
- SQL injection prevention
- XSS protection
- Rate limiting

### Data Protection
- Encrypted data transmission
- Secure file storage
- Audit logging
- Privacy compliance

## Future Enhancements

### Planned Features
1. **Advanced AI Integration**
   - Multi-model orchestration
   - Intelligent routing
   - Performance optimization

2. **Enhanced Analytics**
   - Predictive analytics
   - Machine learning insights
   - Real-time dashboards

3. **Workflow Automation**
   - Visual workflow builder
   - Advanced triggers
   - Integration marketplace

4. **Mobile Support**
   - Mobile-optimized tools
   - Offline capabilities
   - Push notifications

## Support & Documentation

### API Documentation
- OpenAPI/Swagger specifications
- Interactive API explorer
- Code examples
- SDK libraries

### Developer Resources
- Integration guides
- Best practices
- Troubleshooting guides
- Community support

---

**Note**: This MCP server implementation provides a comprehensive foundation for AI agent integration with the HVAC CRM system. All tools are designed to work seamlessly with Gemma and Bielik AI models while maintaining enterprise-grade security and performance standards.
