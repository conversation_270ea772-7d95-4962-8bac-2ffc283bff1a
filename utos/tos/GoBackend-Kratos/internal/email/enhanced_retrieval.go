package email

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/conf"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"github.com/go-kratos/kratos/v2/log"
)

// 📧 Enhanced Email Retrieval Service - Supports dual-source email processing
type EnhancedEmailRetrievalService struct {
	log            *log.Helper
	config         *conf.Email
	coordinator    *EmailCoordinator
	customerClient *client.Client
	audioClient    *client.Client
	lastSync       map[string]time.Time
}

// NewEnhancedEmailRetrievalService creates enhanced email retrieval service
func NewEnhancedEmailRetrievalService(
	config *conf.Email,
	coordinator *EmailCoordinator,
	logger log.Logger,
) *EnhancedEmailRetrievalService {
	return &EnhancedEmailRetrievalService{
		log:         log.NewHelper(logger),
		config:      config,
		coordinator: coordinator,
		lastSync:    make(map[string]time.Time),
	}
}

// ConnectToMailboxes establishes connections to both email accounts
func (s *EnhancedEmailRetrievalService) ConnectToMailboxes(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🔗 Connecting to email accounts...")

	// Connect to customer email account (<EMAIL>)
	if err := s.connectToCustomerAccount(ctx); err != nil {
		return fmt.Errorf("failed to connect to customer account: %w", err)
	}

	// Connect to audio email account (<EMAIL>)
	if err := s.connectToAudioAccount(ctx); err != nil {
		return fmt.Errorf("failed to connect to audio account: %w", err)
	}

	s.log.WithContext(ctx).Info("✅ Connected to both email accounts")
	return nil
}

// connectToCustomerAccount connects to customer email account
func (s *EnhancedEmailRetrievalService) connectToCustomerAccount(ctx context.Context) error {
	s.log.WithContext(ctx).Info("📧 Connecting to customer email account...")

	// Connect to IMAP server (using SMTP config as fallback)
	c, err := client.DialTLS(
		fmt.Sprintf("%s:%d", s.config.Smtp.Host, 993), // Default IMAP port
		nil,
	)
	if err != nil {
		return fmt.Errorf("failed to connect to customer IMAP: %w", err)
	}

	// Login
	if err := c.Login(s.config.Smtp.Username, s.config.Smtp.Password); err != nil {
		c.Close()
		return fmt.Errorf("failed to login to customer account: %w", err)
	}

	s.customerClient = c
	s.log.WithContext(ctx).Info("✅ Connected to customer email account")
	return nil
}

// connectToAudioAccount connects to audio email account
func (s *EnhancedEmailRetrievalService) connectToAudioAccount(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🎵 Connecting to audio email account...")

	// Connect to IMAP server (using hardcoded dolores account for now)
	c, err := client.DialTLS(
		fmt.Sprintf("%s:%d", s.config.Smtp.Host, 993), // Default IMAP port
		nil,
	)
	if err != nil {
		return fmt.Errorf("failed to connect to audio IMAP: %w", err)
	}

	// Login (using dolores account)
	if err := c.Login("<EMAIL>", s.config.Smtp.Password); err != nil {
		c.Close()
		return fmt.Errorf("failed to login to audio account: %w", err)
	}

	s.audioClient = c
	s.log.WithContext(ctx).Info("✅ Connected to audio email account")
	return nil
}

// RetrieveCustomerEmails retrieves and processes customer emails
func (s *EnhancedEmailRetrievalService) RetrieveCustomerEmails(ctx context.Context) error {
	if s.customerClient == nil {
		return fmt.Errorf("customer client not connected")
	}

	s.log.WithContext(ctx).Info("📧 Retrieving customer emails...")

	// Select INBOX
	mbox, err := s.customerClient.Select("INBOX", false)
	if err != nil {
		return fmt.Errorf("failed to select customer INBOX: %w", err)
	}

	// Get recent emails (last 10)
	from := uint32(1)
	to := mbox.Messages
	if mbox.Messages > 10 {
		from = mbox.Messages - 9
	}

	seqset := new(imap.SeqSet)
	seqset.AddRange(from, to)

	// Fetch emails
	messages := make(chan *imap.Message, 10)
	done := make(chan error, 1)

	go func() {
		done <- s.customerClient.Fetch(seqset, []imap.FetchItem{
			imap.FetchEnvelope,
			imap.FetchFlags,
			imap.FetchRFC822,
		}, messages)
	}()

	// Process each message
	for msg := range messages {
		if err := s.processCustomerMessage(ctx, msg); err != nil {
			s.log.WithContext(ctx).Warnf("Failed to process customer message: %v", err)
		}
	}

	if err := <-done; err != nil {
		return fmt.Errorf("failed to fetch customer messages: %w", err)
	}

	s.lastSync["customer"] = time.Now()
	s.log.WithContext(ctx).Info("✅ Customer emails retrieved")
	return nil
}

// RetrieveAudioEmails retrieves and processes audio emails
func (s *EnhancedEmailRetrievalService) RetrieveAudioEmails(ctx context.Context) error {
	if s.audioClient == nil {
		return fmt.Errorf("audio client not connected")
	}

	s.log.WithContext(ctx).Info("🎵 Retrieving audio emails...")

	// Select INBOX
	mbox, err := s.audioClient.Select("INBOX", false)
	if err != nil {
		return fmt.Errorf("failed to select audio INBOX: %w", err)
	}

	// Get recent emails (last 5)
	from := uint32(1)
	to := mbox.Messages
	if mbox.Messages > 5 {
		from = mbox.Messages - 4
	}

	seqset := new(imap.SeqSet)
	seqset.AddRange(from, to)

	// Fetch emails
	messages := make(chan *imap.Message, 5)
	done := make(chan error, 1)

	go func() {
		done <- s.audioClient.Fetch(seqset, []imap.FetchItem{
			imap.FetchEnvelope,
			imap.FetchFlags,
			imap.FetchRFC822,
		}, messages)
	}()

	// Process each message
	for msg := range messages {
		if err := s.processAudioMessage(ctx, msg); err != nil {
			s.log.WithContext(ctx).Warnf("Failed to process audio message: %v", err)
		}
	}

	if err := <-done; err != nil {
		return fmt.Errorf("failed to fetch audio messages: %w", err)
	}

	s.lastSync["audio"] = time.Now()
	s.log.WithContext(ctx).Info("✅ Audio emails retrieved")
	return nil
}

// processCustomerMessage processes individual customer email
func (s *EnhancedEmailRetrievalService) processCustomerMessage(ctx context.Context, msg *imap.Message) error {
	s.log.WithContext(ctx).Debugf("📧 Processing customer message: %s", msg.Envelope.Subject)

	// Get email body
	var emailBody []byte
	for _, r := range msg.Body {
		if r != nil {
			body, err := io.ReadAll(r)
			if err != nil {
				continue
			}
			emailBody = body
			break
		}
	}

	if len(emailBody) == 0 {
		return fmt.Errorf("no email body found")
	}

	// Process with dual source processor
	result, err := s.coordinator.ProcessEmailManually(ctx, emailBody, "<EMAIL>")
	if err != nil {
		return fmt.Errorf("failed to process customer email: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Customer email processed: %s", result.EmailID)
	return nil
}

// processAudioMessage processes individual audio email
func (s *EnhancedEmailRetrievalService) processAudioMessage(ctx context.Context, msg *imap.Message) error {
	s.log.WithContext(ctx).Debugf("🎵 Processing audio message: %s", msg.Envelope.Subject)

	// Get email body
	var emailBody []byte
	for _, r := range msg.Body {
		if r != nil {
			body, err := io.ReadAll(r)
			if err != nil {
				continue
			}
			emailBody = body
			break
		}
	}

	if len(emailBody) == 0 {
		return fmt.Errorf("no email body found")
	}

	// Check if email has attachments (look for M4A files)
	emailStr := string(emailBody)
	if !strings.Contains(strings.ToLower(emailStr), ".m4a") &&
		!strings.Contains(strings.ToLower(emailStr), "audio") &&
		!strings.Contains(strings.ToLower(emailStr), "attachment") {
		s.log.WithContext(ctx).Debug("⏭️ Skipping email without audio attachments")
		return nil
	}

	// Process with dual source processor
	result, err := s.coordinator.ProcessEmailManually(ctx, emailBody, "<EMAIL>")
	if err != nil {
		return fmt.Errorf("failed to process audio email: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Audio email processed: %s", result.EmailID)
	return nil
}

// Close closes all email connections
func (s *EnhancedEmailRetrievalService) Close() error {
	var errors []string

	if s.customerClient != nil {
		if err := s.customerClient.Close(); err != nil {
			errors = append(errors, fmt.Sprintf("customer client: %v", err))
		}
	}

	if s.audioClient != nil {
		if err := s.audioClient.Close(); err != nil {
			errors = append(errors, fmt.Sprintf("audio client: %v", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to close connections: %s", strings.Join(errors, ", "))
	}

	return nil
}

// GetLastSyncTimes returns last sync times for both accounts
func (s *EnhancedEmailRetrievalService) GetLastSyncTimes() map[string]time.Time {
	return s.lastSync
}
