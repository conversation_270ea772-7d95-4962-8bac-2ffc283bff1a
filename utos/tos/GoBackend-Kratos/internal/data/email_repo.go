package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gobackend-hvac-kratos/internal/biz"
)

// EmailRepo implements the email repository
type EmailRepo struct {
	data *Data
	log  *log.Helper
}

// NewEmailRepo creates a new email repository
func NewEmailRepo(data *Data, logger log.Logger) biz.EmailRepo {
	return &EmailRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateEmail creates a new email in the database
func (r *EmailRepo) CreateEmail(ctx context.Context, email *biz.Email) (*biz.Email, error) {
	dbEmail := r.convertEmailToDB(email)

	if err := r.data.db.WithContext(ctx).Create(dbEmail).Error; err != nil {
		return nil, fmt.Errorf("failed to create email: %w", err)
	}

	return r.convertEmailToBiz(dbEmail), nil
}

// GetEmail retrieves an email by ID
func (r *EmailRepo) GetEmail(ctx context.Context, id int64) (*biz.Email, error) {
	var email Email
	if err := r.data.db.WithContext(ctx).
		Preload("Analysis").
		Preload("Attachments").
		First(&email, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEmailNotFound
		}
		return nil, fmt.Errorf("failed to get email: %w", err)
	}

	return r.convertEmailToBiz(&email), nil
}

// GetEmailByMessageID retrieves an email by message ID
func (r *EmailRepo) GetEmailByMessageID(ctx context.Context, messageID string) (*biz.Email, error) {
	var email Email
	if err := r.data.db.WithContext(ctx).
		Preload("Analysis").
		Preload("Attachments").
		Where("message_id = ?", messageID).
		First(&email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEmailNotFound
		}
		return nil, fmt.Errorf("failed to get email by message ID: %w", err)
	}

	return r.convertEmailToBiz(&email), nil
}

// ListEmails retrieves emails with pagination
func (r *EmailRepo) ListEmails(ctx context.Context, page, pageSize int32) ([]*biz.Email, int64, error) {
	var emails []Email
	var total int64

	// Count total records
	if err := r.data.db.WithContext(ctx).Model(&Email{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count emails: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := r.data.db.WithContext(ctx).
		Preload("Analysis").
		Preload("Attachments").
		Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(pageSize)).
		Find(&emails).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list emails: %w", err)
	}

	// Convert to business entities
	bizEmails := make([]*biz.Email, len(emails))
	for i, email := range emails {
		bizEmails[i] = r.convertEmailToBiz(&email)
	}

	return bizEmails, total, nil
}

// UpdateEmail updates an existing email
func (r *EmailRepo) UpdateEmail(ctx context.Context, email *biz.Email) (*biz.Email, error) {
	dbEmail := r.convertEmailToDB(email)

	if err := r.data.db.WithContext(ctx).Save(dbEmail).Error; err != nil {
		return nil, fmt.Errorf("failed to update email: %w", err)
	}

	return r.convertEmailToBiz(dbEmail), nil
}

// DeleteEmail deletes an email by ID
func (r *EmailRepo) DeleteEmail(ctx context.Context, id int64) error {
	if err := r.data.db.WithContext(ctx).Delete(&Email{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete email: %w", err)
	}
	return nil
}

// CreateEmailAnalysis creates a new email analysis
func (r *EmailRepo) CreateEmailAnalysis(ctx context.Context, analysis *biz.EmailAnalysis) (*biz.EmailAnalysis, error) {
	dbAnalysis := r.convertAnalysisToDB(analysis)

	if err := r.data.db.WithContext(ctx).Create(dbAnalysis).Error; err != nil {
		return nil, fmt.Errorf("failed to create email analysis: %w", err)
	}

	return r.convertAnalysisToBiz(dbAnalysis), nil
}

// GetEmailAnalysis retrieves email analysis by email ID
func (r *EmailRepo) GetEmailAnalysis(ctx context.Context, emailID int64) (*biz.EmailAnalysis, error) {
	var analysis EmailAnalysis
	if err := r.data.db.WithContext(ctx).
		Where("email_id = ?", emailID).
		First(&analysis).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEmailAnalysisNotFound
		}
		return nil, fmt.Errorf("failed to get email analysis: %w", err)
	}

	return r.convertAnalysisToBiz(&analysis), nil
}

// UpdateEmailAnalysis updates an existing email analysis
func (r *EmailRepo) UpdateEmailAnalysis(ctx context.Context, analysis *biz.EmailAnalysis) (*biz.EmailAnalysis, error) {
	dbAnalysis := r.convertAnalysisToDB(analysis)

	if err := r.data.db.WithContext(ctx).Save(dbAnalysis).Error; err != nil {
		return nil, fmt.Errorf("failed to update email analysis: %w", err)
	}

	return r.convertAnalysisToBiz(dbAnalysis), nil
}

// DeleteEmailAnalysis deletes email analysis by email ID
func (r *EmailRepo) DeleteEmailAnalysis(ctx context.Context, emailID int64) error {
	if err := r.data.db.WithContext(ctx).
		Where("email_id = ?", emailID).
		Delete(&EmailAnalysis{}).Error; err != nil {
		return fmt.Errorf("failed to delete email analysis: %w", err)
	}
	return nil
}

// CreateEmailAttachment creates a new email attachment
func (r *EmailRepo) CreateEmailAttachment(ctx context.Context, attachment *biz.EmailAttachment) (*biz.EmailAttachment, error) {
	dbAttachment := r.convertAttachmentToDB(attachment)

	if err := r.data.db.WithContext(ctx).Create(dbAttachment).Error; err != nil {
		return nil, fmt.Errorf("failed to create email attachment: %w", err)
	}

	return r.convertAttachmentToBiz(dbAttachment), nil
}

// GetEmailAttachments retrieves attachments by email ID
func (r *EmailRepo) GetEmailAttachments(ctx context.Context, emailID int64) ([]*biz.EmailAttachment, error) {
	var attachments []EmailAttachment
	if err := r.data.db.WithContext(ctx).
		Where("email_id = ?", emailID).
		Find(&attachments).Error; err != nil {
		return nil, fmt.Errorf("failed to get email attachments: %w", err)
	}

	bizAttachments := make([]*biz.EmailAttachment, len(attachments))
	for i, attachment := range attachments {
		bizAttachments[i] = r.convertAttachmentToBiz(&attachment)
	}

	return bizAttachments, nil
}

// UpdateEmailAttachment updates an existing email attachment
func (r *EmailRepo) UpdateEmailAttachment(ctx context.Context, attachment *biz.EmailAttachment) (*biz.EmailAttachment, error) {
	dbAttachment := r.convertAttachmentToDB(attachment)

	if err := r.data.db.WithContext(ctx).Save(dbAttachment).Error; err != nil {
		return nil, fmt.Errorf("failed to update email attachment: %w", err)
	}

	return r.convertAttachmentToBiz(dbAttachment), nil
}

// DeleteEmailAttachment deletes an email attachment by ID
func (r *EmailRepo) DeleteEmailAttachment(ctx context.Context, id int64) error {
	if err := r.data.db.WithContext(ctx).Delete(&EmailAttachment{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete email attachment: %w", err)
	}
	return nil
}

// GetEmailAnalysisResult retrieves complete email analysis result
func (r *EmailRepo) GetEmailAnalysisResult(ctx context.Context, emailID int64) (*biz.EmailAnalysisResult, error) {
	var email Email
	if err := r.data.db.WithContext(ctx).
		Preload("Analysis").
		Preload("Attachments").
		First(&email, emailID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEmailNotFound
		}
		return nil, fmt.Errorf("failed to get email analysis result: %w", err)
	}

	result := &biz.EmailAnalysisResult{
		Email: r.convertEmailToBiz(&email),
	}

	if email.Analysis != nil {
		result.Analysis = r.convertAnalysisToBiz(email.Analysis)
	}

	if len(email.Attachments) > 0 {
		result.Attachments = make([]*biz.EmailAttachment, len(email.Attachments))
		for i, attachment := range email.Attachments {
			result.Attachments[i] = r.convertAttachmentToBiz(attachment)
		}
	}

	return result, nil
}

// StoreEmailAnalysisResult stores complete email analysis result
func (r *EmailRepo) StoreEmailAnalysisResult(ctx context.Context, result *biz.EmailAnalysisResult) error {
	return r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create email
		dbEmail := r.convertEmailToDB(result.Email)
		if err := tx.Create(dbEmail).Error; err != nil {
			return fmt.Errorf("failed to create email: %w", err)
		}

		// Update email ID in result
		result.Email.ID = dbEmail.ID

		// Create analysis if provided
		if result.Analysis != nil {
			result.Analysis.EmailID = dbEmail.ID
			dbAnalysis := r.convertAnalysisToDB(result.Analysis)
			if err := tx.Create(dbAnalysis).Error; err != nil {
				return fmt.Errorf("failed to create email analysis: %w", err)
			}
			result.Analysis.ID = dbAnalysis.ID
		}

		// Create attachments if provided
		if len(result.Attachments) > 0 {
			for _, attachment := range result.Attachments {
				attachment.EmailID = dbEmail.ID
				dbAttachment := r.convertAttachmentToDB(attachment)
				if err := tx.Create(dbAttachment).Error; err != nil {
					return fmt.Errorf("failed to create email attachment: %w", err)
				}
				attachment.ID = dbAttachment.ID
			}
		}

		return nil
	})
}

// SearchEmails searches emails with criteria
func (r *EmailRepo) SearchEmails(ctx context.Context, req *biz.EmailSearchRequest) (*biz.EmailSearchResponse, error) {
	query := r.data.db.WithContext(ctx).Model(&Email{}).
		Preload("Analysis").
		Preload("Attachments")

	// Apply filters
	if req.Query != "" {
		searchTerm := "%" + strings.ToLower(req.Query) + "%"
		query = query.Where("LOWER(subject) LIKE ? OR LOWER(body) LIKE ? OR LOWER(from_address) LIKE ?",
			searchTerm, searchTerm, searchTerm)
	}

	if req.StartDate != nil {
		query = query.Where("created_at >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("created_at <= ?", *req.EndDate)
	}

	// Join with analysis for additional filters
	if req.Category != "" || req.Sentiment != "" || req.Priority != "" ||
	   req.UrgencyLevel != "" || req.HasHVAC != nil || req.IsSpam != nil {
		query = query.Joins("LEFT JOIN email_analysis ON emails.id = email_analysis.email_id")

		if req.Category != "" {
			query = query.Where("email_analysis.category = ?", req.Category)
		}

		if req.Priority != "" {
			query = query.Where("email_analysis.priority = ?", req.Priority)
		}

		if req.UrgencyLevel != "" {
			query = query.Where("email_analysis.urgency_level = ?", req.UrgencyLevel)
		}

		if req.HasHVAC != nil {
			if *req.HasHVAC {
				query = query.Where("email_analysis.hvac_relevance > 0.5")
			} else {
				query = query.Where("email_analysis.hvac_relevance <= 0.5 OR email_analysis.hvac_relevance IS NULL")
			}
		}

		if req.IsSpam != nil {
			query = query.Where("email_analysis.is_spam = ?", *req.IsSpam)
		}

		if req.Sentiment != "" {
			switch req.Sentiment {
			case "positive":
				query = query.Where("email_analysis.sentiment_score > 0.1")
			case "negative":
				query = query.Where("email_analysis.sentiment_score < -0.1")
			case "neutral":
				query = query.Where("email_analysis.sentiment_score BETWEEN -0.1 AND 0.1")
			}
		}
	}

	// Count total results
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count search results: %w", err)
	}

	// Apply pagination and ordering
	var emails []Email
	if err := query.Order("emails.created_at DESC").
		Offset(req.Offset).
		Limit(req.Limit).
		Find(&emails).Error; err != nil {
		return nil, fmt.Errorf("failed to search emails: %w", err)
	}

	// Convert to business entities
	results := make([]*biz.EmailAnalysisResult, len(emails))
	for i, email := range emails {
		result := &biz.EmailAnalysisResult{
			Email: r.convertEmailToBiz(&email),
		}

		if email.Analysis != nil {
			result.Analysis = r.convertAnalysisToBiz(email.Analysis)
		}

		if len(email.Attachments) > 0 {
			result.Attachments = make([]*biz.EmailAttachment, len(email.Attachments))
			for j, attachment := range email.Attachments {
				result.Attachments[j] = r.convertAttachmentToBiz(attachment)
			}
		}

		results[i] = result
	}

	// Calculate pagination
	pageSize := req.Limit
	if pageSize == 0 {
		pageSize = 50
	}
	page := (req.Offset / pageSize) + 1
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &biz.EmailSearchResponse{
		Results:    results,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetDashboardStats retrieves dashboard statistics
func (r *EmailRepo) GetDashboardStats(ctx context.Context) (*biz.DashboardStats, error) {
	stats := &biz.DashboardStats{
		SentimentBreakdown: &biz.SentimentBreakdown{},
		CategoryBreakdown:  &biz.CategoryBreakdown{},
		ProcessingMetrics:  &biz.ProcessingMetrics{},
	}

	// Total emails
	if err := r.data.db.WithContext(ctx).Model(&Email{}).Count(&stats.TotalEmails).Error; err != nil {
		return nil, fmt.Errorf("failed to count total emails: %w", err)
	}

	// Today's emails
	today := time.Now().Truncate(24 * time.Hour)
	if err := r.data.db.WithContext(ctx).Model(&Email{}).
		Where("created_at >= ?", today).
		Count(&stats.TodayEmails).Error; err != nil {
		return nil, fmt.Errorf("failed to count today's emails: %w", err)
	}

	// Week's emails
	weekAgo := time.Now().AddDate(0, 0, -7)
	if err := r.data.db.WithContext(ctx).Model(&Email{}).
		Where("created_at >= ?", weekAgo).
		Count(&stats.WeekEmails).Error; err != nil {
		return nil, fmt.Errorf("failed to count week's emails: %w", err)
	}

	// Sentiment breakdown
	var sentimentStats []struct {
		Sentiment string
		Count     int
	}

	if err := r.data.db.WithContext(ctx).Raw(`
		SELECT
			CASE
				WHEN sentiment_score > 0.1 THEN 'positive'
				WHEN sentiment_score < -0.1 THEN 'negative'
				ELSE 'neutral'
			END as sentiment,
			COUNT(*) as count
		FROM email_analysis
		WHERE sentiment_score IS NOT NULL
		GROUP BY sentiment
	`).Scan(&sentimentStats).Error; err != nil {
		r.log.Warnf("Failed to get sentiment stats: %v", err)
	} else {
		for _, stat := range sentimentStats {
			switch stat.Sentiment {
			case "positive":
				stats.SentimentBreakdown.Positive = stat.Count
			case "negative":
				stats.SentimentBreakdown.Negative = stat.Count
			case "neutral":
				stats.SentimentBreakdown.Neutral = stat.Count
			}
		}
	}

	// Category breakdown
	var categoryStats []struct {
		Category string
		Count    int
	}

	if err := r.data.db.WithContext(ctx).Model(&EmailAnalysis{}).
		Select("category, COUNT(*) as count").
		Where("category != ''").
		Group("category").
		Scan(&categoryStats).Error; err != nil {
		r.log.Warnf("Failed to get category stats: %v", err)
	} else {
		for _, stat := range categoryStats {
			switch stat.Category {
			case "service":
				stats.CategoryBreakdown.Service = stat.Count
			case "sales":
				stats.CategoryBreakdown.Sales = stat.Count
			case "support":
				stats.CategoryBreakdown.Support = stat.Count
			case "emergency":
				stats.CategoryBreakdown.Emergency = stat.Count
			default:
				stats.CategoryBreakdown.Other = stat.Count
			}
		}
	}

	// Processing metrics
	var avgProcessingTime float64
	var totalProcessingTime int64
	var processedCount int64

	if err := r.data.db.WithContext(ctx).Model(&EmailAnalysis{}).
		Where("processing_time IS NOT NULL").
		Select("AVG(processing_time) as avg, SUM(processing_time) as total, COUNT(*) as count").
		Row().Scan(&avgProcessingTime, &totalProcessingTime, &processedCount); err != nil {
		r.log.Warnf("Failed to get processing metrics: %v", err)
	} else {
		stats.ProcessingMetrics.AverageProcessingTime = avgProcessingTime
		stats.ProcessingMetrics.TotalProcessingTime = totalProcessingTime
		if stats.TotalEmails > 0 {
			stats.ProcessingMetrics.SuccessRate = float64(processedCount) / float64(stats.TotalEmails) * 100
		}
	}

	// HVAC relevant count
	if err := r.data.db.WithContext(ctx).Model(&EmailAnalysis{}).
		Where("hvac_relevance > 0.5").
		Count(&stats.HVACRelevantCount).Error; err != nil {
		r.log.Warnf("Failed to count HVAC relevant emails: %v", err)
	}

	// Spam count
	if err := r.data.db.WithContext(ctx).Model(&EmailAnalysis{}).
		Where("is_spam = true").
		Count(&stats.SpamCount).Error; err != nil {
		r.log.Warnf("Failed to count spam emails: %v", err)
	}

	// Top keywords (simplified - would need full-text search for better implementation)
	var keywordStats []struct {
		Keyword string
		Count   int
	}

	if err := r.data.db.WithContext(ctx).Raw(`
		SELECT unnest(key_phrases) as keyword, COUNT(*) as count
		FROM email_analysis
		WHERE key_phrases IS NOT NULL AND array_length(key_phrases, 1) > 0
		GROUP BY keyword
		ORDER BY count DESC
		LIMIT 10
	`).Scan(&keywordStats).Error; err != nil {
		r.log.Warnf("Failed to get keyword stats: %v", err)
	} else {
		stats.TopKeywords = make([]biz.KeywordCount, len(keywordStats))
		for i, stat := range keywordStats {
			stats.TopKeywords[i] = biz.KeywordCount{
				Keyword: stat.Keyword,
				Count:   stat.Count,
			}
		}
	}

	return stats, nil
}

// Conversion methods between database models and business entities

// convertEmailToDB converts business email to database model
func (r *EmailRepo) convertEmailToDB(email *biz.Email) *Email {
	dbEmail := &Email{
		ID:         email.ID,
		MessageID:  email.MessageID,
		From:       email.From,
		To:         StringArray(email.To),
		CC:         StringArray(email.CC),
		BCC:        StringArray(email.BCC),
		Subject:    email.Subject,
		Body:       email.Body,
		HTMLBody:   email.HTMLBody,
		Headers:    email.Headers,
		Priority:   email.Priority,
		Status:     email.Status,
		SentAt:     email.SentAt,
		ReceivedAt: email.ReceivedAt,
		CreatedAt:  email.CreatedAt,
		UpdatedAt:  email.UpdatedAt,
	}
	return dbEmail
}

// convertEmailToBiz converts database email to business entity
func (r *EmailRepo) convertEmailToBiz(email *Email) *biz.Email {
	bizEmail := &biz.Email{
		ID:         email.ID,
		MessageID:  email.MessageID,
		From:       email.From,
		To:         []string(email.To),
		CC:         []string(email.CC),
		BCC:        []string(email.BCC),
		Subject:    email.Subject,
		Body:       email.Body,
		HTMLBody:   email.HTMLBody,
		Headers:    email.Headers,
		Priority:   email.Priority,
		Status:     email.Status,
		SentAt:     email.SentAt,
		ReceivedAt: email.ReceivedAt,
		CreatedAt:  email.CreatedAt,
		UpdatedAt:  email.UpdatedAt,
	}
	return bizEmail
}

// convertAnalysisToDB converts business analysis to database model
func (r *EmailRepo) convertAnalysisToDB(analysis *biz.EmailAnalysis) *EmailAnalysis {
	dbAnalysis := &EmailAnalysis{
		ID:               analysis.ID,
		EmailID:          analysis.EmailID,
		SentimentScore:   analysis.SentimentScore,
		UrgencyLevel:     analysis.UrgencyLevel,
		DetectedIntent:   analysis.DetectedIntent,
		DetectedEntities: JSONMap(analysis.DetectedEntities),
		KeyPhrases:       StringArray(analysis.KeyPhrases),
		LanguageCode:     analysis.LanguageCode,
		IsSpam:           analysis.IsSpam,
		ConfidenceScore:  analysis.ConfidenceScore,
		ProcessingTime:   analysis.ProcessingTime,
		HVACRelevance:    analysis.HVACRelevance,
		Category:         analysis.Category,
		Priority:         analysis.Priority,
		ActionItems:      StringArray(analysis.ActionItems),
		CreatedAt:        analysis.CreatedAt,
		UpdatedAt:        analysis.UpdatedAt,
	}
	return dbAnalysis
}

// convertAnalysisToBiz converts database analysis to business entity
func (r *EmailRepo) convertAnalysisToBiz(analysis *EmailAnalysis) *biz.EmailAnalysis {
	bizAnalysis := &biz.EmailAnalysis{
		ID:               analysis.ID,
		EmailID:          analysis.EmailID,
		SentimentScore:   analysis.SentimentScore,
		UrgencyLevel:     analysis.UrgencyLevel,
		DetectedIntent:   analysis.DetectedIntent,
		DetectedEntities: map[string]interface{}(analysis.DetectedEntities),
		KeyPhrases:       []string(analysis.KeyPhrases),
		LanguageCode:     analysis.LanguageCode,
		IsSpam:           analysis.IsSpam,
		ConfidenceScore:  analysis.ConfidenceScore,
		ProcessingTime:   analysis.ProcessingTime,
		HVACRelevance:    analysis.HVACRelevance,
		Category:         analysis.Category,
		Priority:         analysis.Priority,
		ActionItems:      []string(analysis.ActionItems),
		CreatedAt:        analysis.CreatedAt,
		UpdatedAt:        analysis.UpdatedAt,
	}
	return bizAnalysis
}

// convertAttachmentToDB converts business attachment to database model
func (r *EmailRepo) convertAttachmentToDB(attachment *biz.EmailAttachment) *EmailAttachment {
	dbAttachment := &EmailAttachment{
		ID:          attachment.ID,
		EmailID:     attachment.EmailID,
		Filename:    attachment.Filename,
		ContentType: attachment.ContentType,
		Size:        attachment.Size,
		TextContent: attachment.TextContent,
		IsProcessed: attachment.IsProcessed,
		CreatedAt:   attachment.CreatedAt,
		UpdatedAt:   attachment.UpdatedAt,
	}
	return dbAttachment
}

// convertAttachmentToBiz converts database attachment to business entity
func (r *EmailRepo) convertAttachmentToBiz(attachment *EmailAttachment) *biz.EmailAttachment {
	bizAttachment := &biz.EmailAttachment{
		ID:          attachment.ID,
		EmailID:     attachment.EmailID,
		Filename:    attachment.Filename,
		ContentType: attachment.ContentType,
		Size:        attachment.Size,
		TextContent: attachment.TextContent,
		IsProcessed: attachment.IsProcessed,
		CreatedAt:   attachment.CreatedAt,
		UpdatedAt:   attachment.UpdatedAt,
	}
	return bizAttachment
}
