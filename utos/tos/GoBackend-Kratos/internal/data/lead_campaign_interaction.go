package data

import (
	"context"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// LeadCampaignInteractionRepo implements the lead campaign interaction repository
type LeadCampaignInteractionRepo struct {
	data *Data
	log  *log.Helper
}

// NewLeadCampaignInteractionRepo creates a new lead campaign interaction repository
func NewLeadCampaignInteractionRepo(data *Data, logger log.Logger) biz.LeadCampaignInteractionRepo {
	return &LeadCampaignInteractionRepo{
		data: data,
		log:  log.<PERSON>Helper(logger),
	}
}

// CreateLeadCampaignInteraction creates a new lead campaign interaction in the database
func (r *LeadCampaignInteractionRepo) CreateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error) {
	dbInteraction := &LeadCampaignInteraction{ // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
		LeadID:         interaction.LeadID,
		CampaignID:     interaction.CampaignID,
		EventType:      interaction.EventType,
		EventTimestamp: interaction.EventTimestamp,
	}

	if err := r.data.db.WithContext(ctx).Create(dbInteraction).Error; err != nil {
		return nil, err
	}

	return r.convertToBiz(dbInteraction), nil
}

// GetLeadCampaignInteraction retrieves a lead campaign interaction by ID
func (r *LeadCampaignInteractionRepo) GetLeadCampaignInteraction(ctx context.Context, id int64) (*entity.LeadCampaignInteraction, error) {
	var interaction LeadCampaignInteraction // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
	if err := r.data.db.WithContext(ctx).First(&interaction, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Assuming a specific error for not found, or use a generic one
			return nil, biz.ErrCampaignAttributionFailed // Placeholder error
		}
		return nil, err
	}

	return r.convertToBiz(&interaction), nil
}

// ListLeadCampaignInteractions retrieves lead campaign interactions with pagination and filters
func (r *LeadCampaignInteractionRepo) ListLeadCampaignInteractions(ctx context.Context, page, pageSize int32, leadID, campaignID int64, eventType string) ([]*entity.LeadCampaignInteraction, int32, error) {
	var interactions []LeadCampaignInteraction // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
	var total int64
	query := r.data.db.WithContext(ctx).Model(&LeadCampaignInteraction{}) // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)

	if leadID > 0 {
		query = query.Where("lead_id = ?", leadID)
	}
	if campaignID > 0 {
		query = query.Where("campaign_id = ?", campaignID)
	}
	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := query.Offset(int(offset)).Limit(int(pageSize)).Find(&interactions).Error; err != nil {
		return nil, 0, err
	}

	// Convert to business entities
	bizInteractions := make([]*entity.LeadCampaignInteraction, len(interactions))
	for i, interaction := range interactions {
		bizInteractions[i] = r.convertToBiz(&interaction)
	}

	return bizInteractions, int32(total), nil
}

// UpdateLeadCampaignInteraction updates an existing lead campaign interaction
func (r *LeadCampaignInteractionRepo) UpdateLeadCampaignInteraction(ctx context.Context, interaction *entity.LeadCampaignInteraction) (*entity.LeadCampaignInteraction, error) {
	dbInteraction := &LeadCampaignInteraction{ // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
		ID:             interaction.ID,
		LeadID:         interaction.LeadID,
		CampaignID:     interaction.CampaignID,
		EventType:      interaction.EventType,
		EventTimestamp: interaction.EventTimestamp,
	}

	if err := r.data.db.WithContext(ctx).Save(dbInteraction).Error; err != nil {
		return nil, err
	}

	return r.convertToBiz(dbInteraction), nil
}

// DeleteLeadCampaignInteraction deletes a lead campaign interaction by ID
func (r *LeadCampaignInteractionRepo) DeleteLeadCampaignInteraction(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&LeadCampaignInteraction{}, id).Error // This LeadCampaignInteraction refers to data.LeadCampaignInteraction (GORM model)
}

// convertToBiz converts database model to business entity
func (r *LeadCampaignInteractionRepo) convertToBiz(interaction *LeadCampaignInteraction) *entity.LeadCampaignInteraction {
	return &entity.LeadCampaignInteraction{
		ID:             interaction.ID,
		LeadID:         interaction.LeadID,
		CampaignID:     interaction.CampaignID,
		EventType:      interaction.EventType,
		EventTimestamp: interaction.EventTimestamp,
		CreatedAt:      interaction.CreatedAt,
	}
}
