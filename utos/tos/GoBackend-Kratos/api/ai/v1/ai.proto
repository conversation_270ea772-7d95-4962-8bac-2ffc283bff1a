syntax = "proto3";

package api.ai.v1;

option go_package = "gobackend-hvac-kratos/api/ai/v1;v1";

import "google/api/annotations.proto";

// AI Service Definition
service AIService {
  rpc Chat(ChatRequest) returns (ChatResponse) {
    option (google.api.http) = {
      post: "/api/v1/ai/chat"
      body: "*"
    };
  }

  rpc Analyze(AnalyzeRequest) returns (AnalyzeResponse) {
    option (google.api.http) = {
      post: "/api/v1/ai/analyze"
      body: "*"
    };
  }

  rpc ListModels(ListModelsRequest) returns (ListModelsResponse) {
    option (google.api.http) = {
      get: "/api/v1/ai/models"
    };
  }
}

// Message Definitions
message ChatRequest {
  string message = 1;
  string model = 2;
  repeated string context = 3;
}

message ChatResponse {
  string response = 1;
  string model_used = 2;
  int32 tokens_used = 3;
}

message AnalyzeRequest {
  string content = 1;
  string analysis_type = 2;
  string model = 3;
}

message AnalyzeResponse {
  string analysis = 1;
  float confidence = 2;
  map<string, string> metadata = 3;
}

message ListModelsRequest {}

message ListModelsResponse {
  repeated AIModel models = 1;
}

message AIModel {
  string name = 1;
  string type = 2;
  bool available = 3;
  string endpoint = 4;
}