syntax = "proto3";

package api.hvac.v1;

option go_package = "gobackend-hvac-kratos/api/hvac/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// HVAC Service Definition
service HVACService {
  // Customer Management
  rpc CreateCustomer(CreateCustomerRequest) returns (CreateCustomerResponse) {
    option (google.api.http) = {
      post: "/api/v1/customers"
      body: "*"
    };
  }

  rpc GetCustomer(GetCustomerRequest) returns (GetCustomerResponse) {
    option (google.api.http) = {
      get: "/api/v1/customers/{id}"
    };
  }

  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse) {
    option (google.api.http) = {
      get: "/api/v1/customers"
    };
  }

  // Job Management
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse) {
    option (google.api.http) = {
      post: "/api/v1/jobs"
      body: "*"
    };
  }

  rpc GetJob(GetJobRequest) returns (GetJobResponse) {
    option (google.api.http) = {
      get: "/api/v1/jobs/{id}"
    };
  }

  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/api/v1/jobs"
    };
  }

  // Lead Management
  rpc CreateLead(CreateLeadRequest) returns (CreateLeadResponse) {
    option (google.api.http) = {
      post: "/api/v1/leads"
      body: "*"
    };
  }

  rpc GetLead(GetLeadRequest) returns (GetLeadResponse) {
    option (google.api.http) = {
      get: "/api/v1/leads/{id}"
    };
  }

  rpc ListLeads(ListLeadsRequest) returns (ListLeadsResponse) {
    option (google.api.http) = {
      get: "/api/v1/leads"
    };
  }

  rpc UpdateLead(UpdateLeadRequest) returns (UpdateLeadResponse) {
    option (google.api.http) = {
      put: "/api/v1/leads/{id}"
      body: "*"
    };
  }

  rpc DeleteLead(DeleteLeadRequest) returns (DeleteLeadResponse) {
    option (google.api.http) = {
      delete: "/api/v1/leads/{id}"
    };
  }

  // Lead Import/Export
  rpc ImportLeads(ImportLeadsRequest) returns (ImportLeadsResponse) {
    option (google.api.http) = {
      post: "/api/v1/leads/import"
      body: "*"
    };
  }

  rpc ExportLeads(ExportLeadsRequest) returns (ExportLeadsResponse) {
    option (google.api.http) = {
      get: "/api/v1/leads/export"
    };
  }

  // Advanced Lead Deduplication
  rpc DetectDuplicateLeads(DetectDuplicateLeadsRequest) returns (DetectDuplicateLeadsResponse) {
    option (google.api.http) = {
      post: "/api/v1/leads/deduplicate/detect"
      body: "*"
    };
  }

  rpc MergeLeads(MergeLeadsRequest) returns (MergeLeadsResponse) {
    option (google.api.http) = {
      post: "/api/v1/leads/deduplicate/merge"
      body: "*"
    };
  }

  // Campaign Attribution
  rpc TrackCampaign(TrackCampaignRequest) returns (TrackCampaignResponse) {
    option (google.api.http) = {
      post: "/api/v1/campaigns/track"
      body: "*"
    };
  }

  rpc GetCampaignROI(GetCampaignROIRequest) returns (GetCampaignROIResponse) {
    option (google.api.http) = {
      get: "/api/v1/campaigns/roi"
    };
  }
}

// Message Definitions
message Customer {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string address = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message Lead {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string status = 5;
  string source = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

message Job {
  int64 id = 1;
  int64 customer_id = 2;
  string title = 3;
  string description = 4;
  string status = 5;
  string priority = 6;
  google.protobuf.Timestamp scheduled_at = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}

// Request/Response Messages
message CreateCustomerRequest {
  string name = 1;
  string email = 2;
  string phone = 3;
  string address = 4;
}

message CreateCustomerResponse {
  Customer customer = 1;
}

message GetCustomerRequest {
  int64 id = 1;
}

message GetCustomerResponse {
  Customer customer = 1;
}

message ListCustomersRequest {
  int32 page = 1;
  int32 page_size = 2;
}

message ListCustomersResponse {
  repeated Customer customers = 1;
  int32 total = 2;
}

message CreateJobRequest {
  int64 customer_id = 1;
  string title = 2;
  string description = 3;
  string priority = 4;
  google.protobuf.Timestamp scheduled_at = 5;
}

message CreateJobResponse {
  Job job = 1;
}

message GetJobRequest {
  int64 id = 1;
}

message GetJobResponse {
  Job job = 1;
}

message ListJobsRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 customer_id = 3;
  string status = 4;
}

message ListJobsResponse {
  repeated Job jobs = 1;
  int32 total = 2;
}

message CreateLeadRequest {
  string name = 1;
  string email = 2;
  string phone = 3;
  string status = 4;
  string source = 5;
}

message CreateLeadResponse {
  Lead lead = 1;
}

message GetLeadRequest {
  int64 id = 1;
}

message GetLeadResponse {
  Lead lead = 1;
}

message ListLeadsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string status = 3;
  string source = 4;
}

message ListLeadsResponse {
  repeated Lead leads = 1;
  int32 total = 2;
}

message UpdateLeadRequest {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string status = 5;
  string source = 6;
}

message UpdateLeadResponse {
  Lead lead = 1;
}

message DeleteLeadRequest {
  int64 id = 1;
}

message DeleteLeadResponse {
  bool success = 1;
}

message ImportLeadsRequest {
  bytes file_content = 1; // CSV or Excel file content
  string file_type = 2; // e.g., "csv", "xlsx"
}

message ImportLeadsResponse {
  int32 imported_count = 1;
  int32 failed_count = 2;
  repeated string errors = 3;
}

message ExportLeadsRequest {
  string file_type = 1; // e.g., "csv", "xlsx"
  string status_filter = 2;
  string source_filter = 3;
}

message ExportLeadsResponse {
  bytes file_content = 1;
  string file_name = 2;
}

message DuplicateLeadGroup {
  repeated int64 lead_ids = 1;
  string reason = 2;
}

message DetectDuplicateLeadsRequest {
  // Optional: criteria for detection, e.g., "email", "phone"
  repeated string detection_criteria = 1;
}

message DetectDuplicateLeadsResponse {
  repeated DuplicateLeadGroup duplicate_groups = 1;
}

message MergeLeadsRequest {
  repeated int64 lead_ids_to_merge = 1;
  int64 primary_lead_id = 2;
}

message MergeLeadsResponse {
  Lead merged_lead = 1;
}

message Campaign {
  int64 id = 1;
  string name = 2;
  string source = 3;
  google.protobuf.Timestamp start_date = 4;
  google.protobuf.Timestamp end_date = 5;
}

message TrackCampaignRequest {
  int64 lead_id = 1;
  int64 campaign_id = 2;
  string event_type = 3; // e.g., "click", "conversion"
  google.protobuf.Timestamp event_timestamp = 4;
}

message TrackCampaignResponse {
  bool success = 1;
}

message GetCampaignROIRequest {
  int64 campaign_id = 1;
}

message GetCampaignROIResponse {
  double roi = 1;
  double total_revenue = 2;
  double total_cost = 3;
  int32 conversions = 4;
}
