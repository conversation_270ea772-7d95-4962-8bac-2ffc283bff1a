// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: workflow/v1/workflow.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create Workflow Rule
type CreateWorkflowRuleRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	RuleName          string                 `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	Description       string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TriggerType       string                 `protobuf:"bytes,3,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	TriggerConditions *structpb.Struct       `protobuf:"bytes,4,opt,name=trigger_conditions,json=triggerConditions,proto3" json:"trigger_conditions,omitempty"`
	Actions           *structpb.Struct       `protobuf:"bytes,5,opt,name=actions,proto3" json:"actions,omitempty"`
	Priority          int32                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	IsActive          bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedBy         string                 `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateWorkflowRuleRequest) Reset() {
	*x = CreateWorkflowRuleRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRuleRequest) ProtoMessage() {}

func (x *CreateWorkflowRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRuleRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWorkflowRuleRequest) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CreateWorkflowRuleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateWorkflowRuleRequest) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *CreateWorkflowRuleRequest) GetTriggerConditions() *structpb.Struct {
	if x != nil {
		return x.TriggerConditions
	}
	return nil
}

func (x *CreateWorkflowRuleRequest) GetActions() *structpb.Struct {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *CreateWorkflowRuleRequest) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *CreateWorkflowRuleRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreateWorkflowRuleRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateWorkflowRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Rule          *WorkflowRule          `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWorkflowRuleResponse) Reset() {
	*x = CreateWorkflowRuleResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRuleResponse) ProtoMessage() {}

func (x *CreateWorkflowRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRuleResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRuleResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *CreateWorkflowRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateWorkflowRuleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateWorkflowRuleResponse) GetRule() *WorkflowRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Get Workflow Rules
type GetWorkflowRulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TriggerType   string                 `protobuf:"bytes,1,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"` // Optional filter
	IsActive      *bool                  `protobuf:"varint,2,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`   // Optional filter
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowRulesRequest) Reset() {
	*x = GetWorkflowRulesRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowRulesRequest) ProtoMessage() {}

func (x *GetWorkflowRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowRulesRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowRulesRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *GetWorkflowRulesRequest) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *GetWorkflowRulesRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *GetWorkflowRulesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWorkflowRulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetWorkflowRulesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rules         []*WorkflowRule        `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowRulesResponse) Reset() {
	*x = GetWorkflowRulesResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowRulesResponse) ProtoMessage() {}

func (x *GetWorkflowRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowRulesResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowRulesResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *GetWorkflowRulesResponse) GetRules() []*WorkflowRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *GetWorkflowRulesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetWorkflowRulesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWorkflowRulesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Execute Workflows
type ExecuteWorkflowsForTriggerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TriggerType   string                 `protobuf:"bytes,1,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	EntityId      uint32                 `protobuf:"varint,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	EntityData    *structpb.Struct       `protobuf:"bytes,3,opt,name=entity_data,json=entityData,proto3" json:"entity_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteWorkflowsForTriggerRequest) Reset() {
	*x = ExecuteWorkflowsForTriggerRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteWorkflowsForTriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteWorkflowsForTriggerRequest) ProtoMessage() {}

func (x *ExecuteWorkflowsForTriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteWorkflowsForTriggerRequest.ProtoReflect.Descriptor instead.
func (*ExecuteWorkflowsForTriggerRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *ExecuteWorkflowsForTriggerRequest) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *ExecuteWorkflowsForTriggerRequest) GetEntityId() uint32 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *ExecuteWorkflowsForTriggerRequest) GetEntityData() *structpb.Struct {
	if x != nil {
		return x.EntityData
	}
	return nil
}

type ExecuteWorkflowsForTriggerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Results       []*WorkflowResult      `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteWorkflowsForTriggerResponse) Reset() {
	*x = ExecuteWorkflowsForTriggerResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteWorkflowsForTriggerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteWorkflowsForTriggerResponse) ProtoMessage() {}

func (x *ExecuteWorkflowsForTriggerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteWorkflowsForTriggerResponse.ProtoReflect.Descriptor instead.
func (*ExecuteWorkflowsForTriggerResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *ExecuteWorkflowsForTriggerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ExecuteWorkflowsForTriggerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ExecuteWorkflowsForTriggerResponse) GetResults() []*WorkflowResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// Get Workflow Executions
type GetWorkflowExecutionsRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	WorkflowRuleId *uint32                `protobuf:"varint,1,opt,name=workflow_rule_id,json=workflowRuleId,proto3,oneof" json:"workflow_rule_id,omitempty"`
	Status         string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"` // Optional filter
	StartDate      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate        *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Page           int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetWorkflowExecutionsRequest) Reset() {
	*x = GetWorkflowExecutionsRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowExecutionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowExecutionsRequest) ProtoMessage() {}

func (x *GetWorkflowExecutionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowExecutionsRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowExecutionsRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *GetWorkflowExecutionsRequest) GetWorkflowRuleId() uint32 {
	if x != nil && x.WorkflowRuleId != nil {
		return *x.WorkflowRuleId
	}
	return 0
}

func (x *GetWorkflowExecutionsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetWorkflowExecutionsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetWorkflowExecutionsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetWorkflowExecutionsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWorkflowExecutionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetWorkflowExecutionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Executions    []*WorkflowExecution   `protobuf:"bytes,1,rep,name=executions,proto3" json:"executions,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowExecutionsResponse) Reset() {
	*x = GetWorkflowExecutionsResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowExecutionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowExecutionsResponse) ProtoMessage() {}

func (x *GetWorkflowExecutionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowExecutionsResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowExecutionsResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *GetWorkflowExecutionsResponse) GetExecutions() []*WorkflowExecution {
	if x != nil {
		return x.Executions
	}
	return nil
}

func (x *GetWorkflowExecutionsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetWorkflowExecutionsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWorkflowExecutionsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get Workflow Templates
type GetWorkflowTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      string                 `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"` // Optional filter
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowTemplatesRequest) Reset() {
	*x = GetWorkflowTemplatesRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowTemplatesRequest) ProtoMessage() {}

func (x *GetWorkflowTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowTemplatesRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{8}
}

func (x *GetWorkflowTemplatesRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type GetWorkflowTemplatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Templates     []*WorkflowTemplate    `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowTemplatesResponse) Reset() {
	*x = GetWorkflowTemplatesResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowTemplatesResponse) ProtoMessage() {}

func (x *GetWorkflowTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowTemplatesResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{9}
}

func (x *GetWorkflowTemplatesResponse) GetTemplates() []*WorkflowTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

// Create from Template
type CreateWorkflowFromTemplateRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TemplateId     uint32                 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	RuleName       string                 `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	Customizations *structpb.Struct       `protobuf:"bytes,3,opt,name=customizations,proto3" json:"customizations,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateWorkflowFromTemplateRequest) Reset() {
	*x = CreateWorkflowFromTemplateRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowFromTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowFromTemplateRequest) ProtoMessage() {}

func (x *CreateWorkflowFromTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowFromTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkflowFromTemplateRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{10}
}

func (x *CreateWorkflowFromTemplateRequest) GetTemplateId() uint32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *CreateWorkflowFromTemplateRequest) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CreateWorkflowFromTemplateRequest) GetCustomizations() *structpb.Struct {
	if x != nil {
		return x.Customizations
	}
	return nil
}

type CreateWorkflowFromTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Rule          *WorkflowRule          `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWorkflowFromTemplateResponse) Reset() {
	*x = CreateWorkflowFromTemplateResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowFromTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowFromTemplateResponse) ProtoMessage() {}

func (x *CreateWorkflowFromTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowFromTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkflowFromTemplateResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{11}
}

func (x *CreateWorkflowFromTemplateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateWorkflowFromTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateWorkflowFromTemplateResponse) GetRule() *WorkflowRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Update Workflow Rule
type UpdateWorkflowRuleRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	RuleId            uint32                 `protobuf:"varint,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	RuleName          string                 `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	Description       string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	TriggerConditions *structpb.Struct       `protobuf:"bytes,4,opt,name=trigger_conditions,json=triggerConditions,proto3" json:"trigger_conditions,omitempty"`
	Actions           *structpb.Struct       `protobuf:"bytes,5,opt,name=actions,proto3" json:"actions,omitempty"`
	Priority          int32                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	IsActive          bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateWorkflowRuleRequest) Reset() {
	*x = UpdateWorkflowRuleRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWorkflowRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowRuleRequest) ProtoMessage() {}

func (x *UpdateWorkflowRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowRuleRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateWorkflowRuleRequest) GetRuleId() uint32 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *UpdateWorkflowRuleRequest) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *UpdateWorkflowRuleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateWorkflowRuleRequest) GetTriggerConditions() *structpb.Struct {
	if x != nil {
		return x.TriggerConditions
	}
	return nil
}

func (x *UpdateWorkflowRuleRequest) GetActions() *structpb.Struct {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *UpdateWorkflowRuleRequest) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *UpdateWorkflowRuleRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type UpdateWorkflowRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Rule          *WorkflowRule          `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWorkflowRuleResponse) Reset() {
	*x = UpdateWorkflowRuleResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWorkflowRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowRuleResponse) ProtoMessage() {}

func (x *UpdateWorkflowRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowRuleResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateWorkflowRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateWorkflowRuleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateWorkflowRuleResponse) GetRule() *WorkflowRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Delete Workflow Rule
type DeleteWorkflowRuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RuleId        uint32                 `protobuf:"varint,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWorkflowRuleRequest) Reset() {
	*x = DeleteWorkflowRuleRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWorkflowRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowRuleRequest) ProtoMessage() {}

func (x *DeleteWorkflowRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowRuleRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteWorkflowRuleRequest) GetRuleId() uint32 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

type DeleteWorkflowRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWorkflowRuleResponse) Reset() {
	*x = DeleteWorkflowRuleResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWorkflowRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowRuleResponse) ProtoMessage() {}

func (x *DeleteWorkflowRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowRuleResponse.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowRuleResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteWorkflowRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteWorkflowRuleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type WorkflowRule struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RuleName          string                 `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	Description       string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	TriggerType       string                 `protobuf:"bytes,4,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	TriggerConditions *structpb.Struct       `protobuf:"bytes,5,opt,name=trigger_conditions,json=triggerConditions,proto3" json:"trigger_conditions,omitempty"`
	Actions           *structpb.Struct       `protobuf:"bytes,6,opt,name=actions,proto3" json:"actions,omitempty"`
	Priority          int32                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`
	IsActive          bool                   `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	ExecutionCount    int32                  `protobuf:"varint,9,opt,name=execution_count,json=executionCount,proto3" json:"execution_count,omitempty"`
	SuccessCount      int32                  `protobuf:"varint,10,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	LastExecuted      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=last_executed,json=lastExecuted,proto3" json:"last_executed,omitempty"`
	CreatedBy         string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *WorkflowRule) Reset() {
	*x = WorkflowRule{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRule) ProtoMessage() {}

func (x *WorkflowRule) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRule.ProtoReflect.Descriptor instead.
func (*WorkflowRule) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{16}
}

func (x *WorkflowRule) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkflowRule) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *WorkflowRule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkflowRule) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *WorkflowRule) GetTriggerConditions() *structpb.Struct {
	if x != nil {
		return x.TriggerConditions
	}
	return nil
}

func (x *WorkflowRule) GetActions() *structpb.Struct {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *WorkflowRule) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *WorkflowRule) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *WorkflowRule) GetExecutionCount() int32 {
	if x != nil {
		return x.ExecutionCount
	}
	return 0
}

func (x *WorkflowRule) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *WorkflowRule) GetLastExecuted() *timestamppb.Timestamp {
	if x != nil {
		return x.LastExecuted
	}
	return nil
}

func (x *WorkflowRule) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *WorkflowRule) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WorkflowRule) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type WorkflowExecution struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WorkflowRuleId    uint32                 `protobuf:"varint,2,opt,name=workflow_rule_id,json=workflowRuleId,proto3" json:"workflow_rule_id,omitempty"`
	WorkflowRule      *WorkflowRule          `protobuf:"bytes,3,opt,name=workflow_rule,json=workflowRule,proto3" json:"workflow_rule,omitempty"`
	TriggerEntityId   uint32                 `protobuf:"varint,4,opt,name=trigger_entity_id,json=triggerEntityId,proto3" json:"trigger_entity_id,omitempty"`
	TriggerEntityType string                 `protobuf:"bytes,5,opt,name=trigger_entity_type,json=triggerEntityType,proto3" json:"trigger_entity_type,omitempty"`
	ExecutionStatus   string                 `protobuf:"bytes,6,opt,name=execution_status,json=executionStatus,proto3" json:"execution_status,omitempty"`
	StartedAt         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	CompletedAt       *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	ExecutionTime     *durationpb.Duration   `protobuf:"bytes,9,opt,name=execution_time,json=executionTime,proto3" json:"execution_time,omitempty"`
	ActionsExecuted   *structpb.Struct       `protobuf:"bytes,10,opt,name=actions_executed,json=actionsExecuted,proto3" json:"actions_executed,omitempty"`
	Results           *structpb.Struct       `protobuf:"bytes,11,opt,name=results,proto3" json:"results,omitempty"`
	ErrorMessage      string                 `protobuf:"bytes,12,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	Metadata          *structpb.Struct       `protobuf:"bytes,13,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *WorkflowExecution) Reset() {
	*x = WorkflowExecution{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowExecution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowExecution) ProtoMessage() {}

func (x *WorkflowExecution) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowExecution.ProtoReflect.Descriptor instead.
func (*WorkflowExecution) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{17}
}

func (x *WorkflowExecution) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkflowExecution) GetWorkflowRuleId() uint32 {
	if x != nil {
		return x.WorkflowRuleId
	}
	return 0
}

func (x *WorkflowExecution) GetWorkflowRule() *WorkflowRule {
	if x != nil {
		return x.WorkflowRule
	}
	return nil
}

func (x *WorkflowExecution) GetTriggerEntityId() uint32 {
	if x != nil {
		return x.TriggerEntityId
	}
	return 0
}

func (x *WorkflowExecution) GetTriggerEntityType() string {
	if x != nil {
		return x.TriggerEntityType
	}
	return ""
}

func (x *WorkflowExecution) GetExecutionStatus() string {
	if x != nil {
		return x.ExecutionStatus
	}
	return ""
}

func (x *WorkflowExecution) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *WorkflowExecution) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *WorkflowExecution) GetExecutionTime() *durationpb.Duration {
	if x != nil {
		return x.ExecutionTime
	}
	return nil
}

func (x *WorkflowExecution) GetActionsExecuted() *structpb.Struct {
	if x != nil {
		return x.ActionsExecuted
	}
	return nil
}

func (x *WorkflowExecution) GetResults() *structpb.Struct {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *WorkflowExecution) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *WorkflowExecution) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type WorkflowTemplate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateName  string                 `protobuf:"bytes,2,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`
	Category      string                 `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Template      *structpb.Struct       `protobuf:"bytes,5,opt,name=template,proto3" json:"template,omitempty"`
	IsPublic      bool                   `protobuf:"varint,6,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	UsageCount    int32                  `protobuf:"varint,7,opt,name=usage_count,json=usageCount,proto3" json:"usage_count,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowTemplate) Reset() {
	*x = WorkflowTemplate{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowTemplate) ProtoMessage() {}

func (x *WorkflowTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowTemplate.ProtoReflect.Descriptor instead.
func (*WorkflowTemplate) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{18}
}

func (x *WorkflowTemplate) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkflowTemplate) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *WorkflowTemplate) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *WorkflowTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkflowTemplate) GetTemplate() *structpb.Struct {
	if x != nil {
		return x.Template
	}
	return nil
}

func (x *WorkflowTemplate) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *WorkflowTemplate) GetUsageCount() int32 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

func (x *WorkflowTemplate) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *WorkflowTemplate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type WorkflowResult struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	Success         bool                    `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ActionsExecuted []*WorkflowActionResult `protobuf:"bytes,2,rep,name=actions_executed,json=actionsExecuted,proto3" json:"actions_executed,omitempty"`
	ErrorMessage    string                  `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ExecutionTime   *durationpb.Duration    `protobuf:"bytes,4,opt,name=execution_time,json=executionTime,proto3" json:"execution_time,omitempty"`
	Metadata        *structpb.Struct        `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *WorkflowResult) Reset() {
	*x = WorkflowResult{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowResult) ProtoMessage() {}

func (x *WorkflowResult) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowResult.ProtoReflect.Descriptor instead.
func (*WorkflowResult) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{19}
}

func (x *WorkflowResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *WorkflowResult) GetActionsExecuted() []*WorkflowActionResult {
	if x != nil {
		return x.ActionsExecuted
	}
	return nil
}

func (x *WorkflowResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *WorkflowResult) GetExecutionTime() *durationpb.Duration {
	if x != nil {
		return x.ExecutionTime
	}
	return nil
}

func (x *WorkflowResult) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type WorkflowActionResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        *WorkflowAction        `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Result        *structpb.Struct       `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	Error         string                 `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	Duration      *durationpb.Duration   `protobuf:"bytes,5,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowActionResult) Reset() {
	*x = WorkflowActionResult{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowActionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowActionResult) ProtoMessage() {}

func (x *WorkflowActionResult) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowActionResult.ProtoReflect.Descriptor instead.
func (*WorkflowActionResult) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{20}
}

func (x *WorkflowActionResult) GetAction() *WorkflowAction {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *WorkflowActionResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *WorkflowActionResult) GetResult() *structpb.Struct {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *WorkflowActionResult) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *WorkflowActionResult) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

type WorkflowAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Target        string                 `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	Parameters    *structpb.Struct       `protobuf:"bytes,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Delay         *durationpb.Duration   `protobuf:"bytes,4,opt,name=delay,proto3" json:"delay,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowAction) Reset() {
	*x = WorkflowAction{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowAction) ProtoMessage() {}

func (x *WorkflowAction) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowAction.ProtoReflect.Descriptor instead.
func (*WorkflowAction) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{21}
}

func (x *WorkflowAction) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WorkflowAction) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *WorkflowAction) GetParameters() *structpb.Struct {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *WorkflowAction) GetDelay() *durationpb.Duration {
	if x != nil {
		return x.Delay
	}
	return nil
}

// Health Check
type HealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{22}
}

type HealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Healthy       bool                   `protobuf:"varint,1,opt,name=healthy,proto3" json:"healthy,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	mi := &file_workflow_v1_workflow_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_v1_workflow_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_workflow_v1_workflow_proto_rawDescGZIP(), []int{23}
}

func (x *HealthCheckResponse) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *HealthCheckResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HealthCheckResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

var File_workflow_v1_workflow_proto protoreflect.FileDescriptor

const file_workflow_v1_workflow_proto_rawDesc = "" +
	"\n" +
	"\x1aworkflow/v1/workflow.proto\x12\x0fapi.workflow.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1egoogle/protobuf/duration.proto\"\xd0\x02\n" +
	"\x19CreateWorkflowRuleRequest\x12\x1b\n" +
	"\trule_name\x18\x01 \x01(\tR\bruleName\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12!\n" +
	"\ftrigger_type\x18\x03 \x01(\tR\vtriggerType\x12F\n" +
	"\x12trigger_conditions\x18\x04 \x01(\v2\x17.google.protobuf.StructR\x11triggerConditions\x121\n" +
	"\aactions\x18\x05 \x01(\v2\x17.google.protobuf.StructR\aactions\x12\x1a\n" +
	"\bpriority\x18\x06 \x01(\x05R\bpriority\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"created_by\x18\b \x01(\tR\tcreatedBy\"\x83\x01\n" +
	"\x1aCreateWorkflowRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x04rule\x18\x03 \x01(\v2\x1d.api.workflow.v1.WorkflowRuleR\x04rule\"\x9d\x01\n" +
	"\x17GetWorkflowRulesRequest\x12!\n" +
	"\ftrigger_type\x18\x01 \x01(\tR\vtriggerType\x12 \n" +
	"\tis_active\x18\x02 \x01(\bH\x00R\bisActive\x88\x01\x01\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSizeB\f\n" +
	"\n" +
	"_is_active\"\xa1\x01\n" +
	"\x18GetWorkflowRulesResponse\x123\n" +
	"\x05rules\x18\x01 \x03(\v2\x1d.api.workflow.v1.WorkflowRuleR\x05rules\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x9d\x01\n" +
	"!ExecuteWorkflowsForTriggerRequest\x12!\n" +
	"\ftrigger_type\x18\x01 \x01(\tR\vtriggerType\x12\x1b\n" +
	"\tentity_id\x18\x02 \x01(\rR\bentityId\x128\n" +
	"\ventity_data\x18\x03 \x01(\v2\x17.google.protobuf.StructR\n" +
	"entityData\"\x93\x01\n" +
	"\"ExecuteWorkflowsForTriggerResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x129\n" +
	"\aresults\x18\x03 \x03(\v2\x1f.api.workflow.v1.WorkflowResultR\aresults\"\x9d\x02\n" +
	"\x1cGetWorkflowExecutionsRequest\x12-\n" +
	"\x10workflow_rule_id\x18\x01 \x01(\rH\x00R\x0eworkflowRuleId\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x06 \x01(\x05R\bpageSizeB\x13\n" +
	"\x11_workflow_rule_id\"\xb5\x01\n" +
	"\x1dGetWorkflowExecutionsResponse\x12B\n" +
	"\n" +
	"executions\x18\x01 \x03(\v2\".api.workflow.v1.WorkflowExecutionR\n" +
	"executions\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"9\n" +
	"\x1bGetWorkflowTemplatesRequest\x12\x1a\n" +
	"\bcategory\x18\x01 \x01(\tR\bcategory\"_\n" +
	"\x1cGetWorkflowTemplatesResponse\x12?\n" +
	"\ttemplates\x18\x01 \x03(\v2!.api.workflow.v1.WorkflowTemplateR\ttemplates\"\xa2\x01\n" +
	"!CreateWorkflowFromTemplateRequest\x12\x1f\n" +
	"\vtemplate_id\x18\x01 \x01(\rR\n" +
	"templateId\x12\x1b\n" +
	"\trule_name\x18\x02 \x01(\tR\bruleName\x12?\n" +
	"\x0ecustomizations\x18\x03 \x01(\v2\x17.google.protobuf.StructR\x0ecustomizations\"\x8b\x01\n" +
	"\"CreateWorkflowFromTemplateResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x04rule\x18\x03 \x01(\v2\x1d.api.workflow.v1.WorkflowRuleR\x04rule\"\xa7\x02\n" +
	"\x19UpdateWorkflowRuleRequest\x12\x17\n" +
	"\arule_id\x18\x01 \x01(\rR\x06ruleId\x12\x1b\n" +
	"\trule_name\x18\x02 \x01(\tR\bruleName\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12F\n" +
	"\x12trigger_conditions\x18\x04 \x01(\v2\x17.google.protobuf.StructR\x11triggerConditions\x121\n" +
	"\aactions\x18\x05 \x01(\v2\x17.google.protobuf.StructR\aactions\x12\x1a\n" +
	"\bpriority\x18\x06 \x01(\x05R\bpriority\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\"\x83\x01\n" +
	"\x1aUpdateWorkflowRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x04rule\x18\x03 \x01(\v2\x1d.api.workflow.v1.WorkflowRuleR\x04rule\"4\n" +
	"\x19DeleteWorkflowRuleRequest\x12\x17\n" +
	"\arule_id\x18\x01 \x01(\rR\x06ruleId\"P\n" +
	"\x1aDeleteWorkflowRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xd8\x04\n" +
	"\fWorkflowRule\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1b\n" +
	"\trule_name\x18\x02 \x01(\tR\bruleName\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12!\n" +
	"\ftrigger_type\x18\x04 \x01(\tR\vtriggerType\x12F\n" +
	"\x12trigger_conditions\x18\x05 \x01(\v2\x17.google.protobuf.StructR\x11triggerConditions\x121\n" +
	"\aactions\x18\x06 \x01(\v2\x17.google.protobuf.StructR\aactions\x12\x1a\n" +
	"\bpriority\x18\a \x01(\x05R\bpriority\x12\x1b\n" +
	"\tis_active\x18\b \x01(\bR\bisActive\x12'\n" +
	"\x0fexecution_count\x18\t \x01(\x05R\x0eexecutionCount\x12#\n" +
	"\rsuccess_count\x18\n" +
	" \x01(\x05R\fsuccessCount\x12?\n" +
	"\rlast_executed\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\flastExecuted\x12\x1d\n" +
	"\n" +
	"created_by\x18\f \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xa5\x05\n" +
	"\x11WorkflowExecution\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12(\n" +
	"\x10workflow_rule_id\x18\x02 \x01(\rR\x0eworkflowRuleId\x12B\n" +
	"\rworkflow_rule\x18\x03 \x01(\v2\x1d.api.workflow.v1.WorkflowRuleR\fworkflowRule\x12*\n" +
	"\x11trigger_entity_id\x18\x04 \x01(\rR\x0ftriggerEntityId\x12.\n" +
	"\x13trigger_entity_type\x18\x05 \x01(\tR\x11triggerEntityType\x12)\n" +
	"\x10execution_status\x18\x06 \x01(\tR\x0fexecutionStatus\x129\n" +
	"\n" +
	"started_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12=\n" +
	"\fcompleted_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x12@\n" +
	"\x0eexecution_time\x18\t \x01(\v2\x19.google.protobuf.DurationR\rexecutionTime\x12B\n" +
	"\x10actions_executed\x18\n" +
	" \x01(\v2\x17.google.protobuf.StructR\x0factionsExecuted\x121\n" +
	"\aresults\x18\v \x01(\v2\x17.google.protobuf.StructR\aresults\x12#\n" +
	"\rerror_message\x18\f \x01(\tR\ferrorMessage\x123\n" +
	"\bmetadata\x18\r \x01(\v2\x17.google.protobuf.StructR\bmetadata\"\xd2\x02\n" +
	"\x10WorkflowTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12#\n" +
	"\rtemplate_name\x18\x02 \x01(\tR\ftemplateName\x12\x1a\n" +
	"\bcategory\x18\x03 \x01(\tR\bcategory\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x123\n" +
	"\btemplate\x18\x05 \x01(\v2\x17.google.protobuf.StructR\btemplate\x12\x1b\n" +
	"\tis_public\x18\x06 \x01(\bR\bisPublic\x12\x1f\n" +
	"\vusage_count\x18\a \x01(\x05R\n" +
	"usageCount\x12\x1d\n" +
	"\n" +
	"created_by\x18\b \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\x98\x02\n" +
	"\x0eWorkflowResult\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12P\n" +
	"\x10actions_executed\x18\x02 \x03(\v2%.api.workflow.v1.WorkflowActionResultR\x0factionsExecuted\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12@\n" +
	"\x0eexecution_time\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\rexecutionTime\x123\n" +
	"\bmetadata\x18\x05 \x01(\v2\x17.google.protobuf.StructR\bmetadata\"\xe7\x01\n" +
	"\x14WorkflowActionResult\x127\n" +
	"\x06action\x18\x01 \x01(\v2\x1f.api.workflow.v1.WorkflowActionR\x06action\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12/\n" +
	"\x06result\x18\x03 \x01(\v2\x17.google.protobuf.StructR\x06result\x12\x14\n" +
	"\x05error\x18\x04 \x01(\tR\x05error\x125\n" +
	"\bduration\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\bduration\"\xa6\x01\n" +
	"\x0eWorkflowAction\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x16\n" +
	"\x06target\x18\x02 \x01(\tR\x06target\x127\n" +
	"\n" +
	"parameters\x18\x03 \x01(\v2\x17.google.protobuf.StructR\n" +
	"parameters\x12/\n" +
	"\x05delay\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\x05delay\"\x14\n" +
	"\x12HealthCheckRequest\"\x83\x01\n" +
	"\x13HealthCheckResponse\x12\x18\n" +
	"\ahealthy\x18\x01 \x01(\bR\ahealthy\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp2\x89\v\n" +
	"\x0fWorkflowService\x12\x90\x01\n" +
	"\x12CreateWorkflowRule\x12*.api.workflow.v1.CreateWorkflowRuleRequest\x1a+.api.workflow.v1.CreateWorkflowRuleResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/workflow/rules\x12\x87\x01\n" +
	"\x10GetWorkflowRules\x12(.api.workflow.v1.GetWorkflowRulesRequest\x1a).api.workflow.v1.GetWorkflowRulesResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/workflow/rules\x12\xaa\x01\n" +
	"\x1aExecuteWorkflowsForTrigger\x122.api.workflow.v1.ExecuteWorkflowsForTriggerRequest\x1a3.api.workflow.v1.ExecuteWorkflowsForTriggerResponse\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/api/v1/workflow/execute\x12\x9b\x01\n" +
	"\x15GetWorkflowExecutions\x12-.api.workflow.v1.GetWorkflowExecutionsRequest\x1a..api.workflow.v1.GetWorkflowExecutionsResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v1/workflow/executions\x12\x97\x01\n" +
	"\x14GetWorkflowTemplates\x12,.api.workflow.v1.GetWorkflowTemplatesRequest\x1a-.api.workflow.v1.GetWorkflowTemplatesResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/v1/workflow/templates\x12\xc1\x01\n" +
	"\x1aCreateWorkflowFromTemplate\x122.api.workflow.v1.CreateWorkflowFromTemplateRequest\x1a3.api.workflow.v1.CreateWorkflowFromTemplateResponse\":\x82\xd3\xe4\x93\x024:\x01*\"//api/v1/workflow/templates/{template_id}/create\x12\x9a\x01\n" +
	"\x12UpdateWorkflowRule\x12*.api.workflow.v1.UpdateWorkflowRuleRequest\x1a+.api.workflow.v1.UpdateWorkflowRuleResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\x1a /api/v1/workflow/rules/{rule_id}\x12\x97\x01\n" +
	"\x12DeleteWorkflowRule\x12*.api.workflow.v1.DeleteWorkflowRuleRequest\x1a+.api.workflow.v1.DeleteWorkflowRuleResponse\"(\x82\xd3\xe4\x93\x02\"* /api/v1/workflow/rules/{rule_id}\x12y\n" +
	"\vHealthCheck\x12#.api.workflow.v1.HealthCheckRequest\x1a$.api.workflow.v1.HealthCheckResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/api/v1/workflow/healthB*Z(gobackend-hvac-kratos/api/workflow/v1;v1b\x06proto3"

var (
	file_workflow_v1_workflow_proto_rawDescOnce sync.Once
	file_workflow_v1_workflow_proto_rawDescData []byte
)

func file_workflow_v1_workflow_proto_rawDescGZIP() []byte {
	file_workflow_v1_workflow_proto_rawDescOnce.Do(func() {
		file_workflow_v1_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_workflow_v1_workflow_proto_rawDesc), len(file_workflow_v1_workflow_proto_rawDesc)))
	})
	return file_workflow_v1_workflow_proto_rawDescData
}

var file_workflow_v1_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_workflow_v1_workflow_proto_goTypes = []any{
	(*CreateWorkflowRuleRequest)(nil),          // 0: api.workflow.v1.CreateWorkflowRuleRequest
	(*CreateWorkflowRuleResponse)(nil),         // 1: api.workflow.v1.CreateWorkflowRuleResponse
	(*GetWorkflowRulesRequest)(nil),            // 2: api.workflow.v1.GetWorkflowRulesRequest
	(*GetWorkflowRulesResponse)(nil),           // 3: api.workflow.v1.GetWorkflowRulesResponse
	(*ExecuteWorkflowsForTriggerRequest)(nil),  // 4: api.workflow.v1.ExecuteWorkflowsForTriggerRequest
	(*ExecuteWorkflowsForTriggerResponse)(nil), // 5: api.workflow.v1.ExecuteWorkflowsForTriggerResponse
	(*GetWorkflowExecutionsRequest)(nil),       // 6: api.workflow.v1.GetWorkflowExecutionsRequest
	(*GetWorkflowExecutionsResponse)(nil),      // 7: api.workflow.v1.GetWorkflowExecutionsResponse
	(*GetWorkflowTemplatesRequest)(nil),        // 8: api.workflow.v1.GetWorkflowTemplatesRequest
	(*GetWorkflowTemplatesResponse)(nil),       // 9: api.workflow.v1.GetWorkflowTemplatesResponse
	(*CreateWorkflowFromTemplateRequest)(nil),  // 10: api.workflow.v1.CreateWorkflowFromTemplateRequest
	(*CreateWorkflowFromTemplateResponse)(nil), // 11: api.workflow.v1.CreateWorkflowFromTemplateResponse
	(*UpdateWorkflowRuleRequest)(nil),          // 12: api.workflow.v1.UpdateWorkflowRuleRequest
	(*UpdateWorkflowRuleResponse)(nil),         // 13: api.workflow.v1.UpdateWorkflowRuleResponse
	(*DeleteWorkflowRuleRequest)(nil),          // 14: api.workflow.v1.DeleteWorkflowRuleRequest
	(*DeleteWorkflowRuleResponse)(nil),         // 15: api.workflow.v1.DeleteWorkflowRuleResponse
	(*WorkflowRule)(nil),                       // 16: api.workflow.v1.WorkflowRule
	(*WorkflowExecution)(nil),                  // 17: api.workflow.v1.WorkflowExecution
	(*WorkflowTemplate)(nil),                   // 18: api.workflow.v1.WorkflowTemplate
	(*WorkflowResult)(nil),                     // 19: api.workflow.v1.WorkflowResult
	(*WorkflowActionResult)(nil),               // 20: api.workflow.v1.WorkflowActionResult
	(*WorkflowAction)(nil),                     // 21: api.workflow.v1.WorkflowAction
	(*HealthCheckRequest)(nil),                 // 22: api.workflow.v1.HealthCheckRequest
	(*HealthCheckResponse)(nil),                // 23: api.workflow.v1.HealthCheckResponse
	(*structpb.Struct)(nil),                    // 24: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),              // 25: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),                // 26: google.protobuf.Duration
}
var file_workflow_v1_workflow_proto_depIdxs = []int32{
	24, // 0: api.workflow.v1.CreateWorkflowRuleRequest.trigger_conditions:type_name -> google.protobuf.Struct
	24, // 1: api.workflow.v1.CreateWorkflowRuleRequest.actions:type_name -> google.protobuf.Struct
	16, // 2: api.workflow.v1.CreateWorkflowRuleResponse.rule:type_name -> api.workflow.v1.WorkflowRule
	16, // 3: api.workflow.v1.GetWorkflowRulesResponse.rules:type_name -> api.workflow.v1.WorkflowRule
	24, // 4: api.workflow.v1.ExecuteWorkflowsForTriggerRequest.entity_data:type_name -> google.protobuf.Struct
	19, // 5: api.workflow.v1.ExecuteWorkflowsForTriggerResponse.results:type_name -> api.workflow.v1.WorkflowResult
	25, // 6: api.workflow.v1.GetWorkflowExecutionsRequest.start_date:type_name -> google.protobuf.Timestamp
	25, // 7: api.workflow.v1.GetWorkflowExecutionsRequest.end_date:type_name -> google.protobuf.Timestamp
	17, // 8: api.workflow.v1.GetWorkflowExecutionsResponse.executions:type_name -> api.workflow.v1.WorkflowExecution
	18, // 9: api.workflow.v1.GetWorkflowTemplatesResponse.templates:type_name -> api.workflow.v1.WorkflowTemplate
	24, // 10: api.workflow.v1.CreateWorkflowFromTemplateRequest.customizations:type_name -> google.protobuf.Struct
	16, // 11: api.workflow.v1.CreateWorkflowFromTemplateResponse.rule:type_name -> api.workflow.v1.WorkflowRule
	24, // 12: api.workflow.v1.UpdateWorkflowRuleRequest.trigger_conditions:type_name -> google.protobuf.Struct
	24, // 13: api.workflow.v1.UpdateWorkflowRuleRequest.actions:type_name -> google.protobuf.Struct
	16, // 14: api.workflow.v1.UpdateWorkflowRuleResponse.rule:type_name -> api.workflow.v1.WorkflowRule
	24, // 15: api.workflow.v1.WorkflowRule.trigger_conditions:type_name -> google.protobuf.Struct
	24, // 16: api.workflow.v1.WorkflowRule.actions:type_name -> google.protobuf.Struct
	25, // 17: api.workflow.v1.WorkflowRule.last_executed:type_name -> google.protobuf.Timestamp
	25, // 18: api.workflow.v1.WorkflowRule.created_at:type_name -> google.protobuf.Timestamp
	25, // 19: api.workflow.v1.WorkflowRule.updated_at:type_name -> google.protobuf.Timestamp
	16, // 20: api.workflow.v1.WorkflowExecution.workflow_rule:type_name -> api.workflow.v1.WorkflowRule
	25, // 21: api.workflow.v1.WorkflowExecution.started_at:type_name -> google.protobuf.Timestamp
	25, // 22: api.workflow.v1.WorkflowExecution.completed_at:type_name -> google.protobuf.Timestamp
	26, // 23: api.workflow.v1.WorkflowExecution.execution_time:type_name -> google.protobuf.Duration
	24, // 24: api.workflow.v1.WorkflowExecution.actions_executed:type_name -> google.protobuf.Struct
	24, // 25: api.workflow.v1.WorkflowExecution.results:type_name -> google.protobuf.Struct
	24, // 26: api.workflow.v1.WorkflowExecution.metadata:type_name -> google.protobuf.Struct
	24, // 27: api.workflow.v1.WorkflowTemplate.template:type_name -> google.protobuf.Struct
	25, // 28: api.workflow.v1.WorkflowTemplate.created_at:type_name -> google.protobuf.Timestamp
	20, // 29: api.workflow.v1.WorkflowResult.actions_executed:type_name -> api.workflow.v1.WorkflowActionResult
	26, // 30: api.workflow.v1.WorkflowResult.execution_time:type_name -> google.protobuf.Duration
	24, // 31: api.workflow.v1.WorkflowResult.metadata:type_name -> google.protobuf.Struct
	21, // 32: api.workflow.v1.WorkflowActionResult.action:type_name -> api.workflow.v1.WorkflowAction
	24, // 33: api.workflow.v1.WorkflowActionResult.result:type_name -> google.protobuf.Struct
	26, // 34: api.workflow.v1.WorkflowActionResult.duration:type_name -> google.protobuf.Duration
	24, // 35: api.workflow.v1.WorkflowAction.parameters:type_name -> google.protobuf.Struct
	26, // 36: api.workflow.v1.WorkflowAction.delay:type_name -> google.protobuf.Duration
	25, // 37: api.workflow.v1.HealthCheckResponse.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 38: api.workflow.v1.WorkflowService.CreateWorkflowRule:input_type -> api.workflow.v1.CreateWorkflowRuleRequest
	2,  // 39: api.workflow.v1.WorkflowService.GetWorkflowRules:input_type -> api.workflow.v1.GetWorkflowRulesRequest
	4,  // 40: api.workflow.v1.WorkflowService.ExecuteWorkflowsForTrigger:input_type -> api.workflow.v1.ExecuteWorkflowsForTriggerRequest
	6,  // 41: api.workflow.v1.WorkflowService.GetWorkflowExecutions:input_type -> api.workflow.v1.GetWorkflowExecutionsRequest
	8,  // 42: api.workflow.v1.WorkflowService.GetWorkflowTemplates:input_type -> api.workflow.v1.GetWorkflowTemplatesRequest
	10, // 43: api.workflow.v1.WorkflowService.CreateWorkflowFromTemplate:input_type -> api.workflow.v1.CreateWorkflowFromTemplateRequest
	12, // 44: api.workflow.v1.WorkflowService.UpdateWorkflowRule:input_type -> api.workflow.v1.UpdateWorkflowRuleRequest
	14, // 45: api.workflow.v1.WorkflowService.DeleteWorkflowRule:input_type -> api.workflow.v1.DeleteWorkflowRuleRequest
	22, // 46: api.workflow.v1.WorkflowService.HealthCheck:input_type -> api.workflow.v1.HealthCheckRequest
	1,  // 47: api.workflow.v1.WorkflowService.CreateWorkflowRule:output_type -> api.workflow.v1.CreateWorkflowRuleResponse
	3,  // 48: api.workflow.v1.WorkflowService.GetWorkflowRules:output_type -> api.workflow.v1.GetWorkflowRulesResponse
	5,  // 49: api.workflow.v1.WorkflowService.ExecuteWorkflowsForTrigger:output_type -> api.workflow.v1.ExecuteWorkflowsForTriggerResponse
	7,  // 50: api.workflow.v1.WorkflowService.GetWorkflowExecutions:output_type -> api.workflow.v1.GetWorkflowExecutionsResponse
	9,  // 51: api.workflow.v1.WorkflowService.GetWorkflowTemplates:output_type -> api.workflow.v1.GetWorkflowTemplatesResponse
	11, // 52: api.workflow.v1.WorkflowService.CreateWorkflowFromTemplate:output_type -> api.workflow.v1.CreateWorkflowFromTemplateResponse
	13, // 53: api.workflow.v1.WorkflowService.UpdateWorkflowRule:output_type -> api.workflow.v1.UpdateWorkflowRuleResponse
	15, // 54: api.workflow.v1.WorkflowService.DeleteWorkflowRule:output_type -> api.workflow.v1.DeleteWorkflowRuleResponse
	23, // 55: api.workflow.v1.WorkflowService.HealthCheck:output_type -> api.workflow.v1.HealthCheckResponse
	47, // [47:56] is the sub-list for method output_type
	38, // [38:47] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_workflow_v1_workflow_proto_init() }
func file_workflow_v1_workflow_proto_init() {
	if File_workflow_v1_workflow_proto != nil {
		return
	}
	file_workflow_v1_workflow_proto_msgTypes[2].OneofWrappers = []any{}
	file_workflow_v1_workflow_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_workflow_v1_workflow_proto_rawDesc), len(file_workflow_v1_workflow_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_workflow_v1_workflow_proto_goTypes,
		DependencyIndexes: file_workflow_v1_workflow_proto_depIdxs,
		MessageInfos:      file_workflow_v1_workflow_proto_msgTypes,
	}.Build()
	File_workflow_v1_workflow_proto = out.File
	file_workflow_v1_workflow_proto_goTypes = nil
	file_workflow_v1_workflow_proto_depIdxs = nil
}
