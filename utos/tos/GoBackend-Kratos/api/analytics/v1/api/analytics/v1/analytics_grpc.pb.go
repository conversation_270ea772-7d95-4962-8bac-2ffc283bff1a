// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: api/analytics/v1/analytics.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AnalyticsServiceClient is the client API for AnalyticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnalyticsServiceClient interface {
	// Get executive dashboard data
	GetExecutiveDashboard(ctx context.Context, in *GetExecutiveDashboardRequest, opts ...grpc.CallOption) (*GetExecutiveDashboardResponse, error)
	// Get customer insights dashboard
	GetCustomerInsightsDashboard(ctx context.Context, in *GetCustomerInsightsDashboardRequest, opts ...grpc.CallOption) (*GetCustomerInsightsDashboardResponse, error)
	// Get operational dashboard
	GetOperationalDashboard(ctx context.Context, in *GetOperationalDashboardRequest, opts ...grpc.CallOption) (*GetOperationalDashboardResponse, error)
	// Get performance trends
	GetPerformanceTrends(ctx context.Context, in *GetPerformanceTrendsRequest, opts ...grpc.CallOption) (*GetPerformanceTrendsResponse, error)
	// Get KPIs
	GetKPIs(ctx context.Context, in *GetKPIsRequest, opts ...grpc.CallOption) (*GetKPIsResponse, error)
	// Update KPI
	UpdateKPI(ctx context.Context, in *UpdateKPIRequest, opts ...grpc.CallOption) (*UpdateKPIResponse, error)
	// Get real-time metrics
	GetRealTimeMetrics(ctx context.Context, in *GetRealTimeMetricsRequest, opts ...grpc.CallOption) (*GetRealTimeMetricsResponse, error)
	// Get dashboard widgets
	GetDashboardWidgets(ctx context.Context, in *GetDashboardWidgetsRequest, opts ...grpc.CallOption) (*GetDashboardWidgetsResponse, error)
	// Create dashboard widget
	CreateDashboardWidget(ctx context.Context, in *CreateDashboardWidgetRequest, opts ...grpc.CallOption) (*CreateDashboardWidgetResponse, error)
	// Health check
	HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
}

type analyticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticsServiceClient(cc grpc.ClientConnInterface) AnalyticsServiceClient {
	return &analyticsServiceClient{cc}
}

func (c *analyticsServiceClient) GetExecutiveDashboard(ctx context.Context, in *GetExecutiveDashboardRequest, opts ...grpc.CallOption) (*GetExecutiveDashboardResponse, error) {
	out := new(GetExecutiveDashboardResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetExecutiveDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetCustomerInsightsDashboard(ctx context.Context, in *GetCustomerInsightsDashboardRequest, opts ...grpc.CallOption) (*GetCustomerInsightsDashboardResponse, error) {
	out := new(GetCustomerInsightsDashboardResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetCustomerInsightsDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetOperationalDashboard(ctx context.Context, in *GetOperationalDashboardRequest, opts ...grpc.CallOption) (*GetOperationalDashboardResponse, error) {
	out := new(GetOperationalDashboardResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetOperationalDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetPerformanceTrends(ctx context.Context, in *GetPerformanceTrendsRequest, opts ...grpc.CallOption) (*GetPerformanceTrendsResponse, error) {
	out := new(GetPerformanceTrendsResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetPerformanceTrends", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetKPIs(ctx context.Context, in *GetKPIsRequest, opts ...grpc.CallOption) (*GetKPIsResponse, error) {
	out := new(GetKPIsResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetKPIs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) UpdateKPI(ctx context.Context, in *UpdateKPIRequest, opts ...grpc.CallOption) (*UpdateKPIResponse, error) {
	out := new(UpdateKPIResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/UpdateKPI", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetRealTimeMetrics(ctx context.Context, in *GetRealTimeMetricsRequest, opts ...grpc.CallOption) (*GetRealTimeMetricsResponse, error) {
	out := new(GetRealTimeMetricsResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetRealTimeMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetDashboardWidgets(ctx context.Context, in *GetDashboardWidgetsRequest, opts ...grpc.CallOption) (*GetDashboardWidgetsResponse, error) {
	out := new(GetDashboardWidgetsResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/GetDashboardWidgets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) CreateDashboardWidget(ctx context.Context, in *CreateDashboardWidgetRequest, opts ...grpc.CallOption) (*CreateDashboardWidgetResponse, error) {
	out := new(CreateDashboardWidgetResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/CreateDashboardWidget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, "/api.analytics.v1.AnalyticsService/HealthCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticsServiceServer is the server API for AnalyticsService service.
// All implementations should embed UnimplementedAnalyticsServiceServer
// for forward compatibility
type AnalyticsServiceServer interface {
	// Get executive dashboard data
	GetExecutiveDashboard(context.Context, *GetExecutiveDashboardRequest) (*GetExecutiveDashboardResponse, error)
	// Get customer insights dashboard
	GetCustomerInsightsDashboard(context.Context, *GetCustomerInsightsDashboardRequest) (*GetCustomerInsightsDashboardResponse, error)
	// Get operational dashboard
	GetOperationalDashboard(context.Context, *GetOperationalDashboardRequest) (*GetOperationalDashboardResponse, error)
	// Get performance trends
	GetPerformanceTrends(context.Context, *GetPerformanceTrendsRequest) (*GetPerformanceTrendsResponse, error)
	// Get KPIs
	GetKPIs(context.Context, *GetKPIsRequest) (*GetKPIsResponse, error)
	// Update KPI
	UpdateKPI(context.Context, *UpdateKPIRequest) (*UpdateKPIResponse, error)
	// Get real-time metrics
	GetRealTimeMetrics(context.Context, *GetRealTimeMetricsRequest) (*GetRealTimeMetricsResponse, error)
	// Get dashboard widgets
	GetDashboardWidgets(context.Context, *GetDashboardWidgetsRequest) (*GetDashboardWidgetsResponse, error)
	// Create dashboard widget
	CreateDashboardWidget(context.Context, *CreateDashboardWidgetRequest) (*CreateDashboardWidgetResponse, error)
	// Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
}

// UnimplementedAnalyticsServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAnalyticsServiceServer struct {
}

func (UnimplementedAnalyticsServiceServer) GetExecutiveDashboard(context.Context, *GetExecutiveDashboardRequest) (*GetExecutiveDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExecutiveDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetCustomerInsightsDashboard(context.Context, *GetCustomerInsightsDashboardRequest) (*GetCustomerInsightsDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerInsightsDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetOperationalDashboard(context.Context, *GetOperationalDashboardRequest) (*GetOperationalDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperationalDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetPerformanceTrends(context.Context, *GetPerformanceTrendsRequest) (*GetPerformanceTrendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerformanceTrends not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetKPIs(context.Context, *GetKPIsRequest) (*GetKPIsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKPIs not implemented")
}
func (UnimplementedAnalyticsServiceServer) UpdateKPI(context.Context, *UpdateKPIRequest) (*UpdateKPIResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKPI not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetRealTimeMetrics(context.Context, *GetRealTimeMetricsRequest) (*GetRealTimeMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRealTimeMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetDashboardWidgets(context.Context, *GetDashboardWidgetsRequest) (*GetDashboardWidgetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboardWidgets not implemented")
}
func (UnimplementedAnalyticsServiceServer) CreateDashboardWidget(context.Context, *CreateDashboardWidgetRequest) (*CreateDashboardWidgetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDashboardWidget not implemented")
}
func (UnimplementedAnalyticsServiceServer) HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}

// UnsafeAnalyticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticsServiceServer will
// result in compilation errors.
type UnsafeAnalyticsServiceServer interface {
	mustEmbedUnimplementedAnalyticsServiceServer()
}

func RegisterAnalyticsServiceServer(s grpc.ServiceRegistrar, srv AnalyticsServiceServer) {
	s.RegisterService(&AnalyticsService_ServiceDesc, srv)
}

func _AnalyticsService_GetExecutiveDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExecutiveDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetExecutiveDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetExecutiveDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetExecutiveDashboard(ctx, req.(*GetExecutiveDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetCustomerInsightsDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerInsightsDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetCustomerInsightsDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetCustomerInsightsDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetCustomerInsightsDashboard(ctx, req.(*GetCustomerInsightsDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetOperationalDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationalDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetOperationalDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetOperationalDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetOperationalDashboard(ctx, req.(*GetOperationalDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetPerformanceTrends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerformanceTrendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetPerformanceTrends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetPerformanceTrends",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetPerformanceTrends(ctx, req.(*GetPerformanceTrendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetKPIs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKPIsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetKPIs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetKPIs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetKPIs(ctx, req.(*GetKPIsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_UpdateKPI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKPIRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).UpdateKPI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/UpdateKPI",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).UpdateKPI(ctx, req.(*UpdateKPIRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetRealTimeMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealTimeMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetRealTimeMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetRealTimeMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetRealTimeMetrics(ctx, req.(*GetRealTimeMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetDashboardWidgets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardWidgetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetDashboardWidgets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/GetDashboardWidgets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetDashboardWidgets(ctx, req.(*GetDashboardWidgetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_CreateDashboardWidget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDashboardWidgetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).CreateDashboardWidget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/CreateDashboardWidget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).CreateDashboardWidget(ctx, req.(*CreateDashboardWidgetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.analytics.v1.AnalyticsService/HealthCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).HealthCheck(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticsService_ServiceDesc is the grpc.ServiceDesc for AnalyticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.analytics.v1.AnalyticsService",
	HandlerType: (*AnalyticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetExecutiveDashboard",
			Handler:    _AnalyticsService_GetExecutiveDashboard_Handler,
		},
		{
			MethodName: "GetCustomerInsightsDashboard",
			Handler:    _AnalyticsService_GetCustomerInsightsDashboard_Handler,
		},
		{
			MethodName: "GetOperationalDashboard",
			Handler:    _AnalyticsService_GetOperationalDashboard_Handler,
		},
		{
			MethodName: "GetPerformanceTrends",
			Handler:    _AnalyticsService_GetPerformanceTrends_Handler,
		},
		{
			MethodName: "GetKPIs",
			Handler:    _AnalyticsService_GetKPIs_Handler,
		},
		{
			MethodName: "UpdateKPI",
			Handler:    _AnalyticsService_UpdateKPI_Handler,
		},
		{
			MethodName: "GetRealTimeMetrics",
			Handler:    _AnalyticsService_GetRealTimeMetrics_Handler,
		},
		{
			MethodName: "GetDashboardWidgets",
			Handler:    _AnalyticsService_GetDashboardWidgets_Handler,
		},
		{
			MethodName: "CreateDashboardWidget",
			Handler:    _AnalyticsService_CreateDashboardWidget_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _AnalyticsService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/analytics/v1/analytics.proto",
}
