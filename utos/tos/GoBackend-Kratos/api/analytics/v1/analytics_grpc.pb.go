// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: analytics/v1/analytics.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AnalyticsService_GetExecutiveDashboard_FullMethodName        = "/api.analytics.v1.AnalyticsService/GetExecutiveDashboard"
	AnalyticsService_GetCustomerInsightsDashboard_FullMethodName = "/api.analytics.v1.AnalyticsService/GetCustomerInsightsDashboard"
	AnalyticsService_GetOperationalDashboard_FullMethodName      = "/api.analytics.v1.AnalyticsService/GetOperationalDashboard"
	AnalyticsService_GetPerformanceTrends_FullMethodName         = "/api.analytics.v1.AnalyticsService/GetPerformanceTrends"
	AnalyticsService_GetKPIs_FullMethodName                      = "/api.analytics.v1.AnalyticsService/GetKPIs"
	AnalyticsService_UpdateKPI_FullMethodName                    = "/api.analytics.v1.AnalyticsService/UpdateKPI"
	AnalyticsService_GetRealTimeMetrics_FullMethodName           = "/api.analytics.v1.AnalyticsService/GetRealTimeMetrics"
	AnalyticsService_GetDashboardWidgets_FullMethodName          = "/api.analytics.v1.AnalyticsService/GetDashboardWidgets"
	AnalyticsService_CreateDashboardWidget_FullMethodName        = "/api.analytics.v1.AnalyticsService/CreateDashboardWidget"
	AnalyticsService_HealthCheck_FullMethodName                  = "/api.analytics.v1.AnalyticsService/HealthCheck"
)

// AnalyticsServiceClient is the client API for AnalyticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 📊 Analytics Service - Advanced Dashboard Analytics API
type AnalyticsServiceClient interface {
	// Get executive dashboard data
	GetExecutiveDashboard(ctx context.Context, in *GetExecutiveDashboardRequest, opts ...grpc.CallOption) (*GetExecutiveDashboardResponse, error)
	// Get customer insights dashboard
	GetCustomerInsightsDashboard(ctx context.Context, in *GetCustomerInsightsDashboardRequest, opts ...grpc.CallOption) (*GetCustomerInsightsDashboardResponse, error)
	// Get operational dashboard
	GetOperationalDashboard(ctx context.Context, in *GetOperationalDashboardRequest, opts ...grpc.CallOption) (*GetOperationalDashboardResponse, error)
	// Get performance trends
	GetPerformanceTrends(ctx context.Context, in *GetPerformanceTrendsRequest, opts ...grpc.CallOption) (*GetPerformanceTrendsResponse, error)
	// Get KPIs
	GetKPIs(ctx context.Context, in *GetKPIsRequest, opts ...grpc.CallOption) (*GetKPIsResponse, error)
	// Update KPI
	UpdateKPI(ctx context.Context, in *UpdateKPIRequest, opts ...grpc.CallOption) (*UpdateKPIResponse, error)
	// Get real-time metrics
	GetRealTimeMetrics(ctx context.Context, in *GetRealTimeMetricsRequest, opts ...grpc.CallOption) (*GetRealTimeMetricsResponse, error)
	// Get dashboard widgets
	GetDashboardWidgets(ctx context.Context, in *GetDashboardWidgetsRequest, opts ...grpc.CallOption) (*GetDashboardWidgetsResponse, error)
	// Create dashboard widget
	CreateDashboardWidget(ctx context.Context, in *CreateDashboardWidgetRequest, opts ...grpc.CallOption) (*CreateDashboardWidgetResponse, error)
	// Health check
	HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
}

type analyticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticsServiceClient(cc grpc.ClientConnInterface) AnalyticsServiceClient {
	return &analyticsServiceClient{cc}
}

func (c *analyticsServiceClient) GetExecutiveDashboard(ctx context.Context, in *GetExecutiveDashboardRequest, opts ...grpc.CallOption) (*GetExecutiveDashboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExecutiveDashboardResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetExecutiveDashboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetCustomerInsightsDashboard(ctx context.Context, in *GetCustomerInsightsDashboardRequest, opts ...grpc.CallOption) (*GetCustomerInsightsDashboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomerInsightsDashboardResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetCustomerInsightsDashboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetOperationalDashboard(ctx context.Context, in *GetOperationalDashboardRequest, opts ...grpc.CallOption) (*GetOperationalDashboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOperationalDashboardResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetOperationalDashboard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetPerformanceTrends(ctx context.Context, in *GetPerformanceTrendsRequest, opts ...grpc.CallOption) (*GetPerformanceTrendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPerformanceTrendsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetPerformanceTrends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetKPIs(ctx context.Context, in *GetKPIsRequest, opts ...grpc.CallOption) (*GetKPIsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKPIsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetKPIs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) UpdateKPI(ctx context.Context, in *UpdateKPIRequest, opts ...grpc.CallOption) (*UpdateKPIResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateKPIResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_UpdateKPI_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetRealTimeMetrics(ctx context.Context, in *GetRealTimeMetricsRequest, opts ...grpc.CallOption) (*GetRealTimeMetricsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRealTimeMetricsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetRealTimeMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetDashboardWidgets(ctx context.Context, in *GetDashboardWidgetsRequest, opts ...grpc.CallOption) (*GetDashboardWidgetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDashboardWidgetsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetDashboardWidgets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) CreateDashboardWidget(ctx context.Context, in *CreateDashboardWidgetRequest, opts ...grpc.CallOption) (*CreateDashboardWidgetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDashboardWidgetResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_CreateDashboardWidget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticsServiceServer is the server API for AnalyticsService service.
// All implementations must embed UnimplementedAnalyticsServiceServer
// for forward compatibility.
//
// 📊 Analytics Service - Advanced Dashboard Analytics API
type AnalyticsServiceServer interface {
	// Get executive dashboard data
	GetExecutiveDashboard(context.Context, *GetExecutiveDashboardRequest) (*GetExecutiveDashboardResponse, error)
	// Get customer insights dashboard
	GetCustomerInsightsDashboard(context.Context, *GetCustomerInsightsDashboardRequest) (*GetCustomerInsightsDashboardResponse, error)
	// Get operational dashboard
	GetOperationalDashboard(context.Context, *GetOperationalDashboardRequest) (*GetOperationalDashboardResponse, error)
	// Get performance trends
	GetPerformanceTrends(context.Context, *GetPerformanceTrendsRequest) (*GetPerformanceTrendsResponse, error)
	// Get KPIs
	GetKPIs(context.Context, *GetKPIsRequest) (*GetKPIsResponse, error)
	// Update KPI
	UpdateKPI(context.Context, *UpdateKPIRequest) (*UpdateKPIResponse, error)
	// Get real-time metrics
	GetRealTimeMetrics(context.Context, *GetRealTimeMetricsRequest) (*GetRealTimeMetricsResponse, error)
	// Get dashboard widgets
	GetDashboardWidgets(context.Context, *GetDashboardWidgetsRequest) (*GetDashboardWidgetsResponse, error)
	// Create dashboard widget
	CreateDashboardWidget(context.Context, *CreateDashboardWidgetRequest) (*CreateDashboardWidgetResponse, error)
	// Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	mustEmbedUnimplementedAnalyticsServiceServer()
}

// UnimplementedAnalyticsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAnalyticsServiceServer struct{}

func (UnimplementedAnalyticsServiceServer) GetExecutiveDashboard(context.Context, *GetExecutiveDashboardRequest) (*GetExecutiveDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExecutiveDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetCustomerInsightsDashboard(context.Context, *GetCustomerInsightsDashboardRequest) (*GetCustomerInsightsDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerInsightsDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetOperationalDashboard(context.Context, *GetOperationalDashboardRequest) (*GetOperationalDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperationalDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetPerformanceTrends(context.Context, *GetPerformanceTrendsRequest) (*GetPerformanceTrendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerformanceTrends not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetKPIs(context.Context, *GetKPIsRequest) (*GetKPIsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKPIs not implemented")
}
func (UnimplementedAnalyticsServiceServer) UpdateKPI(context.Context, *UpdateKPIRequest) (*UpdateKPIResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKPI not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetRealTimeMetrics(context.Context, *GetRealTimeMetricsRequest) (*GetRealTimeMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRealTimeMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetDashboardWidgets(context.Context, *GetDashboardWidgetsRequest) (*GetDashboardWidgetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboardWidgets not implemented")
}
func (UnimplementedAnalyticsServiceServer) CreateDashboardWidget(context.Context, *CreateDashboardWidgetRequest) (*CreateDashboardWidgetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDashboardWidget not implemented")
}
func (UnimplementedAnalyticsServiceServer) HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedAnalyticsServiceServer) mustEmbedUnimplementedAnalyticsServiceServer() {}
func (UnimplementedAnalyticsServiceServer) testEmbeddedByValue()                          {}

// UnsafeAnalyticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticsServiceServer will
// result in compilation errors.
type UnsafeAnalyticsServiceServer interface {
	mustEmbedUnimplementedAnalyticsServiceServer()
}

func RegisterAnalyticsServiceServer(s grpc.ServiceRegistrar, srv AnalyticsServiceServer) {
	// If the following call pancis, it indicates UnimplementedAnalyticsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AnalyticsService_ServiceDesc, srv)
}

func _AnalyticsService_GetExecutiveDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExecutiveDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetExecutiveDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetExecutiveDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetExecutiveDashboard(ctx, req.(*GetExecutiveDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetCustomerInsightsDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerInsightsDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetCustomerInsightsDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetCustomerInsightsDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetCustomerInsightsDashboard(ctx, req.(*GetCustomerInsightsDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetOperationalDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationalDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetOperationalDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetOperationalDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetOperationalDashboard(ctx, req.(*GetOperationalDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetPerformanceTrends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerformanceTrendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetPerformanceTrends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetPerformanceTrends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetPerformanceTrends(ctx, req.(*GetPerformanceTrendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetKPIs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKPIsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetKPIs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetKPIs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetKPIs(ctx, req.(*GetKPIsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_UpdateKPI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKPIRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).UpdateKPI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_UpdateKPI_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).UpdateKPI(ctx, req.(*UpdateKPIRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetRealTimeMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealTimeMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetRealTimeMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetRealTimeMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetRealTimeMetrics(ctx, req.(*GetRealTimeMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetDashboardWidgets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardWidgetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetDashboardWidgets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetDashboardWidgets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetDashboardWidgets(ctx, req.(*GetDashboardWidgetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_CreateDashboardWidget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDashboardWidgetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).CreateDashboardWidget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_CreateDashboardWidget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).CreateDashboardWidget(ctx, req.(*CreateDashboardWidgetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).HealthCheck(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticsService_ServiceDesc is the grpc.ServiceDesc for AnalyticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.analytics.v1.AnalyticsService",
	HandlerType: (*AnalyticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetExecutiveDashboard",
			Handler:    _AnalyticsService_GetExecutiveDashboard_Handler,
		},
		{
			MethodName: "GetCustomerInsightsDashboard",
			Handler:    _AnalyticsService_GetCustomerInsightsDashboard_Handler,
		},
		{
			MethodName: "GetOperationalDashboard",
			Handler:    _AnalyticsService_GetOperationalDashboard_Handler,
		},
		{
			MethodName: "GetPerformanceTrends",
			Handler:    _AnalyticsService_GetPerformanceTrends_Handler,
		},
		{
			MethodName: "GetKPIs",
			Handler:    _AnalyticsService_GetKPIs_Handler,
		},
		{
			MethodName: "UpdateKPI",
			Handler:    _AnalyticsService_UpdateKPI_Handler,
		},
		{
			MethodName: "GetRealTimeMetrics",
			Handler:    _AnalyticsService_GetRealTimeMetrics_Handler,
		},
		{
			MethodName: "GetDashboardWidgets",
			Handler:    _AnalyticsService_GetDashboardWidgets_Handler,
		},
		{
			MethodName: "CreateDashboardWidget",
			Handler:    _AnalyticsService_CreateDashboardWidget_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _AnalyticsService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "analytics/v1/analytics.proto",
}
