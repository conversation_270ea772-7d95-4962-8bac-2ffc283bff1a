// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: email/v1/email.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationEmailServiceAnalyzeSentiment = "/api.email.v1.EmailService/AnalyzeSentiment"
const OperationEmailServiceCreateCampaign = "/api.email.v1.EmailService/CreateCampaign"
const OperationEmailServiceGetCampaignStats = "/api.email.v1.EmailService/GetCampaignStats"
const OperationEmailServiceListEmails = "/api.email.v1.EmailService/ListEmails"
const OperationEmailServiceSendEmail = "/api.email.v1.EmailService/SendEmail"

type EmailServiceHTTPServer interface {
	AnalyzeSentiment(context.Context, *AnalyzeSentimentRequest) (*AnalyzeSentimentResponse, error)
	CreateCampaign(context.Context, *CreateCampaignRequest) (*CreateCampaignResponse, error)
	GetCampaignStats(context.Context, *GetCampaignStatsRequest) (*GetCampaignStatsResponse, error)
	ListEmails(context.Context, *ListEmailsRequest) (*ListEmailsResponse, error)
	SendEmail(context.Context, *SendEmailRequest) (*SendEmailResponse, error)
}

func RegisterEmailServiceHTTPServer(s *http.Server, srv EmailServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/emails/send", _EmailService_SendEmail0_HTTP_Handler(srv))
	r.GET("/api/v1/emails", _EmailService_ListEmails0_HTTP_Handler(srv))
	r.POST("/api/v1/campaigns", _EmailService_CreateCampaign0_HTTP_Handler(srv))
	r.GET("/api/v1/campaigns/{campaign_id}/stats", _EmailService_GetCampaignStats0_HTTP_Handler(srv))
	r.POST("/api/v1/emails/analyze-sentiment", _EmailService_AnalyzeSentiment0_HTTP_Handler(srv))
}

func _EmailService_SendEmail0_HTTP_Handler(srv EmailServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendEmailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEmailServiceSendEmail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendEmail(ctx, req.(*SendEmailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendEmailResponse)
		return ctx.Result(200, reply)
	}
}

func _EmailService_ListEmails0_HTTP_Handler(srv EmailServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListEmailsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEmailServiceListEmails)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListEmails(ctx, req.(*ListEmailsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListEmailsResponse)
		return ctx.Result(200, reply)
	}
}

func _EmailService_CreateCampaign0_HTTP_Handler(srv EmailServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCampaignRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEmailServiceCreateCampaign)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCampaign(ctx, req.(*CreateCampaignRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCampaignResponse)
		return ctx.Result(200, reply)
	}
}

func _EmailService_GetCampaignStats0_HTTP_Handler(srv EmailServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCampaignStatsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEmailServiceGetCampaignStats)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCampaignStats(ctx, req.(*GetCampaignStatsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCampaignStatsResponse)
		return ctx.Result(200, reply)
	}
}

func _EmailService_AnalyzeSentiment0_HTTP_Handler(srv EmailServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AnalyzeSentimentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEmailServiceAnalyzeSentiment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AnalyzeSentiment(ctx, req.(*AnalyzeSentimentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AnalyzeSentimentResponse)
		return ctx.Result(200, reply)
	}
}

type EmailServiceHTTPClient interface {
	AnalyzeSentiment(ctx context.Context, req *AnalyzeSentimentRequest, opts ...http.CallOption) (rsp *AnalyzeSentimentResponse, err error)
	CreateCampaign(ctx context.Context, req *CreateCampaignRequest, opts ...http.CallOption) (rsp *CreateCampaignResponse, err error)
	GetCampaignStats(ctx context.Context, req *GetCampaignStatsRequest, opts ...http.CallOption) (rsp *GetCampaignStatsResponse, err error)
	ListEmails(ctx context.Context, req *ListEmailsRequest, opts ...http.CallOption) (rsp *ListEmailsResponse, err error)
	SendEmail(ctx context.Context, req *SendEmailRequest, opts ...http.CallOption) (rsp *SendEmailResponse, err error)
}

type EmailServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewEmailServiceHTTPClient(client *http.Client) EmailServiceHTTPClient {
	return &EmailServiceHTTPClientImpl{client}
}

func (c *EmailServiceHTTPClientImpl) AnalyzeSentiment(ctx context.Context, in *AnalyzeSentimentRequest, opts ...http.CallOption) (*AnalyzeSentimentResponse, error) {
	var out AnalyzeSentimentResponse
	pattern := "/api/v1/emails/analyze-sentiment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEmailServiceAnalyzeSentiment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EmailServiceHTTPClientImpl) CreateCampaign(ctx context.Context, in *CreateCampaignRequest, opts ...http.CallOption) (*CreateCampaignResponse, error) {
	var out CreateCampaignResponse
	pattern := "/api/v1/campaigns"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEmailServiceCreateCampaign))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EmailServiceHTTPClientImpl) GetCampaignStats(ctx context.Context, in *GetCampaignStatsRequest, opts ...http.CallOption) (*GetCampaignStatsResponse, error) {
	var out GetCampaignStatsResponse
	pattern := "/api/v1/campaigns/{campaign_id}/stats"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEmailServiceGetCampaignStats))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EmailServiceHTTPClientImpl) ListEmails(ctx context.Context, in *ListEmailsRequest, opts ...http.CallOption) (*ListEmailsResponse, error) {
	var out ListEmailsResponse
	pattern := "/api/v1/emails"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEmailServiceListEmails))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EmailServiceHTTPClientImpl) SendEmail(ctx context.Context, in *SendEmailRequest, opts ...http.CallOption) (*SendEmailResponse, error) {
	var out SendEmailResponse
	pattern := "/api/v1/emails/send"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEmailServiceSendEmail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
