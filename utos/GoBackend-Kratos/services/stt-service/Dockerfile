# 🎤 HVAC STT Service - Dual-Engine Speech-to-Text Container
# Primary: NVIDIA NeMo FastConformer (Local)
# Backup: ElevenLabs Scribe (Cloud API)

# Build stage for Python dependencies
FROM nvidia/cuda:12.1-devel-ubuntu22.04 AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    git \
    wget \
    curl \
    build-essential \
    libsndfile1 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.10 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1

# Create working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Install NVIDIA NeMo with ASR support
RUN pip3 install --no-cache-dir \
    nemo_toolkit[asr]==1.22.0 \
    torch==2.1.0 \
    torchaudio==2.1.0 \
    torchvision==0.16.0 \
    --index-url https://download.pytorch.org/whl/cu121

# Download Polish FastConformer model
RUN mkdir -p /app/models && \
    wget -O /app/models/stt_pl_fastconformer_hybrid_large_pc.nemo \
    "https://api.ngc.nvidia.com/v2/models/nvidia/nemo/stt_pl_fastconformer_hybrid_large_pc/versions/1.0.0/files/stt_pl_fastconformer_hybrid_large_pc.nemo"

# Runtime stage
FROM nvidia/cuda:12.1-runtime-ubuntu22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    libsndfile1 \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.10 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1

# Create app user
RUN groupadd -r sttuser && useradd -r -g sttuser sttuser

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.10/dist-packages /usr/local/lib/python3.10/dist-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy models from builder
COPY --from=builder /app/models /app/models

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/logs /app/temp /app/uploads && \
    chown -R sttuser:sttuser /app

# Make scripts executable
RUN chmod +x scripts/*.sh

# Switch to app user
USER sttuser

# Expose ports
EXPOSE 8085

# Environment variables
ENV PYTHONPATH=/app/src
ENV CUDA_VISIBLE_DEVICES=0
ENV NEMO_MODEL_PATH=/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo
ENV STT_SERVICE_PORT=8085
ENV STT_SERVICE_HOST=0.0.0.0
ENV LOG_LEVEL=INFO

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8085/health || exit 1

# Start the STT service
CMD ["python3", "src/main.py"]

# Metadata
LABEL maintainer="HVAC CRM Team"
LABEL version="1.0.0"
LABEL description="Dual-Engine STT Service for HVAC CRM - NVIDIA NeMo + ElevenLabs"
LABEL org.opencontainers.image.source="https://github.com/hvac-crm/stt-service"
