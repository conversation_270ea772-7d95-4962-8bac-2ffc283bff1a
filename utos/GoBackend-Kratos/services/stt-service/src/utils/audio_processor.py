"""
🎵 Audio Processor
Handles audio file processing, conversion, and validation for STT service
"""

import asyncio
import io
import os
import tempfile
import time
from typing import Optional, Tu<PERSON>, Dict, Any
import logging

import librosa
import soundfile as sf
from pydub import AudioSegment
import structlog

from models.stt_models import AudioConfig, AudioFormat

logger = structlog.get_logger(__name__)


class AudioProcessor:
    """Audio processing utilities for STT service"""

    def __init__(self, config: Optional[AudioConfig] = None):
        self.config = config or AudioConfig()

        # Ensure temp directory exists
        os.makedirs(self.config.temp_dir, exist_ok=True)

        logger.info(
            "🎵 Audio Processor initialized",
            max_file_size=self.config.max_file_size,
            target_sample_rate=self.config.target_sample_rate,
            supported_formats=self.config.supported_formats
        )

    def validate_audio_file(self, audio_data: bytes, filename: str) -> bool:
        """Validate audio file format and size"""
        try:
            # Check file size
            if len(audio_data) > self.config.max_file_size:
                logger.warning(
                    "Audio file too large",
                    size=len(audio_data),
                    max_size=self.config.max_file_size,
                    filename=filename
                )
                return False

            # Check file extension
            file_ext = os.path.splitext(filename)[1].lower().lstrip('.')
            if file_ext not in [fmt.value for fmt in self.config.supported_formats]:
                logger.warning(
                    "Unsupported audio format",
                    format=file_ext,
                    supported=self.config.supported_formats,
                    filename=filename
                )
                return False

            # Try to load audio to validate format
            try:
                audio_segment = AudioSegment.from_file(io.BytesIO(audio_data))
                if len(audio_segment) == 0:
                    logger.warning("Empty audio file", filename=filename)
                    return False
            except Exception as e:
                logger.warning(
                    "Invalid audio file format",
                    filename=filename,
                    error=str(e)
                )
                return False

            logger.debug(
                "Audio file validation passed",
                filename=filename,
                size=len(audio_data),
                format=file_ext
            )

            return True

        except Exception as e:
            logger.error(
                "Audio validation failed",
                filename=filename,
                error=str(e)
            )
            return False

    async def process_audio(self, audio_data: bytes, filename: str) -> bytes:
        """Process audio file for optimal STT performance"""
        try:
            logger.debug("🔄 Processing audio file", filename=filename)

            # Load audio with pydub
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data))

            # Convert to mono if stereo
            if audio_segment.channels > 1:
                audio_segment = audio_segment.set_channels(1)
                logger.debug("Converted to mono", filename=filename)

            # Resample to target sample rate
            if audio_segment.frame_rate != self.config.target_sample_rate:
                audio_segment = audio_segment.set_frame_rate(self.config.target_sample_rate)
                logger.debug(
                    "Resampled audio",
                    filename=filename,
                    from_rate=audio_segment.frame_rate,
                    to_rate=self.config.target_sample_rate
                )

            # Normalize audio levels if enabled
            if self.config.normalize_audio:
                audio_segment = self._normalize_audio(audio_segment)
                logger.debug("Normalized audio levels", filename=filename)

            # Remove silence if enabled
            if self.config.remove_silence:
                audio_segment = self._remove_silence(audio_segment)
                logger.debug("Removed silence", filename=filename)

            # Export as WAV for processing
            output_buffer = io.BytesIO()
            audio_segment.export(output_buffer, format="wav")
            processed_data = output_buffer.getvalue()

            logger.debug(
                "✅ Audio processing completed",
                filename=filename,
                original_size=len(audio_data),
                processed_size=len(processed_data)
            )

            return processed_data

        except Exception as e:
            logger.error(
                "❌ Audio processing failed",
                filename=filename,
                error=str(e)
            )
            # Return original data if processing fails
            return audio_data

    async def convert_to_wav(self, audio_data: bytes, filename: str) -> bytes:
        """Convert audio to WAV format"""
        try:
            # If already WAV, return as-is
            if filename.lower().endswith('.wav'):
                return audio_data

            # Convert using pydub
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data))

            # Export as WAV
            output_buffer = io.BytesIO()
            audio_segment.export(
                output_buffer,
                format="wav",
                parameters=["-ac", "1", "-ar", str(self.config.target_sample_rate)]
            )

            wav_data = output_buffer.getvalue()

            logger.debug(
                "Converted to WAV",
                filename=filename,
                original_size=len(audio_data),
                wav_size=len(wav_data)
            )

            return wav_data

        except Exception as e:
            logger.error(
                "WAV conversion failed",
                filename=filename,
                error=str(e)
            )
            raise

    async def get_audio_duration(self, audio_data: bytes) -> Optional[float]:
        """Get audio duration in seconds"""
        try:
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data))
            duration = len(audio_segment) / 1000.0  # Convert ms to seconds
            return duration
        except Exception as e:
            logger.warning("Failed to get audio duration", error=str(e))
            return None

    async def get_audio_metadata(self, audio_data: bytes) -> Dict[str, Any]:
        """Get detailed audio metadata"""
        try:
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data))

            metadata = {
                "duration": len(audio_segment) / 1000.0,  # seconds
                "sample_rate": audio_segment.frame_rate,
                "channels": audio_segment.channels,
                "sample_width": audio_segment.sample_width,
                "frame_count": audio_segment.frame_count(),
                "max_possible_amplitude": audio_segment.max_possible_amplitude,
                "file_size": len(audio_data)
            }

            # Try to get additional metadata with librosa
            try:
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    audio_segment.export(temp_file.name, format="wav")

                    y, sr = librosa.load(temp_file.name, sr=None)

                    metadata.update({
                        "rms_energy": float(librosa.feature.rms(y=y).mean()),
                        "zero_crossing_rate": float(librosa.feature.zero_crossing_rate(y).mean()),
                        "spectral_centroid": float(librosa.feature.spectral_centroid(y=y, sr=sr).mean())
                    })

                    # Clean up temp file
                    os.unlink(temp_file.name)

            except Exception as e:
                logger.debug("Could not extract advanced metadata", error=str(e))

            return metadata

        except Exception as e:
            logger.warning("Failed to get audio metadata", error=str(e))
            return {}

    def _normalize_audio(self, audio_segment: AudioSegment) -> AudioSegment:
        """Normalize audio levels"""
        try:
            # Apply gain to reach target dBFS
            target_dBFS = -20.0
            change_in_dBFS = target_dBFS - audio_segment.dBFS
            return audio_segment.apply_gain(change_in_dBFS)
        except Exception as e:
            logger.warning("Audio normalization failed", error=str(e))
            return audio_segment

    def _remove_silence(self, audio_segment: AudioSegment) -> AudioSegment:
        """Remove silence from beginning and end"""
        try:
            # Remove silence with threshold
            silence_threshold = -40  # dB
            chunk_size = 100  # ms

            # Trim silence from start and end
            trimmed = audio_segment.strip_silence(
                silence_len=chunk_size,
                silence_thresh=silence_threshold
            )

            return trimmed if len(trimmed) > 0 else audio_segment

        except Exception as e:
            logger.warning("Silence removal failed", error=str(e))
            return audio_segment

    async def cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            temp_files = os.listdir(self.config.temp_dir)
            for file in temp_files:
                file_path = os.path.join(self.config.temp_dir, file)
                if os.path.isfile(file_path):
                    # Remove files older than 1 hour
                    if time.time() - os.path.getctime(file_path) > 3600:
                        os.unlink(file_path)
                        logger.debug("Cleaned up temp file", file=file_path)
        except Exception as e:
            logger.warning("Temp file cleanup failed", error=str(e))
