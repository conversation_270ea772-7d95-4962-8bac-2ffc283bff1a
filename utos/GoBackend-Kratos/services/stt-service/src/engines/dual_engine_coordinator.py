"""
🎤 Dual Engine Coordinator
Intelligent routing between NVIDIA NeMo (primary) and ElevenLabs (backup) STT engines
"""

import asyncio
import time
from typing import Optional, Dict, Any
import structlog

from models.stt_models import (
    TranscriptionRequest,
    TranscriptionResponse,
    TranscriptionMetadata,
    STTEngineType,
    STTEngineStatus,
    EngineHealthStatus,
    CoordinatorConfig
)
from engines.nemo_engine import NeMoSTTEngine
from engines.elevenlabs_engine import ElevenLabsSTTEngine
from utils.metrics_collector import MetricsCollector

logger = structlog.get_logger(__name__)


class DualEngineCoordinator:
    """Coordinates between primary and backup STT engines"""
    
    def __init__(
        self,
        primary_engine: NeMoSTTEngine,
        backup_engine: ElevenLabsSTTEngine,
        config: CoordinatorConfig,
        metrics: MetricsCollector
    ):
        self.primary_engine = primary_engine
        self.backup_engine = backup_engine
        self.config = config
        self.metrics = metrics
        
        self.current_primary = config.primary_engine
        self.is_initialized = False
        self.health_check_task = None
        
        logger.info(
            "🎯 Initializing Dual Engine Coordinator",
            primary_engine=config.primary_engine,
            fallback_enabled=config.fallback_enabled
        )
    
    async def initialize(self) -> bool:
        """Initialize both engines"""
        try:
            logger.info("🔄 Initializing STT engines...")
            
            # Initialize primary engine
            primary_success = await self.primary_engine.initialize()
            if not primary_success:
                logger.warning("⚠️ Primary engine initialization failed")
            
            # Initialize backup engine
            backup_success = await self.backup_engine.initialize()
            if not backup_success:
                logger.warning("⚠️ Backup engine initialization failed")
            
            # At least one engine must be available
            if not primary_success and not backup_success:
                raise Exception("Both STT engines failed to initialize")
            
            self.is_initialized = True
            
            # Start health check task
            if self.config.health_check_interval > 0:
                self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            logger.info(
                "✅ Dual Engine Coordinator initialized",
                primary_available=primary_success,
                backup_available=backup_success
            )
            
            return True
            
        except Exception as e:
            logger.error("❌ Failed to initialize coordinator", error=str(e))
            return False
    
    async def transcribe(self, request: TranscriptionRequest) -> TranscriptionResponse:
        """Transcribe audio using intelligent engine selection"""
        if not self.is_initialized:
            raise RuntimeError("Coordinator not initialized")
        
        logger.info(
            "🎤 Starting dual-engine transcription",
            filename=request.filename,
            use_primary_only=request.use_primary_only
        )
        
        # Force primary engine if requested
        if request.use_primary_only:
            return await self._transcribe_with_engine(self.primary_engine, request)
        
        # Try primary engine first
        if await self._is_engine_healthy(self.primary_engine):
            try:
                result = await self._transcribe_with_engine(self.primary_engine, request)
                
                # Check if result quality is acceptable
                if self._is_result_acceptable(result):
                    return result
                else:
                    logger.warning(
                        "⚠️ Primary engine result quality below threshold",
                        confidence=result.confidence,
                        threshold=self.config.fallback_threshold
                    )
            
            except Exception as e:
                logger.warning(
                    "⚠️ Primary engine failed, trying backup",
                    error=str(e)
                )
        
        # Fallback to backup engine if enabled
        if self.config.fallback_enabled and await self._is_engine_healthy(self.backup_engine):
            try:
                result = await self._transcribe_with_engine(self.backup_engine, request)
                result.fallback_used = True
                return result
            
            except Exception as e:
                logger.error("❌ Backup engine also failed", error=str(e))
                raise Exception(f"Both engines failed. Last error: {str(e)}")
        
        raise Exception("No available STT engines")
    
    async def _transcribe_with_engine(
        self,
        engine: Any,
        request: TranscriptionRequest
    ) -> TranscriptionResponse:
        """Transcribe with specific engine with timeout"""
        try:
            # Apply timeout
            result = await asyncio.wait_for(
                engine.transcribe(request),
                timeout=self.config.timeout_seconds
            )
            return result
            
        except asyncio.TimeoutError:
            raise Exception(f"Transcription timeout after {self.config.timeout_seconds}s")
    
    def _is_result_acceptable(self, result: TranscriptionResponse) -> bool:
        """Check if transcription result meets quality threshold"""
        if not result.success:
            return False
        
        # Check confidence threshold
        if result.confidence is not None:
            return result.confidence >= self.config.fallback_threshold
        
        # If no confidence score, check transcript length as heuristic
        return len(result.transcript.strip()) > 0
    
    async def _is_engine_healthy(self, engine: Any) -> bool:
        """Check if engine is healthy and available"""
        try:
            status = await engine.get_status()
            return status.available and status.status in [
                STTEngineStatus.HEALTHY,
                STTEngineStatus.DEGRADED
            ]
        except Exception:
            return False
    
    async def _health_check_loop(self):
        """Periodic health check for both engines"""
        while self.is_initialized:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                
                # Check primary engine
                primary_status = await self.primary_engine.get_status()
                
                # Check backup engine
                backup_status = await self.backup_engine.get_status()
                
                logger.debug(
                    "💓 Health check completed",
                    primary_status=primary_status.status,
                    backup_status=backup_status.status
                )
                
                # Record metrics
                if self.metrics:
                    await self.metrics.record_health_check(primary_status, backup_status)
                
            except Exception as e:
                logger.warning("Health check failed", error=str(e))
    
    async def get_primary_engine_status(self) -> EngineHealthStatus:
        """Get primary engine status"""
        return await self.primary_engine.get_status()
    
    async def get_backup_engine_status(self) -> EngineHealthStatus:
        """Get backup engine status"""
        return await self.backup_engine.get_status()
    
    async def get_coordinator_status(self) -> Dict[str, Any]:
        """Get coordinator status"""
        return {
            "initialized": self.is_initialized,
            "current_primary": self.current_primary,
            "fallback_enabled": self.config.fallback_enabled,
            "fallback_threshold": self.config.fallback_threshold,
            "timeout_seconds": self.config.timeout_seconds,
            "health_check_interval": self.config.health_check_interval
        }
    
    async def switch_primary_engine(self, engine_type: str):
        """Switch primary engine (for testing/maintenance)"""
        if engine_type not in ["nemo", "elevenlabs"]:
            raise ValueError("Invalid engine type")
        
        old_primary = self.current_primary
        self.current_primary = STTEngineType(engine_type)
        
        # Swap engines if needed
        if engine_type == "elevenlabs":
            self.primary_engine, self.backup_engine = self.backup_engine, self.primary_engine
        
        logger.info(
            "🔄 Primary engine switched",
            from_engine=old_primary,
            to_engine=self.current_primary
        )
    
    async def shutdown(self):
        """Shutdown coordinator and both engines"""
        logger.info("🛑 Shutting down Dual Engine Coordinator...")
        
        self.is_initialized = False
        
        # Stop health check task
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # Shutdown engines
        await self.primary_engine.shutdown()
        await self.backup_engine.shutdown()
        
        logger.info("✅ Dual Engine Coordinator shutdown complete")
