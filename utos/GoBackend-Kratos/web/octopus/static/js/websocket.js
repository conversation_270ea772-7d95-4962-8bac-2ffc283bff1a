// 🐙 Morphic Octopus WebSocket Client
class OctopusWebSocket {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnected = false;
        this.messageHandlers = new Map();
        
        this.init();
    }
    
    init() {
        this.connect();
        this.setupEventHandlers();
    }
    
    connect() {
        try {
            const wsUrl = window.OCTOPUS_CONFIG.websocketUrl;
            console.log('🔌 Connecting to WebSocket:', wsUrl);
            
            this.ws = new WebSocket(wsUrl);
            this.setupWebSocketHandlers();
            
        } catch (error) {
            console.error('❌ WebSocket connection error:', error);
            this.handleConnectionError();
        }
    }
    
    setupWebSocketHandlers() {
        this.ws.onopen = () => {
            console.log('✅ WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus('connected');
            
            // Request initial data
            this.send({ type: 'get_dashboard_data' });
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('❌ Error parsing WebSocket message:', error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('🔌 WebSocket disconnected');
            this.isConnected = false;
            this.updateConnectionStatus('disconnected');
            this.attemptReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('❌ WebSocket error:', error);
            this.handleConnectionError();
        };
    }
    
    handleMessage(data) {
        console.log('📨 Received WebSocket message:', data);
        
        // Update dashboard data
        if (data.system_status) {
            this.updateSystemStatus(data.system_status);
        }
        
        if (data.customer_metrics) {
            this.updateCustomerMetrics(data.customer_metrics);
        }
        
        if (data.email_intelligence) {
            this.updateEmailIntelligence(data.email_intelligence);
        }
        
        if (data.ai_performance) {
            this.updateAIPerformance(data.ai_performance);
        }
        
        if (data.service_health) {
            this.updateServiceHealth(data.service_health);
        }
        
        if (data.realtime_alerts) {
            this.updateAlerts(data.realtime_alerts);
        }
        
        // Update last updated timestamp
        this.updateLastUpdated();
        
        // Trigger custom handlers
        this.messageHandlers.forEach((handler, type) => {
            if (data[type]) {
                handler(data[type]);
            }
        });
    }
    
    updateSystemStatus(status) {
        const uptimeElement = document.getElementById('system-uptime');
        const cpuElement = document.getElementById('cpu-usage');
        const memoryElement = document.getElementById('memory-usage');
        const connectionsElement = document.getElementById('active-connections');
        
        if (uptimeElement) {
            const uptime = this.formatUptime(status.uptime);
            uptimeElement.textContent = uptime;
            uptimeElement.classList.add('data-update');
            setTimeout(() => uptimeElement.classList.remove('data-update'), 500);
        }
        
        if (cpuElement) {
            cpuElement.textContent = `${status.cpu_usage?.toFixed(1) || 0}%`;
        }
        
        if (memoryElement) {
            memoryElement.textContent = `${status.memory_usage?.toFixed(1) || 0}%`;
        }
        
        if (connectionsElement) {
            connectionsElement.textContent = status.active_websockets || 0;
        }
    }
    
    updateCustomerMetrics(metrics) {
        this.updateElement('total-customers', metrics.total_customers);
        this.updateElement('new-customers-today', metrics.new_today);
        this.updateElement('at-risk-customers', metrics.at_risk_customers);
    }
    
    updateEmailIntelligence(intelligence) {
        this.updateElement('total-emails', intelligence.total_emails);
        this.updateElement('emails-today', intelligence.emails_today);
        this.updateElement('emails-processed', intelligence.processed_emails);
    }
    
    updateAIPerformance(performance) {
        this.updateElement('ai-requests', performance.total_requests);
        this.updateElement('ai-success-rate', `${(performance.success_rate * 100).toFixed(1)}%`);
        this.updateElement('ai-response-time', `${performance.avg_response_time}ms`);
    }
    
    updateServiceHealth(health) {
        const services = ['email', 'transcription', 'customer', 'ai'];
        
        services.forEach(service => {
            const serviceData = health[`${service}_service`];
            if (serviceData) {
                this.updateServiceStatus(service, serviceData);
            }
        });
    }
    
    updateServiceStatus(serviceName, serviceData) {
        const statusElement = document.getElementById(`${serviceName}-service-status`);
        if (!statusElement) return;
        
        const indicator = statusElement.querySelector('.w-3.h-3');
        const statusText = statusElement.querySelector('.text-xs');
        
        // Remove existing status classes
        statusElement.classList.remove('healthy', 'warning', 'error');
        
        // Add new status class
        if (serviceData.status === 'healthy') {
            statusElement.classList.add('healthy');
            if (statusText) statusText.textContent = `✅ ${serviceData.response_time}ms`;
        } else if (serviceData.status === 'warning') {
            statusElement.classList.add('warning');
            if (statusText) statusText.textContent = `⚠️ ${serviceData.status}`;
        } else {
            statusElement.classList.add('error');
            if (statusText) statusText.textContent = `❌ ${serviceData.status}`;
        }
    }
    
    updateAlerts(alerts) {
        const container = document.getElementById('alerts-container');
        if (!container) return;
        
        if (!alerts || alerts.length === 0) {
            container.innerHTML = '<div class="text-gray-400 text-sm">No alerts at this time</div>';
            return;
        }
        
        container.innerHTML = alerts.map(alert => `
            <div class="alert ${alert.type}">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="font-medium">${alert.title}</div>
                        <div class="text-sm mt-1">${alert.message}</div>
                    </div>
                    <div class="text-xs text-gray-400">${this.formatTime(alert.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }
    
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.add('data-update');
            setTimeout(() => element.classList.remove('data-update'), 500);
        }
    }
    
    updateLastUpdated() {
        const element = document.getElementById('last-updated');
        if (element) {
            element.textContent = new Date().toLocaleTimeString();
        }
    }
    
    updateConnectionStatus(status) {
        const indicator = document.getElementById('status-indicator');
        const text = document.getElementById('status-text');
        
        if (indicator) {
            indicator.className = `w-3 h-3 rounded-full ${status}`;
        }
        
        if (text) {
            const statusTexts = {
                connected: 'Connected',
                connecting: 'Connecting...',
                disconnected: 'Disconnected'
            };
            text.textContent = statusTexts[status] || 'Unknown';
        }
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('❌ Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        this.updateConnectionStatus('connecting');
        
        console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay * this.reconnectAttempts);
    }
    
    handleConnectionError() {
        this.isConnected = false;
        this.updateConnectionStatus('disconnected');
    }
    
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            console.warn('⚠️ WebSocket not connected, cannot send message');
        }
    }
    
    setupEventHandlers() {
        // Real-time toggle
        const realtimeToggle = document.getElementById('realtime-toggle');
        if (realtimeToggle) {
            realtimeToggle.addEventListener('click', () => {
                const isEnabled = realtimeToggle.textContent.includes('ON');
                realtimeToggle.textContent = isEnabled ? 'Real-time: OFF' : 'Real-time: ON';
                realtimeToggle.className = isEnabled 
                    ? 'bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm'
                    : 'bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm';
            });
        }
    }
    
    // Utility functions
    formatUptime(uptimeNs) {
        const seconds = Math.floor(uptimeNs / 1000000000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }
    
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString();
    }
    
    // Public API
    onMessage(type, handler) {
        this.messageHandlers.set(type, handler);
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// Initialize WebSocket connection when page loads
let octopusWS;
document.addEventListener('DOMContentLoaded', () => {
    octopusWS = new OctopusWebSocket();
});

// Export for global access
window.OctopusWebSocket = OctopusWebSocket;