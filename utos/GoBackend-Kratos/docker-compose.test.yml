version: '3.8'

# 🧪 Docker Compose for GoBackend-Kratos Testing Environment
# Provides isolated test environment with all required dependencies

services:
  # Test PostgreSQL Database
  postgres-test:
    image: postgres:17.5-alpine
    container_name: hvac-postgres-test
    environment:
      POSTGRES_DB: hvacdb_test
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/test_db_init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d hvacdb_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Test Redis Cache
  redis-test:
    image: redis:7.4-alpine
    container_name: hvac-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass testpass
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "testpass", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Test Qdrant Vector Database
  qdrant-test:
    image: qdrant/qdrant:v1.7.4
    container_name: hvac-qdrant-test
    ports:
      - "6334:6333"
      - "6335:6334"
    volumes:
      - qdrant_test_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Test SMTP Server (for email testing)
  mailhog-test:
    image: mailhog/mailhog:v1.0.1
    container_name: hvac-mailhog-test
    ports:
      - "1026:1025"  # SMTP
      - "8026:8025"  # Web UI
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /maildir
    volumes:
      - mailhog_test_data:/maildir
    networks:
      - test-network

  # Test LM Studio Mock Server (for AI testing)
  lm-studio-mock:
    image: nginx:alpine
    container_name: hvac-lm-studio-mock
    ports:
      - "1235:80"
    volumes:
      - ./tests/mocks/lm-studio-mock.conf:/etc/nginx/conf.d/default.conf
      - ./tests/mocks/lm-studio-responses:/usr/share/nginx/html
    networks:
      - test-network

  # Test Environment Setup Container
  test-setup:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: hvac-test-setup
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
      qdrant-test:
        condition: service_healthy
    environment:
      # Database Configuration
      DB_HOST: postgres-test
      DB_PORT: 5432
      DB_NAME: hvacdb_test
      DB_USER: testuser
      DB_PASSWORD: testpass
      
      # Redis Configuration
      REDIS_HOST: redis-test
      REDIS_PORT: 6379
      REDIS_PASSWORD: testpass
      
      # Qdrant Configuration
      QDRANT_HOST: qdrant-test
      QDRANT_PORT: 6333
      
      # Email Configuration
      SMTP_HOST: mailhog-test
      SMTP_PORT: 1025
      
      # AI Configuration
      LM_STUDIO_HOST: lm-studio-mock
      LM_STUDIO_PORT: 80
      
      # Test Configuration
      TEST_MODE: "true"
      LOG_LEVEL: "debug"
      ENABLE_METRICS: "true"
      
    volumes:
      - .:/app
      - test_logs:/app/logs
    working_dir: /app
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    networks:
      - test-network

  # Test Data Generator
  test-data-generator:
    build:
      context: .
      dockerfile: Dockerfile.test-data
    container_name: hvac-test-data-generator
    depends_on:
      - test-setup
    environment:
      DB_HOST: postgres-test
      DB_PORT: 5432
      DB_NAME: hvacdb_test
      DB_USER: testuser
      DB_PASSWORD: testpass
    volumes:
      - ./tests/data:/test-data
    networks:
      - test-network
    profiles:
      - data-generation

  # Performance Testing Container
  performance-tester:
    build:
      context: .
      dockerfile: Dockerfile.performance
    container_name: hvac-performance-tester
    depends_on:
      - test-setup
    environment:
      TARGET_HOST: test-setup
      TARGET_PORT: 8080
      CONCURRENT_USERS: 50
      TEST_DURATION: 300s
    volumes:
      - ./tests/performance:/performance-tests
      - performance_results:/results
    networks:
      - test-network
    profiles:
      - performance

  # Integration Test Runner
  integration-tester:
    build:
      context: .
      dockerfile: Dockerfile.integration
    container_name: hvac-integration-tester
    depends_on:
      - test-setup
      - mailhog-test
    environment:
      API_BASE_URL: http://test-setup:8080
      SMTP_TEST_URL: http://mailhog-test:8025
      REDIS_TEST_URL: redis://redis-test:6379
    volumes:
      - ./tests/integration:/integration-tests
      - integration_results:/results
    networks:
      - test-network
    profiles:
      - integration

  # HVAC-Remix Compatibility Tester
  compatibility-tester:
    build:
      context: .
      dockerfile: Dockerfile.compatibility
    container_name: hvac-compatibility-tester
    depends_on:
      - test-setup
    environment:
      GOBACKEND_URL: http://test-setup:8080
      HVAC_REMIX_SCHEMA_PATH: /schemas/hvac-remix
      TEST_TIMEOUT: 300s
    volumes:
      - ./tests/compatibility:/compatibility-tests
      - ./tests/schemas:/schemas
      - compatibility_results:/results
    networks:
      - test-network
    profiles:
      - compatibility

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  qdrant_test_data:
    driver: local
  mailhog_test_data:
    driver: local
  test_logs:
    driver: local
  performance_results:
    driver: local
  integration_results:
    driver: local
  compatibility_results:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
