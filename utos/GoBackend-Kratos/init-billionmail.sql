-- BillionMail Database Initialization for HVAC CRM Integration
-- This script creates the necessary tables for BillionMail integration

-- Create BillionMail schema
CREATE SCHEMA IF NOT EXISTS billionmail;

-- BillionMail Domains Table
CREATE TABLE IF NOT EXISTS billionmail.domains (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BillionMail Mailboxes Table
CREATE TABLE IF NOT EXISTS billionmail.mailboxes (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    domain_id INTEGER REFERENCES billionmail.domains(id),
    quota BIGINT DEFAULT 1073741824, -- 1GB default
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BillionMail Aliases Table
CREATE TABLE IF NOT EXISTS billionmail.aliases (
    id SERIAL PRIMARY KEY,
    source VARCHAR(255) NOT NULL,
    destination VARCHAR(255) NOT NULL,
    domain_id INTEGER REFERENCES billionmail.domains(id),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email Messages Table (for tracking sent/received emails)
CREATE TABLE IF NOT EXISTS billionmail.email_messages (
    id SERIAL PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE,
    from_address VARCHAR(255) NOT NULL,
    to_addresses TEXT[] NOT NULL,
    cc_addresses TEXT[],
    bcc_addresses TEXT[],
    subject TEXT,
    body TEXT,
    html_body TEXT,
    headers JSONB,
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP,
    bounced_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email Campaigns Table
CREATE TABLE IF NOT EXISTS billionmail.email_campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    template_id INTEGER,
    sender_email VARCHAR(255) NOT NULL,
    recipients TEXT[] NOT NULL,
    status VARCHAR(50) DEFAULT 'draft',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    total_recipients INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email Templates Table (HVAC-specific)
CREATE TABLE IF NOT EXISTS billionmail.email_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    template_type VARCHAR(100) NOT NULL, -- 'service_reminder', 'quote_follow_up', etc.
    subject VARCHAR(255) NOT NULL,
    body_text TEXT,
    body_html TEXT,
    variables JSONB, -- Template variables like {{CustomerName}}, {{ServiceType}}
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- HVAC-specific Email Integration Table
CREATE TABLE IF NOT EXISTS billionmail.hvac_email_integration (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER, -- References HVAC customers table
    job_id INTEGER,      -- References HVAC jobs table
    email_message_id INTEGER REFERENCES billionmail.email_messages(id),
    campaign_id INTEGER REFERENCES billionmail.email_campaigns(id),
    email_type VARCHAR(100) NOT NULL, -- 'service_reminder', 'quote', 'invoice', 'follow_up'
    ai_sentiment VARCHAR(50), -- 'positive', 'neutral', 'negative'
    ai_confidence DECIMAL(3,2), -- 0.00 to 1.00
    ai_analysis JSONB, -- AI analysis results
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default HVAC domain
INSERT INTO billionmail.domains (domain, description) 
VALUES ('hvac-company.local', 'Default HVAC Company Domain')
ON CONFLICT (domain) DO NOTHING;

-- Insert default HVAC mailbox
INSERT INTO billionmail.mailboxes (email, password, name, domain_id) 
VALUES (
    '<EMAIL>', 
    '$2y$10$defaultpasswordhash', 
    'HVAC Support', 
    (SELECT id FROM billionmail.domains WHERE domain = 'hvac-company.local')
)
ON CONFLICT (email) DO NOTHING;

-- Insert HVAC email templates
INSERT INTO billionmail.email_templates (name, template_type, subject, body_text, body_html, variables) VALUES
(
    'Service Reminder',
    'service_reminder',
    'HVAC Maintenance Reminder - {{CustomerName}}',
    'Dear {{CustomerName}},

This is a friendly reminder that your HVAC system is due for maintenance.
Regular maintenance helps ensure optimal performance and extends equipment life.

Service Details:
- Customer: {{CustomerName}}
- Address: {{CustomerAddress}}
- System Type: {{SystemType}}
- Last Service: {{LastServiceDate}}

Please contact us to schedule your appointment.

Best regards,
{{CompanyName}} HVAC Services',
    '<html><body>
<h2>HVAC Maintenance Reminder</h2>
<p>Dear {{CustomerName}},</p>
<p>This is a friendly reminder that your HVAC system is due for maintenance.</p>
<p>Regular maintenance helps ensure optimal performance and extends equipment life.</p>
<h3>Service Details:</h3>
<ul>
<li><strong>Customer:</strong> {{CustomerName}}</li>
<li><strong>Address:</strong> {{CustomerAddress}}</li>
<li><strong>System Type:</strong> {{SystemType}}</li>
<li><strong>Last Service:</strong> {{LastServiceDate}}</li>
</ul>
<p>Please contact us to schedule your appointment.</p>
<p>Best regards,<br>{{CompanyName}} HVAC Services</p>
</body></html>',
    '{"CustomerName": "string", "CustomerAddress": "string", "SystemType": "string", "LastServiceDate": "date", "CompanyName": "string"}'::jsonb
),
(
    'Quote Follow-up',
    'quote_follow_up',
    'Your HVAC Service Quote - {{CustomerName}}',
    'Dear {{CustomerName}},

Thank you for your interest in our HVAC services. Please find your quote details below.

Quote Summary:
- Service: {{ServiceType}}
- Estimated Cost: {{EstimatedCost}}
- Timeline: {{Timeline}}

This quote is valid for 30 days. Please don''t hesitate to contact us with any questions.

Best regards,
{{CompanyName}}',
    '<html><body>
<h2>Your HVAC Service Quote</h2>
<p>Dear {{CustomerName}},</p>
<p>Thank you for your interest in our HVAC services. Please find your quote details below.</p>
<h3>Quote Summary:</h3>
<ul>
<li><strong>Service:</strong> {{ServiceType}}</li>
<li><strong>Estimated Cost:</strong> {{EstimatedCost}}</li>
<li><strong>Timeline:</strong> {{Timeline}}</li>
</ul>
<p><em>This quote is valid for 30 days.</em></p>
<p>Please don''t hesitate to contact us with any questions.</p>
<p>Best regards,<br>{{CompanyName}}</p>
</body></html>',
    '{"CustomerName": "string", "ServiceType": "string", "EstimatedCost": "string", "Timeline": "string", "CompanyName": "string"}'::jsonb
)
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_messages_status ON billionmail.email_messages(status);
CREATE INDEX IF NOT EXISTS idx_email_messages_from ON billionmail.email_messages(from_address);
CREATE INDEX IF NOT EXISTS idx_email_messages_created ON billionmail.email_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON billionmail.email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_hvac_integration_customer ON billionmail.hvac_email_integration(customer_id);
CREATE INDEX IF NOT EXISTS idx_hvac_integration_job ON billionmail.hvac_email_integration(job_id);
CREATE INDEX IF NOT EXISTS idx_hvac_integration_type ON billionmail.hvac_email_integration(email_type);

-- Grant permissions (adjust as needed for your setup)
GRANT USAGE ON SCHEMA billionmail TO hvac_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA billionmail TO hvac_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA billionmail TO hvac_user;
