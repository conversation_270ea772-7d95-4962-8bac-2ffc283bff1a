-- Create the 'leads' table
CREATE TABLE IF NOT EXISTS leads (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'New',
    source VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create the 'campaigns' table
CREATE TABLE IF NOT EXISTS campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    source VARCHAR(255),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create an index on email for faster lookups in leads table
CREATE INDEX IF NOT EXISTS idx_leads_email ON leads (email);

-- Create an index on phone for faster lookups in leads table
CREATE INDEX IF NOT EXISTS idx_leads_phone ON leads (phone);

-- Create an index on campaign name for faster lookups in campaigns table
CREATE INDEX IF NOT EXISTS idx_campaigns_name ON campaigns (name);

-- Add a table for tracking lead-campaign interactions
CREATE TABLE IF NOT EXISTS lead_campaign_interactions (
    id SERIAL PRIMARY KEY,
    lead_id INT NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    campaign_id INT NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- e.g., 'click', 'conversion', 'impression'
    event_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups in lead_campaign_interactions
CREATE INDEX IF NOT EXISTS idx_lci_lead_id ON lead_campaign_interactions (lead_id);
CREATE INDEX IF NOT EXISTS idx_lci_campaign_id ON lead_campaign_interactions (campaign_id);
CREATE INDEX IF NOT EXISTS idx_lci_event_type ON lead_campaign_interactions (event_type);
