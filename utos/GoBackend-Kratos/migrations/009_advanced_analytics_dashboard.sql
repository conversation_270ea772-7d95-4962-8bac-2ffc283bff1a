-- 📊 Advanced Analytics Dashboard - Database Schema
-- GoBackend-Kratos HVAC CRM System
-- Created: $(date)

-- ============================================================================
-- 📈 ANALYTICS TABLES
-- ============================================================================

-- 1. 📊 Business Intelligence Metrics
CREATE TABLE IF NOT EXISTS business_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_category VARCHAR(50) NOT NULL, -- revenue, performance, customer, operational
    metric_value DECIMAL(15,2) NOT NULL,
    metric_unit VARCHAR(20), -- dollars, percentage, count, hours
    time_period VARCHAR(20) NOT NULL, -- daily, weekly, monthly, quarterly, yearly
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes for performance
    INDEX idx_business_metrics_category (metric_category),
    INDEX idx_business_metrics_period (period_start, period_end),
    INDEX idx_business_metrics_name_period (metric_name, period_start)
);

-- 2. 📈 Real-time Dashboard Data
CREATE TABLE IF NOT EXISTS dashboard_widgets (
    id SERIAL PRIMARY KEY,
    widget_name VARCHAR(100) NOT NULL,
    widget_type VARCHAR(50) NOT NULL, -- chart, kpi, table, gauge, map
    dashboard_category VARCHAR(50) NOT NULL, -- executive, operations, sales, service
    data_source VARCHAR(100) NOT NULL, -- table or query identifier
    refresh_interval INTEGER DEFAULT 300, -- seconds
    widget_config JSONB NOT NULL, -- chart config, colors, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(widget_name, dashboard_category)
);

-- 3. 📊 KPI Tracking
CREATE TABLE IF NOT EXISTS kpi_tracking (
    id SERIAL PRIMARY KEY,
    kpi_name VARCHAR(100) NOT NULL,
    kpi_category VARCHAR(50) NOT NULL, -- financial, operational, customer, employee
    current_value DECIMAL(15,2) NOT NULL,
    target_value DECIMAL(15,2),
    previous_value DECIMAL(15,2),
    trend_direction VARCHAR(10), -- up, down, stable
    trend_percentage DECIMAL(5,2),
    measurement_date DATE NOT NULL,
    measurement_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    alert_threshold_min DECIMAL(15,2),
    alert_threshold_max DECIMAL(15,2),
    is_alert_triggered BOOLEAN DEFAULT false,
    notes TEXT,
    
    INDEX idx_kpi_tracking_category_date (kpi_category, measurement_date),
    INDEX idx_kpi_tracking_name_date (kpi_name, measurement_date)
);

-- 4. 📈 Performance Analytics
CREATE TABLE IF NOT EXISTS performance_analytics (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL, -- customer, job, technician, equipment
    entity_id INTEGER NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- response_time, completion_rate, satisfaction
    metric_value DECIMAL(10,4) NOT NULL,
    benchmark_value DECIMAL(10,4),
    performance_score DECIMAL(5,2), -- 0-100 score
    analysis_period VARCHAR(20) NOT NULL, -- daily, weekly, monthly
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    improvement_suggestions TEXT[],
    
    INDEX idx_performance_entity (entity_type, entity_id),
    INDEX idx_performance_metric_period (metric_type, period_start)
);

-- 5. 📊 Customer Analytics
CREATE TABLE IF NOT EXISTS customer_analytics (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    total_jobs INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    average_job_value DECIMAL(10,2) DEFAULT 0,
    last_service_date DATE,
    next_scheduled_service DATE,
    customer_lifetime_value DECIMAL(12,2) DEFAULT 0,
    satisfaction_score DECIMAL(3,2), -- 1-5 scale
    loyalty_tier VARCHAR(20), -- bronze, silver, gold, platinum
    risk_score DECIMAL(3,2), -- 0-1 (churn risk)
    preferred_technician_id INTEGER,
    preferred_service_time VARCHAR(20), -- morning, afternoon, evening
    communication_preference VARCHAR(20), -- email, phone, sms
    equipment_count INTEGER DEFAULT 0,
    maintenance_compliance DECIMAL(3,2), -- 0-1 compliance rate
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_customer_analytics_customer (customer_id),
    INDEX idx_customer_analytics_tier (loyalty_tier),
    INDEX idx_customer_analytics_risk (risk_score)
);

-- 6. 📈 Revenue Analytics
CREATE TABLE IF NOT EXISTS revenue_analytics (
    id SERIAL PRIMARY KEY,
    revenue_date DATE NOT NULL,
    revenue_category VARCHAR(50) NOT NULL, -- service, maintenance, emergency, installation
    daily_revenue DECIMAL(12,2) DEFAULT 0,
    weekly_revenue DECIMAL(12,2) DEFAULT 0,
    monthly_revenue DECIMAL(12,2) DEFAULT 0,
    quarterly_revenue DECIMAL(12,2) DEFAULT 0,
    yearly_revenue DECIMAL(12,2) DEFAULT 0,
    jobs_completed INTEGER DEFAULT 0,
    average_job_value DECIMAL(10,2) DEFAULT 0,
    technician_utilization DECIMAL(5,2), -- percentage
    customer_acquisition_cost DECIMAL(10,2),
    profit_margin DECIMAL(5,2), -- percentage
    growth_rate DECIMAL(5,2), -- percentage vs previous period
    
    INDEX idx_revenue_analytics_date (revenue_date),
    INDEX idx_revenue_analytics_category (revenue_category),
    UNIQUE(revenue_date, revenue_category)
);

-- 7. 📊 Operational Analytics
CREATE TABLE IF NOT EXISTS operational_analytics (
    id SERIAL PRIMARY KEY,
    analysis_date DATE NOT NULL,
    total_active_jobs INTEGER DEFAULT 0,
    completed_jobs INTEGER DEFAULT 0,
    cancelled_jobs INTEGER DEFAULT 0,
    emergency_jobs INTEGER DEFAULT 0,
    average_response_time INTERVAL,
    average_completion_time INTERVAL,
    technician_efficiency DECIMAL(5,2), -- percentage
    equipment_utilization DECIMAL(5,2), -- percentage
    customer_satisfaction DECIMAL(3,2), -- 1-5 scale
    first_time_fix_rate DECIMAL(5,2), -- percentage
    callback_rate DECIMAL(5,2), -- percentage
    parts_availability DECIMAL(5,2), -- percentage
    fuel_costs DECIMAL(10,2),
    overtime_hours DECIMAL(8,2),
    
    INDEX idx_operational_analytics_date (analysis_date),
    UNIQUE(analysis_date)
);

-- ============================================================================
-- 📊 ANALYTICS VIEWS FOR DASHBOARD
-- ============================================================================

-- Executive Dashboard Summary
CREATE OR REPLACE VIEW executive_dashboard_summary AS
SELECT 
    'Today' as period,
    COALESCE(SUM(CASE WHEN ra.revenue_date = CURRENT_DATE THEN ra.daily_revenue END), 0) as today_revenue,
    COALESCE(SUM(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.completed_jobs END), 0) as today_jobs,
    COALESCE(AVG(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.customer_satisfaction END), 0) as today_satisfaction,
    COALESCE(AVG(CASE WHEN oa.analysis_date = CURRENT_DATE THEN oa.technician_efficiency END), 0) as today_efficiency,
    
    -- Week comparison
    COALESCE(SUM(CASE WHEN ra.revenue_date >= CURRENT_DATE - INTERVAL '7 days' THEN ra.daily_revenue END), 0) as week_revenue,
    COALESCE(SUM(CASE WHEN oa.analysis_date >= CURRENT_DATE - INTERVAL '7 days' THEN oa.completed_jobs END), 0) as week_jobs,
    
    -- Month comparison
    COALESCE(SUM(CASE WHEN ra.revenue_date >= DATE_TRUNC('month', CURRENT_DATE) THEN ra.daily_revenue END), 0) as month_revenue,
    COALESCE(SUM(CASE WHEN oa.analysis_date >= DATE_TRUNC('month', CURRENT_DATE) THEN oa.completed_jobs END), 0) as month_jobs
FROM revenue_analytics ra
FULL OUTER JOIN operational_analytics oa ON ra.revenue_date = oa.analysis_date;

-- Customer Insights View
CREATE OR REPLACE VIEW customer_insights_dashboard AS
SELECT 
    ca.loyalty_tier,
    COUNT(*) as customer_count,
    AVG(ca.customer_lifetime_value) as avg_lifetime_value,
    AVG(ca.satisfaction_score) as avg_satisfaction,
    AVG(ca.risk_score) as avg_churn_risk,
    SUM(ca.total_revenue) as tier_total_revenue
FROM customer_analytics ca
GROUP BY ca.loyalty_tier
ORDER BY 
    CASE ca.loyalty_tier 
        WHEN 'platinum' THEN 1 
        WHEN 'gold' THEN 2 
        WHEN 'silver' THEN 3 
        WHEN 'bronze' THEN 4 
        ELSE 5 
    END;

-- Performance Trends View
CREATE OR REPLACE VIEW performance_trends_dashboard AS
SELECT 
    DATE_TRUNC('week', oa.analysis_date) as week_start,
    AVG(oa.technician_efficiency) as avg_efficiency,
    AVG(oa.customer_satisfaction) as avg_satisfaction,
    AVG(oa.first_time_fix_rate) as avg_first_time_fix,
    SUM(oa.completed_jobs) as total_jobs,
    AVG(EXTRACT(EPOCH FROM oa.average_response_time)/3600) as avg_response_hours
FROM operational_analytics oa
WHERE oa.analysis_date >= CURRENT_DATE - INTERVAL '12 weeks'
GROUP BY DATE_TRUNC('week', oa.analysis_date)
ORDER BY week_start;

-- ============================================================================
-- 📊 ANALYTICS FUNCTIONS
-- ============================================================================

-- Function to calculate customer lifetime value
CREATE OR REPLACE FUNCTION calculate_customer_ltv(customer_id_param INTEGER)
RETURNS DECIMAL(12,2) AS $$
DECLARE
    ltv_value DECIMAL(12,2);
BEGIN
    SELECT 
        COALESCE(
            (total_revenue * 12) / NULLIF(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MIN(created_at))), 0),
            total_revenue
        )
    INTO ltv_value
    FROM customer_analytics ca
    JOIN customers c ON ca.customer_id = c.id
    WHERE ca.customer_id = customer_id_param
    GROUP BY ca.total_revenue;
    
    RETURN COALESCE(ltv_value, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to update KPI tracking
CREATE OR REPLACE FUNCTION update_kpi_tracking()
RETURNS VOID AS $$
BEGIN
    -- Update daily revenue KPI
    INSERT INTO kpi_tracking (kpi_name, kpi_category, current_value, measurement_date)
    SELECT 
        'Daily Revenue',
        'financial',
        COALESCE(SUM(daily_revenue), 0),
        CURRENT_DATE
    FROM revenue_analytics 
    WHERE revenue_date = CURRENT_DATE
    ON CONFLICT (kpi_name, measurement_date) DO UPDATE SET
        current_value = EXCLUDED.current_value,
        measurement_time = CURRENT_TIMESTAMP;
    
    -- Update customer satisfaction KPI
    INSERT INTO kpi_tracking (kpi_name, kpi_category, current_value, measurement_date)
    SELECT 
        'Customer Satisfaction',
        'customer',
        COALESCE(AVG(customer_satisfaction), 0),
        CURRENT_DATE
    FROM operational_analytics 
    WHERE analysis_date = CURRENT_DATE
    ON CONFLICT (kpi_name, measurement_date) DO UPDATE SET
        current_value = EXCLUDED.current_value,
        measurement_time = CURRENT_TIMESTAMP;
        
    -- Update technician efficiency KPI
    INSERT INTO kpi_tracking (kpi_name, kpi_category, current_value, measurement_date)
    SELECT 
        'Technician Efficiency',
        'operational',
        COALESCE(AVG(technician_efficiency), 0),
        CURRENT_DATE
    FROM operational_analytics 
    WHERE analysis_date = CURRENT_DATE
    ON CONFLICT (kpi_name, measurement_date) DO UPDATE SET
        current_value = EXCLUDED.current_value,
        measurement_time = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 📊 TRIGGERS FOR REAL-TIME ANALYTICS
-- ============================================================================

-- Trigger to update customer analytics when jobs change
CREATE OR REPLACE FUNCTION update_customer_analytics_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Update customer analytics when job is completed
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        INSERT INTO customer_analytics (customer_id, total_jobs, total_revenue, last_service_date, updated_at)
        VALUES (NEW.customer_id, 1, COALESCE(NEW.total_amount, 0), NEW.completed_at::DATE, CURRENT_TIMESTAMP)
        ON CONFLICT (customer_id) DO UPDATE SET
            total_jobs = customer_analytics.total_jobs + 1,
            total_revenue = customer_analytics.total_revenue + COALESCE(NEW.total_amount, 0),
            last_service_date = NEW.completed_at::DATE,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to jobs table (if exists)
-- DROP TRIGGER IF EXISTS trigger_update_customer_analytics ON jobs;
-- CREATE TRIGGER trigger_update_customer_analytics
--     AFTER UPDATE ON jobs
--     FOR EACH ROW
--     EXECUTE FUNCTION update_customer_analytics_trigger();

-- ============================================================================
-- 📊 INITIAL DASHBOARD WIDGETS
-- ============================================================================

-- Executive Dashboard Widgets
INSERT INTO dashboard_widgets (widget_name, widget_type, dashboard_category, data_source, widget_config) VALUES
('Revenue Today', 'kpi', 'executive', 'executive_dashboard_summary', '{"color": "green", "icon": "dollar", "format": "currency"}'),
('Jobs Completed Today', 'kpi', 'executive', 'executive_dashboard_summary', '{"color": "blue", "icon": "check", "format": "number"}'),
('Customer Satisfaction', 'gauge', 'executive', 'executive_dashboard_summary', '{"min": 0, "max": 5, "color": "orange"}'),
('Revenue Trend', 'line_chart', 'executive', 'revenue_analytics', '{"timeframe": "30_days", "color": "green"}'),
('Performance Trends', 'multi_line_chart', 'executive', 'performance_trends_dashboard', '{"metrics": ["efficiency", "satisfaction"]}'),
('Customer Distribution', 'pie_chart', 'executive', 'customer_insights_dashboard', '{"field": "loyalty_tier"}');

-- Operations Dashboard Widgets
INSERT INTO dashboard_widgets (widget_name, widget_type, dashboard_category, data_source, widget_config) VALUES
('Active Jobs', 'kpi', 'operations', 'operational_analytics', '{"color": "blue", "icon": "wrench"}'),
('Response Time', 'kpi', 'operations', 'operational_analytics', '{"color": "yellow", "icon": "clock", "format": "time"}'),
('Technician Efficiency', 'gauge', 'operations', 'operational_analytics', '{"min": 0, "max": 100, "color": "blue"}'),
('First Time Fix Rate', 'gauge', 'operations', 'operational_analytics', '{"min": 0, "max": 100, "color": "green"}'),
('Job Status Distribution', 'pie_chart', 'operations', 'operational_analytics', '{"field": "job_status"}'),
('Daily Completion Trend', 'bar_chart', 'operations', 'operational_analytics', '{"timeframe": "14_days"}');

-- Customer Dashboard Widgets
INSERT INTO dashboard_widgets (widget_name, widget_type, dashboard_category, data_source, widget_config) VALUES
('Total Customers', 'kpi', 'customer', 'customer_analytics', '{"color": "purple", "icon": "users"}'),
('Average LTV', 'kpi', 'customer', 'customer_analytics', '{"color": "green", "icon": "dollar", "format": "currency"}'),
('Satisfaction Score', 'gauge', 'customer', 'customer_analytics', '{"min": 0, "max": 5, "color": "orange"}'),
('Loyalty Tiers', 'pie_chart', 'customer', 'customer_insights_dashboard', '{"field": "loyalty_tier"}'),
('Churn Risk Analysis', 'bar_chart', 'customer', 'customer_analytics', '{"field": "risk_score", "bins": 5}'),
('Customer Growth', 'line_chart', 'customer', 'customer_analytics', '{"timeframe": "12_months"}');

-- ============================================================================
-- 📊 SAMPLE DATA FOR TESTING
-- ============================================================================

-- Sample KPI data
INSERT INTO kpi_tracking (kpi_name, kpi_category, current_value, target_value, measurement_date) VALUES
('Daily Revenue', 'financial', 15000.00, 18000.00, CURRENT_DATE),
('Customer Satisfaction', 'customer', 4.2, 4.5, CURRENT_DATE),
('Technician Efficiency', 'operational', 87.5, 90.0, CURRENT_DATE),
('First Time Fix Rate', 'operational', 92.3, 95.0, CURRENT_DATE),
('Response Time (Hours)', 'operational', 2.1, 2.0, CURRENT_DATE);

-- Sample business metrics
INSERT INTO business_metrics (metric_name, metric_category, metric_value, metric_unit, time_period, period_start, period_end) VALUES
('Monthly Revenue', 'revenue', 450000.00, 'dollars', 'monthly', DATE_TRUNC('month', CURRENT_DATE), DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'),
('Customer Acquisition Rate', 'customer', 15.5, 'percentage', 'monthly', DATE_TRUNC('month', CURRENT_DATE), DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'),
('Average Job Value', 'revenue', 285.50, 'dollars', 'weekly', DATE_TRUNC('week', CURRENT_DATE), DATE_TRUNC('week', CURRENT_DATE) + INTERVAL '1 week' - INTERVAL '1 day'),
('Technician Utilization', 'operational', 78.2, 'percentage', 'daily', CURRENT_DATE, CURRENT_DATE);

COMMENT ON TABLE business_metrics IS '📊 Business Intelligence metrics for executive reporting';
COMMENT ON TABLE dashboard_widgets IS '📈 Dashboard widget configurations for real-time monitoring';
COMMENT ON TABLE kpi_tracking IS '📊 Key Performance Indicator tracking with alerts';
COMMENT ON TABLE performance_analytics IS '📈 Performance analytics for entities and processes';
COMMENT ON TABLE customer_analytics IS '📊 Customer analytics and insights';
COMMENT ON TABLE revenue_analytics IS '📈 Revenue analytics and financial metrics';
COMMENT ON TABLE operational_analytics IS '📊 Operational analytics and efficiency metrics';

-- ============================================================================
-- 📊 ANALYTICS SCHEMA COMPLETE
-- ============================================================================

SELECT '📊 Advanced Analytics Dashboard schema created successfully!' as status;