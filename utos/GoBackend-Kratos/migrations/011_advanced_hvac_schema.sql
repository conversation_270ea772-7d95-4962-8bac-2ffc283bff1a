-- Migration: 011_advanced_hvac_schema.sql
-- Description: Advanced HVAC CRM schema with missing tables from hvac-crm
-- Author: GoBackend HVAC Kratos Enhanced
-- Date: 2025-05-26

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 🏢 Suppliers Table
CREATE TABLE IF NOT EXISTS suppliers (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    website VARCHAR(255),
    tax_id VARCHAR(50),
    payment_terms INTEGER DEFAULT 30, -- days
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 📦 Inventory Table
CREATE TABLE IF NOT EXISTS inventory (
    id BIGSERIAL PRIMARY KEY,
    supplier_id BIGINT REFERENCES suppliers(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(100),
    unit_price DECIMAL(10,2),
    quantity_in_stock INTEGER DEFAULT 0,
    minimum_stock_level INTEGER DEFAULT 0,
    maximum_stock_level INTEGER DEFAULT 1000,
    unit_of_measure VARCHAR(50) DEFAULT 'piece',
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 💰 Offers Table
CREATE TABLE IF NOT EXISTS offers (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    offer_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    total_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft', -- draft, sent, accepted, rejected, expired
    valid_until DATE,
    created_by VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🛒 Sales Table
CREATE TABLE IF NOT EXISTS sales (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id) ON DELETE SET NULL,
    offer_id BIGINT REFERENCES offers(id) ON DELETE SET NULL,
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, partial, overdue
    payment_method VARCHAR(50),
    sale_date DATE DEFAULT CURRENT_DATE,
    due_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 📅 Calendar Events Table
CREATE TABLE IF NOT EXISTS calendar_events (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    location VARCHAR(255),
    event_type VARCHAR(50) DEFAULT 'appointment', -- appointment, maintenance, meeting, reminder
    customer_id BIGINT REFERENCES customers(id) ON DELETE SET NULL,
    job_id BIGINT REFERENCES jobs(id) ON DELETE SET NULL,
    technician_id VARCHAR(255), -- Reference to user/technician
    is_all_day BOOLEAN DEFAULT FALSE,
    recurrence_rule TEXT, -- RRULE for recurring events
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, completed, cancelled, rescheduled
    reminder_minutes INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 📎 Attachments Table
CREATE TABLE IF NOT EXISTS attachments (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL, -- customer, job, email, offer, sale
    entity_id BIGINT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    description TEXT,
    uploaded_by VARCHAR(255),
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🧠 Memory Bank Table (AI Integration)
CREATE TABLE IF NOT EXISTS memory_bank (
    id BIGSERIAL PRIMARY KEY,
    memory_type VARCHAR(50) NOT NULL, -- customer_insight, technical_note, business_rule, pattern
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    entity_type VARCHAR(50), -- customer, job, equipment, general
    entity_id BIGINT,
    tags TEXT[], -- Array of tags for categorization
    confidence_score DECIMAL(3,2) DEFAULT 0.5, -- 0.00 to 1.00
    is_verified BOOLEAN DEFAULT FALSE,
    source VARCHAR(100), -- ai_analysis, user_input, system_generated
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🔧 Equipment Table
CREATE TABLE IF NOT EXISTS equipment (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    equipment_type VARCHAR(100) NOT NULL, -- hvac_unit, furnace, ac_unit, heat_pump, etc.
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    installation_date DATE,
    warranty_expiry DATE,
    location_description TEXT,
    specifications JSONB,
    maintenance_schedule VARCHAR(50) DEFAULT 'annual', -- monthly, quarterly, annual, biannual
    last_service_date DATE,
    next_service_due DATE,
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, needs_replacement
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 📊 Service Reports Table
CREATE TABLE IF NOT EXISTS service_reports (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    equipment_id BIGINT REFERENCES equipment(id) ON DELETE SET NULL,
    technician_id VARCHAR(255) NOT NULL,
    report_type VARCHAR(50) DEFAULT 'maintenance', -- maintenance, repair, inspection, installation
    findings TEXT,
    work_performed TEXT,
    parts_used JSONB, -- Array of parts with quantities
    recommendations TEXT,
    before_photos TEXT[], -- Array of photo URLs
    after_photos TEXT[], -- Array of photo URLs
    customer_signature TEXT, -- Base64 encoded signature
    technician_signature TEXT, -- Base64 encoded signature
    completion_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_suppliers_is_active ON suppliers(is_active);

CREATE INDEX IF NOT EXISTS idx_inventory_sku ON inventory(sku);
CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category);
CREATE INDEX IF NOT EXISTS idx_inventory_supplier_id ON inventory(supplier_id);
CREATE INDEX IF NOT EXISTS idx_inventory_stock_level ON inventory(quantity_in_stock);

CREATE INDEX IF NOT EXISTS idx_offers_customer_id ON offers(customer_id);
CREATE INDEX IF NOT EXISTS idx_offers_status ON offers(status);
CREATE INDEX IF NOT EXISTS idx_offers_offer_number ON offers(offer_number);

CREATE INDEX IF NOT EXISTS idx_sales_customer_id ON sales(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_payment_status ON sales(payment_status);
CREATE INDEX IF NOT EXISTS idx_sales_sale_date ON sales(sale_date);

CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_calendar_events_customer_id ON calendar_events(customer_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_job_id ON calendar_events(job_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_technician_id ON calendar_events(technician_id);

CREATE INDEX IF NOT EXISTS idx_attachments_entity ON attachments(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_attachments_filename ON attachments(filename);

CREATE INDEX IF NOT EXISTS idx_memory_bank_entity ON memory_bank(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_memory_bank_type ON memory_bank(memory_type);
CREATE INDEX IF NOT EXISTS idx_memory_bank_tags ON memory_bank USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_equipment_customer_id ON equipment(customer_id);
CREATE INDEX IF NOT EXISTS idx_equipment_type ON equipment(equipment_type);
CREATE INDEX IF NOT EXISTS idx_equipment_next_service ON equipment(next_service_due);

CREATE INDEX IF NOT EXISTS idx_service_reports_job_id ON service_reports(job_id);
CREATE INDEX IF NOT EXISTS idx_service_reports_equipment_id ON service_reports(equipment_id);
CREATE INDEX IF NOT EXISTS idx_service_reports_technician_id ON service_reports(technician_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offers_updated_at BEFORE UPDATE ON offers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_calendar_events_updated_at BEFORE UPDATE ON calendar_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_memory_bank_updated_at BEFORE UPDATE ON memory_bank FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE suppliers IS 'HVAC equipment and parts suppliers';
COMMENT ON TABLE inventory IS 'Inventory management for HVAC parts and equipment';
COMMENT ON TABLE offers IS 'Customer quotes and offers';
COMMENT ON TABLE sales IS 'Completed sales and invoicing';
COMMENT ON TABLE calendar_events IS 'Scheduling and calendar management';
COMMENT ON TABLE attachments IS 'File attachments for various entities';
COMMENT ON TABLE memory_bank IS 'AI-powered knowledge and insights storage';
COMMENT ON TABLE equipment IS 'Customer HVAC equipment tracking';
COMMENT ON TABLE service_reports IS 'Detailed service and maintenance reports';

-- Insert sample data for testing and demonstration

-- Sample suppliers
INSERT INTO suppliers (name, contact_person, email, phone, address, website, payment_terms) VALUES
('HVAC Supply Co.', 'John Smith', '<EMAIL>', '******-0101', '123 Industrial Ave, City, State 12345', 'https://hvacsupply.com', 30),
('Climate Control Parts', 'Sarah Johnson', '<EMAIL>', '******-0102', '456 Commerce St, City, State 12346', 'https://climateparts.com', 15),
('Professional HVAC Solutions', 'Mike Wilson', '<EMAIL>', '******-0103', '789 Business Blvd, City, State 12347', 'https://prohvac.com', 45)
ON CONFLICT DO NOTHING;

-- Sample inventory items
INSERT INTO inventory (supplier_id, name, description, sku, category, unit_price, quantity_in_stock, minimum_stock_level, unit_of_measure) VALUES
(1, 'Air Filter 20x25x1', 'High-efficiency pleated air filter', 'AF-20251-HE', 'Filters', 12.99, 50, 10, 'piece'),
(1, 'Refrigerant R-410A', 'R-410A refrigerant 25lb cylinder', 'REF-410A-25', 'Refrigerants', 89.99, 8, 3, 'cylinder'),
(2, 'Thermostat Digital', 'Programmable digital thermostat', 'THERM-DIG-PRO', 'Controls', 149.99, 15, 5, 'piece'),
(2, 'Condenser Fan Motor', '1/4 HP condenser fan motor', 'MOTOR-COND-25', 'Motors', 189.99, 6, 2, 'piece'),
(3, 'Ductwork Flexible 6"', 'Flexible ductwork 6 inch diameter', 'DUCT-FLEX-6', 'Ductwork', 4.99, 200, 50, 'foot')
ON CONFLICT (sku) DO NOTHING;

-- Sample equipment for existing customers
INSERT INTO equipment (customer_id, equipment_type, brand, model, serial_number, installation_date, location_description, maintenance_schedule) VALUES
(1, 'Central Air Unit', 'Carrier', 'CA-2500', 'CA2500-2023-001', '2023-05-15', 'Backyard, east side of house', 'annual'),
(1, 'Gas Furnace', 'Trane', 'XR95', 'TR-XR95-2022-045', '2022-11-20', 'Basement utility room', 'annual'),
(2, 'Heat Pump', 'Lennox', 'HP-Elite', 'LX-HPE-2024-012', '2024-01-10', 'Side yard, north wall', 'biannual'),
(3, 'Commercial HVAC Unit', 'York', 'YC-5000', 'YK-5000-2021-089', '2021-08-30', 'Rooftop unit #1', 'quarterly')
ON CONFLICT DO NOTHING;

-- Sample memory bank entries (AI insights)
INSERT INTO memory_bank (memory_type, title, content, entity_type, entity_id, tags, confidence_score, source) VALUES
('customer_insight', 'Preferred Service Time', 'Customer prefers morning appointments between 8-10 AM', 'customer', 1, ARRAY['scheduling', 'preference'], 0.95, 'ai_analysis'),
('technical_note', 'Recurring Filter Issue', 'This customer location has high dust levels, recommend premium filters', 'customer', 1, ARRAY['maintenance', 'filters'], 0.88, 'technician_input'),
('business_rule', 'Emergency Response Protocol', 'For commercial customers, response time must be within 2 hours', 'general', NULL, ARRAY['emergency', 'commercial'], 1.0, 'system_generated'),
('pattern', 'Seasonal Maintenance Trend', 'AC maintenance requests peak in April-May before summer season', 'general', NULL, ARRAY['seasonal', 'maintenance'], 0.92, 'ai_analysis')
ON CONFLICT DO NOTHING;
