# 🔐 Ory Kratos Setup Guide - HVAC CRM Authentication

## 🎯 Overview

This guide helps you set up and troubleshoot Ory Kratos authentication for the HVAC CRM system.

## ❌ Common Error Fix

**Error**: `open /etc/config/kratos/kratos.yml: no such file or directory`

**Solution**: The Docker volume mapping was incorrect. Fixed by updating `docker-compose.yml`:

```yaml
# BEFORE (incorrect):
volumes:
  - kratos_config:/etc/config/kratos

# AFTER (correct):
volumes:
  - ./kratos_config:/etc/config/kratos:ro
```

## 🚀 Quick Setup

### 1. Initialize Kratos Database
```bash
# Run database migrations
./scripts/init-kratos.sh
```

### 2. Start Services
```bash
# Start all services including Kratos
docker-compose up -d

# Check Kratos status
curl http://localhost:4434/health/ready
```

### 3. Verify Setup
```bash
# Check Kratos admin API
curl http://localhost:4434/admin/identities

# Check Kratos public API
curl http://localhost:4433/.well-known/ory/kratos/public/schemas
```

## 📁 Configuration Files

### Kratos Configuration (`kratos_config/kratos.yml`)
- **Version**: v1.3.1 (updated from v0.11.0)
- **Database**: External PostgreSQL at **************:5432
- **Email**: SMTP configuration for notifications
- **CORS**: Enabled for frontend integration
- **Sessions**: 24-hour lifespan with persistent cookies

### Identity Schema (`kratos_config/identity.schema.json`)
- **Email-based authentication**
- **HVAC-specific user fields**:
  - Company name
  - Phone number
  - User role (admin, technician, customer, manager)
  - HVAC preferences (service area, contact method, system type)

## 🔧 Service Configuration

### Docker Compose Services
```yaml
kratos:
  image: oryd/kratos:latest
  ports:
    - "4433:4433" # Public API
    - "4434:4434" # Admin API
  volumes:
    - ./kratos_config:/etc/config/kratos:ro
  command: serve -c /etc/config/kratos/kratos.yml --dev --watch-courier
```

### Environment Variables
- `DSN`: PostgreSQL connection string
- `KRATOS_PUBLIC_URL`: http://localhost:4433
- `KRATOS_ADMIN_URL`: http://localhost:4434
- `LOG_LEVEL`: debug (for development)

## 🛠 Troubleshooting

### Issue 1: Configuration File Not Found
**Error**: `open /etc/config/kratos/kratos.yml: no such file or directory`

**Solutions**:
1. Check volume mapping in `docker-compose.yml`
2. Ensure `kratos_config/kratos.yml` exists locally
3. Verify file permissions (should be readable)

### Issue 2: Database Connection Failed
**Error**: Database connection errors

**Solutions**:
1. Verify PostgreSQL server is accessible
2. Check database credentials in `kratos.yml`
3. Run database migrations: `./scripts/init-kratos.sh`

### Issue 3: SMTP Configuration Issues
**Error**: Email sending failures

**Solutions**:
1. Verify SMTP credentials in `kratos.yml`
2. Test SMTP connection manually
3. Check firewall/network connectivity

## 📊 API Endpoints

### Admin API (Port 4434)
- `GET /admin/identities` - List all identities
- `POST /admin/identities` - Create new identity
- `GET /health/ready` - Health check
- `GET /health/alive` - Liveness check

### Public API (Port 4433)
- `GET /.well-known/ory/kratos/public/schemas` - Identity schemas
- `POST /self-service/registration/flows` - Registration flow
- `POST /self-service/login/flows` - Login flow
- `GET /sessions/whoami` - Current session info

## 🔐 Security Configuration

### Secrets (Development Only)
```yaml
secrets:
  cookie:
    - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
  cipher:
    - 32-LONG-SECRET-NOT-SECURE-AT-ALL
```

**⚠️ Important**: Change these secrets in production!

### Session Management
- **Lifespan**: 24 hours
- **Cookie Domain**: localhost (development)
- **Same Site**: Lax
- **Persistent**: true

## 🎯 HVAC-Specific Features

### User Roles
- **admin**: Full system access
- **manager**: Management dashboard access
- **technician**: Field service access
- **customer**: Customer portal access

### HVAC Preferences
- **Service Area**: Geographic coverage
- **Contact Method**: Email, phone, or SMS
- **System Type**: Residential, commercial, or industrial

## 🚀 Integration with HVAC Services

### Octopus Interface Integration
```go
// Example: Get current user session
func (o *MorphicOctopusInterface) getCurrentUser(r *http.Request) (*User, error) {
    sessionCookie := r.Header.Get("Cookie")
    // Validate session with Kratos
    resp, err := http.Get("http://localhost:4433/sessions/whoami")
    // Parse user identity
}
```

### Frontend Integration
```javascript
// Check authentication status
async function checkAuth() {
    const response = await fetch('http://localhost:4433/sessions/whoami', {
        credentials: 'include'
    });
    return response.ok;
}
```

## 📋 Maintenance Tasks

### Regular Tasks
1. **Monitor logs**: `docker-compose logs kratos`
2. **Check health**: `curl http://localhost:4434/health/ready`
3. **Backup identities**: Export via Admin API
4. **Update secrets**: Rotate secrets regularly

### Database Maintenance
1. **Run migrations**: When updating Kratos version
2. **Monitor connections**: Check PostgreSQL connection pool
3. **Clean sessions**: Remove expired sessions periodically

## 🎉 Success Verification

### Checklist
- [ ] Kratos container starts without errors
- [ ] Configuration file is loaded successfully
- [ ] Database migrations completed
- [ ] Admin API responds to health checks
- [ ] Public API serves identity schemas
- [ ] SMTP configuration works for emails
- [ ] Frontend can authenticate users

### Test Commands
```bash
# Health check
curl http://localhost:4434/health/ready

# List schemas
curl http://localhost:4433/.well-known/ory/kratos/public/schemas

# Create test identity (Admin API)
curl -X POST http://localhost:4434/admin/identities \
  -H "Content-Type: application/json" \
  -d '{
    "schema_id": "default",
    "traits": {
      "email": "<EMAIL>",
      "name": {"first": "Test", "last": "User"},
      "role": "customer"
    }
  }'
```

---

## 🎯 Summary

The Kratos setup is now properly configured for the HVAC CRM system with:
- ✅ Correct Docker volume mapping
- ✅ Updated configuration (v1.3.1)
- ✅ HVAC-specific identity schema
- ✅ Email integration
- ✅ Database migrations
- ✅ Comprehensive troubleshooting guide

🔐 **Kratos Authentication - Ready for HVAC CRM!** 🚀