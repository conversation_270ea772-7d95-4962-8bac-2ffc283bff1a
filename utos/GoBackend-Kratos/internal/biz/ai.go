package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

// AIModel represents an AI model configuration
type AIModel struct {
	Name      string `json:"name"`
	Type      string `json:"type"`
	Available bool   `json:"available"`
	Endpoint  string `json:"endpoint"`
}

// ChatRequest represents a chat request
type ChatRequest struct {
	Message string   `json:"message"`
	Model   string   `json:"model"`
	Context []string `json:"context"`
}

// ChatResponse represents a chat response
type ChatResponse struct {
	Response   string `json:"response"`
	ModelUsed  string `json:"model_used"`
	TokensUsed int32  `json:"tokens_used"`
}

// AnalyzeRequest represents an analysis request
type AnalyzeRequest struct {
	Content      string `json:"content"`
	AnalysisType string `json:"analysis_type"`
	Model        string `json:"model"`
}

// AnalyzeResponse represents an analysis response
type AnalyzeResponse struct {
	Analysis   string            `json:"analysis"`
	Confidence float32           `json:"confidence"`
	Metadata   map[string]string `json:"metadata"`
}

// AIRepo defines the interface for AI operations
type AIRepo interface {
	Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
	Analyze(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error)
	ListModels(ctx context.Context) ([]*AIModel, error)
	IsModelAvailable(ctx context.Context, modelName string) (bool, error)
}

// AIUsecase encapsulates AI business logic
type AIUsecase struct {
	repo AIRepo
	log  *log.Helper
}// NewAIUsecase creates a new AI usecase
func NewAIUsecase(repo AIRepo, logger log.Logger) *AIUsecase {
	return &AIUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// Chat processes a chat request with validation
func (uc *AIUsecase) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	uc.log.WithContext(ctx).Infof("Processing chat request with model: %s", req.Model)
	
	// Validate request
	if req.Message == "" {
		return nil, ErrInvalidAIRequest
	}
	
	// Set default model if not specified
	if req.Model == "" {
		req.Model = "gemma-3-4b-it"
	}
	
	// Check if model is available
	available, err := uc.repo.IsModelAvailable(ctx, req.Model)
	if err != nil {
		return nil, err
	}
	if !available {
		return nil, ErrAIModelUnavailable
	}
	
	return uc.repo.Chat(ctx, req)
}

// Analyze processes an analysis request
func (uc *AIUsecase) Analyze(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error) {
	uc.log.WithContext(ctx).Infof("Processing analysis request: %s", req.AnalysisType)
	
	// Validate request
	if req.Content == "" {
		return nil, ErrInvalidAIRequest
	}
	if req.AnalysisType == "" {
		req.AnalysisType = "general"
	}
	
	// Set default model
	if req.Model == "" {
		req.Model = "bielik-v3"
	}
	
	return uc.repo.Analyze(ctx, req)
}

// ListModels returns available AI models
func (uc *AIUsecase) ListModels(ctx context.Context) ([]*AIModel, error) {
	uc.log.WithContext(ctx).Info("Listing available AI models")
	return uc.repo.ListModels(ctx)
}