package biz

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAnalyticsRepo is a mock implementation of AnalyticsRepo
type MockAnalyticsRepo struct {
	mock.Mock
}

func (m *MockAnalyticsRepo) GetExecutiveDashboard(ctx context.Context) (*ExecutiveDashboardSummary, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*ExecutiveDashboardSummary), args.Error(1)
}

func (m *MockAnalyticsRepo) GetCustomerInsights(ctx context.Context) ([]*CustomerInsight, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*CustomerInsight), args.Error(1)
}

func (m *MockAnalyticsRepo) GetOperationalAnalytics(ctx context.Context, date time.Time) (*OperationalAnalytics, error) {
	args := m.Called(ctx, date)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*OperationalAnalytics), args.Error(1)
}

func (m *MockAnalyticsRepo) GetPerformanceTrends(ctx context.Context, weeks int) ([]*PerformanceTrend, error) {
	args := m.Called(ctx, weeks)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*PerformanceTrend), args.Error(1)
}

func (m *MockAnalyticsRepo) GetKPIs(ctx context.Context, category string) ([]*KPI, error) {
	args := m.Called(ctx, category)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*KPI), args.Error(1)
}

func (m *MockAnalyticsRepo) UpdateKPI(ctx context.Context, kpi *KPI) error {
	args := m.Called(ctx, kpi)
	return args.Error(0)
}

func (m *MockAnalyticsRepo) GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockAnalyticsRepo) GetDashboardWidgets(ctx context.Context, category string) ([]*DashboardWidget, error) {
	args := m.Called(ctx, category)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*DashboardWidget), args.Error(1)
}

func (m *MockAnalyticsRepo) CreateDashboardWidget(ctx context.Context, widget *DashboardWidget) error {
	args := m.Called(ctx, widget)
	return args.Error(0)
}

func (m *MockAnalyticsRepo) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	args := m.Called(ctx, customerID)
	return args.Error(0)
}

func (m *MockAnalyticsRepo) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	args := m.Called(ctx, date, category, revenue, jobsCount)
	return args.Error(0)
}

func (m *MockAnalyticsRepo) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *OperationalAnalytics) error {
	args := m.Called(ctx, date, data)
	return args.Error(0)
}

func TestNewAnalyticsUsecase(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestGetExecutiveDashboard(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedSummary := &ExecutiveDashboardSummary{Period: "Today", TodayRevenue: 1000.0}
	mockRepo.On("GetExecutiveDashboard", ctx).Return(expectedSummary, nil).Once()
	summary, err := uc.GetExecutiveDashboard(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedSummary, summary)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetExecutiveDashboard", ctx).Return(nil, errors.New("db error")).Once()
	summary, err = uc.GetExecutiveDashboard(ctx)
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetCustomerInsights(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedInsights := []*CustomerInsight{{LoyaltyTier: "Gold", CustomerCount: 100}}
	mockRepo.On("GetCustomerInsights", ctx).Return(expectedInsights, nil).Once()
	insights, err := uc.GetCustomerInsights(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedInsights, insights)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetCustomerInsights", ctx).Return(nil, errors.New("db error")).Once()
	insights, err = uc.GetCustomerInsights(ctx)
	assert.Error(t, err)
	assert.Nil(t, insights)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetOperationalDashboard(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedOperational := &OperationalAnalytics{TotalActiveJobs: 50}
	mockRepo.On("GetOperationalAnalytics", ctx, mock.AnythingOfType("time.Time")).Return(expectedOperational, nil).Once()
	operational, err := uc.GetOperationalDashboard(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedOperational, operational)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetOperationalAnalytics", ctx, mock.AnythingOfType("time.Time")).Return(nil, errors.New("db error")).Once()
	operational, err = uc.GetOperationalDashboard(ctx)
	assert.Error(t, err)
	assert.Nil(t, operational)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetPerformanceTrends(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	weeks := 4

	// Success case
	expectedTrends := []*PerformanceTrend{{WeekStart: time.Now(), AvgEfficiency: 0.8}}
	mockRepo.On("GetPerformanceTrends", ctx, weeks).Return(expectedTrends, nil).Once()
	trends, err := uc.GetPerformanceTrends(ctx, weeks)
	assert.NoError(t, err)
	assert.Equal(t, expectedTrends, trends)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetPerformanceTrends", ctx, weeks).Return(nil, errors.New("db error")).Once()
	trends, err = uc.GetPerformanceTrends(ctx, weeks)
	assert.Error(t, err)
	assert.Nil(t, trends)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetKPIs(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	category := "Sales"

	// Success case
	expectedKPIs := []*KPI{{KPIName: "Revenue", CurrentValue: 10000}}
	mockRepo.On("GetKPIs", ctx, category).Return(expectedKPIs, nil).Once()
	kpis, err := uc.GetKPIs(ctx, category)
	assert.NoError(t, err)
	assert.Equal(t, expectedKPIs, kpis)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetKPIs", ctx, category).Return(nil, errors.New("db error")).Once()
	kpis, err = uc.GetKPIs(ctx, category)
	assert.Error(t, err)
	assert.Nil(t, kpis)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestUpdateKPI(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	kpiName := "Revenue"
	category := "Sales"
	value := 12000.0
	target := 15000.0

	// Success case
	mockRepo.On("UpdateKPI", ctx, mock.AnythingOfType("*biz.KPI")).Return(nil).Once()
	err := uc.UpdateKPI(ctx, kpiName, category, value, &target)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("UpdateKPI", ctx, mock.AnythingOfType("*biz.KPI")).Return(errors.New("db error")).Once()
	err = uc.UpdateKPI(ctx, kpiName, category, value, &target)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetRealTimeMetrics(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedMetrics := map[string]interface{}{"cpu_usage": 0.5, "memory_usage": 0.7}
	mockRepo.On("GetRealTimeMetrics", ctx).Return(expectedMetrics, nil).Once()
	metrics, err := uc.GetRealTimeMetrics(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedMetrics, metrics)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetRealTimeMetrics", ctx).Return(nil, errors.New("db error")).Once()
	metrics, err = uc.GetRealTimeMetrics(ctx)
	assert.Error(t, err)
	assert.Nil(t, metrics)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetDashboardWidgets(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	category := "Sales"

	// Success case
	expectedWidgets := []*DashboardWidget{{WidgetName: "Sales Chart", WidgetType: "chart"}}
	mockRepo.On("GetDashboardWidgets", ctx, category).Return(expectedWidgets, nil).Once()
	widgets, err := uc.GetDashboardWidgets(ctx, category)
	assert.NoError(t, err)
	assert.Equal(t, expectedWidgets, widgets)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("GetDashboardWidgets", ctx, category).Return(nil, errors.New("db error")).Once()
	widgets, err = uc.GetDashboardWidgets(ctx, category)
	assert.Error(t, err)
	assert.Nil(t, widgets)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestCreateDashboardWidget(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	widget := &DashboardWidget{WidgetName: "New Widget", WidgetType: "table"}

	// Success case
	mockRepo.On("CreateDashboardWidget", ctx, mock.AnythingOfType("*biz.DashboardWidget")).Return(nil).Once()
	err := uc.CreateDashboardWidget(ctx, widget)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("CreateDashboardWidget", ctx, mock.AnythingOfType("*biz.DashboardWidget")).Return(errors.New("db error")).Once()
	err = uc.CreateDashboardWidget(ctx, widget)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestCalculateCustomerAnalytics(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	customerID := uint(123)

	// Success case
	mockRepo.On("CalculateCustomerAnalytics", ctx, customerID).Return(nil).Once()
	err := uc.CalculateCustomerAnalytics(ctx, customerID)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("CalculateCustomerAnalytics", ctx, customerID).Return(errors.New("db error")).Once()
	err = uc.CalculateCustomerAnalytics(ctx, customerID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestUpdateRevenueAnalytics(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	date := time.Now()
	category := "Service"
	revenue := 500.0
	jobsCount := 5

	// Success case
	mockRepo.On("UpdateRevenueAnalytics", ctx, date, category, revenue, jobsCount).Return(nil).Once()
	err := uc.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("UpdateRevenueAnalytics", ctx, date, category, revenue, jobsCount).Return(errors.New("db error")).Once()
	err = uc.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestUpdateOperationalAnalytics(t *testing.T) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	date := time.Now()
	data := &OperationalAnalytics{TotalActiveJobs: 10}

	// Success case
	mockRepo.On("UpdateOperationalAnalytics", ctx, date, data).Return(nil).Once()
	err := uc.UpdateOperationalAnalytics(ctx, date, data)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case
	mockRepo.On("UpdateOperationalAnalytics", ctx, date, data).Return(errors.New("db error")).Once()
	err = uc.UpdateOperationalAnalytics(ctx, date, data)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

// Benchmarks
func BenchmarkGetExecutiveDashboard(b *testing.B) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	expectedSummary := &ExecutiveDashboardSummary{Period: "Today", TodayRevenue: 1000.0}
	mockRepo.On("GetExecutiveDashboard", ctx).Return(expectedSummary, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.GetExecutiveDashboard(ctx)
	}
}

func BenchmarkCalculateCustomerAnalytics(b *testing.B) {
	mockRepo := new(MockAnalyticsRepo)
	uc := NewAnalyticsUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	customerID := uint(123)

	mockRepo.On("CalculateCustomerAnalytics", ctx, customerID).Return(nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = uc.CalculateCustomerAnalytics(ctx, customerID)
	}
}
