package octopus

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// 🧠 LangChain Management Handlers

// handleLangChainMetrics returns current LangChain performance metrics
func (o *MorphicOctopusInterface) handleLangChainMetrics(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	metrics, err := o.buildLangChainMetrics(ctx)
	if err != nil {
		http.Error(w, "Failed to get LangChain metrics", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// handleLangChainChains returns available chains and their status
func (o *MorphicOctopusInterface) handleLangChainChains(w http.ResponseWriter, r *http.Request) {
	chains := []map[string]interface{}{
		{
			"name":        "customer_analysis",
			"description": "Analyzes customer data and provides insights",
			"status":      "active",
			"executions":  1250,
			"avg_time":    "2.5s",
			"success_rate": 94.8,
			"last_used":   time.Now().Add(-5 * time.Minute),
		},
		{
			"name":        "maintenance_planning",
			"description": "Creates maintenance schedules and plans",
			"status":      "active",
			"executions":  890,
			"avg_time":    "3.2s",
			"success_rate": 96.2,
			"last_used":   time.Now().Add(-2 * time.Minute),
		},
		{
			"name":        "troubleshooting",
			"description": "Provides step-by-step troubleshooting guides",
			"status":      "active",
			"executions":  2340,
			"avg_time":    "1.8s",
			"success_rate": 92.5,
			"last_used":   time.Now().Add(-1 * time.Minute),
		},
		{
			"name":        "quote_generation",
			"description": "Generates professional service quotes",
			"status":      "active",
			"executions":  567,
			"avg_time":    "4.1s",
			"success_rate": 97.3,
			"last_used":   time.Now().Add(-10 * time.Minute),
		},
		{
			"name":        "email_triage",
			"description": "Analyzes and categorizes incoming emails",
			"status":      "active",
			"executions":  3420,
			"avg_time":    "0.8s",
			"success_rate": 98.1,
			"last_used":   time.Now().Add(-30 * time.Second),
		},
		{
			"name":        "call_analysis",
			"description": "Analyzes customer call transcriptions",
			"status":      "active",
			"executions":  1890,
			"avg_time":    "2.1s",
			"success_rate": 95.7,
			"last_used":   time.Now().Add(-3 * time.Minute),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"chains": chains,
		"total_chains": len(chains),
		"active_chains": len(chains),
		"timestamp": time.Now(),
	})
}

// handleLangChainExecute executes a specific chain
func (o *MorphicOctopusInterface) handleLangChainExecute(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	chainName := vars["chain"]

	var executeRequest struct {
		Input   map[string]interface{} `json:"input"`
		Options map[string]interface{} `json:"options,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&executeRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	o.log.Infof("🧠 LangChain execution requested: chain=%s", chainName)

	// Mock execution result
	result := map[string]interface{}{
		"execution_id": "exec_" + time.Now().Format("20060102_150405"),
		"chain":        chainName,
		"status":       "completed",
		"input":        executeRequest.Input,
		"output": map[string]interface{}{
			"result":     "Chain execution completed successfully",
			"confidence": 0.94,
			"metadata": map[string]interface{}{
				"tokens_used": 1250,
				"model":       "gemma-3-4b-it",
				"duration":    "2.3s",
			},
		},
		"started_at":  time.Now().Add(-3 * time.Second),
		"completed_at": time.Now(),
		"duration":    "2.3s",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// handleLangChainHistory returns execution history
func (o *MorphicOctopusInterface) handleLangChainHistory(w http.ResponseWriter, r *http.Request) {
	// Mock execution history
	history := []map[string]interface{}{
		{
			"execution_id": "exec_20241226_143022",
			"chain":        "customer_analysis",
			"status":       "completed",
			"duration":     "2.1s",
			"tokens_used":  890,
			"success":      true,
			"timestamp":    time.Now().Add(-5 * time.Minute),
		},
		{
			"execution_id": "exec_20241226_142845",
			"chain":        "email_triage",
			"status":       "completed",
			"duration":     "0.7s",
			"tokens_used":  234,
			"success":      true,
			"timestamp":    time.Now().Add(-8 * time.Minute),
		},
		{
			"execution_id": "exec_20241226_142630",
			"chain":        "troubleshooting",
			"status":       "failed",
			"duration":     "1.2s",
			"tokens_used":  456,
			"success":      false,
			"error":        "Model timeout",
			"timestamp":    time.Now().Add(-12 * time.Minute),
		},
		{
			"execution_id": "exec_20241226_142415",
			"chain":        "quote_generation",
			"status":       "completed",
			"duration":     "4.5s",
			"tokens_used":  1890,
			"success":      true,
			"timestamp":    time.Now().Add(-15 * time.Minute),
		},
	}

	response := map[string]interface{}{
		"history":      history,
		"total_count":  len(history),
		"success_rate": 75.0, // 3 out of 4 successful
		"avg_duration": "2.1s",
		"timestamp":    time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleLangChainConfig returns and updates LangChain configuration
func (o *MorphicOctopusInterface) handleLangChainConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		config := map[string]interface{}{
			"model_url":     "http://localhost:1234/v1",
			"model_name":    "gemma-3-4b-it-qat",
			"max_tokens":    4096,
			"temperature":   0.7,
			"timeout":       "30s",
			"enabled_chains": []string{
				"customer_analysis",
				"maintenance_planning",
				"troubleshooting",
				"quote_generation",
				"email_triage",
				"call_analysis",
			},
			"performance": map[string]interface{}{
				"max_concurrent_executions": 10,
				"queue_size":                100,
				"retry_attempts":            3,
				"cache_enabled":             true,
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(config)

	} else if r.Method == "PUT" {
		var configUpdate map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&configUpdate); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		o.log.Infof("🧠 LangChain config update requested: %v", configUpdate)

		response := map[string]interface{}{
			"status":    "updated",
			"message":   "LangChain configuration updated successfully",
			"timestamp": time.Now(),
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}

// handleLangChainHealth returns LangChain service health
func (o *MorphicOctopusInterface) handleLangChainHealth(w http.ResponseWriter, r *http.Request) {
	health := map[string]interface{}{
		"status":           "healthy",
		"model_status":     "active",
		"chains_available": 6,
		"chains_active":    6,
		"queue_length":     3,
		"avg_response_time": "2.1s",
		"uptime":          "2h 45m",
		"memory_usage":    "1.2GB",
		"cpu_usage":       "15.3%",
		"last_check":      time.Now(),
		"version":         "v1.0.0",
		"dependencies": map[string]string{
			"gemma_model": "healthy",
			"vector_db":   "healthy",
			"cache":       "healthy",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(health)
}

// setupLangChainRoutes sets up LangChain-specific routes
func (o *MorphicOctopusInterface) setupLangChainRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/langchain").Subrouter()

	api.HandleFunc("/metrics", o.handleLangChainMetrics).Methods("GET")
	api.HandleFunc("/chains", o.handleLangChainChains).Methods("GET")
	api.HandleFunc("/chains/{chain}/execute", o.handleLangChainExecute).Methods("POST")
	api.HandleFunc("/history", o.handleLangChainHistory).Methods("GET")
	api.HandleFunc("/config", o.handleLangChainConfig).Methods("GET", "PUT")
	api.HandleFunc("/health", o.handleLangChainHealth).Methods("GET")
}
