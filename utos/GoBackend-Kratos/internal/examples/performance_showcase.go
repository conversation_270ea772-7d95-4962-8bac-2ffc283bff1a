package examples

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/common"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/service"
)

// 🚀 Performance Showcase - Demonstrating Go 1.23 Advanced Features
// Real-world examples of enhanced performance patterns

// ==========================================
// PERFORMANCE SHOWCASE RUNNER
// ==========================================

// PerformanceShowcase demonstrates the enhanced capabilities
type PerformanceShowcase struct {
	log                *log.Helper
	enhancedService    *service.EnhancedCustomerService
	workerPools        *common.HVACWorkerPools
	memoryMonitor      *common.MemoryMonitor
	perfMonitor        *common.PerformanceMonitor
	objectPools        *common.HVACObjectPools
}

// NewPerformanceShowcase creates a new performance showcase
func NewPerformanceShowcase(logger log.Logger) *PerformanceShowcase {
	return &PerformanceShowcase{
		log: log.NewHelper(logger),
	}
}

// RunShowcase runs all performance demonstrations
func (ps *PerformanceShowcase) RunShowcase(ctx context.Context) error {
	ps.log.Info("🚀 Starting GoBackend-Kratos Performance Showcase")

	// Initialize components
	if err := ps.initializeComponents(ctx); err != nil {
		return fmt.Errorf("failed to initialize components: %w", err)
	}

	// Run demonstrations
	demos := []struct {
		name string
		fn   func(context.Context) error
	}{
		{"Memory Optimization Demo", ps.memoryOptimizationDemo},
		{"Concurrency Patterns Demo", ps.concurrencyPatternsDemo},
		{"Generic Repository Demo", ps.genericRepositoryDemo},
		{"Performance Monitoring Demo", ps.performanceMonitoringDemo},
		{"Bulk Operations Demo", ps.bulkOperationsDemo},
		{"Cache Performance Demo", ps.cachePerformanceDemo},
	}

	for _, demo := range demos {
		ps.log.Infof("🎯 Running: %s", demo.name)
		start := time.Now()
		
		if err := demo.fn(ctx); err != nil {
			ps.log.Errorf("❌ %s failed: %v", demo.name, err)
			continue
		}
		
		duration := time.Since(start)
		ps.log.Infof("✅ %s completed in %v", demo.name, duration)
	}

	// Cleanup
	ps.cleanup()

	ps.log.Info("🎉 Performance Showcase completed successfully!")
	return nil
}

// ==========================================
// INITIALIZATION
// ==========================================

func (ps *PerformanceShowcase) initializeComponents(ctx context.Context) error {
	// Initialize worker pools
	ps.workerPools = common.NewHVACWorkerPools(ps.log.Logger)
	ps.workerPools.Start()

	// Initialize memory monitor
	ps.memoryMonitor = common.NewMemoryMonitor(
		ps.log.Logger,
		256*1024*1024, // 256MB threshold
		10*time.Second, // Monitor every 10 seconds
	)
	ps.memoryMonitor.Start(ctx)

	// Initialize performance monitor
	ps.perfMonitor = common.NewPerformanceMonitor(ps.log.Logger, &common.MonitorConfig{
		EnableProfiling: true,
		EnableMetrics:   true,
		EnableAlerts:    true,
		ProfilerPort:    6062, // Showcase profiler port
	})
	if err := ps.perfMonitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start performance monitor: %w", err)
	}

	// Initialize object pools
	ps.objectPools = common.NewHVACObjectPools(ps.log.Logger)

	ps.log.Info("✅ All components initialized successfully")
	return nil
}

// ==========================================
// DEMONSTRATION FUNCTIONS
// ==========================================

// memoryOptimizationDemo demonstrates memory optimization patterns
func (ps *PerformanceShowcase) memoryOptimizationDemo(ctx context.Context) error {
	ps.log.Info("📊 Memory Optimization Demo - Object Pooling & Buffer Reuse")

	// Get initial memory stats
	allocBefore, sysBefore := common.GetMemoryUsage()
	ps.log.Infof("Memory before: Alloc=%dMB, Sys=%dMB", allocBefore, sysBefore)

	// Simulate high-frequency operations using object pools
	const iterations = 10000
	
	start := time.Now()
	for i := 0; i < iterations; i++ {
		// Use email buffer from pool
		emailBuf := ps.objectPools.EmailPool.Get()
		emailBuf.Subject = fmt.Sprintf("Test Email %d", i)
		emailBuf.Body = "This is a test email body with some content"
		emailBuf.From = "<EMAIL>"
		emailBuf.To = append(emailBuf.To, "<EMAIL>")
		
		// Simulate processing
		_ = len(emailBuf.Subject) + len(emailBuf.Body)
		
		// Return to pool
		emailBuf.Reset()
		ps.objectPools.EmailPool.Put(emailBuf)

		// Use JSON buffer from pool
		jsonBuf := ps.objectPools.BufferPool.Get()
		jsonBuf.WriteString(`{"test": "data"}`)
		
		// Simulate JSON processing
		_ = jsonBuf.Len()
		
		// Return to pool
		ps.objectPools.BufferPool.Put(jsonBuf)
	}
	duration := time.Since(start)

	// Get final memory stats
	allocAfter, sysAfter := common.GetMemoryUsage()
	ps.log.Infof("Memory after: Alloc=%dMB, Sys=%dMB", allocAfter, sysAfter)
	ps.log.Infof("Processed %d operations in %v (%.2f ops/sec)", 
		iterations, duration, float64(iterations)/duration.Seconds())

	// Trigger memory optimization
	common.OptimizeMemoryUsage(ps.log.Logger)

	return nil
}

// concurrencyPatternsDemo demonstrates advanced concurrency patterns
func (ps *PerformanceShowcase) concurrencyPatternsDemo(ctx context.Context) error {
	ps.log.Info("⚡ Concurrency Patterns Demo - Worker Pools & Pipelines")

	// Test worker pool performance
	const jobCount = 1000
	jobs := make([]common.EmailProcessingJob, jobCount)
	
	for i := 0; i < jobCount; i++ {
		jobs[i] = common.EmailProcessingJob{
			EmailID: int64(i),
			Subject: fmt.Sprintf("Email %d", i),
			Body:    "Test email body",
			From:    "<EMAIL>",
			To:      []string{"<EMAIL>"},
			Priority: "normal",
		}
	}

	start := time.Now()
	var wg sync.WaitGroup
	results := make([]common.EmailProcessingResult, jobCount)

	// Submit jobs to worker pool
	for i, job := range jobs {
		wg.Add(1)
		go func(idx int, j common.EmailProcessingJob) {
			defer wg.Done()
			
			resultCh := ps.workerPools.EmailProcessor.Submit(ctx, fmt.Sprintf("job_%d", idx), j)
			select {
			case result := <-resultCh:
				if result.Error != nil {
					ps.log.Errorf("Job %d failed: %v", idx, result.Error)
				} else {
					results[idx] = result.Result
				}
			case <-ctx.Done():
				ps.log.Warnf("Job %d cancelled", idx)
			}
		}(i, job)
	}

	wg.Wait()
	duration := time.Since(start)

	// Get worker pool metrics
	metrics := ps.workerPools.EmailProcessor.GetMetrics()
	ps.log.Infof("Processed %d jobs in %v (%.2f jobs/sec)", 
		jobCount, duration, float64(jobCount)/duration.Seconds())
	ps.log.Infof("Worker Pool Metrics: Processed=%d, Successful=%d, Failed=%d, AvgLatency=%v",
		metrics.JobsProcessed, metrics.JobsSuccessful, metrics.JobsFailed, metrics.AverageLatency)

	return nil
}

// genericRepositoryDemo demonstrates type-safe repository operations
func (ps *PerformanceShowcase) genericRepositoryDemo(ctx context.Context) error {
	ps.log.Info("🔧 Generic Repository Demo - Type-Safe Operations")

	// Create mock repository (in real scenario, this would be connected to database)
	repo := &MockEnhancedCustomerRepo{
		customers: make(map[int64]*data.EnhancedCustomer),
		log:       ps.log,
	}

	// Test CRUD operations
	customer := &data.EnhancedCustomer{
		Name:    "John Doe",
		Email:   "<EMAIL>",
		Phone:   "+1234567890",
		Address: "123 Main St",
		Priority: "normal",
	}

	// Create
	start := time.Now()
	createdCustomer, err := repo.Create(ctx, customer)
	if err != nil {
		return fmt.Errorf("failed to create customer: %w", err)
	}
	createDuration := time.Since(start)

	// Read
	start = time.Now()
	retrievedCustomer, err := repo.GetByID(ctx, createdCustomer.ID)
	if err != nil {
		return fmt.Errorf("failed to get customer: %w", err)
	}
	readDuration := time.Since(start)

	// Update
	retrievedCustomer.Phone = "+0987654321"
	start = time.Now()
	updatedCustomer, err := repo.Update(ctx, retrievedCustomer)
	if err != nil {
		return fmt.Errorf("failed to update customer: %w", err)
	}
	updateDuration := time.Since(start)

	ps.log.Infof("CRUD Performance: Create=%v, Read=%v, Update=%v", 
		createDuration, readDuration, updateDuration)
	ps.log.Infof("Customer: ID=%d, Name=%s, Phone=%s", 
		updatedCustomer.ID, updatedCustomer.Name, updatedCustomer.Phone)

	return nil
}

// performanceMonitoringDemo demonstrates real-time performance monitoring
func (ps *PerformanceShowcase) performanceMonitoringDemo(ctx context.Context) error {
	ps.log.Info("📈 Performance Monitoring Demo - Real-time Metrics")

	// Simulate various operations to generate metrics
	operations := []struct {
		endpoint string
		latency  time.Duration
		status   int
	}{
		{"/api/customers", 25 * time.Millisecond, 200},
		{"/api/customers", 30 * time.Millisecond, 200},
		{"/api/customers/search", 45 * time.Millisecond, 200},
		{"/api/customers/1", 15 * time.Millisecond, 200},
		{"/api/customers/999", 20 * time.Millisecond, 404},
	}

	for _, op := range operations {
		ps.perfMonitor.RecordHTTPRequest(op.endpoint, op.status, op.latency)
	}

	// Get current metrics
	metrics := ps.perfMonitor.GetMetrics()
	ps.log.Infof("System Metrics: Memory=%dMB, Goroutines=%d, GC=%d",
		metrics.SystemMetrics.MemoryUsage/1024/1024,
		metrics.SystemMetrics.GoroutineCount,
		metrics.SystemMetrics.GCStats.NumGC)

	// Get memory monitor stats
	memStats := ps.memoryMonitor.GetStats()
	ps.log.Infof("Memory Monitor: Alloc=%dMB, TotalAlloc=%dMB, NumGC=%d",
		memStats.Alloc/1024/1024, memStats.TotalAlloc/1024/1024, memStats.NumGC)

	return nil
}

// bulkOperationsDemo demonstrates optimized bulk operations
func (ps *PerformanceShowcase) bulkOperationsDemo(ctx context.Context) error {
	ps.log.Info("📦 Bulk Operations Demo - Batch Processing")

	// Create test customers
	customers := make([]*data.EnhancedCustomer, 100)
	for i := 0; i < 100; i++ {
		customers[i] = &data.EnhancedCustomer{
			Name:     fmt.Sprintf("Customer %d", i),
			Email:    fmt.Sprintf("<EMAIL>", i),
			Phone:    fmt.Sprintf("+123456%04d", i),
			Address:  fmt.Sprintf("%d Main St", i),
			Priority: "normal",
		}
	}

	// Mock repository for bulk operations
	repo := &MockEnhancedCustomerRepo{
		customers: make(map[int64]*data.EnhancedCustomer),
		log:       ps.log,
	}

	// Test bulk create
	start := time.Now()
	createdCustomers, err := repo.CreateBatch(ctx, customers)
	if err != nil {
		return fmt.Errorf("failed to create customers batch: %w", err)
	}
	bulkCreateDuration := time.Since(start)

	ps.log.Infof("Bulk Create: %d customers in %v (%.2f customers/sec)",
		len(createdCustomers), bulkCreateDuration, 
		float64(len(createdCustomers))/bulkCreateDuration.Seconds())

	return nil
}

// cachePerformanceDemo demonstrates caching performance
func (ps *PerformanceShowcase) cachePerformanceDemo(ctx context.Context) error {
	ps.log.Info("🚀 Cache Performance Demo - Memory Caching")

	// This would demonstrate cache performance with a real cache implementation
	// For now, we'll simulate cache operations
	
	const operations = 1000
	start := time.Now()
	
	for i := 0; i < operations; i++ {
		// Simulate cache operations
		key := fmt.Sprintf("customer:%d", i%100)
		_ = key // Simulate cache get/set
		
		// Simulate some processing time
		time.Sleep(100 * time.Microsecond)
	}
	
	duration := time.Since(start)
	ps.log.Infof("Cache Operations: %d operations in %v (%.2f ops/sec)",
		operations, duration, float64(operations)/duration.Seconds())

	return nil
}

// ==========================================
// CLEANUP
// ==========================================

func (ps *PerformanceShowcase) cleanup() {
	if ps.workerPools != nil {
		ps.workerPools.Stop()
	}
	if ps.memoryMonitor != nil {
		ps.memoryMonitor.Stop()
	}
	if ps.perfMonitor != nil {
		ps.perfMonitor.Stop()
	}
	
	// Force garbage collection
	runtime.GC()
	
	ps.log.Info("🧹 Cleanup completed")
}

// ==========================================
// MOCK REPOSITORY FOR DEMONSTRATION
// ==========================================

// MockEnhancedCustomerRepo provides a mock implementation for demonstration
type MockEnhancedCustomerRepo struct {
	customers map[int64]*data.EnhancedCustomer
	nextID    int64
	mu        sync.RWMutex
	log       *log.Helper
}

func (r *MockEnhancedCustomerRepo) Create(ctx context.Context, customer *data.EnhancedCustomer) (*data.EnhancedCustomer, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.nextID++
	customer.ID = r.nextID
	customer.CreatedAt = time.Now()
	customer.UpdatedAt = time.Now()
	
	r.customers[customer.ID] = customer
	return customer, nil
}

func (r *MockEnhancedCustomerRepo) GetByID(ctx context.Context, id int64) (*data.EnhancedCustomer, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	customer, exists := r.customers[id]
	if !exists {
		return nil, fmt.Errorf("customer not found: %d", id)
	}
	return customer, nil
}

func (r *MockEnhancedCustomerRepo) Update(ctx context.Context, customer *data.EnhancedCustomer) (*data.EnhancedCustomer, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.customers[customer.ID]; !exists {
		return nil, fmt.Errorf("customer not found: %d", customer.ID)
	}
	
	customer.UpdatedAt = time.Now()
	r.customers[customer.ID] = customer
	return customer, nil
}

func (r *MockEnhancedCustomerRepo) Delete(ctx context.Context, id int64) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.customers[id]; !exists {
		return fmt.Errorf("customer not found: %d", id)
	}
	
	delete(r.customers, id)
	return nil
}

func (r *MockEnhancedCustomerRepo) CreateBatch(ctx context.Context, customers []*data.EnhancedCustomer) ([]*data.EnhancedCustomer, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	for _, customer := range customers {
		r.nextID++
		customer.ID = r.nextID
		customer.CreatedAt = time.Now()
		customer.UpdatedAt = time.Now()
		r.customers[customer.ID] = customer
	}
	
	return customers, nil
}

// Implement other required methods with basic functionality
func (r *MockEnhancedCustomerRepo) List(ctx context.Context, criteria common.SearchCriteria[*data.EnhancedCustomer]) (*common.SearchResult[*data.EnhancedCustomer], error) {
	return &common.SearchResult[*data.EnhancedCustomer]{}, nil
}

func (r *MockEnhancedCustomerRepo) Search(ctx context.Context, criteria common.SearchCriteria[*data.EnhancedCustomer]) (*common.SearchResult[*data.EnhancedCustomer], error) {
	return &common.SearchResult[*data.EnhancedCustomer]{}, nil
}

func (r *MockEnhancedCustomerRepo) Count(ctx context.Context, filters map[string]interface{}) (int64, error) {
	return int64(len(r.customers)), nil
}

func (r *MockEnhancedCustomerRepo) UpdateBatch(ctx context.Context, customers []*data.EnhancedCustomer) ([]*data.EnhancedCustomer, error) {
	return customers, nil
}

func (r *MockEnhancedCustomerRepo) DeleteBatch(ctx context.Context, ids []int64) error {
	return nil
}

func (r *MockEnhancedCustomerRepo) Exists(ctx context.Context, id int64) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	_, exists := r.customers[id]
	return exists, nil
}

func (r *MockEnhancedCustomerRepo) ExistsByField(ctx context.Context, field string, value interface{}) (bool, error) {
	return false, nil
}
