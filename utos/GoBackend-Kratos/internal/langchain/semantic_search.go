package langchain

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/philippgille/chromem-go"
	"github.com/tmc/langchaingo/llms"
)

// SimpleEmbedder provides a simple embedding interface
type SimpleEmbedder struct {
	llm llms.LLM
}

// EmbedQuery generates embeddings for a query string
func (e *SimpleEmbedder) EmbedQuery(ctx context.Context, query string) ([]float32, error) {
	// For now, return a simple hash-based embedding
	// In production, integrate with actual embedding models
	embedding := make([]float32, 384) // Standard embedding size
	hash := simpleHash(query)
	for i := range embedding {
		embedding[i] = float32((hash + i) % 1000) / 1000.0
	}
	return embedding, nil
}

// EmbedDocuments generates embeddings for multiple documents
func (e *SimpleEmbedder) EmbedDocuments(ctx context.Context, docs []string) ([][]float32, error) {
	embeddings := make([][]float32, len(docs))
	for i, doc := range docs {
		embedding, err := e.EmbedQuery(ctx, doc)
		if err != nil {
			return nil, err
		}
		embeddings[i] = embedding
	}
	return embeddings, nil
}

// simpleHash creates a simple hash for demonstration
func simpleHash(s string) int {
	hash := 0
	for _, c := range s {
		hash = hash*31 + int(c)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// 🔍 Semantic Search Service - Advanced Vector-based Knowledge Retrieval
// Provides intelligent semantic search capabilities for HVAC knowledge management

type SemanticSearchService struct {
	vectorDB         *chromem.DB
	embedder         *SimpleEmbedder
	llm              llms.LLM
	log              *log.Helper
	collections      map[string]*chromem.Collection
	searchMetrics    *SearchMetrics
}

// SearchMetrics tracks semantic search performance
type SearchMetrics struct {
	TotalSearches       int64     `json:"total_searches"`
	SuccessfulSearches  int64     `json:"successful_searches"`
	FailedSearches      int64     `json:"failed_searches"`
	AverageResponseTime float64   `json:"average_response_time"`
	AverageRelevance    float64   `json:"average_relevance"`
	LastSearch          time.Time `json:"last_search"`
}

// SearchRequest represents a semantic search request
type SearchRequest struct {
	Query           string                 `json:"query"`
	Collection      string                 `json:"collection"`
	TopK            int                    `json:"top_k"`
	MinSimilarity   float64                `json:"min_similarity"`
	Filters         map[string]interface{} `json:"filters,omitempty"`
	IncludeMetadata bool                   `json:"include_metadata"`
}

// SearchResult represents a semantic search result
type SearchResult struct {
	ID          string                 `json:"id"`
	Content     string                 `json:"content"`
	Similarity  float64                `json:"similarity"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Collection  string                 `json:"collection"`
	ProcessedAt time.Time              `json:"processed_at"`
}

// SearchResponse represents the complete search response
type SearchResponse struct {
	Query       string          `json:"query"`
	Results     []*SearchResult `json:"results"`
	TotalFound  int             `json:"total_found"`
	SearchTime  time.Duration   `json:"search_time"`
	ProcessedAt time.Time       `json:"processed_at"`
}

// KnowledgeDocument represents a document to be indexed
type KnowledgeDocument struct {
	ID          string                 `json:"id"`
	Content     string                 `json:"content"`
	Title       string                 `json:"title"`
	Category    string                 `json:"category"`
	Tags        []string               `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// NewSemanticSearchService creates a new semantic search service
func NewSemanticSearchService(llm llms.LLM, logger log.Logger) (*SemanticSearchService, error) {
	helper := log.NewHelper(logger)
	helper.Info("🔍 Initializing Semantic Search Service...")

	// Initialize chromem-go vector database
	db := chromem.NewDB()

	// Initialize embedder - simplified for chromem-go compatibility
	// Note: We'll use the LLM directly for embeddings in this implementation
	embedder := &SimpleEmbedder{llm: llm}

	service := &SemanticSearchService{
		vectorDB:      db,
		embedder:      embedder,
		llm:           llm,
		log:           helper,
		collections:   make(map[string]*chromem.Collection),
		searchMetrics: &SearchMetrics{},
	}

	// Initialize default collections
	if err := service.initializeCollections(); err != nil {
		return nil, fmt.Errorf("failed to initialize collections: %w", err)
	}

	helper.Info("✅ Semantic Search Service initialized successfully!")
	return service, nil
}

// initializeCollections sets up default knowledge collections
func (s *SemanticSearchService) initializeCollections() error {
	collections := []string{
		"equipment_manuals",     // Equipment documentation and manuals
		"service_history",       // Historical service records
		"troubleshooting_guides", // Troubleshooting procedures
		"customer_communications", // Customer interaction history
		"technical_specifications", // Technical specs and standards
		"maintenance_procedures",  // Maintenance and repair procedures
		"safety_protocols",       // Safety guidelines and protocols
		"business_knowledge",     // Business processes and policies
	}

	for _, collectionName := range collections {
		collection, err := s.vectorDB.CreateCollection(collectionName, nil, nil)
		if err != nil {
			return fmt.Errorf("failed to create collection %s: %w", collectionName, err)
		}
		s.collections[collectionName] = collection
		s.log.Infof("✅ Created collection: %s", collectionName)
	}

	return nil
}

// IndexDocument adds a document to the knowledge base
func (s *SemanticSearchService) IndexDocument(ctx context.Context, doc *KnowledgeDocument, collection string) error {
	s.log.WithContext(ctx).Infof("📚 Indexing document: %s in collection: %s", doc.ID, collection)

	// Get or create collection
	coll, exists := s.collections[collection]
	if !exists {
		var err error
		coll, err = s.vectorDB.CreateCollection(collection, nil, nil)
		if err != nil {
			return fmt.Errorf("failed to create collection %s: %w", collection, err)
		}
		s.collections[collection] = coll
	}

	// Generate embedding for the document content
	embedding, err := s.embedder.EmbedQuery(ctx, doc.Content)
	if err != nil {
		return fmt.Errorf("failed to generate embedding: %w", err)
	}

	// Prepare metadata for chromem-go (string values only)
	metadata := make(map[string]string)
	metadata["title"] = doc.Title
	metadata["category"] = doc.Category
	metadata["tags"] = strings.Join(doc.Tags, ",")
	metadata["created_at"] = doc.CreatedAt.Format(time.RFC3339)
	metadata["updated_at"] = doc.UpdatedAt.Format(time.RFC3339)

	// Add additional metadata as strings
	if doc.Metadata != nil {
		for k, v := range doc.Metadata {
			metadata[k] = fmt.Sprintf("%v", v)
		}
	}

	// Add document to collection
	err = coll.Add(ctx, []string{doc.ID}, [][]float32{embedding}, []map[string]string{metadata}, []string{doc.Content})
	if err != nil {
		return fmt.Errorf("failed to add document to collection: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Document indexed successfully: %s", doc.ID)
	return nil
}

// Search performs semantic search across knowledge base
func (s *SemanticSearchService) Search(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("🔍 Performing semantic search: %s", req.Query)
	s.searchMetrics.TotalSearches++

	// Generate query embedding
	queryEmbedding, err := s.embedder.EmbedQuery(ctx, req.Query)
	if err != nil {
		s.searchMetrics.FailedSearches++
		return nil, fmt.Errorf("failed to generate query embedding: %w", err)
	}

	// Get collection
	coll, exists := s.collections[req.Collection]
	if !exists {
		s.searchMetrics.FailedSearches++
		return nil, fmt.Errorf("collection not found: %s", req.Collection)
	}

	// Perform vector search using QueryEmbedding method
	results, err := coll.QueryEmbedding(ctx, queryEmbedding, req.TopK, nil, nil)
	if err != nil {
		s.searchMetrics.FailedSearches++
		return nil, fmt.Errorf("vector search failed: %w", err)
	}

	// Process results
	searchResults := make([]*SearchResult, 0, len(results))
	for _, result := range results {
		// Filter by minimum similarity
		if float64(result.Similarity) < req.MinSimilarity {
			continue
		}

		searchResult := &SearchResult{
			ID:          result.ID,
			Content:     result.Content,
			Similarity:  float64(result.Similarity),
			Collection:  req.Collection,
			ProcessedAt: time.Now(),
		}

		// Include metadata if requested
		if req.IncludeMetadata && result.Metadata != nil {
			// Convert string metadata to interface{} map
			metadata := make(map[string]interface{})
			for k, v := range result.Metadata {
				metadata[k] = v
			}
			searchResult.Metadata = metadata
		}

		searchResults = append(searchResults, searchResult)
	}

	searchTime := time.Since(startTime)
	s.searchMetrics.SuccessfulSearches++
	s.searchMetrics.LastSearch = time.Now()

	response := &SearchResponse{
		Query:       req.Query,
		Results:     searchResults,
		TotalFound:  len(searchResults),
		SearchTime:  searchTime,
		ProcessedAt: time.Now(),
	}

	s.log.WithContext(ctx).Infof("✅ Search completed: %d results in %v", len(searchResults), searchTime)
	return response, nil
}

// SearchMultipleCollections searches across multiple collections
func (s *SemanticSearchService) SearchMultipleCollections(ctx context.Context, query string, collections []string, topK int) (*SearchResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("🔍 Multi-collection search: %s across %d collections", query, len(collections))

	allResults := make([]*SearchResult, 0)

	for _, collection := range collections {
		req := &SearchRequest{
			Query:           query,
			Collection:      collection,
			TopK:            topK,
			MinSimilarity:   0.5, // Default minimum similarity
			IncludeMetadata: true,
		}

		response, err := s.Search(ctx, req)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Search failed for collection %s: %v", collection, err)
			continue
		}

		allResults = append(allResults, response.Results...)
	}

	// Sort results by similarity (highest first)
	for i := 0; i < len(allResults)-1; i++ {
		for j := i + 1; j < len(allResults); j++ {
			if allResults[i].Similarity < allResults[j].Similarity {
				allResults[i], allResults[j] = allResults[j], allResults[i]
			}
		}
	}

	// Limit to topK results
	if len(allResults) > topK {
		allResults = allResults[:topK]
	}

	searchTime := time.Since(startTime)

	response := &SearchResponse{
		Query:       query,
		Results:     allResults,
		TotalFound:  len(allResults),
		SearchTime:  searchTime,
		ProcessedAt: time.Now(),
	}

	s.log.WithContext(ctx).Infof("✅ Multi-collection search completed: %d results in %v", len(allResults), searchTime)
	return response, nil
}

// FindSimilarIssues finds similar HVAC issues based on description
func (s *SemanticSearchService) FindSimilarIssues(ctx context.Context, issueDescription string, topK int) (*SearchResponse, error) {
	s.log.WithContext(ctx).Infof("🔧 Finding similar HVAC issues for: %s", issueDescription)

	// Search in service history and troubleshooting guides
	collections := []string{"service_history", "troubleshooting_guides"}

	return s.SearchMultipleCollections(ctx, issueDescription, collections, topK)
}

// FindEquipmentInfo finds equipment information and manuals
func (s *SemanticSearchService) FindEquipmentInfo(ctx context.Context, equipmentQuery string, topK int) (*SearchResponse, error) {
	s.log.WithContext(ctx).Infof("📖 Finding equipment information for: %s", equipmentQuery)

	// Search in equipment manuals and technical specifications
	collections := []string{"equipment_manuals", "technical_specifications"}

	return s.SearchMultipleCollections(ctx, equipmentQuery, collections, topK)
}

// FindMaintenanceProcedures finds relevant maintenance procedures
func (s *SemanticSearchService) FindMaintenanceProcedures(ctx context.Context, maintenanceQuery string, topK int) (*SearchResponse, error) {
	s.log.WithContext(ctx).Infof("🔧 Finding maintenance procedures for: %s", maintenanceQuery)

	// Search in maintenance procedures and safety protocols
	collections := []string{"maintenance_procedures", "safety_protocols"}

	return s.SearchMultipleCollections(ctx, maintenanceQuery, collections, topK)
}

// GetCollectionStats returns statistics for a collection
func (s *SemanticSearchService) GetCollectionStats(collection string) (map[string]interface{}, error) {
	_, exists := s.collections[collection]
	if !exists {
		return nil, fmt.Errorf("collection not found: %s", collection)
	}

	// Get collection count (simplified - chromem-go doesn't expose count directly)
	stats := map[string]interface{}{
		"collection_name": collection,
		"status":         "active",
		"last_updated":   time.Now(),
	}

	return stats, nil
}

// GetSearchMetrics returns current search metrics
func (s *SemanticSearchService) GetSearchMetrics() *SearchMetrics {
	return s.searchMetrics
}

// HealthCheck verifies semantic search service health
func (s *SemanticSearchService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🏥 Performing semantic search health check")

	// Test embedder
	_, err := s.embedder.EmbedQuery(ctx, "health check test")
	if err != nil {
		return fmt.Errorf("embedder health check failed: %w", err)
	}

	// Test vector database
	if s.vectorDB == nil {
		return fmt.Errorf("vector database not initialized")
	}

	return nil
}

// IndexHVACKnowledge indexes common HVAC knowledge documents
func (s *SemanticSearchService) IndexHVACKnowledge(ctx context.Context) error {
	s.log.WithContext(ctx).Info("📚 Indexing default HVAC knowledge base...")

	// Sample HVAC knowledge documents
	documents := []*KnowledgeDocument{
		{
			ID:       "hvac_basics_001",
			Content:  "HVAC systems control temperature, humidity, and air quality in buildings. Main components include heating units, cooling units, ventilation systems, and ductwork.",
			Title:    "HVAC System Basics",
			Category: "fundamentals",
			Tags:     []string{"basics", "overview", "components"},
			Metadata: map[string]interface{}{"difficulty": "beginner", "type": "educational"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:       "troubleshoot_001",
			Content:  "When HVAC system is not cooling properly, check: 1) Air filter condition, 2) Thermostat settings, 3) Refrigerant levels, 4) Condenser coil cleanliness, 5) Electrical connections.",
			Title:    "Cooling System Troubleshooting",
			Category: "troubleshooting",
			Tags:     []string{"cooling", "troubleshooting", "diagnosis"},
			Metadata: map[string]interface{}{"difficulty": "intermediate", "type": "procedure"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:       "maintenance_001",
			Content:  "Regular HVAC maintenance includes: monthly filter replacement, annual professional inspection, seasonal cleaning of outdoor units, checking and cleaning vents and ducts.",
			Title:    "Preventive Maintenance Schedule",
			Category: "maintenance",
			Tags:     []string{"maintenance", "preventive", "schedule"},
			Metadata: map[string]interface{}{"difficulty": "beginner", "type": "schedule"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// Index documents in appropriate collections
	for _, doc := range documents {
		var collection string
		switch doc.Category {
		case "troubleshooting":
			collection = "troubleshooting_guides"
		case "maintenance":
			collection = "maintenance_procedures"
		default:
			collection = "equipment_manuals"
		}

		if err := s.IndexDocument(ctx, doc, collection); err != nil {
			s.log.WithContext(ctx).Warnf("Failed to index document %s: %v", doc.ID, err)
		}
	}

	s.log.WithContext(ctx).Info("✅ Default HVAC knowledge base indexed successfully!")
	return nil
}
