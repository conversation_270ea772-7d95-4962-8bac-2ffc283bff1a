package transport

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	kratosgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	kratoshttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/websocket"
	"google.golang.org/grpc"

	"gobackend-hvac-kratos/internal/common"
	"gobackend-hvac-kratos/internal/middleware"
)

// 🚀 Advanced HVAC Transport Layer for Kratos Framework Excellence
// Optimized transport with gRPC streaming, HTTP/2, and WebSocket support

// ==========================================
// HVAC TRANSPORT MANAGER
// ==========================================

// HVACTransportManager manages all transport protocols for HVAC services
type HVACTransportManager struct {
	httpServer    *OptimizedHTTPServer
	grpcServer    *OptimizedGRPCServer
	wsServer      *WebSocketServer
	config        *TransportConfig
	middleware    *middleware.HVACMiddlewareChain
	perfMonitor   *common.PerformanceMonitor
	log           *log.Helper
	running       bool
	mu            sync.RWMutex
}

// TransportConfig configures the transport layer
type TransportConfig struct {
	HTTP      *HTTPConfig      `json:"http"`
	GRPC      *GRPCConfig      `json:"grpc"`
	WebSocket *WebSocketConfig `json:"websocket"`
	TLS       *TLSConfig       `json:"tls"`
	Metrics   *MetricsConfig   `json:"metrics"`
}

// HTTPConfig configures HTTP transport
type HTTPConfig struct {
	Address         string        `json:"address"`
	Port            int           `json:"port"`
	ReadTimeout     time.Duration `json:"read_timeout"`
	WriteTimeout    time.Duration `json:"write_timeout"`
	IdleTimeout     time.Duration `json:"idle_timeout"`
	MaxHeaderBytes  int           `json:"max_header_bytes"`
	EnableHTTP2     bool          `json:"enable_http2"`
	EnableGzip      bool          `json:"enable_gzip"`
	CORSEnabled     bool          `json:"cors_enabled"`
	CORSOrigins     []string      `json:"cors_origins"`
}

// GRPCConfig configures gRPC transport
type GRPCConfig struct {
	Address           string        `json:"address"`
	Port              int           `json:"port"`
	MaxRecvMsgSize    int           `json:"max_recv_msg_size"`
	MaxSendMsgSize    int           `json:"max_send_msg_size"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	EnableReflection  bool          `json:"enable_reflection"`
	EnableStreaming   bool          `json:"enable_streaming"`
	KeepAlive         *KeepAliveConfig `json:"keep_alive"`
}

// KeepAliveConfig configures gRPC keep-alive
type KeepAliveConfig struct {
	Time                time.Duration `json:"time"`
	Timeout             time.Duration `json:"timeout"`
	EnforcementPolicy   bool          `json:"enforcement_policy"`
	PermitWithoutStream bool          `json:"permit_without_stream"`
}

// WebSocketConfig configures WebSocket transport
type WebSocketConfig struct {
	Address         string        `json:"address"`
	Port            int           `json:"port"`
	ReadBufferSize  int           `json:"read_buffer_size"`
	WriteBufferSize int           `json:"write_buffer_size"`
	CheckOrigin     bool          `json:"check_origin"`
	EnablePing      bool          `json:"enable_ping"`
	PingInterval    time.Duration `json:"ping_interval"`
	MaxConnections  int           `json:"max_connections"`
}

// TLSConfig configures TLS settings
type TLSConfig struct {
	Enabled  bool   `json:"enabled"`
	CertFile string `json:"cert_file"`
	KeyFile  string `json:"key_file"`
	CAFile   string `json:"ca_file"`
}

// MetricsConfig configures transport metrics
type MetricsConfig struct {
	Enabled           bool          `json:"enabled"`
	CollectionInterval time.Duration `json:"collection_interval"`
	EnableLatencyHist bool          `json:"enable_latency_hist"`
	EnableSizeHist    bool          `json:"enable_size_hist"`
}

// NewHVACTransportManager creates a new transport manager
func NewHVACTransportManager(
	config *TransportConfig,
	middlewareChain *middleware.HVACMiddlewareChain,
	perfMonitor *common.PerformanceMonitor,
	logger log.Logger,
) *HVACTransportManager {
	if config == nil {
		config = getDefaultTransportConfig()
	}

	return &HVACTransportManager{
		config:      config,
		middleware:  middlewareChain,
		perfMonitor: perfMonitor,
		log:         log.NewHelper(logger),
	}
}

// Start starts all transport servers
func (tm *HVACTransportManager) Start(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.running {
		return fmt.Errorf("transport manager already running")
	}

	// Start HTTP server
	if tm.config.HTTP != nil {
		httpServer, err := tm.createHTTPServer()
		if err != nil {
			return fmt.Errorf("failed to create HTTP server: %w", err)
		}
		tm.httpServer = httpServer
		go tm.httpServer.Start()
	}

	// Start gRPC server
	if tm.config.GRPC != nil {
		grpcServer, err := tm.createGRPCServer()
		if err != nil {
			return fmt.Errorf("failed to create gRPC server: %w", err)
		}
		tm.grpcServer = grpcServer
		go tm.grpcServer.Start()
	}

	// Start WebSocket server
	if tm.config.WebSocket != nil {
		wsServer, err := tm.createWebSocketServer()
		if err != nil {
			return fmt.Errorf("failed to create WebSocket server: %w", err)
		}
		tm.wsServer = wsServer
		go tm.wsServer.Start()
	}

	tm.running = true
	tm.log.Info("HVAC Transport Manager started")
	return nil
}

// Stop stops all transport servers
func (tm *HVACTransportManager) Stop() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if !tm.running {
		return nil
	}

	// Stop HTTP server
	if tm.httpServer != nil {
		tm.httpServer.Stop()
	}

	// Stop gRPC server
	if tm.grpcServer != nil {
		tm.grpcServer.Stop()
	}

	// Stop WebSocket server
	if tm.wsServer != nil {
		tm.wsServer.Stop()
	}

	tm.running = false
	tm.log.Info("HVAC Transport Manager stopped")
	return nil
}

// ==========================================
// OPTIMIZED HTTP SERVER
// ==========================================

// OptimizedHTTPServer provides optimized HTTP transport
type OptimizedHTTPServer struct {
	server      *kratoshttp.Server
	config      *HTTPConfig
	perfMonitor *common.PerformanceMonitor
	log         *log.Helper
}

func (tm *HVACTransportManager) createHTTPServer() (*OptimizedHTTPServer, error) {
	opts := []kratoshttp.ServerOption{
		kratoshttp.Address(fmt.Sprintf("%s:%d", tm.config.HTTP.Address, tm.config.HTTP.Port)),
		kratoshttp.Timeout(tm.config.HTTP.ReadTimeout),
	}

	// Add middleware chain
	if tm.middleware != nil {
		opts = append(opts, kratoshttp.Middleware(tm.middleware.BuildHTTPChain()...))
	}

	// Enable HTTP/2 if configured
	if tm.config.HTTP.EnableHTTP2 {
		// HTTP/2 configuration would go here
	}

	server := kratoshttp.NewServer(opts...)

	return &OptimizedHTTPServer{
		server:      server,
		config:      tm.config.HTTP,
		perfMonitor: tm.perfMonitor,
		log:         tm.log,
	}, nil
}

func (hs *OptimizedHTTPServer) Start() {
	hs.log.Infof("Starting optimized HTTP server on %s:%d", hs.config.Address, hs.config.Port)
	if err := hs.server.Start(context.Background()); err != nil {
		hs.log.Errorf("HTTP server error: %v", err)
	}
}

func (hs *OptimizedHTTPServer) Stop() {
	hs.log.Info("Stopping optimized HTTP server")
	if err := hs.server.Stop(context.Background()); err != nil {
		hs.log.Errorf("HTTP server stop error: %v", err)
	}
}

// ==========================================
// OPTIMIZED GRPC SERVER
// ==========================================

// OptimizedGRPCServer provides optimized gRPC transport
type OptimizedGRPCServer struct {
	server      *kratosgrpc.Server
	config      *GRPCConfig
	perfMonitor *common.PerformanceMonitor
	log         *log.Helper
}

func (tm *HVACTransportManager) createGRPCServer() (*OptimizedGRPCServer, error) {
	opts := []kratosgrpc.ServerOption{
		kratosgrpc.Address(fmt.Sprintf("%s:%d", tm.config.GRPC.Address, tm.config.GRPC.Port)),
		kratosgrpc.Timeout(tm.config.GRPC.ConnectionTimeout),
	}

	// Add middleware chain
	if tm.middleware != nil {
		opts = append(opts, kratosgrpc.Middleware(tm.middleware.BuildGRPCChain()...))
	}

	// Configure message sizes
	if tm.config.GRPC.MaxRecvMsgSize > 0 {
		opts = append(opts, kratosgrpc.Options(grpc.MaxRecvMsgSize(tm.config.GRPC.MaxRecvMsgSize)))
	}
	if tm.config.GRPC.MaxSendMsgSize > 0 {
		opts = append(opts, kratosgrpc.Options(grpc.MaxSendMsgSize(tm.config.GRPC.MaxSendMsgSize)))
	}

	// Configure keep-alive
	if tm.config.GRPC.KeepAlive != nil {
		// Keep-alive configuration would go here
	}

	server := kratosgrpc.NewServer(opts...)

	return &OptimizedGRPCServer{
		server:      server,
		config:      tm.config.GRPC,
		perfMonitor: tm.perfMonitor,
		log:         tm.log,
	}, nil
}

func (gs *OptimizedGRPCServer) Start() {
	gs.log.Infof("Starting optimized gRPC server on %s:%d", gs.config.Address, gs.config.Port)
	if err := gs.server.Start(context.Background()); err != nil {
		gs.log.Errorf("gRPC server error: %v", err)
	}
}

func (gs *OptimizedGRPCServer) Stop() {
	gs.log.Info("Stopping optimized gRPC server")
	if err := gs.server.Stop(context.Background()); err != nil {
		gs.log.Errorf("gRPC server stop error: %v", err)
	}
}

// ==========================================
// WEBSOCKET SERVER
// ==========================================

// WebSocketServer provides real-time WebSocket communication
type WebSocketServer struct {
	upgrader    websocket.Upgrader
	connections map[string]*WebSocketConnection
	config      *WebSocketConfig
	perfMonitor *common.PerformanceMonitor
	log         *log.Helper
	mu          sync.RWMutex
	stopChan    chan struct{}
}

// WebSocketConnection represents a WebSocket connection
type WebSocketConnection struct {
	ID       string
	Conn     *websocket.Conn
	UserID   string
	LastPing time.Time
	Metadata map[string]interface{}
}

func (tm *HVACTransportManager) createWebSocketServer() (*WebSocketServer, error) {
	upgrader := websocket.Upgrader{
		ReadBufferSize:  tm.config.WebSocket.ReadBufferSize,
		WriteBufferSize: tm.config.WebSocket.WriteBufferSize,
		CheckOrigin: func(r *http.Request) bool {
			return tm.config.WebSocket.CheckOrigin
		},
	}

	return &WebSocketServer{
		upgrader:    upgrader,
		connections: make(map[string]*WebSocketConnection),
		config:      tm.config.WebSocket,
		perfMonitor: tm.perfMonitor,
		log:         tm.log,
		stopChan:    make(chan struct{}),
	}, nil
}

func (ws *WebSocketServer) Start() {
	ws.log.Infof("Starting WebSocket server on %s:%d", ws.config.Address, ws.config.Port)

	// Start ping routine if enabled
	if ws.config.EnablePing {
		go ws.pingRoutine()
	}

	// Start connection cleanup routine
	go ws.cleanupRoutine()

	// HTTP server for WebSocket upgrades would be started here
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", ws.handleWebSocket)

	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", ws.config.Address, ws.config.Port),
		Handler: mux,
	}

	if err := server.ListenAndServe(); err != nil {
		ws.log.Errorf("WebSocket server error: %v", err)
	}
}

func (ws *WebSocketServer) Stop() {
	ws.log.Info("Stopping WebSocket server")
	close(ws.stopChan)

	// Close all connections
	ws.mu.Lock()
	for _, conn := range ws.connections {
		conn.Conn.Close()
	}
	ws.connections = make(map[string]*WebSocketConnection)
	ws.mu.Unlock()
}

func (ws *WebSocketServer) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := ws.upgrader.Upgrade(w, r, nil)
	if err != nil {
		ws.log.Errorf("WebSocket upgrade error: %v", err)
		return
	}

	// Create connection
	wsConn := &WebSocketConnection{
		ID:       fmt.Sprintf("ws_%d", time.Now().UnixNano()),
		Conn:     conn,
		LastPing: time.Now(),
		Metadata: make(map[string]interface{}),
	}

	// Store connection
	ws.mu.Lock()
	if len(ws.connections) >= ws.config.MaxConnections {
		ws.mu.Unlock()
		conn.Close()
		return
	}
	ws.connections[wsConn.ID] = wsConn
	ws.mu.Unlock()

	ws.log.Infof("New WebSocket connection: %s", wsConn.ID)

	// Handle connection
	go ws.handleConnection(wsConn)
}

func (ws *WebSocketServer) handleConnection(conn *WebSocketConnection) {
	defer func() {
		ws.mu.Lock()
		delete(ws.connections, conn.ID)
		ws.mu.Unlock()
		conn.Conn.Close()
		ws.log.Infof("WebSocket connection closed: %s", conn.ID)
	}()

	for {
		messageType, message, err := conn.Conn.ReadMessage()
		if err != nil {
			ws.log.Errorf("WebSocket read error: %v", err)
			break
		}

		// Process message
		ws.processMessage(conn, messageType, message)
	}
}

func (ws *WebSocketServer) processMessage(conn *WebSocketConnection, messageType int, message []byte) {
	// Update last ping
	conn.LastPing = time.Now()

	// Process the message based on type
	ws.log.Infof("Received WebSocket message from %s: %s", conn.ID, string(message))

	// Echo back for now (in production, implement proper message handling)
	if err := conn.Conn.WriteMessage(messageType, message); err != nil {
		ws.log.Errorf("WebSocket write error: %v", err)
	}
}

func (ws *WebSocketServer) pingRoutine() {
	ticker := time.NewTicker(ws.config.PingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ws.stopChan:
			return
		case <-ticker.C:
			ws.sendPingToAll()
		}
	}
}

func (ws *WebSocketServer) sendPingToAll() {
	ws.mu.RLock()
	connections := make([]*WebSocketConnection, 0, len(ws.connections))
	for _, conn := range ws.connections {
		connections = append(connections, conn)
	}
	ws.mu.RUnlock()

	for _, conn := range connections {
		if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
			ws.log.Errorf("WebSocket ping error for %s: %v", conn.ID, err)
		}
	}
}

func (ws *WebSocketServer) cleanupRoutine() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ws.stopChan:
			return
		case <-ticker.C:
			ws.cleanupStaleConnections()
		}
	}
}

func (ws *WebSocketServer) cleanupStaleConnections() {
	ws.mu.Lock()
	defer ws.mu.Unlock()

	now := time.Now()
	for id, conn := range ws.connections {
		if now.Sub(conn.LastPing) > 2*ws.config.PingInterval {
			ws.log.Warnf("Removing stale WebSocket connection: %s", id)
			conn.Conn.Close()
			delete(ws.connections, id)
		}
	}
}

// ==========================================
// TRANSPORT METRICS
// ==========================================

// TransportMetrics tracks transport layer performance
type TransportMetrics struct {
	HTTPRequests    int64         `json:"http_requests"`
	GRPCRequests    int64         `json:"grpc_requests"`
	WSConnections   int           `json:"ws_connections"`
	AvgLatency      time.Duration `json:"avg_latency"`
	ErrorRate       float64       `json:"error_rate"`
	ThroughputRPS   float64       `json:"throughput_rps"`
}

// GetMetrics returns current transport metrics
func (tm *HVACTransportManager) GetMetrics() *TransportMetrics {
	metrics := &TransportMetrics{}

	// Collect HTTP metrics
	if tm.httpServer != nil {
		// HTTP metrics collection would go here
	}

	// Collect gRPC metrics
	if tm.grpcServer != nil {
		// gRPC metrics collection would go here
	}

	// Collect WebSocket metrics
	if tm.wsServer != nil {
		tm.wsServer.mu.RLock()
		metrics.WSConnections = len(tm.wsServer.connections)
		tm.wsServer.mu.RUnlock()
	}

	return metrics
}

// ==========================================
// DEFAULT CONFIGURATION
// ==========================================

func getDefaultTransportConfig() *TransportConfig {
	return &TransportConfig{
		HTTP: &HTTPConfig{
			Address:        "0.0.0.0",
			Port:           8080,
			ReadTimeout:    30 * time.Second,
			WriteTimeout:   30 * time.Second,
			IdleTimeout:    60 * time.Second,
			MaxHeaderBytes: 1 << 20, // 1MB
			EnableHTTP2:    true,
			EnableGzip:     true,
			CORSEnabled:    true,
			CORSOrigins:    []string{"*"},
		},
		GRPC: &GRPCConfig{
			Address:           "0.0.0.0",
			Port:              9000,
			MaxRecvMsgSize:    4 * 1024 * 1024, // 4MB
			MaxSendMsgSize:    4 * 1024 * 1024, // 4MB
			ConnectionTimeout: 30 * time.Second,
			EnableReflection:  true,
			EnableStreaming:   true,
			KeepAlive: &KeepAliveConfig{
				Time:                30 * time.Second,
				Timeout:             5 * time.Second,
				EnforcementPolicy:   true,
				PermitWithoutStream: false,
			},
		},
		WebSocket: &WebSocketConfig{
			Address:         "0.0.0.0",
			Port:            8081,
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin:     true,
			EnablePing:      true,
			PingInterval:    30 * time.Second,
			MaxConnections:  1000,
		},
		TLS: &TLSConfig{
			Enabled: false,
		},
		Metrics: &MetricsConfig{
			Enabled:            true,
			CollectionInterval: 30 * time.Second,
			EnableLatencyHist:  true,
			EnableSizeHist:     true,
		},
	}
}
