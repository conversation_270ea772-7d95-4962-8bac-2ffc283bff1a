package extraction

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/data"

	"github.com/go-kratos/kratos/v2/log"

	// Using pdfcpu for open-source PDF processing
	// Alternative: unidoc for commercial use with more features
	"github.com/pdfcpu/pdfcpu"
)

// 📄 PDF Content Extractor using pdfcpu
// High-performance PDF text extraction with metadata support

type PDFExtractor struct {
	log *log.Helper
}

// NewPDFExtractor creates a new PDF extractor
func NewPDFExtractor(logger log.Logger) *PDFExtractor {
	return &PDFExtractor{
		log: log.NewHelper(logger),
	}
}

// Extract extracts text and metadata from PDF
func (pe *PDFExtractor) Extract(ctx context.Context, reader io.Reader) (string, *data.ExtractionMetadata, error) {
	startTime := time.Now()
	pe.log.WithContext(ctx).Debug("📄 Starting PDF extraction with pdfcpu...")

	// Read PDF data
	pdfData, err := io.ReadAll(reader)
	if err != nil {
		return "", nil, fmt.Errorf("failed to read PDF data: %w", err)
	}

	// Create PDF context from bytes
	ctx_pdf, err := pdfcpu.ReadContextFromBytes(pdfData)
	if err != nil {
		return "", nil, fmt.Errorf("failed to create PDF context: %w", err)
	}

	// Extract text content
	text, err := pe.extractText(ctx_pdf)
	if err != nil {
		return "", nil, fmt.Errorf("failed to extract text: %w", err)
	}

	// Extract metadata
	metadata, err := pe.extractMetadata(ctx_pdf)
	if err != nil {
		pe.log.WithContext(ctx).Warnf("Failed to extract metadata: %v", err)
		// Continue with basic metadata
		metadata = &data.ExtractionMetadata{
			FileType:         "pdf",
			ProcessorUsed:    "pdfcpu",
			ProcessorVersion: "0.6.0", // Keep hardcoded for now
			ExtractionTime:   int(time.Since(startTime).Milliseconds()),
		}
	}

	// Update processing info
	metadata.ExtractionTime = int(time.Since(startTime).Milliseconds())
	metadata.ProcessorUsed = "pdfcpu"
	metadata.ProcessorVersion = "0.6.0" // Keep hardcoded for now
	metadata.WordCount = len(strings.Fields(text))
	metadata.CharCount = len(text)

	pe.log.WithContext(ctx).Debugf("📄 PDF extraction completed: %d pages, %d words",
		metadata.PageCount, metadata.WordCount)

	return text, metadata, nil
}

// extractText extracts text content from PDF
func (pe *PDFExtractor) extractText(ctx *pdfcpu.Context) (string, error) {
	var allText strings.Builder

	// Get page count
	pageCount := ctx.PageCount

	// Extract text from each page
	for i := 1; i <= pageCount; i++ {
		// Extract text from page
		pageText, err := pdfcpu.ExtractTextFromPage(ctx, i)
		if err != nil {
			pe.log.Warnf("Failed to extract text from page %d: %v", i, err)
			continue
		}

		if pageText != "" {
			allText.WriteString(pageText)
			allText.WriteString("\n\n") // Separate pages
		}
	}

	return allText.String(), nil
}

// extractMetadata extracts metadata from PDF
func (pe *PDFExtractor) extractMetadata(ctx *pdfcpu.Context) (*data.ExtractionMetadata, error) {
	metadata := &data.ExtractionMetadata{
		FileType:    "pdf",
		PageCount:   ctx.PageCount,
		PDFVersion:  ctx.HeaderVersion,
		IsEncrypted: ctx.Encrypt != nil,
	}

	// Extract document info if available
	if ctx.Info != nil {
		info := ctx.Info

		// Extract basic document properties
		if title, ok := info["Title"]; ok {
			if titleStr, ok := title.(string); ok {
				metadata.Title = titleStr
			}
		}

		if author, ok := info["Author"]; ok {
			if authorStr, ok := author.(string); ok {
				metadata.Author = authorStr
			}
		}

		if subject, ok := info["Subject"]; ok {
			if subjectStr, ok := subject.(string); ok {
				metadata.Subject = subjectStr
			}
		}

		if keywords, ok := info["Keywords"]; ok {
			if keywordsStr, ok := keywords.(string); ok {
				// Split keywords by common separators
				keywordList := strings.FieldsFunc(keywordsStr, func(c rune) bool {
					return c == ',' || c == ';' || c == ' '
				})
				metadata.Keywords = keywordList
			}
		}

		if creator, ok := info["Creator"]; ok {
			if creatorStr, ok := creator.(string); ok {
				metadata.Application = creatorStr
			}
		}

		// Extract dates
		if creationDate, ok := info["CreationDate"]; ok {
			if dateStr, ok := creationDate.(string); ok {
				if parsedDate, err := pe.parsePDFDate(dateStr); err == nil {
					metadata.CreatedDate = &parsedDate
				}
			}
		}

		if modDate, ok := info["ModDate"]; ok {
			if dateStr, ok := modDate.(string); ok {
				if parsedDate, err := pe.parsePDFDate(dateStr); err == nil {
					metadata.ModifiedDate = &parsedDate
				}
			}
		}
	}

	// Check for forms
	metadata.HasForms = pe.hasInteractiveForms(ctx)

	return metadata, nil
}

// parsePDFDate parses PDF date format (D:YYYYMMDDHHmmSSOHH'mm')
func (pe *PDFExtractor) parsePDFDate(dateStr string) (time.Time, error) {
	// Remove D: prefix if present
	if strings.HasPrefix(dateStr, "D:") {
		dateStr = dateStr[2:]
	}

	// Try different date formats
	formats := []string{
		"20060102150405-07'00'",
		"20060102150405",
		"20060102",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("unable to parse date: %s", dateStr)
}

// hasInteractiveForms checks if PDF has interactive forms
func (pe *PDFExtractor) hasInteractiveForms(ctx *pdfcpu.Context) bool {
	// Check if AcroForm exists in catalog
	if ctx.RootDict == nil {
		return false
	}

	catalog := ctx.RootDict
	if acroForm, ok := catalog.Find("AcroForm"); ok && acroForm != nil {
		return true
	}

	return false
}

// ExtractImages extracts images from PDF (if needed)
func (pe *PDFExtractor) ExtractImages(ctx context.Context, reader io.Reader) ([]ImageInfo, error) {
	pe.log.WithContext(ctx).Debug("🖼️ Extracting images from PDF...")

	// Read PDF data
	pdfData, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF data: %w", err)
	}

	// Create PDF context
	ctx_pdf, err := pdfcpu.ReadContextFromBytes(pdfData)
	if err != nil {
		return nil, fmt.Errorf("failed to create PDF context: %w", err)
	}

	var images []ImageInfo

	// Extract images from each page
	for i := 1; i <= ctx_pdf.PageCount; i++ {
		pageImages, err := pe.extractImagesFromPage(ctx_pdf, i)
		if err != nil {
			pe.log.WithContext(ctx).Warnf("Failed to extract images from page %d: %v", i, err)
			continue
		}
		images = append(images, pageImages...)
	}

	pe.log.WithContext(ctx).Debugf("🖼️ Extracted %d images from PDF", len(images))
	return images, nil
}

// extractImagesFromPage extracts images from a specific page
func (pe *PDFExtractor) extractImagesFromPage(ctx *pdfcpu.Context, pageNum int) ([]ImageInfo, error) {
	var images []ImageInfo

	// This is a simplified implementation
	// In a full implementation, you would traverse the page's resource dictionary
	// and extract image XObjects

	// For now, return empty slice
	// Full implementation would require deeper PDF parsing

	return images, nil
}

// ImageInfo represents extracted image information
type ImageInfo struct {
	Name       string `json:"name"`
	Format     string `json:"format"`
	Width      int    `json:"width"`
	Height     int    `json:"height"`
	Size       int64  `json:"size"`
	PageNumber int    `json:"page_number"`
	Data       []byte `json:"-"` // Image data
}

// ValidatePDF validates if the PDF is readable and not corrupted
func (pe *PDFExtractor) ValidatePDF(ctx context.Context, reader io.Reader) error {
	pe.log.WithContext(ctx).Debug("🔍 Validating PDF...")

	// Read PDF data
	pdfData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("failed to read PDF data: %w", err)
	}

	// Try to create PDF context
	_, err = pdfcpu.ReadContextFromBytes(pdfData)
	if err != nil {
		return fmt.Errorf("invalid PDF: %w", err)
	}

	pe.log.WithContext(ctx).Debug("✅ PDF validation successful")
	return nil
}

// GetPDFInfo returns basic PDF information without full extraction
func (pe *PDFExtractor) GetPDFInfo(ctx context.Context, reader io.Reader) (*PDFInfo, error) {
	// Read PDF data
	pdfData, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF data: %w", err)
	}

	// Create PDF context
	ctx_pdf, err := pdfcpu.ReadContextFromBytes(pdfData)
	if err != nil {
		return nil, fmt.Errorf("failed to create PDF context: %w", err)
	}

	info := &PDFInfo{
		PageCount:   ctx_pdf.PageCount,
		PDFVersion:  ctx_pdf.HeaderVersion,
		IsEncrypted: ctx_pdf.Encrypt != nil,
		FileSize:    int64(len(pdfData)),
	}

	return info, nil
}

// PDFInfo represents basic PDF information
type PDFInfo struct {
	PageCount   int    `json:"page_count"`
	PDFVersion  string `json:"pdf_version"`
	IsEncrypted bool   `json:"is_encrypted"`
	FileSize    int64  `json:"file_size"`
	HasForms    bool   `json:"has_forms"`
}
