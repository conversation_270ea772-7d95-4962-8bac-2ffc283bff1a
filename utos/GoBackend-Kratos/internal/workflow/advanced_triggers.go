// GoBackend-Kratos/internal/workflow/advanced_triggers.go
package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/robfig/cron/v3"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/langchain"
)

// 🚀 Advanced Workflow Trigger System
// Supports time-based, event-based, and webhook-based triggers for comprehensive automation

// AdvancedTrigger represents an enhanced trigger with multiple activation methods
type AdvancedTrigger struct {
	ID          uint                   `gorm:"primaryKey" json:"id"`
	Name        string                 `gorm:"type:varchar(200);not null" json:"name"`
	Description string                 `gorm:"type:text" json:"description"`
	Type        TriggerType            `gorm:"type:varchar(50);not null" json:"type"`
	IsActive    bool                   `gorm:"default:true" json:"is_active"`

	// Time-based triggers
	Schedule    *CronSchedule          `gorm:"embedded;embeddedPrefix:schedule_" json:"schedule,omitempty"`

	// Event-based triggers
	EventType   string                 `gorm:"type:varchar(100)" json:"event_type,omitempty"`
	EventFilter map[string]interface{} `gorm:"type:jsonb;default:'{}'" json:"event_filter,omitempty"`

	// Condition-based triggers
	Conditions  []TriggerCondition     `gorm:"type:jsonb;default:'[]'" json:"conditions"`

	// Webhook triggers
	WebhookURL  string                 `gorm:"type:varchar(500)" json:"webhook_url,omitempty"`
	WebhookAuth *WebhookAuth           `gorm:"embedded;embeddedPrefix:webhook_" json:"webhook_auth,omitempty"`

	// Execution settings
	MaxExecutions    *int              `json:"max_executions,omitempty"`
	ExecutionCount   int               `gorm:"default:0" json:"execution_count"`
	LastExecutedAt   *time.Time        `json:"last_executed_at"`
	NextExecutionAt  *time.Time        `json:"next_execution_at"`

	// Associated workflow
	WorkflowID  uint                   `gorm:"not null" json:"workflow_id"`
	Workflow    *biz.WorkflowRule      `gorm:"foreignKey:WorkflowID" json:"workflow,omitempty"`

	// Metadata
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	CreatedBy   string                 `gorm:"type:varchar(100)" json:"created_by"`
}

// TriggerType defines the type of trigger
type TriggerType string

const (
	TriggerTypeSchedule   TriggerType = "schedule"   // Time-based (cron)
	TriggerTypeEvent      TriggerType = "event"      // Event-driven
	TriggerTypeCondition  TriggerType = "condition"  // Condition-based
	TriggerTypeWebhook    TriggerType = "webhook"    // Webhook-based
	TriggerTypeManual     TriggerType = "manual"     // Manual execution
	TriggerTypeComposite  TriggerType = "composite"  // Multiple trigger types
)

// CronSchedule represents a cron-based schedule
type CronSchedule struct {
	Expression    string    `gorm:"type:varchar(100)" json:"expression"`
	Timezone      string    `gorm:"type:varchar(50);default:'UTC'" json:"timezone"`
	StartDate     *time.Time `json:"start_date,omitempty"`
	EndDate       *time.Time `json:"end_date,omitempty"`
	IsRecurring   bool      `gorm:"default:true" json:"is_recurring"`
}

// TriggerCondition represents a condition that must be met
type TriggerCondition struct {
	Field     string      `json:"field"`
	Operator  string      `json:"operator"`
	Value     interface{} `json:"value"`
	DataType  string      `json:"data_type"`
}

// WebhookAuth represents webhook authentication settings
type WebhookAuth struct {
	Type      string            `gorm:"type:varchar(50)" json:"type"`
	Headers   map[string]string `gorm:"type:jsonb;default:'{}'" json:"headers"`
	Token     string            `gorm:"type:varchar(500)" json:"token,omitempty"`
	Username  string            `gorm:"type:varchar(100)" json:"username,omitempty"`
	Password  string            `gorm:"type:varchar(100)" json:"password,omitempty"`
}

// AdvancedTriggerEngine manages advanced workflow triggers
type AdvancedTriggerEngine struct {
	log        *log.Helper
	db         *gorm.DB
	cron       *cron.Cron
	eventChan  chan *WorkflowEvent
	stopChan   chan bool
	triggers   map[uint]*AdvancedTrigger
	workflows  *langchain.WorkflowEngine
}

// WorkflowEvent represents an event that can trigger workflows
type WorkflowEvent struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id,omitempty"`
}

// NewAdvancedTriggerEngine creates a new advanced trigger engine
func NewAdvancedTriggerEngine(db *gorm.DB, workflows *langchain.WorkflowEngine, logger *log.Helper) *AdvancedTriggerEngine {
	return &AdvancedTriggerEngine{
		log:       logger,
		db:        db,
		cron:      cron.New(cron.WithSeconds()),
		eventChan: make(chan *WorkflowEvent, 1000),
		stopChan:  make(chan bool),
		triggers:  make(map[uint]*AdvancedTrigger),
		workflows: workflows,
	}
}

// Start initializes and starts the advanced trigger engine
func (ate *AdvancedTriggerEngine) Start(ctx context.Context) error {
	ate.log.Info("🚀 Starting Advanced Trigger Engine")

	// Load all active triggers from database
	if err := ate.loadTriggers(ctx); err != nil {
		return fmt.Errorf("failed to load triggers: %w", err)
	}

	// Start cron scheduler
	ate.cron.Start()

	// Start event processor
	go ate.processEvents()

	// Start condition checker
	go ate.checkConditionTriggers(ctx)

	ate.log.Info("✅ Advanced Trigger Engine started successfully")
	return nil
}

// Stop stops the advanced trigger engine
func (ate *AdvancedTriggerEngine) Stop() {
	ate.log.Info("🛑 Stopping Advanced Trigger Engine")

	// Stop cron scheduler
	ate.cron.Stop()

	// Stop event processor
	ate.stopChan <- true

	ate.log.Info("✅ Advanced Trigger Engine stopped")
}

// loadTriggers loads all active triggers from the database
func (ate *AdvancedTriggerEngine) loadTriggers(ctx context.Context) error {
	var triggers []AdvancedTrigger

	err := ate.db.WithContext(ctx).
		Where("is_active = ?", true).
		Preload("Workflow").
		Find(&triggers).Error

	if err != nil {
		return err
	}

	ate.log.Infof("📋 Loading %d active triggers", len(triggers))

	for _, trigger := range triggers {
		if err := ate.registerTrigger(&trigger); err != nil {
			ate.log.Errorf("❌ Failed to register trigger %s: %v", trigger.Name, err)
			continue
		}

		ate.triggers[trigger.ID] = &trigger
	}

	ate.log.Infof("✅ Loaded %d triggers successfully", len(ate.triggers))
	return nil
}

// registerTrigger registers a trigger based on its type
func (ate *AdvancedTriggerEngine) registerTrigger(trigger *AdvancedTrigger) error {
	switch trigger.Type {
	case TriggerTypeSchedule:
		return ate.registerScheduleTrigger(trigger)
	case TriggerTypeEvent:
		return ate.registerEventTrigger(trigger)
	case TriggerTypeCondition:
		return ate.registerConditionTrigger(trigger)
	case TriggerTypeWebhook:
		return ate.registerWebhookTrigger(trigger)
	case TriggerTypeComposite:
		return ate.registerCompositeTrigger(trigger)
	default:
		return fmt.Errorf("unsupported trigger type: %s", trigger.Type)
	}
}

// registerScheduleTrigger registers a cron-based schedule trigger
func (ate *AdvancedTriggerEngine) registerScheduleTrigger(trigger *AdvancedTrigger) error {
	if trigger.Schedule == nil {
		return fmt.Errorf("schedule configuration is required for schedule trigger")
	}

	// Create cron job
	_, err := ate.cron.AddFunc(trigger.Schedule.Expression, func() {
		ate.executeScheduleTrigger(trigger)
	})

	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}

	ate.log.Infof("📅 Registered schedule trigger: %s (%s)", trigger.Name, trigger.Schedule.Expression)
	return nil
}

// registerEventTrigger registers an event-based trigger
func (ate *AdvancedTriggerEngine) registerEventTrigger(trigger *AdvancedTrigger) error {
	ate.log.Infof("📡 Registered event trigger: %s (event: %s)", trigger.Name, trigger.EventType)
	return nil
}

// registerConditionTrigger registers a condition-based trigger
func (ate *AdvancedTriggerEngine) registerConditionTrigger(trigger *AdvancedTrigger) error {
	ate.log.Infof("🔍 Registered condition trigger: %s (%d conditions)", trigger.Name, len(trigger.Conditions))
	return nil
}

// registerWebhookTrigger registers a webhook-based trigger
func (ate *AdvancedTriggerEngine) registerWebhookTrigger(trigger *AdvancedTrigger) error {
	ate.log.Infof("🔗 Registered webhook trigger: %s (URL: %s)", trigger.Name, trigger.WebhookURL)
	return nil
}

// registerCompositeTrigger registers a composite trigger (multiple types)
func (ate *AdvancedTriggerEngine) registerCompositeTrigger(trigger *AdvancedTrigger) error {
	ate.log.Infof("🔄 Registered composite trigger: %s", trigger.Name)
	return nil
}

// executeScheduleTrigger executes a schedule-based trigger
func (ate *AdvancedTriggerEngine) executeScheduleTrigger(trigger *AdvancedTrigger) {
	ctx := context.Background()

	ate.log.Infof("⏰ Executing schedule trigger: %s", trigger.Name)

	// Check if trigger has reached max executions
	if trigger.MaxExecutions != nil && trigger.ExecutionCount >= *trigger.MaxExecutions {
		ate.log.Warnf("⚠️ Trigger %s has reached max executions (%d)", trigger.Name, *trigger.MaxExecutions)
		return
	}

	// Execute associated workflow
	if trigger.Workflow != nil {
		// Create workflow request
		workflowReq := &langchain.WorkflowRequest{
			WorkflowID: fmt.Sprintf("%d", trigger.Workflow.ID),
			Context: map[string]interface{}{
				"trigger_type": "schedule",
				"trigger_id":   trigger.ID,
			},
		}

		result, err := ate.workflows.ExecuteWorkflow(ctx, workflowReq)

		// Update execution statistics
		success := err == nil && result != nil && result.Status == "completed"
		ate.updateTriggerExecution(ctx, trigger, success)

		if success {
			ate.log.Infof("✅ Schedule trigger %s executed successfully", trigger.Name)
		} else {
			errorMsg := "unknown error"
			if err != nil {
				errorMsg = err.Error()
			} else if result != nil && len(result.Errors) > 0 {
				errorMsg = result.Errors[0]
			}
			ate.log.Errorf("❌ Schedule trigger %s execution failed: %s", trigger.Name, errorMsg)
		}
	}
}

// processEvents processes incoming events for event-based triggers
func (ate *AdvancedTriggerEngine) processEvents() {
	for {
		select {
		case event := <-ate.eventChan:
			ate.handleEvent(event)
		case <-ate.stopChan:
			return
		}
	}
}

// handleEvent handles an incoming event and checks for matching triggers
func (ate *AdvancedTriggerEngine) handleEvent(event *WorkflowEvent) {
	ctx := context.Background()

	for _, trigger := range ate.triggers {
		if trigger.Type == TriggerTypeEvent && trigger.EventType == event.Type {
			// Check event filter conditions
			if ate.matchesEventFilter(event, trigger.EventFilter) {
				ate.log.Infof("📡 Event trigger matched: %s for event %s", trigger.Name, event.Type)

				// Execute workflow
				if trigger.Workflow != nil {
					// Create workflow request
					workflowReq := &langchain.WorkflowRequest{
						WorkflowID: fmt.Sprintf("%d", trigger.Workflow.ID),
						Context: map[string]interface{}{
							"trigger_type": "event",
							"event":        event,
						},
					}

					result, err := ate.workflows.ExecuteWorkflow(ctx, workflowReq)
					success := err == nil && result != nil && result.Status == "completed"
					ate.updateTriggerExecution(ctx, trigger, success)
				}
			}
		}
	}
}

// checkConditionTriggers periodically checks condition-based triggers
func (ate *AdvancedTriggerEngine) checkConditionTriggers(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ate.evaluateConditionTriggers(ctx)
		case <-ate.stopChan:
			return
		}
	}
}

// evaluateConditionTriggers evaluates all condition-based triggers
func (ate *AdvancedTriggerEngine) evaluateConditionTriggers(ctx context.Context) {
	for _, trigger := range ate.triggers {
		if trigger.Type == TriggerTypeCondition {
			if ate.evaluateConditions(trigger.Conditions) {
				ate.log.Infof("🔍 Condition trigger matched: %s", trigger.Name)

				// Execute workflow
				if trigger.Workflow != nil {
					// Create workflow request
					workflowReq := &langchain.WorkflowRequest{
						WorkflowID: fmt.Sprintf("%d", trigger.Workflow.ID),
						Context: map[string]interface{}{
							"trigger_type": "condition",
							"conditions":   trigger.Conditions,
						},
					}

					result, err := ate.workflows.ExecuteWorkflow(ctx, workflowReq)
					success := err == nil && result != nil && result.Status == "completed"
					ate.updateTriggerExecution(ctx, trigger, success)
				}
			}
		}
	}
}

// matchesEventFilter checks if an event matches the trigger's filter
func (ate *AdvancedTriggerEngine) matchesEventFilter(event *WorkflowEvent, filter map[string]interface{}) bool {
	if len(filter) == 0 {
		return true // No filter means match all
	}

	for key, expectedValue := range filter {
		if actualValue, exists := event.Data[key]; !exists || actualValue != expectedValue {
			return false
		}
	}

	return true
}

// evaluateConditions evaluates a set of trigger conditions
func (ate *AdvancedTriggerEngine) evaluateConditions(conditions []TriggerCondition) bool {
	if len(conditions) == 0 {
		return false
	}

	// For now, all conditions must be true (AND logic)
	for _, condition := range conditions {
		if !ate.evaluateCondition(condition) {
			return false
		}
	}

	return true
}

// evaluateCondition evaluates a single condition
func (ate *AdvancedTriggerEngine) evaluateCondition(condition TriggerCondition) bool {
	// This would integrate with the database to check actual values
	// For now, return a placeholder implementation

	ate.log.Debugf("🔍 Evaluating condition: %s %s %v", condition.Field, condition.Operator, condition.Value)

	// Placeholder: randomly return true/false for demonstration
	// In real implementation, this would query the database
	return false
}

// updateTriggerExecution updates trigger execution statistics
func (ate *AdvancedTriggerEngine) updateTriggerExecution(ctx context.Context, trigger *AdvancedTrigger, success bool) {
	now := time.Now()

	updates := map[string]interface{}{
		"execution_count":   trigger.ExecutionCount + 1,
		"last_executed_at":  now,
		"updated_at":        now,
	}

	err := ate.db.WithContext(ctx).
		Model(&AdvancedTrigger{}).
		Where("id = ?", trigger.ID).
		Updates(updates).Error

	if err != nil {
		ate.log.Errorf("❌ Failed to update trigger execution stats: %v", err)
	}

	// Update in-memory trigger
	trigger.ExecutionCount++
	trigger.LastExecutedAt = &now
}

// SendEvent sends an event to the trigger engine
func (ate *AdvancedTriggerEngine) SendEvent(event *WorkflowEvent) {
	select {
	case ate.eventChan <- event:
		ate.log.Debugf("📡 Event sent: %s", event.Type)
	default:
		ate.log.Warnf("⚠️ Event channel full, dropping event: %s", event.Type)
	}
}

// CreateTrigger creates a new advanced trigger
func (ate *AdvancedTriggerEngine) CreateTrigger(ctx context.Context, trigger *AdvancedTrigger) error {
	// Validate trigger configuration
	if err := ate.validateTrigger(trigger); err != nil {
		return fmt.Errorf("trigger validation failed: %w", err)
	}

	// Save to database
	if err := ate.db.WithContext(ctx).Create(trigger).Error; err != nil {
		return fmt.Errorf("failed to create trigger: %w", err)
	}

	// Register trigger if active
	if trigger.IsActive {
		if err := ate.registerTrigger(trigger); err != nil {
			ate.log.Errorf("❌ Failed to register new trigger: %v", err)
		} else {
			ate.triggers[trigger.ID] = trigger
		}
	}

	ate.log.Infof("✅ Created new trigger: %s", trigger.Name)
	return nil
}

// validateTrigger validates trigger configuration
func (ate *AdvancedTriggerEngine) validateTrigger(trigger *AdvancedTrigger) error {
	if strings.TrimSpace(trigger.Name) == "" {
		return fmt.Errorf("trigger name is required")
	}

	switch trigger.Type {
	case TriggerTypeSchedule:
		if trigger.Schedule == nil || trigger.Schedule.Expression == "" {
			return fmt.Errorf("schedule expression is required for schedule trigger")
		}
	case TriggerTypeEvent:
		if trigger.EventType == "" {
			return fmt.Errorf("event type is required for event trigger")
		}
	case TriggerTypeCondition:
		if len(trigger.Conditions) == 0 {
			return fmt.Errorf("at least one condition is required for condition trigger")
		}
	case TriggerTypeWebhook:
		if trigger.WebhookURL == "" {
			return fmt.Errorf("webhook URL is required for webhook trigger")
		}
	}

	return nil
}
