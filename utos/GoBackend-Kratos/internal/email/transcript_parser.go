package email

import (
	"context"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/ai"

	"github.com/go-kratos/kratos/v2/log"
)

// 📝 TranscriptParser - Specialized parser for call transcriptions
type TranscriptParser struct {
	log    *log.Helper
	gemma3 *ai.Gemma3Service
}

// EmailAttachment represents an email attachment
type EmailAttachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Content     []byte `json:"content"`
	Size        int64  `json:"size"`
}

// SentimentAnalysis represents sentiment analysis results
type SentimentAnalysis struct {
	Sentiment         string  `json:"sentiment"`
	Score             float64 `json:"score"`
	Confidence        float64 `json:"confidence"`
	EmotionalTone     string  `json:"emotional_tone"`
	SatisfactionLevel string  `json:"satisfaction_level"`
}

// ActionItem represents an action item extracted from transcription
type ActionItem struct {
	Type        string     `json:"type"`
	Description string     `json:"description"`
	Assignee    string     `json:"assignee,omitempty"`
	DueDate     *time.Time `json:"due_date,omitempty"`
	Priority    string     `json:"priority"`
	Status      string     `json:"status"`
}

// EquipmentRecord represents equipment in customer inventory
type EquipmentRecord struct {
	ID           string     `json:"id"`
	Type         string     `json:"type"`
	Brand        string     `json:"brand"`
	Model        string     `json:"model"`
	SerialNumber string     `json:"serial_number,omitempty"`
	Location     string     `json:"location"`
	InstallDate  *time.Time `json:"install_date,omitempty"`
	WarrantyEnd  *time.Time `json:"warranty_end,omitempty"`
	Status       string     `json:"status"`
}

// ServiceRecord represents a service history record
type ServiceRecord struct {
	ID          string    `json:"id"`
	Date        time.Time `json:"date"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Technician  string    `json:"technician,omitempty"`
	Cost        *float64  `json:"cost,omitempty"`
	Status      string    `json:"status"`
}

// NewTranscriptParser creates a new transcript parser
func NewTranscriptParser(logger log.Logger, gemma3 *ai.Gemma3Service) *TranscriptParser {
	return &TranscriptParser{
		log:    log.NewHelper(logger),
		gemma3: gemma3,
	}
}

// ParseTranscriptions parses transcription attachments
func (tp *TranscriptParser) ParseTranscriptions(ctx context.Context, attachments []EmailAttachment) (*TranscriptionAnalysis, error) {
	tp.log.WithContext(ctx).Debug("📝 Parsing transcription attachments...")

	var transcriptionContent string
	var transcriptSource string

	// Look for transcription files
	for _, attachment := range attachments {
		if tp.isTranscriptionFile(attachment.Filename) {
			transcriptionContent = string(attachment.Content)
			transcriptSource = attachment.Filename
			break
		}
	}

	if transcriptionContent == "" {
		tp.log.WithContext(ctx).Debug("📭 No transcription files found")
		return nil, nil
	}

	tp.log.WithContext(ctx).Infof("📝 Found transcription: %s", transcriptSource)

	// Parse transcription content
	analysis := &TranscriptionAnalysis{
		HasTranscription: true,
		TranscriptSource: transcriptSource,
	}

	// Extract speaker segments
	if err := tp.extractSpeakerSegments(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract speaker segments: %v", err)
	}

	// Extract technical terms
	if err := tp.extractTechnicalTerms(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract technical terms: %v", err)
	}

	// Extract equipment mentions
	if err := tp.extractEquipmentMentions(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract equipment mentions: %v", err)
	}

	// Extract service requests
	if err := tp.extractServiceRequests(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract service requests: %v", err)
	}

	// Extract urgency indicators
	if err := tp.extractUrgencyIndicators(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract urgency indicators: %v", err)
	}

	// Analyze customer sentiment
	if err := tp.analyzeCustomerSentiment(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to analyze customer sentiment: %v", err)
	}

	// Extract action items
	if err := tp.extractActionItems(ctx, transcriptionContent, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to extract action items: %v", err)
	}

	// Estimate call value
	if err := tp.estimateCallValue(ctx, analysis); err != nil {
		tp.log.WithContext(ctx).Warnf("Failed to estimate call value: %v", err)
	}

	tp.log.WithContext(ctx).Infof("✅ Transcription analysis completed: %d segments, %d technical terms",
		len(analysis.SpeakerSegments), len(analysis.TechnicalTerms))

	return analysis, nil
}

// isTranscriptionFile checks if filename indicates a transcription
func (tp *TranscriptParser) isTranscriptionFile(filename string) bool {
	filename = strings.ToLower(filename)
	transcriptionIndicators := []string{
		"transcript", "transcription", "call", "recording",
		"conversation", "meeting", "audio", "voice",
	}

	for _, indicator := range transcriptionIndicators {
		if strings.Contains(filename, indicator) {
			return true
		}
	}

	// Check file extensions
	extensions := []string{".txt", ".docx", ".pdf"}
	for _, ext := range extensions {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}

	return false
}

// extractSpeakerSegments extracts speaker segments from transcription
func (tp *TranscriptParser) extractSpeakerSegments(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("🗣️ Extracting speaker segments...")

	// Pattern to match speaker segments: [Speaker]: text or Speaker: text
	speakerPattern := regexp.MustCompile(`(?i)(?:\[([^\]]+)\]|([^:]+)):\s*(.+)`)
	timePattern := regexp.MustCompile(`(?i)(\d{1,2}:\d{2}(?::\d{2})?)\s*-\s*(\d{1,2}:\d{2}(?::\d{2})?)`)

	lines := strings.Split(content, "\n")
	var segments []SpeakerSegment
	participantMap := make(map[string]bool)

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Extract time information if present
		var startTime, endTime time.Duration
		if timeMatch := timePattern.FindStringSubmatch(line); timeMatch != nil {
			startTime = tp.parseTimeString(timeMatch[1])
			endTime = tp.parseTimeString(timeMatch[2])
		}

		// Extract speaker and text
		if speakerMatch := speakerPattern.FindStringSubmatch(line); speakerMatch != nil {
			speaker := speakerMatch[1]
			if speaker == "" {
				speaker = speakerMatch[2]
			}
			text := speakerMatch[3]

			speaker = strings.TrimSpace(speaker)
			text = strings.TrimSpace(text)

			if speaker != "" && text != "" {
				segment := SpeakerSegment{
					Speaker:    speaker,
					StartTime:  startTime,
					EndTime:    endTime,
					Text:       text,
					Confidence: 0.8, // Default confidence
					Sentiment:  tp.quickSentimentAnalysis(text),
				}

				segments = append(segments, segment)
				participantMap[speaker] = true
			}
		}
	}

	analysis.SpeakerSegments = segments
	analysis.ParticipantCount = len(participantMap)

	tp.log.WithContext(ctx).Infof("🗣️ Extracted %d speaker segments with %d participants",
		len(segments), len(participantMap))

	return nil
}

// parseTimeString parses time string to duration
func (tp *TranscriptParser) parseTimeString(timeStr string) time.Duration {
	parts := strings.Split(timeStr, ":")
	if len(parts) < 2 {
		return 0
	}

	hours := 0
	minutes := 0
	seconds := 0

	if len(parts) == 3 {
		hours, _ = strconv.Atoi(parts[0])
		minutes, _ = strconv.Atoi(parts[1])
		seconds, _ = strconv.Atoi(parts[2])
	} else {
		minutes, _ = strconv.Atoi(parts[0])
		seconds, _ = strconv.Atoi(parts[1])
	}

	return time.Duration(hours)*time.Hour +
		time.Duration(minutes)*time.Minute +
		time.Duration(seconds)*time.Second
}

// quickSentimentAnalysis performs quick sentiment analysis
func (tp *TranscriptParser) quickSentimentAnalysis(text string) string {
	text = strings.ToLower(text)

	positiveWords := []string{"good", "great", "excellent", "satisfied", "happy", "pleased", "thank"}
	negativeWords := []string{"bad", "terrible", "awful", "angry", "frustrated", "disappointed", "problem"}

	positiveCount := 0
	negativeCount := 0

	for _, word := range positiveWords {
		if strings.Contains(text, word) {
			positiveCount++
		}
	}

	for _, word := range negativeWords {
		if strings.Contains(text, word) {
			negativeCount++
		}
	}

	if positiveCount > negativeCount {
		return "positive"
	} else if negativeCount > positiveCount {
		return "negative"
	}

	return "neutral"
}

// extractTechnicalTerms extracts HVAC technical terms
func (tp *TranscriptParser) extractTechnicalTerms(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("🔧 Extracting technical terms...")

	hvacTerms := []string{
		"air conditioning", "ac", "hvac", "heat pump", "furnace", "boiler",
		"ductwork", "vents", "thermostat", "compressor", "condenser", "evaporator",
		"refrigerant", "freon", "filter", "maintenance", "repair", "installation",
		"cooling", "heating", "ventilation", "temperature", "humidity",
		"split system", "central air", "mini split", "ductless",
	}

	content = strings.ToLower(content)
	var foundTerms []string
	termMap := make(map[string]bool)

	for _, term := range hvacTerms {
		if strings.Contains(content, term) && !termMap[term] {
			foundTerms = append(foundTerms, term)
			termMap[term] = true
		}
	}

	analysis.TechnicalTerms = foundTerms

	tp.log.WithContext(ctx).Infof("🔧 Found %d technical terms", len(foundTerms))
	return nil
}

// extractEquipmentMentions extracts equipment mentions from transcription
func (tp *TranscriptParser) extractEquipmentMentions(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("🏭 Extracting equipment mentions...")

	equipmentTerms := []string{
		"unit", "system", "conditioner", "heater", "pump", "fan", "motor",
		"coil", "valve", "pipe", "duct", "register", "grill", "panel",
	}

	content = strings.ToLower(content)
	var foundEquipment []string
	equipmentMap := make(map[string]bool)

	for _, term := range equipmentTerms {
		if strings.Contains(content, term) && !equipmentMap[term] {
			foundEquipment = append(foundEquipment, term)
			equipmentMap[term] = true
		}
	}

	// Store in metadata for now
	if analysis.Metadata == nil {
		analysis.Metadata = make(map[string]interface{})
	}
	analysis.Metadata["equipment_mentions"] = foundEquipment

	tp.log.WithContext(ctx).Infof("🏭 Found %d equipment mentions", len(foundEquipment))
	return nil
}

// extractServiceRequests extracts service requests from transcription
func (tp *TranscriptParser) extractServiceRequests(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("🔧 Extracting service requests...")

	serviceKeywords := []string{
		"repair", "fix", "broken", "not working", "maintenance", "service",
		"install", "replace", "clean", "check", "inspect", "tune-up",
	}

	content = strings.ToLower(content)
	var foundRequests []string
	requestMap := make(map[string]bool)

	for _, keyword := range serviceKeywords {
		if strings.Contains(content, keyword) && !requestMap[keyword] {
			foundRequests = append(foundRequests, keyword)
			requestMap[keyword] = true
		}
	}

	// Store in metadata
	if analysis.Metadata == nil {
		analysis.Metadata = make(map[string]interface{})
	}
	analysis.Metadata["service_requests"] = foundRequests

	tp.log.WithContext(ctx).Infof("🔧 Found %d service requests", len(foundRequests))
	return nil
}

// extractUrgencyIndicators extracts urgency indicators from transcription
func (tp *TranscriptParser) extractUrgencyIndicators(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("⚡ Extracting urgency indicators...")

	urgencyKeywords := []string{
		"urgent", "emergency", "asap", "immediately", "right away", "critical",
		"broken down", "not working", "no heat", "no cooling", "freezing", "hot",
	}

	content = strings.ToLower(content)
	urgencyScore := 0
	var foundIndicators []string

	for _, keyword := range urgencyKeywords {
		if strings.Contains(content, keyword) {
			urgencyScore++
			foundIndicators = append(foundIndicators, keyword)
		}
	}

	// Determine urgency level
	urgencyLevel := "low"
	if urgencyScore >= 3 {
		urgencyLevel = "high"
	} else if urgencyScore >= 1 {
		urgencyLevel = "medium"
	}

	// Store in metadata
	if analysis.Metadata == nil {
		analysis.Metadata = make(map[string]interface{})
	}
	analysis.Metadata["urgency_level"] = urgencyLevel
	analysis.Metadata["urgency_indicators"] = foundIndicators

	tp.log.WithContext(ctx).Infof("⚡ Urgency level: %s (score: %d)", urgencyLevel, urgencyScore)
	return nil
}

// analyzeCustomerSentiment analyzes customer sentiment from transcription
func (tp *TranscriptParser) analyzeCustomerSentiment(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("😊 Analyzing customer sentiment...")

	// Use the existing quick sentiment analysis
	sentiment := tp.quickSentimentAnalysis(content)

	// Store in metadata
	if analysis.Metadata == nil {
		analysis.Metadata = make(map[string]interface{})
	}
	analysis.Metadata["customer_sentiment"] = sentiment

	tp.log.WithContext(ctx).Infof("😊 Customer sentiment: %s", sentiment)
	return nil
}

// extractActionItems extracts action items from transcription
func (tp *TranscriptParser) extractActionItems(ctx context.Context, content string, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("📋 Extracting action items...")

	actionKeywords := []string{
		"schedule", "appointment", "call back", "follow up", "send quote",
		"order parts", "contact", "visit", "inspect", "estimate",
	}

	content = strings.ToLower(content)
	var foundActions []string
	actionMap := make(map[string]bool)

	for _, keyword := range actionKeywords {
		if strings.Contains(content, keyword) && !actionMap[keyword] {
			foundActions = append(foundActions, keyword)
			actionMap[keyword] = true
		}
	}

	// Store in metadata
	if analysis.Metadata == nil {
		analysis.Metadata = make(map[string]interface{})
	}
	analysis.Metadata["action_items"] = foundActions

	tp.log.WithContext(ctx).Infof("📋 Found %d action items", len(foundActions))
	return nil
}

// estimateCallValue estimates the potential value of the call
func (tp *TranscriptParser) estimateCallValue(ctx context.Context, analysis *TranscriptionAnalysis) error {
	tp.log.WithContext(ctx).Debug("💰 Estimating call value...")

	// Base value estimation logic
	baseValue := 100.0 // Base service call value

	// Increase value based on technical terms
	if len(analysis.TechnicalTerms) > 5 {
		baseValue += 200.0 // Complex technical discussion
	} else if len(analysis.TechnicalTerms) > 2 {
		baseValue += 100.0 // Moderate technical discussion
	}

	// Increase value based on speaker segments (longer calls = more complex)
	if len(analysis.SpeakerSegments) > 20 {
		baseValue += 300.0 // Long, detailed conversation
	} else if len(analysis.SpeakerSegments) > 10 {
		baseValue += 150.0 // Moderate conversation
	}

	// Check for high-value keywords in technical terms
	highValueTerms := []string{"installation", "replacement", "new system", "upgrade", "commercial"}
	for _, term := range analysis.TechnicalTerms {
		for _, hvTerm := range highValueTerms {
			if strings.Contains(strings.ToLower(term), hvTerm) {
				baseValue += 500.0
				break
			}
		}
	}

	analysis.EstimatedValue = &baseValue

	tp.log.WithContext(ctx).Infof("💰 Estimated call value: $%.2f", baseValue)
	return nil
}
