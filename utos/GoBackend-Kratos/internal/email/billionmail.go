package email

import (
	"bytes"
	"context"
	"fmt"
	"net/smtp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/conf"
)

// SimpleEmailService provides basic SMTP/IMAP client functionality
type SimpleEmailService struct {
	config     *conf.Email
	smtpAuth   smtp.Auth
	log        *log.Helper
}

// EmailMessage represents an email message
type EmailMessage struct {
	ID          string            `json:"id"`
	From        string            `json:"from"`
	To          []string          `json:"to"`
	CC          []string          `json:"cc,omitempty"`
	BCC         []string          `json:"bcc,omitempty"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	HTMLBody    string            `json:"html_body,omitempty"`
	Attachments []Attachment      `json:"attachments,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	Priority    string            `json:"priority,omitempty"`
	Timestamp   time.Time         `json:"timestamp"`
	Status      string            `json:"status"`
}

// Attachment represents an email attachment
type Attachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Size        int64  `json:"size"`
	Data        []byte `json:"data,omitempty"`
	URL         string `json:"url,omitempty"`
}

// Campaign represents an email marketing campaign
type Campaign struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Subject     string    `json:"subject"`
	Template    string    `json:"template"`
	Recipients  []string  `json:"recipients"`
	ScheduledAt time.Time `json:"scheduled_at"`
	Status      string    `json:"status"`
	Stats       CampaignStats `json:"stats"`
}

// CampaignStats represents campaign statistics
type CampaignStats struct {
	Sent      int     `json:"sent"`
	Delivered int     `json:"delivered"`
	Opened    int     `json:"opened"`
	Clicked   int     `json:"clicked"`
	Bounced   int     `json:"bounced"`
	OpenRate  float64 `json:"open_rate"`
	ClickRate float64 `json:"click_rate"`
}// NewSimpleEmailService creates a new simple email service
func NewSimpleEmailService(config *conf.Email, logger log.Logger) *SimpleEmailService {
	// Setup SMTP authentication
	var auth smtp.Auth
	if config.Smtp != nil && config.Smtp.Username != "" {
		auth = smtp.PlainAuth("", config.Smtp.Username, config.Smtp.Password, config.Smtp.Host)
	}

	return &SimpleEmailService{
		config:   config,
		smtpAuth: auth,
		log:      log.NewHelper(logger),
	}
}

// SendEmail sends an email through external SMTP server
func (s *SimpleEmailService) SendEmail(ctx context.Context, msg *EmailMessage) error {
	s.log.WithContext(ctx).Infof("Sending email to: %v", msg.To)

	// Send via SMTP only (no API fallback needed)
	return s.sendEmailSMTP(ctx, msg)
}

// sendEmailSMTP sends email via SMTP
func (s *SimpleEmailService) sendEmailSMTP(ctx context.Context, msg *EmailMessage) error {
	if s.config.Smtp == nil {
		return fmt.Errorf("SMTP configuration not available")
	}

	// Prepare email message
	var emailBody bytes.Buffer

	// Headers
	emailBody.WriteString(fmt.Sprintf("From: %s\r\n", msg.From))
	emailBody.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ", ")))
	if len(msg.CC) > 0 {
		emailBody.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(msg.CC, ", ")))
	}
	emailBody.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
	emailBody.WriteString("MIME-Version: 1.0\r\n")

	if msg.HTMLBody != "" {
		emailBody.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		emailBody.WriteString("\r\n")
		emailBody.WriteString(msg.HTMLBody)
	} else {
		emailBody.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		emailBody.WriteString("\r\n")
		emailBody.WriteString(msg.Body)
	}

	// Combine all recipients
	allRecipients := append(msg.To, msg.CC...)
	allRecipients = append(allRecipients, msg.BCC...)

	// Send email
	smtpAddr := fmt.Sprintf("%s:%d", s.config.Smtp.Host, s.config.Smtp.Port)
	return smtp.SendMail(smtpAddr, s.smtpAuth, msg.From, allRecipients, emailBody.Bytes())
}

// 📧 Simple email service - no API calls needed, only SMTP client

// 📧 Simplified Email Functions - Focus on IMAP/SMTP client only

// ListEmails retrieves emails using IMAP (moved to retrieval.go)
func (s *SimpleEmailService) ListEmails(ctx context.Context, limit, offset int) ([]*EmailMessage, error) {
	s.log.WithContext(ctx).Infof("📧 Use EmailRetrievalService for listing emails from IMAP servers")

	// This function is now handled by EmailRetrievalService in retrieval.go
	// which connects directly to external IMAP servers
	return nil, fmt.Errorf("use EmailRetrievalService for email retrieval")
}

// 🗑️ Removed BillionMail API calls - using only SMTP/IMAP clients

// AnalyzeEmailSentiment analyzes email sentiment using AI
func (s *SimpleEmailService) AnalyzeEmailSentiment(ctx context.Context, emailContent string) (string, float64, error) {
	s.log.WithContext(ctx).Info("Analyzing email sentiment")

	// This integrates with LM Studio Gemma model for sentiment analysis
	// Mock implementation - will be replaced with actual LM Studio API call
	sentiments := []string{"positive", "neutral", "negative"}
	sentiment := sentiments[len(emailContent)%3]
	confidence := 0.85

	return sentiment, confidence, nil
}

// CreateHVACEmailTemplate creates HVAC-specific email templates
func (s *SimpleEmailService) CreateHVACEmailTemplate(templateType string) string {
	templates := map[string]string{
		"service_reminder": `
Dear {{.CustomerName}},

This is a friendly reminder that your HVAC system is due for maintenance.
Regular maintenance helps ensure optimal performance and extends equipment life.

Service Details:
- Customer: {{.CustomerName}}
- Address: {{.CustomerAddress}}
- System Type: {{.SystemType}}
- Last Service: {{.LastServiceDate}}

Please contact us to schedule your appointment.

Best regards,
{{.CompanyName}} HVAC Services
`,
		"quote_follow_up": `
Dear {{.CustomerName}},

Thank you for your interest in our HVAC services. Please find your quote attached.

Quote Summary:
- Service: {{.ServiceType}}
- Estimated Cost: {{.EstimatedCost}}
- Timeline: {{.Timeline}}

This quote is valid for 30 days. Please don't hesitate to contact us with any questions.

Best regards,
{{.CompanyName}}
`,
	}

	if template, exists := templates[templateType]; exists {
		return template
	}

	return "Default HVAC email template"
}