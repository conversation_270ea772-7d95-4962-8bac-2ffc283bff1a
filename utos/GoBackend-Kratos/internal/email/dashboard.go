package email

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
)

// 📊 Email Analysis Dashboard Service
type EmailDashboardService struct {
	log             *log.Helper
	analysisService *EmailAnalysisService
	emailStore      EmailStoreInterface
}

// 📈 Dashboard Statistics
type DashboardStats struct {
	TotalEmails        int                    `json:"total_emails"`
	TodayEmails        int                    `json:"today_emails"`
	HVACEmails         int                    `json:"hvac_emails"`
	HighPriorityEmails int                    `json:"high_priority_emails"`
	AttachmentsCount   int                    `json:"attachments_count"`
	SentimentBreakdown *SentimentBreakdown    `json:"sentiment_breakdown"`
	CategoryBreakdown  *CategoryBreakdown     `json:"category_breakdown"`
	RecentAnalysis     []*EmailAnalysisResult `json:"recent_analysis"`
	TopKeywords        []KeywordCount         `json:"top_keywords"`
	ProcessingMetrics  *ProcessingMetrics     `json:"processing_metrics"`
}

// 💭 Sentiment Breakdown
type SentimentBreakdown struct {
	Positive int `json:"positive"`
	Negative int `json:"negative"`
	Neutral  int `json:"neutral"`
}

// 📂 Category Breakdown
type CategoryBreakdown struct {
	HVACService int `json:"hvac_service"`
	General     int `json:"general"`
	Support     int `json:"support"`
	Sales       int `json:"sales"`
}

// 🔑 Keyword Count
type KeywordCount struct {
	Keyword string `json:"keyword"`
	Count   int    `json:"count"`
}

// ⚡ Processing Metrics
type ProcessingMetrics struct {
	AverageProcessingTime time.Duration `json:"average_processing_time"`
	SuccessRate          float64       `json:"success_rate"`
	ErrorRate            float64       `json:"error_rate"`
	LastProcessed        time.Time     `json:"last_processed"`
}

// 📧 EmailStoreInterface defines the interface for email storage
type EmailStoreInterface interface {
	Store(result *EmailAnalysisResult) error
	Get(emailID string) (*EmailAnalysisResult, bool)
	List() []*EmailAnalysisResult
	Search(req *EmailSearchRequest) []*EmailAnalysisResult
}

// 📧 Email Store for in-memory persistence (legacy)
type EmailStore struct {
	emails map[string]*EmailAnalysisResult
	log    *log.Helper
}

// 🔍 Email Search Request
type EmailSearchRequest struct {
	Query      string    `json:"query"`
	StartDate  time.Time `json:"start_date,omitempty"`
	EndDate    time.Time `json:"end_date,omitempty"`
	Category   string    `json:"category,omitempty"`
	Sentiment  string    `json:"sentiment,omitempty"`
	Priority   string    `json:"priority,omitempty"`
	HasHVAC    *bool     `json:"has_hvac,omitempty"`
	Limit      int       `json:"limit,omitempty"`
}

// 📊 Email Search Response
type EmailSearchResponse struct {
	Results    []*EmailAnalysisResult `json:"results"`
	Total      int                    `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// NewEmailDashboardService creates a new dashboard service with database store
func NewEmailDashboardService(analysisService *EmailAnalysisService, emailStore EmailStoreInterface, logger log.Logger) *EmailDashboardService {
	return &EmailDashboardService{
		log:             log.NewHelper(logger),
		analysisService: analysisService,
		emailStore:      emailStore,
	}
}

// NewEmailDashboardServiceWithMemoryStore creates a new dashboard service with in-memory store (legacy)
func NewEmailDashboardServiceWithMemoryStore(analysisService *EmailAnalysisService, logger log.Logger) *EmailDashboardService {
	return &EmailDashboardService{
		log:             log.NewHelper(logger),
		analysisService: analysisService,
		emailStore:      NewEmailStore(logger),
	}
}

// NewEmailStore creates a new email store
func NewEmailStore(logger log.Logger) *EmailStore {
	return &EmailStore{
		emails: make(map[string]*EmailAnalysisResult),
		log:    log.NewHelper(logger),
	}
}

// 💾 Store email analysis result
func (s *EmailStore) Store(result *EmailAnalysisResult) error {
	s.emails[result.EmailID] = result
	s.log.Infof("Stored email analysis: %s", result.EmailID)
	return nil
}

// 🔍 Get email by ID
func (s *EmailStore) Get(emailID string) (*EmailAnalysisResult, bool) {
	result, exists := s.emails[emailID]
	return result, exists
}

// 📋 List all emails
func (s *EmailStore) List() []*EmailAnalysisResult {
	var results []*EmailAnalysisResult
	for _, email := range s.emails {
		results = append(results, email)
	}

	// Sort by timestamp (newest first)
	sort.Slice(results, func(i, j int) bool {
		return results[i].Timestamp.After(results[j].Timestamp)
	})

	return results
}

// 🔍 Search emails
func (s *EmailStore) Search(req *EmailSearchRequest) []*EmailAnalysisResult {
	var results []*EmailAnalysisResult

	for _, email := range s.emails {
		if s.matchesSearchCriteria(email, req) {
			results = append(results, email)
		}
	}

	// Sort by timestamp (newest first)
	sort.Slice(results, func(i, j int) bool {
		return results[i].Timestamp.After(results[j].Timestamp)
	})

	// Apply limit
	if req.Limit > 0 && len(results) > req.Limit {
		results = results[:req.Limit]
	}

	return results
}

// 🎯 Check if email matches search criteria
func (s *EmailStore) matchesSearchCriteria(email *EmailAnalysisResult, req *EmailSearchRequest) bool {
	// Date range filter
	if !req.StartDate.IsZero() && email.Timestamp.Before(req.StartDate) {
		return false
	}
	if !req.EndDate.IsZero() && email.Timestamp.After(req.EndDate) {
		return false
	}

	// Category filter
	if req.Category != "" && email.Category != req.Category {
		return false
	}

	// Sentiment filter
	if req.Sentiment != "" && email.Sentiment != req.Sentiment {
		return false
	}

	// Priority filter
	if req.Priority != "" && email.Priority != req.Priority {
		return false
	}

	// HVAC relevance filter
	if req.HasHVAC != nil {
		hasHVAC := email.HVACRelevance != nil && email.HVACRelevance.IsHVACRelated
		if *req.HasHVAC != hasHVAC {
			return false
		}
	}

	// Text search in subject and body
	if req.Query != "" {
		query := req.Query
		if !containsIgnoreCase(email.Subject, query) &&
		   (email.BodyAnalysis == nil || !containsIgnoreCase(email.BodyAnalysis.Content, query)) {
			return false
		}
	}

	return true
}

// 📊 Get dashboard statistics
func (s *EmailDashboardService) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	s.log.WithContext(ctx).Info("Generating dashboard statistics")

	emails := s.emailStore.List()
	today := time.Now().Truncate(24 * time.Hour)

	stats := &DashboardStats{
		TotalEmails:        len(emails),
		SentimentBreakdown: &SentimentBreakdown{},
		CategoryBreakdown:  &CategoryBreakdown{},
		ProcessingMetrics:  &ProcessingMetrics{},
	}

	keywordCounts := make(map[string]int)

	for _, email := range emails {
		// Count today's emails
		if email.Timestamp.After(today) {
			stats.TodayEmails++
		}

		// Count HVAC emails
		if email.HVACRelevance != nil && email.HVACRelevance.IsHVACRelated {
			stats.HVACEmails++
		}

		// Count high priority emails
		if email.Priority == "high" {
			stats.HighPriorityEmails++
		}

		// Count attachments
		stats.AttachmentsCount += email.AttachmentCount

		// Sentiment breakdown
		switch email.Sentiment {
		case "positive":
			stats.SentimentBreakdown.Positive++
		case "negative":
			stats.SentimentBreakdown.Negative++
		default:
			stats.SentimentBreakdown.Neutral++
		}

		// Category breakdown
		switch email.Category {
		case "hvac_service":
			stats.CategoryBreakdown.HVACService++
		case "support":
			stats.CategoryBreakdown.Support++
		case "sales":
			stats.CategoryBreakdown.Sales++
		default:
			stats.CategoryBreakdown.General++
		}

		// Collect keywords
		if email.BodyAnalysis != nil {
			for _, keyword := range email.BodyAnalysis.KeyPhrases {
				keywordCounts[keyword]++
			}
		}
	}

	// Get top keywords
	stats.TopKeywords = getTopKeywords(keywordCounts, 10)

	// Get recent analysis (last 10)
	if len(emails) > 10 {
		stats.RecentAnalysis = emails[:10]
	} else {
		stats.RecentAnalysis = emails
	}

	// Calculate processing metrics
	stats.ProcessingMetrics = s.calculateProcessingMetrics(emails)

	return stats, nil
}

// 📈 Calculate processing metrics
func (s *EmailDashboardService) calculateProcessingMetrics(emails []*EmailAnalysisResult) *ProcessingMetrics {
	if len(emails) == 0 {
		return &ProcessingMetrics{}
	}

	successCount := 0
	var lastProcessed time.Time

	for _, email := range emails {
		if email.BodyAnalysis != nil {
			successCount++
		}
		if email.Timestamp.After(lastProcessed) {
			lastProcessed = email.Timestamp
		}
	}

	successRate := float64(successCount) / float64(len(emails)) * 100
	errorRate := 100 - successRate

	return &ProcessingMetrics{
		AverageProcessingTime: 2 * time.Second, // Placeholder
		SuccessRate:          successRate,
		ErrorRate:            errorRate,
		LastProcessed:        lastProcessed,
	}
}

// 🔝 Get top keywords
func getTopKeywords(keywordCounts map[string]int, limit int) []KeywordCount {
	var keywords []KeywordCount

	for keyword, count := range keywordCounts {
		keywords = append(keywords, KeywordCount{
			Keyword: keyword,
			Count:   count,
		})
	}

	// Sort by count (descending)
	sort.Slice(keywords, func(i, j int) bool {
		return keywords[i].Count > keywords[j].Count
	})

	// Apply limit
	if len(keywords) > limit {
		keywords = keywords[:limit]
	}

	return keywords
}

// 🌐 HTTP Handlers for Dashboard API

// 📊 Dashboard stats endpoint
func (s *EmailDashboardService) HandleDashboardStats(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	stats, err := s.GetDashboardStats(ctx)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// 🔍 Email search endpoint
func (s *EmailDashboardService) HandleEmailSearch(w http.ResponseWriter, r *http.Request) {
	var searchReq EmailSearchRequest
	if err := json.NewDecoder(r.Body).Decode(&searchReq); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set default limit
	if searchReq.Limit == 0 {
		searchReq.Limit = 50
	}

	results := s.emailStore.Search(&searchReq)

	// Pagination
	page := 1
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := searchReq.Limit
	totalPages := (len(results) + pageSize - 1) / pageSize

	startIdx := (page - 1) * pageSize
	endIdx := startIdx + pageSize
	if endIdx > len(results) {
		endIdx = len(results)
	}

	var pageResults []*EmailAnalysisResult
	if startIdx < len(results) {
		pageResults = results[startIdx:endIdx]
	}

	response := EmailSearchResponse{
		Results:    pageResults,
		Total:      len(results),
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 📧 Get email by ID endpoint
func (s *EmailDashboardService) HandleGetEmail(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	emailID := vars["id"]

	if emailID == "" {
		http.Error(w, "Email ID required", http.StatusBadRequest)
		return
	}

	email, exists := s.emailStore.Get(emailID)
	if !exists {
		http.Error(w, "Email not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(email)
}

// 📤 Analyze new email endpoint
func (s *EmailDashboardService) HandleAnalyzeEmail(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Read email data from request body
	emailData := make([]byte, r.ContentLength)
	_, err := r.Body.Read(emailData)
	if err != nil {
		http.Error(w, "Failed to read email data", http.StatusBadRequest)
		return
	}

	// Analyze email
	result, err := s.analysisService.AnalyzeEmail(ctx, emailData)
	if err != nil {
		http.Error(w, fmt.Sprintf("Analysis failed: %v", err), http.StatusInternalServerError)
		return
	}

	// Store result
	err = s.emailStore.Store(result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to store email: %v", err)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// 🔍 Similar emails endpoint
func (s *EmailDashboardService) HandleSimilarEmails(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	query := r.URL.Query().Get("query")
	if query == "" {
		http.Error(w, "Query parameter required", http.StatusBadRequest)
		return
	}

	limit := 10
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	results, err := s.analysisService.SearchSimilarEmails(ctx, query, limit)
	if err != nil {
		http.Error(w, fmt.Sprintf("Search failed: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(results)
}

// 🔧 Setup dashboard routes
func (s *EmailDashboardService) SetupRoutes(router *mux.Router) {
	// Dashboard API routes
	api := router.PathPrefix("/api/v1/email-analysis").Subrouter()

	api.HandleFunc("/dashboard/stats", s.HandleDashboardStats).Methods("GET")
	api.HandleFunc("/search", s.HandleEmailSearch).Methods("POST")
	api.HandleFunc("/emails/{id}", s.HandleGetEmail).Methods("GET")
	api.HandleFunc("/analyze", s.HandleAnalyzeEmail).Methods("POST")
	api.HandleFunc("/analyze-advanced", s.HandleAdvancedAnalyze).Methods("POST")
	api.HandleFunc("/similar", s.HandleSimilarEmails).Methods("GET")

	s.log.Info("Email analysis dashboard routes configured")
}

// 🤖 Advanced analyze endpoint with Gemma 3
func (s *EmailDashboardService) HandleAdvancedAnalyze(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Read email data from request body
	emailData := make([]byte, r.ContentLength)
	_, err := r.Body.Read(emailData)
	if err != nil {
		http.Error(w, "Failed to read email data", http.StatusBadRequest)
		return
	}

	// Analyze email with advanced Gemma 3 analysis
	result, err := s.analysisService.AnalyzeEmail(ctx, emailData)
	if err != nil {
		http.Error(w, fmt.Sprintf("Advanced analysis failed: %v", err), http.StatusInternalServerError)
		return
	}

	// Store result
	err = s.emailStore.Store(result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to store email: %v", err)
	}

	// Add metadata about advanced analysis
	response := map[string]interface{}{
		"analysis_result": result,
		"analysis_type":   "advanced_gemma3",
		"features_used": []string{
			"gemma3_4b_instruct",
			"128k_context_window",
			"8192_output_tokens",
			"hvac_specific_analysis",
			"multimodal_ready",
			"seasonal_context",
			"business_intelligence",
		},
		"model_capabilities": map[string]interface{}{
			"context_window":    128000,
			"output_tokens":     8192,
			"multimodal":        true,
			"image_resolution":  896,
			"hvac_specialized":  true,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 🔧 Helper function for case-insensitive string search
func containsIgnoreCase(text, substr string) bool {
	return len(text) >= len(substr) &&
		   len(substr) > 0 &&
		   strings.Contains(strings.ToLower(text), strings.ToLower(substr))
}
