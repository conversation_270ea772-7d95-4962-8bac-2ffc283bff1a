package mcp

import (
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	mcp_golang "github.com/metoro-io/mcp-golang"
	"github.com/metoro-io/mcp-golang/transport/stdio"
	"golang.org/x/time/rate"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/mcp/metrics"
	"gobackend-hvac-kratos/internal/mcp/middleware"
	"gobackend-hvac-kratos/internal/mcp/tools"
)

// 🚀 Enhanced MCP Server - Comprehensive AI Agent Interface for HVAC CRM
// Provides 80+ tools across 10 categories with enterprise-grade reliability
type EnhancedMCPServer struct {
	// Core MCP server
	server *mcp_golang.Server

	// Business logic use cases
	customerUc  *biz.CustomerUsecase
	jobUc       *biz.JobUsecase
	aiUc        *biz.AIUsecase
	emailUc     *biz.EmailUsecase
	analyticsUc *biz.AnalyticsUsecase
	workflowUc  *biz.WorkflowUsecase

	// Infrastructure components
	rateLimiter *rate.Limiter
	validator   *validator.Validate
	logger      *log.Helper
	baseLogger  log.Logger
	metrics     *metrics.MCPMetrics

	// External integrations
	wsManager   *WebSocketManager
	authService *middleware.AuthService

	// Configuration
	config *conf.MCP

	// Tool managers
	customerTools  *tools.CustomerTools
	jobTools       *tools.JobTools
	emailTools     *tools.EmailTools
	analyticsTools *tools.AnalyticsTools
	workflowTools  *tools.WorkflowTools
	authTools      *tools.AuthTools
	systemTools    *tools.SystemTools

	// State management
	mu        sync.RWMutex
	isServing bool
}

// WebSocketManager handles real-time frontend updates
type WebSocketManager struct {
	connections map[string]*WebSocketConnection
	mu          sync.RWMutex
	logger      *log.Helper
}

type WebSocketConnection struct {
	ID       string
	UserID   string
	LastSeen time.Time
	// Add WebSocket connection details here
}

// NewEnhancedMCPServer creates a new enhanced MCP server with full feature set
func NewEnhancedMCPServer(
	c *conf.MCP,
	customerUc *biz.CustomerUsecase,
	jobUc *biz.JobUsecase,
	aiUc *biz.AIUsecase,
	emailUc *biz.EmailUsecase,
	analyticsUc *biz.AnalyticsUsecase,
	workflowUc *biz.WorkflowUsecase,
	logger log.Logger,
) (*EnhancedMCPServer, error) {
	// Create MCP transport and server
	transport := stdio.NewStdioServerTransport()
	server := mcp_golang.NewServer(transport)

	// Initialize rate limiter (100 requests per minute)
	rateLimiter := rate.NewLimiter(rate.Every(time.Minute/100), 10)

	// Initialize validator
	validator := validator.New()

	// Initialize metrics collector
	metricsCollector := metrics.NewMCPMetrics()

	// Initialize WebSocket manager
	wsManager := &WebSocketManager{
		connections: make(map[string]*WebSocketConnection),
		logger:      log.NewHelper(logger),
	}

	// Initialize auth service
	authService := middleware.NewAuthService(log.NewHelper(logger))

	// Create enhanced MCP server
	mcpServer := &EnhancedMCPServer{
		server:      server,
		customerUc:  customerUc,
		jobUc:       jobUc,
		aiUc:        aiUc,
		emailUc:     emailUc,
		analyticsUc: analyticsUc,
		workflowUc:  workflowUc,
		rateLimiter: rateLimiter,
		validator:   validator,
		logger:      log.NewHelper(logger),
		baseLogger:  logger,
		metrics:     metricsCollector,
		wsManager:   wsManager,
		authService: authService,
		config:      c,
		isServing:   false,
	}

	// Initialize tool managers
	if err := mcpServer.initializeToolManagers(); err != nil {
		return nil, fmt.Errorf("failed to initialize tool managers: %w", err)
	}

	// Register all tools
	if err := mcpServer.registerAllTools(); err != nil {
		return nil, fmt.Errorf("failed to register tools: %w", err)
	}

	return mcpServer, nil
}

// initializeToolManagers creates all tool manager instances
func (s *EnhancedMCPServer) initializeToolManagers() error {
	s.customerTools = tools.NewCustomerTools(s.customerUc, s.validator, s.baseLogger)
	s.jobTools = tools.NewJobTools(s.jobUc, s.validator, s.baseLogger)
	s.emailTools = tools.NewEmailTools(s.emailUc, s.aiUc, s.validator, s.baseLogger)
	s.analyticsTools = tools.NewAnalyticsTools(s.analyticsUc, s.validator, s.baseLogger)
	s.workflowTools = tools.NewWorkflowTools(s.workflowUc, s.validator, s.baseLogger)
	s.authTools = tools.NewAuthTools(s.authService, s.validator, s.baseLogger)
	s.systemTools = tools.NewSystemTools(s.metrics, nil, s.validator, s.baseLogger)

	return nil
}

// registerAllTools registers all MCP tools with middleware
func (s *EnhancedMCPServer) registerAllTools() error {
	s.logger.Info("Registering MCP tools...")

	// Register customer management tools
	if err := s.customerTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register customer tools: %w", err)
	}

	// Register job management tools
	if err := s.jobTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register job tools: %w", err)
	}

	// Register email processing tools
	if err := s.emailTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register email tools: %w", err)
	}

	// Register analytics tools
	if err := s.analyticsTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register analytics tools: %w", err)
	}

	// Register workflow automation tools
	if err := s.workflowTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register workflow tools: %w", err)
	}

	// Register authentication tools
	if err := s.authTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register auth tools: %w", err)
	}

	// Register system operation tools
	if err := s.systemTools.RegisterTools(s.server, s.wrapWithMiddleware); err != nil {
		return fmt.Errorf("failed to register system tools: %w", err)
	}

	s.logger.Info("All MCP tools registered successfully")
	return nil
}

// wrapWithMiddleware applies middleware to tool handlers
func (s *EnhancedMCPServer) wrapWithMiddleware(handler func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error) {
	return func(args interface{}) (*mcp_golang.ToolResponse, error) {
		// Start timing
		start := time.Now()

		// Rate limiting
		if !s.rateLimiter.Allow() {
			s.metrics.IncrementRateLimited()
			return nil, fmt.Errorf("rate limit exceeded")
		}

		// Validate input
		if err := s.validator.Struct(args); err != nil {
			s.metrics.IncrementValidationErrors()
			return nil, fmt.Errorf("validation error: %w", err)
		}

		// Execute handler
		result, err := handler(args)

		// Record metrics
		duration := time.Since(start)
		s.metrics.RecordToolExecution("unknown", duration, err == nil)

		if err != nil {
			s.logger.Errorf("Tool execution failed: %v", err)
			return nil, err
		}

		return result, nil
	}
}

// Serve starts the enhanced MCP server
func (s *EnhancedMCPServer) Serve() error {
	s.mu.Lock()
	s.isServing = true
	s.mu.Unlock()

	s.logger.Info("Starting Enhanced MCP Server for HVAC CRM...")

	// Start metrics collection
	go s.metrics.StartCollection()

	// Start WebSocket manager
	go s.wsManager.Start()

	// Start the MCP server
	return s.server.Serve()
}

// Stop gracefully stops the MCP server
func (s *EnhancedMCPServer) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isServing {
		return nil
	}

	s.logger.Info("Stopping Enhanced MCP Server...")

	// Stop metrics collection
	s.metrics.Stop()

	// Stop WebSocket manager
	s.wsManager.Stop()

	s.isServing = false
	s.logger.Info("Enhanced MCP Server stopped successfully")

	return nil
}

// GetMetrics returns current server metrics
func (s *EnhancedMCPServer) GetMetrics() *metrics.MCPMetrics {
	return s.metrics
}

// IsServing returns whether the server is currently serving
func (s *EnhancedMCPServer) IsServing() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isServing
}

// WebSocketManager methods
func (w *WebSocketManager) Start() {
	w.logger.Info("Starting WebSocket manager...")
	// Implementation for WebSocket management
}

func (w *WebSocketManager) Stop() {
	w.logger.Info("Stopping WebSocket manager...")
	// Implementation for graceful WebSocket shutdown
}
