package tools

import (
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/metoro-io/mcp-golang"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/mcp/middleware"
	"gobackend-hvac-kratos/internal/mcp/types"
)

// 🔐 Authentication Tools for MCP
// Comprehensive authentication and authorization management
type AuthTools struct {
	authService *middleware.AuthService
	validator   *validator.Validate
	logger      *zap.Logger
}

// NewAuthTools creates a new auth tools instance
func NewAuthTools(authService *middleware.AuthService, validator *validator.Validate, logger *zap.Logger) *AuthTools {
	return &AuthTools{
		authService: authService,
		validator:   validator,
		logger:      logger,
	}
}

// RegisterTools registers all authentication tools
func (t *AuthTools) RegisterTools(server *mcp_golang.Server, middleware func(func(interface{}) (*mcp_golang.ToolResponse, error)) func(interface{}) (*mcp_golang.ToolResponse, error)) error {
	// Authenticate User Tool
	server.RegisterTool("authenticate_user", "Authenticate user with token",
		middleware(t.authenticateUser))
	
	// Create User Tool
	server.RegisterTool("create_user", "Create a new user account (admin only)",
		middleware(t.createUser))
	
	// Update User Role Tool
	server.RegisterTool("update_user_role", "Update user role and permissions",
		middleware(t.updateUserRole))
	
	// Check Permissions Tool
	server.RegisterTool("check_permissions", "Check if user has specific permissions",
		middleware(t.checkPermissions))
	
	// Create Session Tool
	server.RegisterTool("create_session", "Create a new user session",
		middleware(t.createSession))
	
	// Validate Session Tool
	server.RegisterTool("validate_session", "Validate an existing session",
		middleware(t.validateSession))
	
	// Revoke Session Tool
	server.RegisterTool("revoke_session", "Revoke a user session",
		middleware(t.revokeSession))
	
	// Get User Permissions Tool
	server.RegisterTool("get_user_permissions", "Get all permissions for a user",
		middleware(t.getUserPermissions))
	
	t.logger.Info("Authentication tools registered successfully")
	return nil
}

// authenticateUser authenticates a user with token
func (t *AuthTools) authenticateUser(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.AuthenticateRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for authenticate_user")
	}
	
	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}
	
	// Authenticate user
	user, err := t.authService.ValidateToken(req.Token)
	if err != nil {
		t.logger.Error("Authentication failed", zap.Error(err))
		return nil, fmt.Errorf("authentication failed: %w", err)
	}
	
	t.logger.Info("User authenticated successfully", 
		zap.String("user_id", user.ID),
		zap.String("email", user.Email),
	)
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ User authenticated: %s %s (%s)", 
			user.FirstName, user.LastName, user.Role)),
	), nil
}

// createUser creates a new user account
func (t *AuthTools) createUser(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.CreateUserRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for create_user")
	}
	
	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}
	
	// Create user
	user, err := t.authService.CreateUser(req.Email, req.Password, req.FirstName, req.LastName, req.Role)
	if err != nil {
		t.logger.Error("Failed to create user", zap.Error(err))
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	
	t.logger.Info("User created successfully", 
		zap.String("user_id", user.ID),
		zap.String("email", user.Email),
		zap.String("role", user.Role),
	)
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("👤 User created: %s %s (%s)", 
			user.FirstName, user.LastName, user.Role)),
	), nil
}

// updateUserRole updates user role and permissions
func (t *AuthTools) updateUserRole(args interface{}) (*mcp_golang.ToolResponse, error) {
	req, ok := args.(types.UpdateUserRoleRequest)
	if !ok {
		return nil, fmt.Errorf("invalid request type for update_user_role")
	}
	
	// Validate request
	if err := t.validator.Struct(req); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}
	
	// Update user role
	err := t.authService.UpdateUserRole(req.UserID, req.Role)
	if err != nil {
		t.logger.Error("Failed to update user role", zap.Error(err))
		return nil, fmt.Errorf("failed to update user role: %w", err)
	}
	
	t.logger.Info("User role updated successfully", 
		zap.String("user_id", req.UserID),
		zap.String("new_role", req.Role),
	)
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent(fmt.Sprintf("✅ User role updated to: %s", req.Role)),
	), nil
}

// checkPermissions checks if user has specific permissions
func (t *AuthTools) checkPermissions(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement permission checking with proper request type
	t.logger.Info("Permission check requested")
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("🔐 Permission Check (Feature coming soon)"),
	), nil
}

// createSession creates a new user session
func (t *AuthTools) createSession(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement session creation with proper request type
	t.logger.Info("Session creation requested")
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("🔑 Session Created (Feature coming soon)"),
	), nil
}

// validateSession validates an existing session
func (t *AuthTools) validateSession(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement session validation with proper request type
	t.logger.Info("Session validation requested")
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("✅ Session Valid (Feature coming soon)"),
	), nil
}

// revokeSession revokes a user session
func (t *AuthTools) revokeSession(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement session revocation with proper request type
	t.logger.Info("Session revocation requested")
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("🚫 Session Revoked (Feature coming soon)"),
	), nil
}

// getUserPermissions gets all permissions for a user
func (t *AuthTools) getUserPermissions(args interface{}) (*mcp_golang.ToolResponse, error) {
	// TODO: Implement permission retrieval with proper request type
	t.logger.Info("User permissions requested")
	
	return mcp_golang.NewToolResponse(
		mcp_golang.NewTextContent("📋 User Permissions (Feature coming soon)"),
	), nil
}
