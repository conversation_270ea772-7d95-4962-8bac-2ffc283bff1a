package executive

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/langchain"
)

// 🎯 TriageEngine - AI-powered email classification and decision making
type TriageEngine struct {
	log              *log.Helper
	db               *gorm.DB
	aiService        *ai.EnhancedService
	langchainService *langchain.Service
	config           *Config
	hvacClassifier   *HVACClassifier
	sentimentAnalyzer *SentimentAnalyzer
	intentDetector   *IntentDetector
}

// 📊 TriageResult represents the result of email triage analysis
type TriageResult struct {
	// Core Decision
	Action     data.TriageAction `json:"action"`
	Reasoning  string            `json:"reasoning"`
	Confidence float64           `json:"confidence"`

	// Priority and Urgency
	Priority     data.PriorityLevel `json:"priority"`
	UrgencyScore float64            `json:"urgency_score"`

	// Classification
	Category    string `json:"category"`
	Subcategory string `json:"subcategory"`

	// HVAC-Specific
	HVACServiceType string `json:"hvac_service_type"`
	EquipmentType   string `json:"equipment_type"`
	CustomerType    string `json:"customer_type"`

	// AI Analysis
	DetectedIntent   string                 `json:"detected_intent"`
	DetectedEntities map[string]interface{} `json:"detected_entities"`
	KeyPhrases       []string               `json:"key_phrases"`
	Sentiment        data.SentimentType     `json:"sentiment"`
	SentimentScore   float64                `json:"sentiment_score"`

	// Processing Metadata
	AIModelUsed      string `json:"ai_model_used"`
	ProcessingTimeMs int    `json:"processing_time_ms"`
	ProcessedAt      time.Time `json:"processed_at"`
}

// 🏭 HVAC-specific classifier
type HVACClassifier struct {
	serviceTypeKeywords map[string][]string
	equipmentKeywords   map[string][]string
	urgencyIndicators   []string
}

// 😊 Sentiment analyzer
type SentimentAnalyzer struct {
	positiveKeywords []string
	negativeKeywords []string
	angryKeywords    []string
}

// 🎯 Intent detector
type IntentDetector struct {
	intentPatterns map[string][]string
}

// NewTriageEngine creates a new email triage engine
func NewTriageEngine(
	db *gorm.DB,
	aiService *ai.EnhancedService,
	langchainService *langchain.Service,
	config *Config,
	logger log.Logger,
) *TriageEngine {
	helper := log.NewHelper(logger)

	return &TriageEngine{
		log:              helper,
		db:               db,
		aiService:        aiService,
		langchainService: langchainService,
		config:           config,
		hvacClassifier:   newHVACClassifier(),
		sentimentAnalyzer: newSentimentAnalyzer(),
		intentDetector:   newIntentDetector(),
	}
}

// 🧠 TriageEmail - Main triage function with comprehensive AI analysis
func (t *TriageEngine) TriageEmail(ctx context.Context, email *data.Email) (*TriageResult, error) {
	startTime := time.Now()
	t.log.WithContext(ctx).Infof("🎯 Starting email triage for: %s", email.Subject)

	result := &TriageResult{
		ProcessedAt: time.Now(),
		AIModelUsed: t.config.PrimaryAIModel,
	}

	// 1. Prepare email content for analysis
	emailContent := t.prepareEmailContent(email)

	// 2. Perform AI-powered analysis using LangChain
	aiAnalysis, err := t.performAIAnalysis(ctx, emailContent, email)
	if err != nil {
		t.log.WithContext(ctx).Warnf("AI analysis failed, falling back to rule-based: %v", err)
		return t.performRuleBasedTriage(ctx, email)
	}

	// 3. Extract core information from AI analysis
	t.extractCoreInformation(aiAnalysis, result)

	// 4. Perform HVAC-specific classification
	t.performHVACClassification(emailContent, result)

	// 5. Analyze sentiment
	t.analyzeSentiment(emailContent, result)

	// 6. Detect intent
	t.detectIntent(emailContent, result)

	// 7. Determine priority and urgency
	t.determinePriorityAndUrgency(result)

	// 8. Make final triage decision
	t.makeFinalTriageDecision(result)

	// 9. Validate and adjust confidence
	t.validateAndAdjustConfidence(result)

	result.ProcessingTimeMs = int(time.Since(startTime).Milliseconds())

	t.log.WithContext(ctx).Infof("✅ Triage completed: %s (confidence: %.2f)",
		result.Action, result.Confidence)

	return result, nil
}

// 🤖 Perform AI analysis using LangChain and Gemma
func (t *TriageEngine) performAIAnalysis(ctx context.Context, content string, email *data.Email) (*langchain.EmailAnalysisResult, error) {
	// Use LangChain for advanced email analysis
	result, err := t.langchainService.ProcessEmail(ctx, content, email.From)
	if err != nil {
		return nil, fmt.Errorf("langchain email analysis failed: %w", err)
	}

	return result, nil
}

// 📝 Prepare email content for analysis
func (t *TriageEngine) prepareEmailContent(email *data.Email) string {
	var content strings.Builder

	content.WriteString("Subject: " + email.Subject + "\n")
	content.WriteString("From: " + email.From + "\n")
	content.WriteString("To: " + strings.Join(email.To, ", ") + "\n")

	if len(email.CC) > 0 {
		content.WriteString("CC: " + strings.Join(email.CC, ", ") + "\n")
	}

	content.WriteString("\n")
	content.WriteString(email.Body)

	// Add attachment summaries if available
	if email.Attachments != nil {
		for _, attachment := range email.Attachments {
			if attachment.IsProcessed && attachment.TextContent != "" {
				content.WriteString(fmt.Sprintf("\n[Attachment: %s]\n%s\n",
					attachment.Filename, attachment.TextContent))
			}
		}
	}

	return content.String()
}

// 🔍 Extract core information from AI analysis
func (t *TriageEngine) extractCoreInformation(analysis *langchain.EmailAnalysisResult, result *TriageResult) {
	if analysis == nil {
		return
	}

	// Extract detected entities from extracted info
	result.DetectedEntities = make(map[string]interface{})
	if analysis.ExtractedInfo.CustomerContact.Name != "" {
		result.DetectedEntities["customer_contact"] = analysis.ExtractedInfo.CustomerContact
	}
	if analysis.ExtractedInfo.ServiceAddress.Street != "" {
		result.DetectedEntities["service_address"] = analysis.ExtractedInfo.ServiceAddress
	}
	if len(analysis.ExtractedInfo.EquipmentDetails) > 0 {
		result.DetectedEntities["equipment_details"] = analysis.ExtractedInfo.EquipmentDetails
	}

	// Extract key phrases from content analysis
	if analysis.ContentAnalysis.KeyIssues != nil {
		result.KeyPhrases = analysis.ContentAnalysis.KeyIssues
	}

	// Extract intent
	if analysis.ContentAnalysis.CustomerIntent != "" {
		result.DetectedIntent = analysis.ContentAnalysis.CustomerIntent
	}

	// Extract category
	if analysis.Classification.Category != "" {
		result.Category = string(analysis.Classification.Category)
	}

	// Extract confidence
	if analysis.Confidence > 0 {
		result.Confidence = analysis.Confidence
	}
}

// 🏭 Perform HVAC-specific classification
func (t *TriageEngine) performHVACClassification(content string, result *TriageResult) {
	lowerContent := strings.ToLower(content)

	// Classify service type
	result.HVACServiceType = t.hvacClassifier.classifyServiceType(lowerContent)

	// Classify equipment type
	result.EquipmentType = t.hvacClassifier.classifyEquipmentType(lowerContent)

	// Determine customer type
	result.CustomerType = t.hvacClassifier.determineCustomerType(lowerContent)
}

// 😊 Analyze sentiment
func (t *TriageEngine) analyzeSentiment(content string, result *TriageResult) {
	sentiment, score := t.sentimentAnalyzer.analyzeSentiment(content)
	result.Sentiment = sentiment
	result.SentimentScore = score
}

// 🎯 Detect intent
func (t *TriageEngine) detectIntent(content string, result *TriageResult) {
	if result.DetectedIntent == "" {
		result.DetectedIntent = t.intentDetector.detectIntent(content)
	}
}

// ⚡ Determine priority and urgency
func (t *TriageEngine) determinePriorityAndUrgency(result *TriageResult) {
	urgencyScore := 0.0

	// Base urgency on service type
	switch result.HVACServiceType {
	case "emergency":
		urgencyScore += 0.8
	case "repair":
		urgencyScore += 0.6
	case "maintenance":
		urgencyScore += 0.3
	case "installation":
		urgencyScore += 0.4
	case "quote":
		urgencyScore += 0.2
	}

	// Adjust based on sentiment
	switch result.Sentiment {
	case data.SentimentAngry:
		urgencyScore += 0.3
	case data.SentimentNegative:
		urgencyScore += 0.2
	case data.SentimentPositive:
		urgencyScore -= 0.1
	}

	// Adjust based on keywords
	urgencyKeywords := []string{"urgent", "emergency", "asap", "immediately", "critical", "broken", "not working"}
	content := strings.ToLower(result.DetectedIntent + " " + strings.Join(result.KeyPhrases, " "))

	for _, keyword := range urgencyKeywords {
		if strings.Contains(content, keyword) {
			urgencyScore += 0.2
			break
		}
	}

	// Normalize urgency score
	if urgencyScore > 1.0 {
		urgencyScore = 1.0
	}
	result.UrgencyScore = urgencyScore

	// Determine priority level
	switch {
	case urgencyScore >= 0.8:
		result.Priority = data.PriorityUrgent
	case urgencyScore >= 0.6:
		result.Priority = data.PriorityHigh
	case urgencyScore >= 0.3:
		result.Priority = data.PriorityNormal
	default:
		result.Priority = data.PriorityLow
	}
}

// 🎯 Make final triage decision
func (t *TriageEngine) makeFinalTriageDecision(result *TriageResult) {
	// Decision matrix based on priority, sentiment, and intent

	// Emergency situations - always escalate
	if result.Priority == data.PriorityUrgent || result.HVACServiceType == "emergency" {
		result.Action = data.TriageActionEscalate
		result.Reasoning = "High priority/emergency situation requiring immediate attention"
		return
	}

	// Angry customers - escalate or notify
	if result.Sentiment == data.SentimentAngry {
		result.Action = data.TriageActionEscalate
		result.Reasoning = "Customer appears angry, requires human intervention"
		return
	}

	// High confidence automated responses
	if result.Confidence >= t.config.AutoRespondThreshold {
		// Check if it's a standard request type
		standardRequests := []string{"quote", "appointment", "information", "schedule"}
		for _, reqType := range standardRequests {
			if strings.Contains(strings.ToLower(result.DetectedIntent), reqType) {
				result.Action = data.TriageActionRespond
				result.Reasoning = fmt.Sprintf("Standard %s request with high confidence", reqType)
				return
			}
		}
	}

	// Medium priority - notify for human review
	if result.Priority == data.PriorityHigh || result.Priority == data.PriorityNormal {
		result.Action = data.TriageActionNotify
		result.Reasoning = "Requires human review before response"
		return
	}

	// Low priority or spam - ignore
	if result.Priority == data.PriorityLow || result.Confidence < 0.3 {
		result.Action = data.TriageActionIgnore
		result.Reasoning = "Low priority or low confidence classification"
		return
	}

	// Default fallback
	result.Action = data.TriageActionNotify
	result.Reasoning = "Default action - requires human review"
}

// ✅ Validate and adjust confidence
func (t *TriageEngine) validateAndAdjustConfidence(result *TriageResult) {
	// Adjust confidence based on various factors

	// Lower confidence if sentiment analysis is uncertain
	if result.SentimentScore < 0.5 {
		result.Confidence *= 0.9
	}

	// Lower confidence if no clear intent detected
	if result.DetectedIntent == "" || result.DetectedIntent == "unknown" {
		result.Confidence *= 0.8
	}

	// Lower confidence if no HVAC-specific classification
	if result.HVACServiceType == "" || result.HVACServiceType == "unknown" {
		result.Confidence *= 0.9
	}

	// Ensure confidence is within bounds
	if result.Confidence > 1.0 {
		result.Confidence = 1.0
	}
	if result.Confidence < 0.0 {
		result.Confidence = 0.0
	}
}

// 🔄 Fallback rule-based triage when AI fails
func (t *TriageEngine) performRuleBasedTriage(ctx context.Context, email *data.Email) (*TriageResult, error) {
	t.log.WithContext(ctx).Info("🔄 Performing rule-based triage fallback")

	result := &TriageResult{
		ProcessedAt: time.Now(),
		AIModelUsed: "rule-based-fallback",
		Confidence:  0.6, // Lower confidence for rule-based
	}

	content := strings.ToLower(t.prepareEmailContent(email))

	// Basic HVAC classification
	t.performHVACClassification(content, result)

	// Basic sentiment analysis
	t.analyzeSentiment(content, result)

	// Basic intent detection
	t.detectIntent(content, result)

	// Determine priority
	t.determinePriorityAndUrgency(result)

	// Simple decision logic
	if strings.Contains(content, "emergency") || strings.Contains(content, "urgent") {
		result.Action = data.TriageActionEscalate
		result.Reasoning = "Emergency keywords detected"
	} else if result.Sentiment == data.SentimentAngry || result.Sentiment == data.SentimentNegative {
		result.Action = data.TriageActionNotify
		result.Reasoning = "Negative sentiment detected"
	} else {
		result.Action = data.TriageActionNotify
		result.Reasoning = "Default rule-based action"
	}

	return result, nil
}

// ==========================================
// HVAC CLASSIFIER IMPLEMENTATION
// ==========================================

func newHVACClassifier() *HVACClassifier {
	return &HVACClassifier{
		serviceTypeKeywords: map[string][]string{
			"emergency":    {"emergency", "urgent", "broken", "not working", "no heat", "no cooling", "leak"},
			"repair":       {"repair", "fix", "broken", "problem", "issue", "malfunction"},
			"maintenance":  {"maintenance", "service", "tune-up", "check", "inspection", "clean"},
			"installation": {"install", "new", "replace", "upgrade", "setup"},
			"quote":        {"quote", "estimate", "price", "cost", "how much"},
		},
		equipmentKeywords: map[string][]string{
			"ac":           {"air conditioning", "ac", "a/c", "cooling", "conditioner"},
			"heating":      {"heating", "furnace", "boiler", "heat pump", "heater"},
			"ventilation":  {"ventilation", "vent", "ductwork", "air flow", "exhaust"},
			"thermostat":   {"thermostat", "temperature control", "programmable"},
		},
		urgencyIndicators: []string{"emergency", "urgent", "asap", "immediately", "critical", "broken"},
	}
}

func (h *HVACClassifier) classifyServiceType(content string) string {
	for serviceType, keywords := range h.serviceTypeKeywords {
		for _, keyword := range keywords {
			if strings.Contains(content, keyword) {
				return serviceType
			}
		}
	}
	return "general"
}

func (h *HVACClassifier) classifyEquipmentType(content string) string {
	for equipmentType, keywords := range h.equipmentKeywords {
		for _, keyword := range keywords {
			if strings.Contains(content, keyword) {
				return equipmentType
			}
		}
	}
	return "general"
}

func (h *HVACClassifier) determineCustomerType(content string) string {
	commercialKeywords := []string{"business", "office", "commercial", "building", "facility"}
	residentialKeywords := []string{"home", "house", "residential", "apartment"}

	for _, keyword := range commercialKeywords {
		if strings.Contains(content, keyword) {
			return "commercial"
		}
	}

	for _, keyword := range residentialKeywords {
		if strings.Contains(content, keyword) {
			return "residential"
		}
	}

	return "unknown"
}

// ==========================================
// SENTIMENT ANALYZER IMPLEMENTATION
// ==========================================

func newSentimentAnalyzer() *SentimentAnalyzer {
	return &SentimentAnalyzer{
		positiveKeywords: []string{"great", "excellent", "satisfied", "happy", "pleased", "thank you"},
		negativeKeywords: []string{"disappointed", "unhappy", "problem", "issue", "complaint", "poor"},
		angryKeywords:    []string{"angry", "furious", "outraged", "disgusted", "terrible", "awful"},
	}
}

func (s *SentimentAnalyzer) analyzeSentiment(content string) (data.SentimentType, float64) {
	lowerContent := strings.ToLower(content)

	angryScore := 0
	negativeScore := 0
	positiveScore := 0

	// Count keyword occurrences
	for _, keyword := range s.angryKeywords {
		if strings.Contains(lowerContent, keyword) {
			angryScore++
		}
	}

	for _, keyword := range s.negativeKeywords {
		if strings.Contains(lowerContent, keyword) {
			negativeScore++
		}
	}

	for _, keyword := range s.positiveKeywords {
		if strings.Contains(lowerContent, keyword) {
			positiveScore++
		}
	}

	// Determine sentiment
	if angryScore > 0 {
		return data.SentimentAngry, float64(angryScore) / 10.0
	}
	if negativeScore > positiveScore {
		return data.SentimentNegative, float64(negativeScore) / 10.0
	}
	if positiveScore > 0 {
		return data.SentimentPositive, float64(positiveScore) / 10.0
	}

	return data.SentimentNeutral, 0.5
}

// ==========================================
// INTENT DETECTOR IMPLEMENTATION
// ==========================================

func newIntentDetector() *IntentDetector {
	return &IntentDetector{
		intentPatterns: map[string][]string{
			"schedule_appointment": {"schedule", "appointment", "book", "meeting", "visit"},
			"request_quote":        {"quote", "estimate", "price", "cost", "how much"},
			"report_problem":       {"problem", "issue", "broken", "not working", "malfunction"},
			"request_service":      {"service", "maintenance", "tune-up", "check"},
			"ask_question":         {"question", "how", "what", "when", "where", "why"},
			"complaint":            {"complaint", "dissatisfied", "unhappy", "poor service"},
			"compliment":           {"thank you", "great job", "excellent", "satisfied"},
		},
	}
}

func (i *IntentDetector) detectIntent(content string) string {
	lowerContent := strings.ToLower(content)

	for intent, patterns := range i.intentPatterns {
		for _, pattern := range patterns {
			if strings.Contains(lowerContent, pattern) {
				return intent
			}
		}
	}

	return "general_inquiry"
}

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

func getCurrentSeasonForTriage() string {
	month := time.Now().Month()
	switch {
	case month >= 3 && month <= 5:
		return "spring"
	case month >= 6 && month <= 8:
		return "summer"
	case month >= 9 && month <= 11:
		return "fall"
	default:
		return "winter"
	}
}