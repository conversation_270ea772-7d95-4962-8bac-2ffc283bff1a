package executive

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/data"
)

// 🧠 MemoryBankService - Long-term context and learning for AI
type MemoryBankService struct {
	log       *log.Helper
	db        *gorm.DB
	aiService *ai.EnhancedService
	config    *Config
	extractor *MemoryExtractor
	indexer   *MemoryIndexer
}

// 💾 MemoryUpdate represents a memory bank update
type MemoryUpdate struct {
	MemoryID     int64                  `json:"memory_id"`
	MemoryType   data.MemoryType        `json:"memory_type"`
	Title        string                 `json:"title"`
	Content      string                 `json:"content"`
	EntityType   string                 `json:"entity_type"`
	EntityID     string                 `json:"entity_id"`
	Confidence   float64                `json:"confidence"`
	Source       string                 `json:"source"`
	IsN<PERSON>        bool                   `json:"is_new"`
	IsUpdated    bool                   `json:"is_updated"`
}

// 🔍 MemorySearchResult represents search results from memory bank
type MemorySearchResult struct {
	Memory      *data.ExecutiveMemoryBank `json:"memory"`
	Relevance   float64                   `json:"relevance"`
	Similarity  float64                   `json:"similarity"`
	Context     map[string]interface{}    `json:"context"`
}

// 🧩 MemoryExtractor extracts insights from emails and interactions
type MemoryExtractor struct {
	patterns map[data.MemoryType][]string
}

// 📇 MemoryIndexer manages memory indexing and retrieval
type MemoryIndexer struct {
	vectorStore map[string][]float64 // Simple in-memory vector store
}

// NewMemoryBankService creates a new memory bank service
func NewMemoryBankService(
	db *gorm.DB,
	aiService *ai.EnhancedService,
	config *Config,
	logger log.Logger,
) *MemoryBankService {
	helper := log.NewHelper(logger)

	return &MemoryBankService{
		log:       helper,
		db:        db,
		aiService: aiService,
		config:    config,
		extractor: newMemoryExtractor(),
		indexer:   newMemoryIndexer(),
	}
}

// 📧 ProcessEmailForMemory - Extract and store memories from email processing
func (m *MemoryBankService) ProcessEmailForMemory(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	m.log.WithContext(ctx).Infof("🧠 Processing email for memory extraction: %s", email.Subject)

	var updates []*MemoryUpdate

	// 1. Extract customer preferences
	customerMemories, err := m.extractCustomerPreferences(ctx, email, triage)
	if err != nil {
		m.log.WithContext(ctx).Warnf("Failed to extract customer preferences: %v", err)
	} else {
		updates = append(updates, customerMemories...)
	}

	// 2. Extract communication patterns
	patternMemories, err := m.extractCommunicationPatterns(ctx, email, triage)
	if err != nil {
		m.log.WithContext(ctx).Warnf("Failed to extract communication patterns: %v", err)
	} else {
		updates = append(updates, patternMemories...)
	}

	// 3. Extract business rules and insights
	businessMemories, err := m.extractBusinessInsights(ctx, email, triage)
	if err != nil {
		m.log.WithContext(ctx).Warnf("Failed to extract business insights: %v", err)
	} else {
		updates = append(updates, businessMemories...)
	}

	// 4. Extract recurring issues
	issueMemories, err := m.extractRecurringIssues(ctx, email, triage)
	if err != nil {
		m.log.WithContext(ctx).Warnf("Failed to extract recurring issues: %v", err)
	} else {
		updates = append(updates, issueMemories...)
	}

	// 5. Extract important details
	detailMemories, err := m.extractImportantDetails(ctx, email, triage)
	if err != nil {
		m.log.WithContext(ctx).Warnf("Failed to extract important details: %v", err)
	} else {
		updates = append(updates, detailMemories...)
	}

	// 6. Store all memories in database
	for _, update := range updates {
		err := m.storeMemory(ctx, update, email.ID)
		if err != nil {
			m.log.WithContext(ctx).Errorf("Failed to store memory: %v", err)
		}
	}

	m.log.WithContext(ctx).Infof("✅ Extracted %d memories from email", len(updates))
	return updates, nil
}

// 🔍 SearchMemories - Search memory bank for relevant information
func (m *MemoryBankService) SearchMemories(ctx context.Context, query string, entityType string, limit int) ([]*data.ExecutiveMemoryBank, error) {
	m.log.WithContext(ctx).Infof("🔍 Searching memories: %s", query)

	var memories []*data.ExecutiveMemoryBank

	dbQuery := m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("is_active = ?", true)

	// Add entity type filter if specified
	if entityType != "" {
		dbQuery = dbQuery.Where("entity_type = ?", entityType)
	}

	// Add text search
	if query != "" {
		dbQuery = dbQuery.Where(
			"to_tsvector('english', title || ' ' || content) @@ plainto_tsquery('english', ?)",
			query,
		)
	}

	// Order by relevance and access count
	dbQuery = dbQuery.Order("relevance_score DESC, access_count DESC, created_at DESC")

	if limit > 0 {
		dbQuery = dbQuery.Limit(limit)
	}

	err := dbQuery.Find(&memories).Error
	if err != nil {
		return nil, fmt.Errorf("failed to search memories: %w", err)
	}

	// Update access counts for retrieved memories
	for _, memory := range memories {
		m.incrementAccessCount(ctx, memory.ID)
	}

	return memories, nil
}

// 📊 GetMemoryInsights - Get insights and analytics from memory bank
func (m *MemoryBankService) GetMemoryInsights(ctx context.Context, entityType string, timeRange time.Duration) (*MemoryInsights, error) {
	since := time.Now().Add(-timeRange)

	var insights MemoryInsights

	// Count memories by type
	err := m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Select("memory_type, COUNT(*) as count").
		Where("created_at >= ? AND is_active = ?", since, true).
		Group("memory_type").
		Scan(&insights.MemoryTypeCounts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get memory type counts: %w", err)
	}

	// Get most accessed memories
	err = m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("created_at >= ? AND is_active = ?", since, true).
		Order("access_count DESC").
		Limit(10).
		Find(&insights.MostAccessedMemories).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get most accessed memories: %w", err)
	}

	// Calculate average confidence
	var avgConfidence float64
	err = m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("created_at >= ? AND is_active = ?", since, true).
		Select("AVG(confidence_score)").
		Scan(&avgConfidence).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate average confidence: %w", err)
	}
	insights.AverageConfidence = avgConfidence

	return &insights, nil
}

// 🔄 UpdateMemoryRelevance - Update memory relevance scores based on usage
func (m *MemoryBankService) UpdateMemoryRelevance(ctx context.Context) error {
	m.log.WithContext(ctx).Info("🔄 Updating memory relevance scores")

	// Get all active memories
	var memories []*data.ExecutiveMemoryBank
	err := m.db.WithContext(ctx).Where("is_active = ?", true).Find(&memories).Error
	if err != nil {
		return fmt.Errorf("failed to retrieve memories: %w", err)
	}

	// Update relevance scores based on access patterns and age
	for _, memory := range memories {
		newRelevance := m.calculateRelevanceScore(memory)

		err = m.db.WithContext(ctx).Model(memory).Update("relevance_score", newRelevance).Error
		if err != nil {
			m.log.WithContext(ctx).Warnf("Failed to update relevance for memory %d: %v", memory.ID, err)
		}
	}

	m.log.WithContext(ctx).Infof("✅ Updated relevance scores for %d memories", len(memories))
	return nil
}

// 🧹 CleanupExpiredMemories - Remove expired and low-relevance memories
func (m *MemoryBankService) CleanupExpiredMemories(ctx context.Context) error {
	m.log.WithContext(ctx).Info("🧹 Cleaning up expired memories")

	// Mark expired memories as inactive
	err := m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Update("is_active", false).Error
	if err != nil {
		return fmt.Errorf("failed to mark expired memories: %w", err)
	}

	// Mark low-relevance memories as inactive
	lowRelevanceThreshold := 0.1
	err = m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("relevance_score < ? AND created_at < ?", lowRelevanceThreshold, time.Now().AddDate(0, -6, 0)).
		Update("is_active", false).Error
	if err != nil {
		return fmt.Errorf("failed to mark low-relevance memories: %w", err)
	}

	// Delete very old inactive memories
	deleteThreshold := time.Now().AddDate(-1, 0, 0) // 1 year old
	result := m.db.WithContext(ctx).
		Where("is_active = ? AND created_at < ?", false, deleteThreshold).
		Delete(&data.ExecutiveMemoryBank{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete old memories: %w", result.Error)
	}

	m.log.WithContext(ctx).Infof("✅ Cleaned up memories, deleted %d old records", result.RowsAffected)
	return nil
}

// ==========================================
// MEMORY EXTRACTION METHODS
// ==========================================

// 👤 Extract customer preferences from email
func (m *MemoryBankService) extractCustomerPreferences(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	var updates []*MemoryUpdate

	// Extract customer email domain for entity identification
	customerEmail := email.From
	_ = m.extractDomain(customerEmail) // Domain extracted but not used in current implementation

	// Look for preference indicators
	content := strings.ToLower(email.Body + " " + email.Subject)

	// Time preferences
	if timePrefs := m.extractTimePreferences(content); timePrefs != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeCustomerPreference,
			Title:      "Preferred Contact Times",
			Content:    timePrefs,
			EntityType: "customer",
			EntityID:   customerEmail,
			Confidence: 0.8,
			Source:     "email_analysis",
			IsNew:      true,
		})
	}

	// Communication preferences
	if commPrefs := m.extractCommunicationPreferences(content); commPrefs != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeCustomerPreference,
			Title:      "Communication Preferences",
			Content:    commPrefs,
			EntityType: "customer",
			EntityID:   customerEmail,
			Confidence: 0.7,
			Source:     "email_analysis",
			IsNew:      true,
		})
	}

	// Service preferences
	if servicePrefs := m.extractServicePreferences(content, triage); servicePrefs != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeCustomerPreference,
			Title:      "Service Preferences",
			Content:    servicePrefs,
			EntityType: "customer",
			EntityID:   customerEmail,
			Confidence: 0.8,
			Source:     "email_analysis",
			IsNew:      true,
		})
	}

	return updates, nil
}

// 💬 Extract communication patterns
func (m *MemoryBankService) extractCommunicationPatterns(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	var updates []*MemoryUpdate

	customerEmail := email.From

	// Analyze communication style
	style := m.analyzeCommunicationStyle(email.Body)
	if style != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeCommunicationPattern,
			Title:      "Communication Style",
			Content:    fmt.Sprintf("Customer %s typically communicates in a %s style", customerEmail, style),
			EntityType: "customer",
			EntityID:   customerEmail,
			Confidence: 0.7,
			Source:     "communication_analysis",
			IsNew:      true,
		})
	}

	// Track response time expectations
	if urgencyLevel := m.extractUrgencyExpectations(email.Body, triage); urgencyLevel != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeCommunicationPattern,
			Title:      "Response Time Expectations",
			Content:    fmt.Sprintf("Customer expects %s response times", urgencyLevel),
			EntityType: "customer",
			EntityID:   customerEmail,
			Confidence: 0.8,
			Source:     "urgency_analysis",
			IsNew:      true,
		})
	}

	return updates, nil
}

// 💼 Extract business insights
func (m *MemoryBankService) extractBusinessInsights(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	var updates []*MemoryUpdate

	// Extract equipment information
	if equipment := m.extractEquipmentInfo(email.Body, triage); equipment != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeImportantDetail,
			Title:      "Equipment Information",
			Content:    equipment,
			EntityType: "equipment",
			EntityID:   m.generateEquipmentID(email.From, equipment),
			Confidence: 0.9,
			Source:     "equipment_analysis",
			IsNew:      true,
		})
	}

	// Extract location information
	if location := m.extractLocationInfo(email.Body); location != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeContactInfo,
			Title:      "Service Location",
			Content:    location,
			EntityType: "customer",
			EntityID:   email.From,
			Confidence: 0.8,
			Source:     "location_analysis",
			IsNew:      true,
		})
	}

	return updates, nil
}

// 🔄 Extract recurring issues
func (m *MemoryBankService) extractRecurringIssues(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	var updates []*MemoryUpdate

	// Check if this is a recurring issue
	if m.isRecurringIssue(ctx, email.From, triage.HVACServiceType) {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeRecurringIssue,
			Title:      fmt.Sprintf("Recurring %s Issue", triage.HVACServiceType),
			Content:    fmt.Sprintf("Customer %s has recurring %s issues", email.From, triage.HVACServiceType),
			EntityType: "customer",
			EntityID:   email.From,
			Confidence: 0.9,
			Source:     "pattern_analysis",
			IsNew:      true,
		})
	}

	return updates, nil
}

// 📝 Extract important details
func (m *MemoryBankService) extractImportantDetails(ctx context.Context, email *data.Email, triage *TriageResult) ([]*MemoryUpdate, error) {
	var updates []*MemoryUpdate

	// Extract contact information
	if contacts := m.extractContactDetails(email.Body); len(contacts) > 0 {
		for _, contact := range contacts {
			updates = append(updates, &MemoryUpdate{
				MemoryType: data.MemoryTypeContactInfo,
				Title:      "Additional Contact Information",
				Content:    contact,
				EntityType: "customer",
				EntityID:   email.From,
				Confidence: 0.8,
				Source:     "contact_extraction",
				IsNew:      true,
			})
		}
	}

	// Extract special requirements
	if requirements := m.extractSpecialRequirements(email.Body); requirements != "" {
		updates = append(updates, &MemoryUpdate{
			MemoryType: data.MemoryTypeImportantDetail,
			Title:      "Special Requirements",
			Content:    requirements,
			EntityType: "customer",
			EntityID:   email.From,
			Confidence: 0.8,
			Source:     "requirements_analysis",
			IsNew:      true,
		})
	}

	return updates, nil
}

// ==========================================
// HELPER METHODS
// ==========================================

func (m *MemoryBankService) storeMemory(ctx context.Context, update *MemoryUpdate, sourceEmailID int64) error {
	// Check if similar memory already exists
	existing, err := m.findSimilarMemory(ctx, update)
	if err == nil && existing != nil {
		// Update existing memory
		return m.updateExistingMemory(ctx, existing, update)
	}

	// Create new memory
	memory := &data.ExecutiveMemoryBank{
		MemoryType:        update.MemoryType,
		MemoryCategory:    string(update.MemoryType),
		Title:             update.Title,
		Content:           update.Content,
		EntityType:        &update.EntityType,
		EntityID:          &update.EntityID,
		SourceType:        data.SourceTypeEmail,
		SourceID:          &sourceEmailID,
		ConfidenceScore:   update.Confidence,
		IsVerified:        false,
		AccessCount:       0,
		RelevanceScore:    0.5,
		IsActive:          true,
		ExtractedEntities: make(map[string]interface{}),
		KeyConcepts:       []string{},
	}

	err = m.db.WithContext(ctx).Create(memory).Error
	if err != nil {
		return fmt.Errorf("failed to create memory: %w", err)
	}

	update.MemoryID = memory.ID
	return nil
}

func (m *MemoryBankService) findSimilarMemory(ctx context.Context, update *MemoryUpdate) (*data.ExecutiveMemoryBank, error) {
	var memory data.ExecutiveMemoryBank

	err := m.db.WithContext(ctx).Where(
		"memory_type = ? AND entity_type = ? AND entity_id = ? AND title = ? AND is_active = ?",
		update.MemoryType, update.EntityType, update.EntityID, update.Title, true,
	).First(&memory).Error

	if err != nil {
		return nil, err
	}

	return &memory, nil
}

func (m *MemoryBankService) updateExistingMemory(ctx context.Context, existing *data.ExecutiveMemoryBank, update *MemoryUpdate) error {
	// Update content and confidence
	updates := map[string]interface{}{
		"content":          update.Content,
		"confidence_score": update.Confidence,
		"access_count":     existing.AccessCount + 1,
		"updated_at":       time.Now(),
	}

	return m.db.WithContext(ctx).Model(existing).Updates(updates).Error
}

func (m *MemoryBankService) incrementAccessCount(ctx context.Context, memoryID int64) {
	m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("id = ?", memoryID).
		Updates(map[string]interface{}{
			"access_count":      gorm.Expr("access_count + 1"),
			"last_accessed_at":  time.Now(),
		})
}

func (m *MemoryBankService) calculateRelevanceScore(memory *data.ExecutiveMemoryBank) float64 {
	// Base relevance on age, access count, and confidence
	age := time.Since(memory.CreatedAt).Hours() / 24 // days

	// Decay factor based on age
	ageFactor := 1.0 / (1.0 + age/30.0) // Decay over 30 days

	// Access factor
	accessFactor := float64(memory.AccessCount) / 100.0
	if accessFactor > 1.0 {
		accessFactor = 1.0
	}

	// Confidence factor
	confidenceFactor := memory.ConfidenceScore

	// Combined relevance score
	relevance := (ageFactor*0.3 + accessFactor*0.4 + confidenceFactor*0.3)

	if relevance > 1.0 {
		relevance = 1.0
	}
	if relevance < 0.0 {
		relevance = 0.0
	}

	return relevance
}

// ==========================================
// EXTRACTION HELPER FUNCTIONS
// ==========================================

func (m *MemoryBankService) extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) == 2 {
		return parts[1]
	}
	return ""
}

func (m *MemoryBankService) extractTimePreferences(content string) string {
	timePatterns := []string{
		"morning", "afternoon", "evening", "weekday", "weekend",
		"before 9", "after 5", "business hours", "early", "late",
	}

	for _, pattern := range timePatterns {
		if strings.Contains(content, pattern) {
			return fmt.Sprintf("Prefers %s contact", pattern)
		}
	}
	return ""
}

func (m *MemoryBankService) extractCommunicationPreferences(content string) string {
	commPatterns := map[string]string{
		"email":    "Prefers email communication",
		"phone":    "Prefers phone communication",
		"text":     "Prefers text communication",
		"call me":  "Prefers phone calls",
		"don't call": "Prefers no phone calls",
	}

	for pattern, pref := range commPatterns {
		if strings.Contains(content, pattern) {
			return pref
		}
	}
	return ""
}

func (m *MemoryBankService) extractServicePreferences(content string, triage *TriageResult) string {
	if triage.HVACServiceType != "" {
		return fmt.Sprintf("Frequently requests %s services", triage.HVACServiceType)
	}
	return ""
}

func (m *MemoryBankService) analyzeCommunicationStyle(body string) string {
	if len(body) < 50 {
		return "brief"
	}
	if strings.Count(body, "!") > 2 {
		return "enthusiastic"
	}
	if strings.Contains(strings.ToLower(body), "please") && strings.Contains(strings.ToLower(body), "thank") {
		return "polite"
	}
	if strings.Count(body, ".") > 5 {
		return "detailed"
	}
	return "standard"
}

func (m *MemoryBankService) extractUrgencyExpectations(body string, triage *TriageResult) string {
	urgentWords := []string{"urgent", "asap", "immediately", "emergency"}
	lowerBody := strings.ToLower(body)

	for _, word := range urgentWords {
		if strings.Contains(lowerBody, word) {
			return "immediate"
		}
	}

	if triage.Priority == data.PriorityHigh {
		return "fast"
	}

	return "standard"
}

func (m *MemoryBankService) extractEquipmentInfo(body string, triage *TriageResult) string {
	if triage.EquipmentType != "" {
		return fmt.Sprintf("Has %s equipment", triage.EquipmentType)
	}
	return ""
}

func (m *MemoryBankService) extractLocationInfo(body string) string {
	// Simple location extraction - in production, use more sophisticated NLP
	locationWords := []string{"address", "location", "building", "floor", "apartment", "suite"}
	lines := strings.Split(body, "\n")

	for _, line := range lines {
		lowerLine := strings.ToLower(line)
		for _, word := range locationWords {
			if strings.Contains(lowerLine, word) {
				return strings.TrimSpace(line)
			}
		}
	}
	return ""
}

func (m *MemoryBankService) isRecurringIssue(ctx context.Context, customerEmail string, serviceType string) bool {
	var count int64

	// Check if customer has had similar issues in the past 6 months
	since := time.Now().AddDate(0, -6, 0)

	m.db.WithContext(ctx).Model(&data.ExecutiveMemoryBank{}).
		Where("entity_id = ? AND memory_type = ? AND content LIKE ? AND created_at >= ?",
			customerEmail, data.MemoryTypeRecurringIssue, "%"+serviceType+"%", since).
		Count(&count)

	return count > 0
}

func (m *MemoryBankService) extractContactDetails(body string) []string {
	var contacts []string

	// Simple phone number extraction
	// phonePattern := `\b\d{3}[-.]?\d{3}[-.]?\d{4}\b` // TODO: implement regex extraction
	// In production, use proper regex

	lines := strings.Split(body, "\n")
	for _, line := range lines {
		if strings.Contains(line, "phone") || strings.Contains(line, "mobile") {
			contacts = append(contacts, strings.TrimSpace(line))
		}
	}

	return contacts
}

func (m *MemoryBankService) extractSpecialRequirements(body string) string {
	requirements := []string{"wheelchair", "pet", "allergy", "access", "key", "code"}
	lowerBody := strings.ToLower(body)

	for _, req := range requirements {
		if strings.Contains(lowerBody, req) {
			return fmt.Sprintf("Has special requirement: %s", req)
		}
	}
	return ""
}

func (m *MemoryBankService) generateEquipmentID(customerEmail, equipment string) string {
	return fmt.Sprintf("%s_%s", customerEmail, strings.ReplaceAll(equipment, " ", "_"))
}

// ==========================================
// SUPPORTING TYPES
// ==========================================

type MemoryInsights struct {
	MemoryTypeCounts      []MemoryTypeCount             `json:"memory_type_counts"`
	MostAccessedMemories  []*data.ExecutiveMemoryBank   `json:"most_accessed_memories"`
	AverageConfidence     float64                       `json:"average_confidence"`
	TotalMemories         int64                         `json:"total_memories"`
	ActiveMemories        int64                         `json:"active_memories"`
}

type MemoryTypeCount struct {
	MemoryType data.MemoryType `json:"memory_type"`
	Count      int64            `json:"count"`
}

// ==========================================
// INITIALIZATION FUNCTIONS
// ==========================================

func newMemoryExtractor() *MemoryExtractor {
	return &MemoryExtractor{
		patterns: map[data.MemoryType][]string{
			data.MemoryTypeCustomerPreference:   {"prefer", "like", "want", "need", "always", "usually"},
			data.MemoryTypeCommunicationPattern: {"call", "email", "text", "contact", "reach"},
			data.MemoryTypeBusinessRule:         {"policy", "rule", "procedure", "standard", "requirement"},
			data.MemoryTypeContactInfo:          {"phone", "address", "email", "contact", "reach"},
			data.MemoryTypeRecurringIssue:       {"again", "recurring", "repeat", "same", "similar"},
			data.MemoryTypeImportantDetail:      {"important", "note", "remember", "special", "critical"},
		},
	}
}

func newMemoryIndexer() *MemoryIndexer {
	return &MemoryIndexer{
		vectorStore: make(map[string][]float64),
	}
}