package common

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
)

// 🚀 Advanced Go 1.23 Generics Implementation
// Type-safe repository and service patterns for GoBackend-Kratos

// ==========================================
// CORE ENTITY INTERFACES
// ==========================================

// Entity represents a basic database entity with common fields
type Entity interface {
	GetID() int64
	SetID(id int64)
	GetCreatedAt() time.Time
	GetUpdatedAt() time.Time
}

// HVACEntity extends Entity with HVAC-specific requirements
type HVACEntity interface {
	Entity
	Validate() error
	TableName() string
}

// Searchable defines entities that support search operations
type Searchable[T any] interface {
	SearchFields() []string
	MatchesSearch(query string) bool
}

// Cacheable defines entities that can be cached
type Cacheable interface {
	CacheKey() string
	CacheTTL() time.Duration
}

// ==========================================
// SEARCH CRITERIA
// ==========================================

// SearchCriteria provides type-safe search parameters
type SearchCriteria[T any] struct {
	Query          string                 `json:"query"`
	Filters        map[string]interface{} `json:"filters"`
	SortBy         string                 `json:"sort_by"`
	SortOrder      string                 `json:"sort_order"` // "asc" or "desc"
	Page           int                    `json:"page"`
	PageSize       int                    `json:"page_size"`
	DateRange      *DateRange             `json:"date_range,omitempty"`
	CustomCriteria map[string]interface{} `json:"custom_criteria,omitempty"`
}

// DateRange for time-based filtering
type DateRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// SearchResult provides paginated search results
type SearchResult[T any] struct {
	Items      []T   `json:"items"`
	Total      int64 `json:"total"`
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	TotalPages int   `json:"total_pages"`
}

// ==========================================
// GENERIC REPOSITORY INTERFACE
// ==========================================

// Repository provides type-safe CRUD operations
type Repository[T HVACEntity] interface {
	// Basic CRUD operations
	Create(ctx context.Context, entity T) (T, error)
	GetByID(ctx context.Context, id int64) (T, error)
	Update(ctx context.Context, entity T) (T, error)
	Delete(ctx context.Context, id int64) error

	// Advanced operations
	List(ctx context.Context, criteria SearchCriteria[T]) (*SearchResult[T], error)
	Search(ctx context.Context, criteria SearchCriteria[T]) (*SearchResult[T], error)
	Count(ctx context.Context, filters map[string]interface{}) (int64, error)

	// Batch operations
	CreateBatch(ctx context.Context, entities []T) ([]T, error)
	UpdateBatch(ctx context.Context, entities []T) ([]T, error)
	DeleteBatch(ctx context.Context, ids []int64) error

	// Existence checks
	Exists(ctx context.Context, id int64) (bool, error)
	ExistsByField(ctx context.Context, field string, value interface{}) (bool, error)
}

// ==========================================
// GENERIC CACHE INTERFACE
// ==========================================

// Cache provides type-safe caching operations
type Cache[T any] interface {
	Get(ctx context.Context, key string) (T, error)
	Set(ctx context.Context, key string, value T, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	Clear(ctx context.Context, pattern string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// ==========================================
// GENERIC SERVICE METRICS
// ==========================================

// ServiceMetrics provides type-safe metrics collection
type ServiceMetrics[T any] interface {
	RecordOperation(operation string, duration time.Duration, success bool)
	RecordEntityCount(entityType string, count int64)
	RecordCacheHit(entityType string, hit bool)
	RecordValidationError(entityType string, field string)
	GetMetrics() map[string]interface{}
}

// ==========================================
// GENERIC SERVICE LAYER
// ==========================================

// Service provides type-safe business logic operations
type Service[T HVACEntity, R Repository[T]] struct {
	repo      R
	cache     Cache[T]
	validator *validator.Validate
	metrics   ServiceMetrics[T]
	log       *log.Helper
	config    *ServiceConfig
}

// GetRepository returns the underlying repository
func (s *Service[T, R]) GetRepository() R {
	return s.repo
}

// ServiceConfig provides service-level configuration
type ServiceConfig struct {
	EnableCache      bool          `json:"enable_cache"`
	CacheTTL         time.Duration `json:"cache_ttl"`
	EnableMetrics    bool          `json:"enable_metrics"`
	EnableValidation bool          `json:"enable_validation"`
	MaxBatchSize     int           `json:"max_batch_size"`
	Timeout          time.Duration `json:"timeout"`
}

// NewService creates a new generic service instance
func NewService[T HVACEntity, R Repository[T]](
	repo R,
	cache Cache[T],
	validator *validator.Validate,
	metrics ServiceMetrics[T],
	logger log.Logger,
	config *ServiceConfig,
) *Service[T, R] {
	if config == nil {
		config = &ServiceConfig{
			EnableCache:      true,
			CacheTTL:         5 * time.Minute,
			EnableMetrics:    true,
			EnableValidation: true,
			MaxBatchSize:     100,
			Timeout:          30 * time.Second,
		}
	}

	return &Service[T, R]{
		repo:      repo,
		cache:     cache,
		validator: validator,
		metrics:   metrics,
		log:       log.NewHelper(logger),
		config:    config,
	}
}

// ==========================================
// GENERIC SERVICE METHODS
// ==========================================

// Create creates a new entity with validation and caching
func (s *Service[T, R]) Create(ctx context.Context, entity T) (T, error) {
	start := time.Now()
	var zero T

	// Validation
	if s.config.EnableValidation {
		if err := entity.Validate(); err != nil {
			s.recordMetrics("create", start, false)
			return zero, err
		}

		if err := s.validator.Struct(entity); err != nil {
			s.recordMetrics("create", start, false)
			return zero, err
		}
	}

	// Create entity
	result, err := s.repo.Create(ctx, entity)
	if err != nil {
		s.recordMetrics("create", start, false)
		return zero, err
	}

	// Cache the result
	if s.config.EnableCache {
		if cacheable, ok := any(result).(Cacheable); ok {
			_ = s.cache.Set(ctx, cacheable.CacheKey(), result, s.config.CacheTTL)
		}
	}

	s.recordMetrics("create", start, true)
	return result, nil
}

// GetByID retrieves an entity by ID with caching
func (s *Service[T, R]) GetByID(ctx context.Context, id int64) (T, error) {
	start := time.Now()
	var zero T

	// Try cache first
	if s.config.EnableCache {
		// Generate cache key - simplified approach
		cacheKey := fmt.Sprintf("%T:%d", zero, id)
		if cached, err := s.cache.Get(ctx, cacheKey); err == nil {
			s.recordCacheHit(true)
			s.recordMetrics("get_by_id", start, true)
			return cached, nil
		}
		s.recordCacheHit(false)
	}

	// Get from repository
	result, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.recordMetrics("get_by_id", start, false)
		return zero, err
	}

	// Cache the result
	if s.config.EnableCache {
		if cacheable, ok := any(result).(Cacheable); ok {
			_ = s.cache.Set(ctx, cacheable.CacheKey(), result, s.config.CacheTTL)
		}
	}

	s.recordMetrics("get_by_id", start, true)
	return result, nil
}

// Update updates an entity with validation and cache invalidation
func (s *Service[T, R]) Update(ctx context.Context, entity T) (T, error) {
	start := time.Now()
	var zero T

	// Validation
	if s.config.EnableValidation {
		if err := entity.Validate(); err != nil {
			s.recordMetrics("update", start, false)
			return zero, err
		}

		if err := s.validator.Struct(entity); err != nil {
			s.recordMetrics("update", start, false)
			return zero, err
		}
	}

	// Update entity
	result, err := s.repo.Update(ctx, entity)
	if err != nil {
		s.recordMetrics("update", start, false)
		return zero, err
	}

	// Invalidate cache
	if s.config.EnableCache {
		if cacheable, ok := any(result).(Cacheable); ok {
			_ = s.cache.Delete(ctx, cacheable.CacheKey())
		}
	}

	s.recordMetrics("update", start, true)
	return result, nil
}

// Search performs type-safe search operations
func (s *Service[T, R]) Search(ctx context.Context, criteria SearchCriteria[T]) (*SearchResult[T], error) {
	start := time.Now()

	result, err := s.repo.Search(ctx, criteria)
	if err != nil {
		s.recordMetrics("search", start, false)
		return nil, err
	}

	s.recordMetrics("search", start, true)
	return result, nil
}

// ==========================================
// HELPER METHODS
// ==========================================

func (s *Service[T, R]) recordMetrics(operation string, start time.Time, success bool) {
	if s.config.EnableMetrics && s.metrics != nil {
		duration := time.Since(start)
		s.metrics.RecordOperation(operation, duration, success)
	}
}

func (s *Service[T, R]) recordCacheHit(hit bool) {
	if s.config.EnableMetrics && s.metrics != nil {
		var zero T
		entityType := fmt.Sprintf("%T", zero)
		s.metrics.RecordCacheHit(entityType, hit)
	}
}
