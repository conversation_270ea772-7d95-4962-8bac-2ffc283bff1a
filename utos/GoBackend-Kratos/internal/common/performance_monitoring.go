package common

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	_ "net/http/pprof" // Enable pprof endpoints
	"runtime"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Advanced Performance Monitoring for Go 1.23
// Real-time metrics, profiling integration, and performance optimization

// ==========================================
// PERFORMANCE MONITOR
// ==========================================

// PerformanceMonitor provides comprehensive performance monitoring
type PerformanceMonitor struct {
	log              *log.Helper
	metrics          *PerformanceMetrics
	profiler         *ProfilerManager
	alertManager     *AlertManager
	config           *MonitorConfig
	httpServer       *http.Server
	stopChan         chan struct{}
	running          bool
	mu               sync.RWMutex
}

// MonitorConfig configures the performance monitor
type MonitorConfig struct {
	EnableProfiling     bool          `json:"enable_profiling"`
	EnableMetrics       bool          `json:"enable_metrics"`
	EnableAlerts        bool          `json:"enable_alerts"`
	MetricsInterval     time.Duration `json:"metrics_interval"`
	ProfilerPort        int           `json:"profiler_port"`
	AlertThresholds     *AlertThresholds `json:"alert_thresholds"`
	RetentionPeriod     time.Duration `json:"retention_period"`
}

// AlertThresholds defines performance alert thresholds
type AlertThresholds struct {
	CPUUsagePercent    float64       `json:"cpu_usage_percent"`
	MemoryUsageMB      uint64        `json:"memory_usage_mb"`
	ResponseTimeMs     int64         `json:"response_time_ms"`
	ErrorRatePercent   float64       `json:"error_rate_percent"`
	GoroutineCount     int           `json:"goroutine_count"`
	GCPauseMs          int64         `json:"gc_pause_ms"`
}

// PerformanceMetrics holds comprehensive performance data
type PerformanceMetrics struct {
	SystemMetrics    *SystemMetrics    `json:"system_metrics"`
	ApplicationMetrics *ApplicationMetrics `json:"application_metrics"`
	DatabaseMetrics  *DatabaseMetrics  `json:"database_metrics"`
	HTTPMetrics      *HTTPMetrics      `json:"http_metrics"`
	CustomMetrics    map[string]interface{} `json:"custom_metrics"`
	Timestamp        time.Time         `json:"timestamp"`
	mu               sync.RWMutex
}

// SystemMetrics tracks system-level performance
type SystemMetrics struct {
	CPUUsage        float64       `json:"cpu_usage"`
	MemoryUsage     uint64        `json:"memory_usage"`
	MemoryTotal     uint64        `json:"memory_total"`
	GoroutineCount  int           `json:"goroutine_count"`
	GCStats         *GCStats      `json:"gc_stats"`
	Uptime          time.Duration `json:"uptime"`
}

// GCStats tracks garbage collection statistics
type GCStats struct {
	NumGC         uint32        `json:"num_gc"`
	PauseTotal    time.Duration `json:"pause_total"`
	LastPause     time.Duration `json:"last_pause"`
	NextGC        uint64        `json:"next_gc"`
	LastGC        time.Time     `json:"last_gc"`
}

// ApplicationMetrics tracks application-specific performance
type ApplicationMetrics struct {
	RequestCount     int64         `json:"request_count"`
	ErrorCount       int64         `json:"error_count"`
	AverageLatency   time.Duration `json:"average_latency"`
	P95Latency       time.Duration `json:"p95_latency"`
	P99Latency       time.Duration `json:"p99_latency"`
	ThroughputRPS    float64       `json:"throughput_rps"`
	ActiveConnections int          `json:"active_connections"`
}

// DatabaseMetrics tracks database performance
type DatabaseMetrics struct {
	ConnectionCount    int           `json:"connection_count"`
	ActiveQueries      int           `json:"active_queries"`
	AverageQueryTime   time.Duration `json:"average_query_time"`
	SlowQueryCount     int64         `json:"slow_query_count"`
	ErrorCount         int64         `json:"error_count"`
	PoolUtilization    float64       `json:"pool_utilization"`
}

// HTTPMetrics tracks HTTP-specific performance
type HTTPMetrics struct {
	RequestsPerSecond  float64            `json:"requests_per_second"`
	StatusCodes        map[int]int64      `json:"status_codes"`
	EndpointMetrics    map[string]*EndpointMetrics `json:"endpoint_metrics"`
	AverageResponseSize int64             `json:"average_response_size"`
}

// EndpointMetrics tracks per-endpoint performance
type EndpointMetrics struct {
	RequestCount   int64         `json:"request_count"`
	ErrorCount     int64         `json:"error_count"`
	AverageLatency time.Duration `json:"average_latency"`
	MaxLatency     time.Duration `json:"max_latency"`
	MinLatency     time.Duration `json:"min_latency"`
}

// NewPerformanceMonitor creates a new performance monitor
func NewPerformanceMonitor(logger log.Logger, config *MonitorConfig) *PerformanceMonitor {
	if config == nil {
		config = &MonitorConfig{
			EnableProfiling: true,
			EnableMetrics:   true,
			EnableAlerts:    true,
			MetricsInterval: 30 * time.Second,
			ProfilerPort:    6060,
			AlertThresholds: &AlertThresholds{
				CPUUsagePercent:  80.0,
				MemoryUsageMB:    512,
				ResponseTimeMs:   1000,
				ErrorRatePercent: 5.0,
				GoroutineCount:   1000,
				GCPauseMs:        100,
			},
			RetentionPeriod: 24 * time.Hour,
		}
	}

	pm := &PerformanceMonitor{
		log:          log.NewHelper(logger),
		config:       config,
		stopChan:     make(chan struct{}),
		metrics:      &PerformanceMetrics{
			SystemMetrics:      &SystemMetrics{},
			ApplicationMetrics: &ApplicationMetrics{},
			DatabaseMetrics:    &DatabaseMetrics{},
			HTTPMetrics:        &HTTPMetrics{
				StatusCodes:     make(map[int]int64),
				EndpointMetrics: make(map[string]*EndpointMetrics),
			},
			CustomMetrics: make(map[string]interface{}),
		},
	}

	if config.EnableProfiling {
		pm.profiler = NewProfilerManager(logger, config.ProfilerPort)
	}

	if config.EnableAlerts {
		pm.alertManager = NewAlertManager(logger, config.AlertThresholds)
	}

	return pm
}

// Start starts the performance monitor
func (pm *PerformanceMonitor) Start(ctx context.Context) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.running {
		return fmt.Errorf("performance monitor already running")
	}

	pm.running = true

	// Start profiler if enabled
	if pm.config.EnableProfiling && pm.profiler != nil {
		if err := pm.profiler.Start(); err != nil {
			return fmt.Errorf("failed to start profiler: %w", err)
		}
	}

	// Start metrics collection
	if pm.config.EnableMetrics {
		go pm.metricsCollectionLoop(ctx)
	}

	pm.log.Info("Performance monitor started")
	return nil
}

// Stop stops the performance monitor
func (pm *PerformanceMonitor) Stop() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if !pm.running {
		return
	}

	pm.running = false
	close(pm.stopChan)

	// Stop profiler
	if pm.profiler != nil {
		pm.profiler.Stop()
	}

	pm.log.Info("Performance monitor stopped")
}

// GetMetrics returns current performance metrics
func (pm *PerformanceMonitor) GetMetrics() *PerformanceMetrics {
	pm.metrics.mu.RLock()
	defer pm.metrics.mu.RUnlock()

	// Create a deep copy to avoid race conditions
	metricsJSON, _ := json.Marshal(pm.metrics)
	var metricsCopy PerformanceMetrics
	json.Unmarshal(metricsJSON, &metricsCopy)

	return &metricsCopy
}

// RecordHTTPRequest records an HTTP request for metrics
func (pm *PerformanceMonitor) RecordHTTPRequest(endpoint string, statusCode int, latency time.Duration) {
	if !pm.config.EnableMetrics {
		return
	}

	pm.metrics.mu.Lock()
	defer pm.metrics.mu.Unlock()

	// Update HTTP metrics
	pm.metrics.HTTPMetrics.StatusCodes[statusCode]++

	// Update endpoint metrics
	if pm.metrics.HTTPMetrics.EndpointMetrics[endpoint] == nil {
		pm.metrics.HTTPMetrics.EndpointMetrics[endpoint] = &EndpointMetrics{
			MinLatency: latency,
			MaxLatency: latency,
		}
	}

	endpointMetrics := pm.metrics.HTTPMetrics.EndpointMetrics[endpoint]
	endpointMetrics.RequestCount++

	if statusCode >= 400 {
		endpointMetrics.ErrorCount++
	}

	// Update latency statistics
	if latency < endpointMetrics.MinLatency {
		endpointMetrics.MinLatency = latency
	}
	if latency > endpointMetrics.MaxLatency {
		endpointMetrics.MaxLatency = latency
	}

	// Simple average calculation (could be improved with sliding window)
	endpointMetrics.AverageLatency = (endpointMetrics.AverageLatency*time.Duration(endpointMetrics.RequestCount-1) + latency) / time.Duration(endpointMetrics.RequestCount)
}

// metricsCollectionLoop runs the metrics collection loop
func (pm *PerformanceMonitor) metricsCollectionLoop(ctx context.Context) {
	ticker := time.NewTicker(pm.config.MetricsInterval)
	defer ticker.Stop()

	startTime := time.Now()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.collectMetrics(startTime)
			pm.checkAlerts()
		}
	}
}

// collectMetrics collects current system and application metrics
func (pm *PerformanceMonitor) collectMetrics(startTime time.Time) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	pm.metrics.mu.Lock()
	defer pm.metrics.mu.Unlock()

	// Update system metrics
	pm.metrics.SystemMetrics.MemoryUsage = m.Alloc
	pm.metrics.SystemMetrics.MemoryTotal = m.Sys
	pm.metrics.SystemMetrics.GoroutineCount = runtime.NumGoroutine()
	pm.metrics.SystemMetrics.Uptime = time.Since(startTime)

	// Update GC stats
	pm.metrics.SystemMetrics.GCStats = &GCStats{
		NumGC:      m.NumGC,
		PauseTotal: time.Duration(m.PauseTotalNs),
		NextGC:     m.NextGC,
	}

	if m.NumGC > 0 {
		pm.metrics.SystemMetrics.GCStats.LastGC = time.Unix(0, int64(m.LastGC))
		pm.metrics.SystemMetrics.GCStats.LastPause = time.Duration(m.PauseNs[(m.NumGC+255)%256])
	}

	pm.metrics.Timestamp = time.Now()

	// Log metrics periodically
	pm.log.Infof("Performance Metrics - Memory: %d MB, Goroutines: %d, GC: %d",
		m.Alloc/1024/1024, runtime.NumGoroutine(), m.NumGC)
}

// checkAlerts checks if any performance thresholds are exceeded
func (pm *PerformanceMonitor) checkAlerts() {
	if !pm.config.EnableAlerts || pm.alertManager == nil {
		return
	}

	metrics := pm.GetMetrics()
	pm.alertManager.CheckThresholds(metrics)
}

// ==========================================
// PROFILER MANAGER
// ==========================================

// ProfilerManager manages pprof profiling
type ProfilerManager struct {
	log        *log.Helper
	port       int
	server     *http.Server
	running    bool
	mu         sync.RWMutex
}

// NewProfilerManager creates a new profiler manager
func NewProfilerManager(logger log.Logger, port int) *ProfilerManager {
	return &ProfilerManager{
		log:  log.NewHelper(logger),
		port: port,
	}
}

// Start starts the profiler HTTP server
func (pm *ProfilerManager) Start() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.running {
		return fmt.Errorf("profiler already running")
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/debug/pprof/", http.DefaultServeMux.ServeHTTP)

	pm.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", pm.port),
		Handler: mux,
	}

	go func() {
		if err := pm.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			pm.log.Errorf("Profiler server error: %v", err)
		}
	}()

	pm.running = true
	pm.log.Infof("Profiler started on port %d", pm.port)
	return nil
}

// Stop stops the profiler HTTP server
func (pm *ProfilerManager) Stop() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if !pm.running {
		return
	}

	if pm.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		pm.server.Shutdown(ctx)
	}

	pm.running = false
	pm.log.Info("Profiler stopped")
}

// ==========================================
// ALERT MANAGER
// ==========================================

// AlertManager manages performance alerts
type AlertManager struct {
	log        *log.Helper
	thresholds *AlertThresholds
	alerts     map[string]time.Time
	mu         sync.RWMutex
}

// NewAlertManager creates a new alert manager
func NewAlertManager(logger log.Logger, thresholds *AlertThresholds) *AlertManager {
	return &AlertManager{
		log:        log.NewHelper(logger),
		thresholds: thresholds,
		alerts:     make(map[string]time.Time),
	}
}

// CheckThresholds checks if any thresholds are exceeded
func (am *AlertManager) CheckThresholds(metrics *PerformanceMetrics) {
	am.mu.Lock()
	defer am.mu.Unlock()

	// Check memory usage
	memoryMB := metrics.SystemMetrics.MemoryUsage / 1024 / 1024
	if memoryMB > am.thresholds.MemoryUsageMB {
		am.triggerAlert("high_memory_usage", fmt.Sprintf("Memory usage: %d MB (threshold: %d MB)", memoryMB, am.thresholds.MemoryUsageMB))
	}

	// Check goroutine count
	if metrics.SystemMetrics.GoroutineCount > am.thresholds.GoroutineCount {
		am.triggerAlert("high_goroutine_count", fmt.Sprintf("Goroutine count: %d (threshold: %d)", metrics.SystemMetrics.GoroutineCount, am.thresholds.GoroutineCount))
	}

	// Check GC pause time
	if metrics.SystemMetrics.GCStats != nil {
		pauseMs := metrics.SystemMetrics.GCStats.LastPause.Milliseconds()
		if pauseMs > am.thresholds.GCPauseMs {
			am.triggerAlert("high_gc_pause", fmt.Sprintf("GC pause: %d ms (threshold: %d ms)", pauseMs, am.thresholds.GCPauseMs))
		}
	}
}

// triggerAlert triggers an alert if it hasn't been triggered recently
func (am *AlertManager) triggerAlert(alertType, message string) {
	lastAlert, exists := am.alerts[alertType]
	if !exists || time.Since(lastAlert) > 5*time.Minute {
		am.log.Warnf("PERFORMANCE ALERT [%s]: %s", alertType, message)
		am.alerts[alertType] = time.Now()
	}
}
