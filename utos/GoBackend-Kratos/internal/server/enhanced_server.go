package server

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"

	aiv1 "gobackend-hvac-kratos/api/ai/v1"
	analyticsv1 "gobackend-hvac-kratos/api/analytics/v1"
	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	workflowv1 "gobackend-hvac-kratos/api/workflow/v1"
	"gobackend-hvac-kratos/internal/common"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/discovery"
	"gobackend-hvac-kratos/internal/errors"
	"gobackend-hvac-kratos/internal/middleware"
	"gobackend-hvac-kratos/internal/service"
	"gobackend-hvac-kratos/internal/transport"
)

// 🚀 Enhanced Server with Kratos Framework Excellence
// Integration of all Phase 2 components for enterprise-grade performance

// ==========================================
// ENHANCED SERVER MANAGER
// ==========================================

// EnhancedServerManager manages all enhanced server components
type EnhancedServerManager struct {
	// Core servers
	httpServer *http.Server
	grpcServer *grpc.Server

	// Enhanced components
	middlewareChain  *middleware.HVACMiddlewareChain
	serviceRegistry  *discovery.HVACServiceRegistry
	errorFactory     *errors.HVACErrorFactory
	transportManager *transport.HVACTransportManager
	perfMonitor      *common.PerformanceMonitor

	// Services
	hvacService      *service.HVACService
	aiService        *service.AIService
	analyticsService *service.AnalyticsService
	workflowService  *service.WorkflowService

	// Configuration
	config *EnhancedServerConfig
	log    *log.Helper
	logger log.Logger
}

// EnhancedServerConfig provides comprehensive server configuration
type EnhancedServerConfig struct {
	Server      *conf.Server                     `json:"server"`
	Middleware  *middleware.HVACAuthConfig       `json:"middleware"`
	Discovery   *discovery.ServiceRegistryConfig `json:"discovery"`
	Transport   *transport.TransportConfig       `json:"transport"`
	Performance *common.MonitorConfig            `json:"performance"`
	Environment string                           `json:"environment"`
	Version     string                           `json:"version"`
}

// NewEnhancedServerManager creates a new enhanced server manager
func NewEnhancedServerManager(
	config *EnhancedServerConfig,
	hvacService *service.HVACService,
	aiService *service.AIService,
	analyticsService *service.AnalyticsService,
	workflowService *service.WorkflowService,
	logger log.Logger,
) (*EnhancedServerManager, error) {
	if config == nil {
		config = getDefaultEnhancedConfig()
	}

	// Initialize performance monitor
	perfMonitor := common.NewPerformanceMonitor(logger, config.Performance)

	// Initialize error factory
	errorFactory := errors.NewHVACErrorFactory(logger, config.Environment, config.Version)

	// Initialize service registry
	serviceRegistry := discovery.NewHVACServiceRegistry(config.Discovery, logger)

	// Initialize middleware chain
	middlewareChain := middleware.NewHVACMiddlewareChain(
		config.Middleware,
		&middleware.HVACBusinessConfig{
			EnableValidation:     true,
			EnableAuditLogging:   true,
			EnableRateLimit:      true,
			RateLimitRPS:         1000,
			EnableCircuitBreaker: true,
			FailureThreshold:     5,
			Timeout:              30 * time.Second,
		},
		perfMonitor,
		logger,
	)

	// Initialize transport manager
	transportManager := transport.NewHVACTransportManager(
		config.Transport,
		middlewareChain,
		perfMonitor,
		logger,
	)

	manager := &EnhancedServerManager{
		middlewareChain:  middlewareChain,
		serviceRegistry:  serviceRegistry,
		errorFactory:     errorFactory,
		transportManager: transportManager,
		perfMonitor:      perfMonitor,
		hvacService:      hvacService,
		aiService:        aiService,
		analyticsService: analyticsService,
		workflowService:  workflowService,
		config:           config,
		log:              log.NewHelper(logger),
		logger:           logger,
	}

	// Create enhanced servers
	if err := manager.createEnhancedServers(); err != nil {
		return nil, fmt.Errorf("failed to create enhanced servers: %w", err)
	}

	return manager, nil
}

// Start starts all enhanced server components
func (esm *EnhancedServerManager) Start(ctx context.Context) error {
	esm.log.Info("🚀 Starting Enhanced Server Manager with Kratos Framework Excellence")

	// Start performance monitor
	if err := esm.perfMonitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start performance monitor: %w", err)
	}

	// Start service registry
	if err := esm.serviceRegistry.Start(ctx); err != nil {
		return fmt.Errorf("failed to start service registry: %w", err)
	}

	// Start transport manager
	if err := esm.transportManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start transport manager: %w", err)
	}

	// Register services in discovery
	if err := esm.registerServices(ctx); err != nil {
		return fmt.Errorf("failed to register services: %w", err)
	}

	// Start HTTP server
	if esm.httpServer != nil {
		go func() {
			esm.log.Info("Starting enhanced HTTP server")
			if err := esm.httpServer.Start(ctx); err != nil {
				esm.log.Errorf("HTTP server error: %v", err)
			}
		}()
	}

	// Start gRPC server
	if esm.grpcServer != nil {
		go func() {
			esm.log.Info("Starting enhanced gRPC server")
			if err := esm.grpcServer.Start(ctx); err != nil {
				esm.log.Errorf("gRPC server error: %v", err)
			}
		}()
	}

	esm.log.Info("✅ Enhanced Server Manager started successfully")
	return nil
}

// Stop stops all enhanced server components
func (esm *EnhancedServerManager) Stop(ctx context.Context) error {
	esm.log.Info("🛑 Stopping Enhanced Server Manager")

	// Stop HTTP server
	if esm.httpServer != nil {
		if err := esm.httpServer.Stop(ctx); err != nil {
			esm.log.Errorf("HTTP server stop error: %v", err)
		}
	}

	// Stop gRPC server
	if esm.grpcServer != nil {
		if err := esm.grpcServer.Stop(ctx); err != nil {
			esm.log.Errorf("gRPC server stop error: %v", err)
		}
	}

	// Stop transport manager
	if err := esm.transportManager.Stop(); err != nil {
		esm.log.Errorf("Transport manager stop error: %v", err)
	}

	// Stop service registry
	esm.serviceRegistry.Stop()

	// Stop performance monitor
	esm.perfMonitor.Stop()

	esm.log.Info("✅ Enhanced Server Manager stopped successfully")
	return nil
}

// ==========================================
// ENHANCED SERVER CREATION
// ==========================================

func (esm *EnhancedServerManager) createEnhancedServers() error {
	// Create enhanced HTTP server
	if err := esm.createEnhancedHTTPServer(); err != nil {
		return fmt.Errorf("failed to create enhanced HTTP server: %w", err)
	}

	// Create enhanced gRPC server
	if err := esm.createEnhancedGRPCServer(); err != nil {
		return fmt.Errorf("failed to create enhanced gRPC server: %w", err)
	}

	return nil
}

func (esm *EnhancedServerManager) createEnhancedHTTPServer() error {
	// Build enhanced middleware chain
	middlewares := []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			logging.Server(esm.logger),
			metrics.Server(),
		),
	}

	// Add HVAC middleware chain
	hvacMiddlewares := esm.middlewareChain.BuildHTTPChain()
	for _, mw := range hvacMiddlewares {
		middlewares = append(middlewares, http.Middleware(mw))
	}

	// Server configuration
	if esm.config.Server.Http.Network != "" {
		middlewares = append(middlewares, http.Network(esm.config.Server.Http.Network))
	}
	if esm.config.Server.Http.Addr != "" {
		middlewares = append(middlewares, http.Address(esm.config.Server.Http.Addr))
	}
	if esm.config.Server.Http.Timeout != nil {
		middlewares = append(middlewares, http.Timeout(esm.config.Server.Http.Timeout.AsDuration()))
	}

	// Create server
	srv := http.NewServer(middlewares...)

	// Register services
	hvacv1.RegisterHVACServiceHTTPServer(srv, esm.hvacService)
	aiv1.RegisterAIServiceHTTPServer(srv, esm.aiService)
	analyticsv1.RegisterAnalyticsServiceHTTPServer(srv, esm.analyticsService)
	workflowv1.RegisterWorkflowServiceHTTPServer(srv, esm.workflowService)

	esm.httpServer = srv
	esm.log.Info("✅ Enhanced HTTP server created with HVAC middleware chain")
	return nil
}

func (esm *EnhancedServerManager) createEnhancedGRPCServer() error {
	// Build enhanced middleware chain
	middlewares := []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			logging.Server(esm.logger),
			metrics.Server(),
		),
	}

	// Add HVAC middleware chain
	hvacMiddlewares := esm.middlewareChain.BuildGRPCChain()
	for _, mw := range hvacMiddlewares {
		middlewares = append(middlewares, grpc.Middleware(mw))
	}

	// Server configuration
	if esm.config.Server.Grpc.Network != "" {
		middlewares = append(middlewares, grpc.Network(esm.config.Server.Grpc.Network))
	}
	if esm.config.Server.Grpc.Addr != "" {
		middlewares = append(middlewares, grpc.Address(esm.config.Server.Grpc.Addr))
	}
	if esm.config.Server.Grpc.Timeout != nil {
		middlewares = append(middlewares, grpc.Timeout(esm.config.Server.Grpc.Timeout.AsDuration()))
	}

	// Create server
	srv := grpc.NewServer(middlewares...)

	// Register services
	hvacv1.RegisterHVACServiceServer(srv.Server, esm.hvacService)
	aiv1.RegisterAIServiceServer(srv.Server, esm.aiService)
	analyticsv1.RegisterAnalyticsServiceServer(srv.Server, esm.analyticsService)
	workflowv1.RegisterWorkflowServiceServer(srv.Server, esm.workflowService)

	esm.grpcServer = srv
	esm.log.Info("✅ Enhanced gRPC server created with HVAC middleware chain")
	return nil
}

// ==========================================
// SERVICE REGISTRATION
// ==========================================

func (esm *EnhancedServerManager) registerServices(ctx context.Context) error {
	// Register HVAC service
	hvacServiceInfo := &discovery.HVACServiceInfo{
		ID:      "hvac-service-1",
		Name:    "hvac-service",
		Version: esm.config.Version,
		Type:    discovery.ServiceTypeCustomer,
		Endpoints: []discovery.ServiceEndpoint{
			{
				Protocol: "http",
				Address:  "localhost",
				Port:     8080,
				Path:     "/api/hvac",
			},
			{
				Protocol: "grpc",
				Address:  "localhost",
				Port:     9000,
			},
		},
		Metadata: map[string]interface{}{
			"environment":  esm.config.Environment,
			"capabilities": []string{"customer_management", "job_scheduling"},
		},
		Tags:   []string{"hvac", "customer", "core"},
		Region: "us-east-1",
		Zone:   "us-east-1a",
	}

	if err := esm.serviceRegistry.RegisterService(ctx, hvacServiceInfo); err != nil {
		return fmt.Errorf("failed to register HVAC service: %w", err)
	}

	// Register AI service
	aiServiceInfo := &discovery.HVACServiceInfo{
		ID:      "ai-service-1",
		Name:    "ai-service",
		Version: esm.config.Version,
		Type:    discovery.ServiceTypeAI,
		Endpoints: []discovery.ServiceEndpoint{
			{
				Protocol: "http",
				Address:  "localhost",
				Port:     8080,
				Path:     "/api/ai",
			},
			{
				Protocol: "grpc",
				Address:  "localhost",
				Port:     9000,
			},
		},
		Metadata: map[string]interface{}{
			"environment": esm.config.Environment,
			"models":      []string{"gemma-3-4b-it", "langchain"},
		},
		Tags:   []string{"hvac", "ai", "ml"},
		Region: "us-east-1",
		Zone:   "us-east-1a",
	}

	if err := esm.serviceRegistry.RegisterService(ctx, aiServiceInfo); err != nil {
		return fmt.Errorf("failed to register AI service: %w", err)
	}

	esm.log.Info("✅ All services registered in discovery")
	return nil
}

// ==========================================
// HEALTH CHECK & METRICS
// ==========================================

// GetHealthStatus returns comprehensive health status
func (esm *EnhancedServerManager) GetHealthStatus() map[string]interface{} {
	status := map[string]interface{}{
		"status":      "healthy",
		"timestamp":   time.Now(),
		"version":     esm.config.Version,
		"environment": esm.config.Environment,
	}

	// Performance metrics
	if esm.perfMonitor != nil {
		metrics := esm.perfMonitor.GetMetrics()
		status["performance"] = map[string]interface{}{
			"memory_usage_mb": metrics.SystemMetrics.MemoryUsage / 1024 / 1024,
			"goroutines":      metrics.SystemMetrics.GoroutineCount,
			"gc_count":        metrics.SystemMetrics.GCStats.NumGC,
		}
	}

	// Service registry status
	if esm.serviceRegistry != nil {
		// Get registered services count
		status["services"] = map[string]interface{}{
			"registered_count": "2", // HVAC + AI services
			"registry_status":  "active",
		}
	}

	// Transport metrics
	if esm.transportManager != nil {
		transportMetrics := esm.transportManager.GetMetrics()
		status["transport"] = map[string]interface{}{
			"http_requests":  transportMetrics.HTTPRequests,
			"grpc_requests":  transportMetrics.GRPCRequests,
			"ws_connections": transportMetrics.WSConnections,
			"avg_latency_ms": transportMetrics.AvgLatency.Milliseconds(),
		}
	}

	return status
}

// GetDetailedMetrics returns detailed system metrics
func (esm *EnhancedServerManager) GetDetailedMetrics() map[string]interface{} {
	metrics := map[string]interface{}{
		"timestamp": time.Now(),
		"version":   esm.config.Version,
	}

	// Performance metrics
	if esm.perfMonitor != nil {
		perfMetrics := esm.perfMonitor.GetMetrics()
		metrics["performance"] = perfMetrics
	}

	// Transport metrics
	if esm.transportManager != nil {
		transportMetrics := esm.transportManager.GetMetrics()
		metrics["transport"] = transportMetrics
	}

	// Middleware metrics would go here
	metrics["middleware"] = map[string]interface{}{
		"auth_enabled":     true,
		"business_enabled": true,
		"perf_enabled":     true,
	}

	return metrics
}

// ==========================================
// DEFAULT CONFIGURATION
// ==========================================

func getDefaultEnhancedConfig() *EnhancedServerConfig {
	return &EnhancedServerConfig{
		Middleware: &middleware.HVACAuthConfig{
			EnableRoleBasedAuth: true,
			RequiredRoles:       []string{"user"},
			AdminRoles:          []string{"admin", "manager"},
			TechnicianRoles:     []string{"technician", "senior_technician"},
			CustomerRoles:       []string{"customer"},
			BypassPaths:         []string{"/health", "/metrics", "/api/public"},
			TokenHeader:         "Authorization",
			SessionTimeout:      24 * time.Hour,
		},
		Discovery: &discovery.ServiceRegistryConfig{
			HealthCheckInterval: 30 * time.Second,
			ServiceTTL:          5 * time.Minute,
			EnableLoadBalancing: true,
			EnableEventBus:      true,
			MaxRetries:          3,
			RetryDelay:          1 * time.Second,
		},
		Transport: &transport.TransportConfig{
			HTTP: &transport.HTTPConfig{
				Address:        "0.0.0.0",
				Port:           8080,
				ReadTimeout:    30 * time.Second,
				WriteTimeout:   30 * time.Second,
				IdleTimeout:    60 * time.Second,
				MaxHeaderBytes: 1 << 20, // 1MB
				EnableHTTP2:    true,
				EnableGzip:     true,
				CORSEnabled:    true,
				CORSOrigins:    []string{"*"},
			},
			GRPC: &transport.GRPCConfig{
				Address:           "0.0.0.0",
				Port:              9000,
				MaxRecvMsgSize:    4 * 1024 * 1024, // 4MB
				MaxSendMsgSize:    4 * 1024 * 1024, // 4MB
				ConnectionTimeout: 30 * time.Second,
				EnableReflection:  true,
				EnableStreaming:   true,
			},
			WebSocket: &transport.WebSocketConfig{
				Address:         "0.0.0.0",
				Port:            8081,
				ReadBufferSize:  1024,
				WriteBufferSize: 1024,
				CheckOrigin:     true,
				EnablePing:      true,
				PingInterval:    30 * time.Second,
				MaxConnections:  1000,
			},
		},
		Performance: &common.MonitorConfig{
			EnableProfiling: true,
			EnableMetrics:   true,
			EnableAlerts:    true,
			ProfilerPort:    6060,
		},
		Environment: "development",
		Version:     "2.0.0",
	}
}
