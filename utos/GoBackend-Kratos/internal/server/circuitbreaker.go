package server

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/biz"
)

// 🔄 Circuit Breaker Service - Prevents cascading failures and provides resilience
type CircuitBreakerService struct {
	breakers map[string]*gobreaker.CircuitBreaker
	logger   *log.Helper
	zapLogger *zap.Logger
}

// CircuitBreakerConfig holds configuration for circuit breakers
type CircuitBreakerConfig struct {
	Name            string
	MaxRequests     uint32
	Interval        time.Duration
	Timeout         time.Duration
	FailureRatio    float64
	MinRequests     uint32
	OnStateChange   func(name string, from gobreaker.State, to gobreaker.State)
}

// NewCircuitBreakerService creates a new circuit breaker service
func NewCircuitBreakerService(logger log.Logger) *CircuitBreakerService {
	zapLogger, _ := zap.NewProduction()
	
	service := &CircuitBreakerService{
		breakers:  make(map[string]*gobreaker.CircuitBreaker),
		logger:    log.<PERSON><PERSON><PERSON>(logger),
		zapLogger: zapLogger,
	}

	// Initialize default circuit breakers for HVAC services
	service.initializeDefaultBreakers()
	
	return service
}

// initializeDefaultBreakers sets up circuit breakers for common HVAC operations
func (cb *CircuitBreakerService) initializeDefaultBreakers() {
	// Customer service circuit breaker
	cb.CreateCircuitBreaker(CircuitBreakerConfig{
		Name:         "customer-service",
		MaxRequests:  10,
		Interval:     1 * time.Minute,
		Timeout:      30 * time.Second,
		FailureRatio: 0.6,
		MinRequests:  5,
	})

	// Job service circuit breaker
	cb.CreateCircuitBreaker(CircuitBreakerConfig{
		Name:         "job-service",
		MaxRequests:  15,
		Interval:     1 * time.Minute,
		Timeout:      45 * time.Second,
		FailureRatio: 0.7,
		MinRequests:  5,
	})

	// AI service circuit breaker (more tolerant due to AI processing complexity)
	cb.CreateCircuitBreaker(CircuitBreakerConfig{
		Name:         "ai-service",
		MaxRequests:  5,
		Interval:     2 * time.Minute,
		Timeout:      60 * time.Second,
		FailureRatio: 0.8,
		MinRequests:  3,
	})

	// Analytics service circuit breaker
	cb.CreateCircuitBreaker(CircuitBreakerConfig{
		Name:         "analytics-service",
		MaxRequests:  20,
		Interval:     30 * time.Second,
		Timeout:      15 * time.Second,
		FailureRatio: 0.5,
		MinRequests:  10,
	})

	// Workflow service circuit breaker
	cb.CreateCircuitBreaker(CircuitBreakerConfig{
		Name:         "workflow-service",
		MaxRequests:  12,
		Interval:     1 * time.Minute,
		Timeout:      30 * time.Second,
		FailureRatio: 0.6,
		MinRequests:  6,
	})
}

// CreateCircuitBreaker creates a new circuit breaker with the given configuration
func (cb *CircuitBreakerService) CreateCircuitBreaker(config CircuitBreakerConfig) {
	settings := gobreaker.Settings{
		Name:        config.Name,
		MaxRequests: config.MaxRequests,
		Interval:    config.Interval,
		Timeout:     config.Timeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= config.MinRequests && failureRatio >= config.FailureRatio
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			cb.logger.Infof("🔄 Circuit breaker %s state changed: %s -> %s", name, from, to)
			cb.zapLogger.Info("Circuit breaker state change",
				zap.String("name", name),
				zap.String("from", from.String()),
				zap.String("to", to.String()),
				zap.Time("timestamp", time.Now()),
			)
			
			// Call custom state change handler if provided
			if config.OnStateChange != nil {
				config.OnStateChange(name, from, to)
			}
		},
	}

	cb.breakers[config.Name] = gobreaker.NewCircuitBreaker(settings)
	cb.logger.Infof("🔄 Circuit breaker created: %s", config.Name)
}

// Execute runs a function through the specified circuit breaker
func (cb *CircuitBreakerService) Execute(ctx context.Context, breakerName string, fn func() (interface{}, error)) (interface{}, *biz.CustomError) {
	breaker, exists := cb.breakers[breakerName]
	if !exists {
		err := biz.NewCustomError(biz.ErrCodeInternalError, "Circuit breaker not found", breakerName)
		cb.logger.Errorf("Circuit breaker not found: %s", breakerName)
		return nil, err
	}

	// Add request ID to context for tracking
	requestID := fmt.Sprintf("%s-%d", breakerName, time.Now().UnixNano())
	
	cb.zapLogger.Debug("Executing function through circuit breaker",
		zap.String("breaker", breakerName),
		zap.String("request_id", requestID),
		zap.String("state", breaker.State().String()),
	)

	result, err := breaker.Execute(fn)
	if err != nil {
		// Check if it's a circuit breaker error
		if err == gobreaker.ErrOpenState {
			customErr := biz.NewCustomError(biz.ErrCodeCircuitBreakerOpen, "Circuit breaker is open", map[string]interface{}{
				"breaker_name": breakerName,
				"state":        breaker.State().String(),
				"request_id":   requestID,
			})
			customErr.WithRequestID(requestID)
			
			cb.zapLogger.Warn("Circuit breaker is open",
				zap.String("breaker", breakerName),
				zap.String("request_id", requestID),
			)
			
			return nil, customErr
		}

		// Regular function error
		customErr := biz.NewCustomError(biz.ErrCodeInternalError, "Function execution failed", err.Error())
		customErr.WithRequestID(requestID).WithContext("breaker_name", breakerName)
		
		cb.zapLogger.Error("Function execution failed through circuit breaker",
			zap.String("breaker", breakerName),
			zap.String("request_id", requestID),
			zap.Error(err),
		)
		
		return nil, customErr
	}

	cb.zapLogger.Debug("Function executed successfully through circuit breaker",
		zap.String("breaker", breakerName),
		zap.String("request_id", requestID),
	)

	return result, nil
}

// GetBreakerState returns the current state of a circuit breaker
func (cb *CircuitBreakerService) GetBreakerState(breakerName string) (gobreaker.State, *biz.CustomError) {
	breaker, exists := cb.breakers[breakerName]
	if !exists {
		err := biz.NewCustomError(biz.ErrCodeNotFound, "Circuit breaker not found", breakerName)
		return gobreaker.StateClosed, err
	}

	return breaker.State(), nil
}

// GetBreakerCounts returns the current counts for a circuit breaker
func (cb *CircuitBreakerService) GetBreakerCounts(breakerName string) (gobreaker.Counts, *biz.CustomError) {
	breaker, exists := cb.breakers[breakerName]
	if !exists {
		err := biz.NewCustomError(biz.ErrCodeNotFound, "Circuit breaker not found", breakerName)
		return gobreaker.Counts{}, err
	}

	return breaker.Counts(), nil
}

// GetAllBreakerStates returns the states of all circuit breakers
func (cb *CircuitBreakerService) GetAllBreakerStates() map[string]gobreaker.State {
	states := make(map[string]gobreaker.State)
	for name, breaker := range cb.breakers {
		states[name] = breaker.State()
	}
	return states
}

// GetHealthStatus returns overall health status based on circuit breaker states
func (cb *CircuitBreakerService) GetHealthStatus() map[string]interface{} {
	states := cb.GetAllBreakerStates()
	
	healthy := 0
	degraded := 0
	unhealthy := 0
	
	for _, state := range states {
		switch state {
		case gobreaker.StateClosed:
			healthy++
		case gobreaker.StateHalfOpen:
			degraded++
		case gobreaker.StateOpen:
			unhealthy++
		}
	}
	
	totalBreakers := len(states)
	healthPercentage := float64(healthy) / float64(totalBreakers) * 100
	
	var overallStatus string
	if healthPercentage >= 80 {
		overallStatus = "healthy"
	} else if healthPercentage >= 50 {
		overallStatus = "degraded"
	} else {
		overallStatus = "unhealthy"
	}
	
	return map[string]interface{}{
		"overall_status":    overallStatus,
		"health_percentage": healthPercentage,
		"total_breakers":    totalBreakers,
		"healthy_count":     healthy,
		"degraded_count":    degraded,
		"unhealthy_count":   unhealthy,
		"breaker_states":    states,
		"timestamp":         time.Now(),
	}
}

// ResetBreaker manually resets a circuit breaker (use with caution)
func (cb *CircuitBreakerService) ResetBreaker(breakerName string) *biz.CustomError {
	breaker, exists := cb.breakers[breakerName]
	if !exists {
		return biz.NewCustomError(biz.ErrCodeNotFound, "Circuit breaker not found", breakerName)
	}

	// Note: gobreaker doesn't have a public Reset method, so we log the action
	cb.logger.Warnf("🔄 Manual reset requested for circuit breaker: %s (current state: %s)", 
		breakerName, breaker.State().String())
	
	cb.zapLogger.Warn("Manual circuit breaker reset requested",
		zap.String("breaker", breakerName),
		zap.String("current_state", breaker.State().String()),
		zap.Time("timestamp", time.Now()),
	)

	return nil
}

// Cleanup closes all circuit breakers and cleans up resources
func (cb *CircuitBreakerService) Cleanup() {
	cb.logger.Info("🔄 Cleaning up circuit breaker service")
	
	for name := range cb.breakers {
		cb.logger.Infof("🔄 Cleaning up circuit breaker: %s", name)
	}
	
	cb.breakers = make(map[string]*gobreaker.CircuitBreaker)
	
	if cb.zapLogger != nil {
		cb.zapLogger.Sync()
	}
}
