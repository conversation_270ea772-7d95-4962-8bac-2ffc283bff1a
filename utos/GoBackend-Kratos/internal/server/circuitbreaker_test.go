package server

import (
	"context"
	"errors"
	"io"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sony/gobreaker"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gobackend-hvac-kratos/internal/biz"
)

func TestCircuitBreakerService_Creation(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Service_Created_Successfully", func(t *testing.T) {
		assert.NotNil(t, cb)
		assert.NotNil(t, cb.breakers)
		assert.NotNil(t, cb.logger)
		assert.NotNil(t, cb.zapLogger)
	})

	t.Run("Default_Breakers_Initialized", func(t *testing.T) {
		expectedBreakers := []string{
			"customer-service",
			"job-service",
			"ai-service",
			"analytics-service",
			"workflow-service",
		}

		for _, name := range expectedBreakers {
			_, exists := cb.breakers[name]
			assert.True(t, exists, "Expected circuit breaker %s to be initialized", name)
		}
	})

	t.Run("All_Breakers_Initially_Closed", func(t *testing.T) {
		states := cb.GetAllBreakerStates()
		for name, state := range states {
			assert.Equal(t, gobreaker.StateClosed, state, "Circuit breaker %s should be initially closed", name)
		}
	})
}

func TestCircuitBreakerService_Execute(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)
	ctx := context.Background()

	t.Run("Successful_Execution", func(t *testing.T) {
		expectedResult := "success"

		result, err := cb.Execute(ctx, "customer-service", func() (interface{}, error) {
			return expectedResult, nil
		})

		assert.Nil(t, err)
		assert.Equal(t, expectedResult, result)
	})

	t.Run("Function_Error", func(t *testing.T) {
		expectedError := errors.New("function failed")

		result, err := cb.Execute(ctx, "customer-service", func() (interface{}, error) {
			return nil, expectedError
		})

		assert.Nil(t, result)
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeInternalError, err.Code)
		assert.Contains(t, err.Message, "Function execution failed")
	})

	t.Run("Nonexistent_Breaker", func(t *testing.T) {
		result, err := cb.Execute(ctx, "nonexistent-breaker", func() (interface{}, error) {
			return "should not execute", nil
		})

		assert.Nil(t, result)
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeInternalError, err.Code)
		assert.Contains(t, err.Message, "Circuit breaker not found")
	})
}

func TestCircuitBreakerService_CreateCustomBreaker(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Create_Custom_Breaker", func(t *testing.T) {
		config := CircuitBreakerConfig{
			Name:         "test-service",
			MaxRequests:  5,
			Interval:     30 * time.Second,
			Timeout:      10 * time.Second,
			FailureRatio: 0.5,
			MinRequests:  3,
		}

		cb.CreateCircuitBreaker(config)

		// Verify the breaker was created
		_, exists := cb.breakers["test-service"]
		assert.True(t, exists)

		// Verify initial state
		state, err := cb.GetBreakerState("test-service")
		assert.Nil(t, err)
		assert.Equal(t, gobreaker.StateClosed, state)
	})
}

func TestCircuitBreakerService_StateManagement(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Get_Breaker_State", func(t *testing.T) {
		state, err := cb.GetBreakerState("customer-service")
		assert.Nil(t, err)
		assert.Equal(t, gobreaker.StateClosed, state)
	})

	t.Run("Get_Nonexistent_Breaker_State", func(t *testing.T) {
		state, err := cb.GetBreakerState("nonexistent")
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeNotFound, err.Code)
		assert.Equal(t, gobreaker.StateClosed, state) // Default return value
	})

	t.Run("Get_Breaker_Counts", func(t *testing.T) {
		counts, err := cb.GetBreakerCounts("customer-service")
		assert.Nil(t, err)
		assert.Equal(t, uint32(0), counts.Requests)
		assert.Equal(t, uint32(0), counts.TotalSuccesses)
		assert.Equal(t, uint32(0), counts.TotalFailures)
	})

	t.Run("Get_All_Breaker_States", func(t *testing.T) {
		states := cb.GetAllBreakerStates()
		assert.Greater(t, len(states), 0)

		// All should be closed initially
		for name, state := range states {
			assert.Equal(t, gobreaker.StateClosed, state, "Breaker %s should be closed", name)
		}
	})
}

func TestCircuitBreakerService_HealthStatus(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Initial_Health_Status", func(t *testing.T) {
		health := cb.GetHealthStatus()

		assert.Equal(t, "healthy", health["overall_status"])
		assert.Equal(t, 100.0, health["health_percentage"])
		assert.Greater(t, health["total_breakers"], 0)
		assert.Equal(t, health["total_breakers"], health["healthy_count"])
		assert.Equal(t, 0, health["degraded_count"])
		assert.Equal(t, 0, health["unhealthy_count"])
		assert.NotNil(t, health["breaker_states"])
		assert.NotNil(t, health["timestamp"])
	})
}

func TestCircuitBreakerService_BreakerTripping(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)
	ctx := context.Background()

	// Create a test breaker with low thresholds for easier testing
	config := CircuitBreakerConfig{
		Name:         "test-trip-breaker",
		MaxRequests:  2,
		Interval:     1 * time.Second,
		Timeout:      1 * time.Second,
		FailureRatio: 0.5, // 50% failure rate
		MinRequests:  2,   // Minimum 2 requests before tripping
	}
	cb.CreateCircuitBreaker(config)

	t.Run("Breaker_Trips_After_Failures", func(t *testing.T) {
		// First failure
		_, err1 := cb.Execute(ctx, "test-trip-breaker", func() (interface{}, error) {
			return nil, errors.New("failure 1")
		})
		assert.NotNil(t, err1)

		// Second failure - should trip the breaker
		_, err2 := cb.Execute(ctx, "test-trip-breaker", func() (interface{}, error) {
			return nil, errors.New("failure 2")
		})
		assert.NotNil(t, err2)

		// Give the breaker a moment to process the failures
		time.Sleep(10 * time.Millisecond)

		// Check counts
		counts, err := cb.GetBreakerCounts("test-trip-breaker")
		assert.Nil(t, err)
		assert.Equal(t, uint32(2), counts.Requests)
		assert.Equal(t, uint32(2), counts.TotalFailures)
	})
}

func TestCircuitBreakerService_Cleanup(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Cleanup_Removes_All_Breakers", func(t *testing.T) {
		// Verify breakers exist before cleanup
		initialCount := len(cb.breakers)
		assert.Greater(t, initialCount, 0)

		// Cleanup
		cb.Cleanup()

		// Verify all breakers are removed
		assert.Equal(t, 0, len(cb.breakers))
	})
}

func TestCircuitBreakerService_ResetBreaker(t *testing.T) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	t.Run("Reset_Existing_Breaker", func(t *testing.T) {
		err := cb.ResetBreaker("customer-service")
		assert.Nil(t, err)
	})

	t.Run("Reset_Nonexistent_Breaker", func(t *testing.T) {
		err := cb.ResetBreaker("nonexistent")
		require.NotNil(t, err)
		assert.Equal(t, biz.ErrCodeNotFound, err.Code)
	})
}

// Benchmark tests for performance
func BenchmarkCircuitBreakerService_Execute(b *testing.B) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cb.Execute(ctx, "customer-service", func() (interface{}, error) {
			return "success", nil
		})
	}
}

func BenchmarkCircuitBreakerService_GetHealthStatus(b *testing.B) {
	logger := log.NewStdLogger(io.Discard)
	cb := NewCircuitBreakerService(logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cb.GetHealthStatus()
	}
}
