package server

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"

	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	"gobackend-hvac-kratos/internal/service"
)

// TRPCBridge provides HTTP endpoints that mimic tRPC for frontend compatibility
type TRPCBridge struct {
	hvacService *service.HVACService
	log         *log.Helper
}

// NewTRPCBridge creates a new tRPC bridge
func NewTRPCBridge(
	hvacService *service.HVACService,
	logger log.Logger,
) *TRPCBridge {
	return &TRPCBridge{
		hvacService: hvacService,
		log:         log.NewHelper(logger),
	}
}

// TRPCRequest represents a tRPC-style request
type TRPCRequest struct {
	Input interface{} `json:"input"`
}

// TRPCResponse represents a tRPC-style response
type TRPCResponse struct {
	Result interface{} `json:"result"`
	Error  *TRPCError  `json:"error,omitempty"`
}

// TRPCError represents a tRPC error
type TRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Customer represents the frontend customer type
type Customer struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// Job represents the frontend job type
type Job struct {
	ID          string    `json:"id"`
	CustomerID  string    `json:"customerId"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	ScheduledAt *time.Time `json:"scheduledDate,omitempty"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Data  interface{} `json:"data"`
	Total int32       `json:"total"`
	Page  int32       `json:"page"`
	Limit int32       `json:"limit"`
}

// RegisterRoutes registers tRPC bridge routes
func (tb *TRPCBridge) RegisterRoutes(router *mux.Router) {
	// Enable CORS for all tRPC routes
	router.Use(tb.corsMiddleware)

	// Customer routes
	router.HandleFunc("/api/trpc/customer.list", tb.handleCustomerList).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/customer.get", tb.handleCustomerGet).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/customer.create", tb.handleCustomerCreate).Methods("POST", "OPTIONS")

	// Job routes
	router.HandleFunc("/api/trpc/job.list", tb.handleJobList).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/job.get", tb.handleJobGet).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/job.create", tb.handleJobCreate).Methods("POST", "OPTIONS")

	// System routes
	router.HandleFunc("/api/trpc/system.health", tb.handleSystemHealth).Methods("POST", "OPTIONS")

	tb.log.Info("🌉 tRPC Bridge routes registered")
}

// CORS middleware
func (tb *TRPCBridge) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// Authentication middleware (basic implementation)
func (tb *TRPCBridge) authenticateRequest(r *http.Request) error {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		return fmt.Errorf("missing authorization header")
	}

	// Basic validation - accept any Bearer token for now
	if !strings.HasPrefix(authHeader, "Bearer ") {
		return fmt.Errorf("invalid authorization format")
	}

	// TODO: Implement proper JWT validation with Kratos
	return nil
}

// Helper function to send tRPC response
func (tb *TRPCBridge) sendTRPCResponse(w http.ResponseWriter, result interface{}, err error) {
	w.Header().Set("Content-Type", "application/json")

	response := TRPCResponse{}

	if err != nil {
		response.Error = &TRPCError{
			Code:    500,
			Message: err.Error(),
		}
		w.WriteHeader(http.StatusInternalServerError)
	} else {
		response.Result = result
		w.WriteHeader(http.StatusOK)
	}

	json.NewEncoder(w).Encode(response)
}

// Convert protobuf Customer to frontend Customer
func (tb *TRPCBridge) convertCustomer(pbCustomer *hvacv1.Customer) *Customer {
	if pbCustomer == nil {
		return nil
	}

	customer := &Customer{
		ID:      strconv.FormatInt(pbCustomer.Id, 10),
		Name:    pbCustomer.Name,
		Email:   pbCustomer.Email,
		Phone:   pbCustomer.Phone,
		Address: pbCustomer.Address,
		Status:  "active", // Default status
	}

	if pbCustomer.CreatedAt != nil {
		customer.CreatedAt = pbCustomer.CreatedAt.AsTime()
	}
	if pbCustomer.UpdatedAt != nil {
		customer.UpdatedAt = pbCustomer.UpdatedAt.AsTime()
	}

	return customer
}

// Convert protobuf Job to frontend Job
func (tb *TRPCBridge) convertJob(pbJob *hvacv1.Job) *Job {
	if pbJob == nil {
		return nil
	}

	job := &Job{
		ID:          strconv.FormatInt(pbJob.Id, 10),
		CustomerID:  strconv.FormatInt(pbJob.CustomerId, 10),
		Title:       pbJob.Title,
		Description: pbJob.Description,
		Status:      pbJob.Status,
		Priority:    pbJob.Priority,
	}

	if pbJob.ScheduledAt != nil {
		scheduledAt := pbJob.ScheduledAt.AsTime()
		job.ScheduledAt = &scheduledAt
	}
	if pbJob.CreatedAt != nil {
		job.CreatedAt = pbJob.CreatedAt.AsTime()
	}
	if pbJob.UpdatedAt != nil {
		job.UpdatedAt = pbJob.UpdatedAt.AsTime()
	}

	return job
}

// Customer handlers

// handleCustomerList handles customer.list tRPC calls
func (tb *TRPCBridge) handleCustomerList(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling customer.list tRPC call")

	// Skip auth for now to test basic connectivity
	// if err := tb.authenticateRequest(r); err != nil {
	//     tb.sendTRPCResponse(w, nil, err)
	//     return
	// }

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract pagination parameters
	page := int32(1)
	pageSize := int32(10)

	if input, ok := req.Input.(map[string]interface{}); ok {
		if p, exists := input["page"]; exists {
			if pFloat, ok := p.(float64); ok {
				page = int32(pFloat)
			}
		}
		if ps, exists := input["limit"]; exists {
			if psFloat, ok := ps.(float64); ok {
				pageSize = int32(psFloat)
			}
		}
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.ListCustomers(ctx, &hvacv1.ListCustomersRequest{
		Page:     page,
		PageSize: pageSize,
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to list customers: %v", err))
		return
	}

	// Convert customers
	customers := make([]*Customer, len(resp.Customers))
	for i, pbCustomer := range resp.Customers {
		customers[i] = tb.convertCustomer(pbCustomer)
	}

	result := PaginatedResponse{
		Data:  customers,
		Total: resp.Total,
		Page:  page,
		Limit: pageSize,
	}

	tb.sendTRPCResponse(w, result, nil)
}

// handleCustomerGet handles customer.get tRPC calls
func (tb *TRPCBridge) handleCustomerGet(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling customer.get tRPC call")

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract customer ID
	var customerID int64
	if input, ok := req.Input.(map[string]interface{}); ok {
		if id, exists := input["id"]; exists {
			if idStr, ok := id.(string); ok {
				var err error
				customerID, err = strconv.ParseInt(idStr, 10, 64)
				if err != nil {
					tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid customer ID: %v", err))
					return
				}
			}
		}
	}

	if customerID == 0 {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("customer ID is required"))
		return
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.GetCustomer(ctx, &hvacv1.GetCustomerRequest{
		Id: customerID,
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to get customer: %v", err))
		return
	}

	customer := tb.convertCustomer(resp.Customer)
	tb.sendTRPCResponse(w, customer, nil)
}

// handleCustomerCreate handles customer.create tRPC calls
func (tb *TRPCBridge) handleCustomerCreate(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling customer.create tRPC call")

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract customer data
	var name, email, phone, address string
	if input, ok := req.Input.(map[string]interface{}); ok {
		if n, exists := input["name"]; exists {
			name, _ = n.(string)
		}
		if e, exists := input["email"]; exists {
			email, _ = e.(string)
		}
		if p, exists := input["phone"]; exists {
			phone, _ = p.(string)
		}
		if a, exists := input["address"]; exists {
			address, _ = a.(string)
		}
	}

	if name == "" {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("customer name is required"))
		return
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.CreateCustomer(ctx, &hvacv1.CreateCustomerRequest{
		Name:    name,
		Email:   email,
		Phone:   phone,
		Address: address,
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to create customer: %v", err))
		return
	}

	customer := tb.convertCustomer(resp.Customer)
	tb.sendTRPCResponse(w, customer, nil)
}

// Job handlers

// handleJobList handles job.list tRPC calls
func (tb *TRPCBridge) handleJobList(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling job.list tRPC call")

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract pagination parameters
	page := int32(1)
	pageSize := int32(10)

	if input, ok := req.Input.(map[string]interface{}); ok {
		if p, exists := input["page"]; exists {
			if pFloat, ok := p.(float64); ok {
				page = int32(pFloat)
			}
		}
		if ps, exists := input["limit"]; exists {
			if psFloat, ok := ps.(float64); ok {
				pageSize = int32(psFloat)
			}
		}
		// Note: customerId and status filtering will be implemented when protobuf is updated
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.ListJobs(ctx, &hvacv1.ListJobsRequest{
		Page:     page,
		PageSize: pageSize,
		// Note: CustomerId and Status not available in current protobuf definition
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to list jobs: %v", err))
		return
	}

	// Convert jobs
	jobs := make([]*Job, len(resp.Jobs))
	for i, pbJob := range resp.Jobs {
		jobs[i] = tb.convertJob(pbJob)
	}

	result := PaginatedResponse{
		Data:  jobs,
		Total: resp.Total,
		Page:  page,
		Limit: pageSize,
	}

	tb.sendTRPCResponse(w, result, nil)
}

// handleJobGet handles job.get tRPC calls
func (tb *TRPCBridge) handleJobGet(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling job.get tRPC call")

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract job ID
	var jobID int64
	if input, ok := req.Input.(map[string]interface{}); ok {
		if id, exists := input["id"]; exists {
			if idStr, ok := id.(string); ok {
				var err error
				jobID, err = strconv.ParseInt(idStr, 10, 64)
				if err != nil {
					tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid job ID: %v", err))
					return
				}
			}
		}
	}

	if jobID == 0 {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("job ID is required"))
		return
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.GetJob(ctx, &hvacv1.GetJobRequest{
		Id: jobID,
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to get job: %v", err))
		return
	}

	job := tb.convertJob(resp.Job)
	tb.sendTRPCResponse(w, job, nil)
}

// handleJobCreate handles job.create tRPC calls
func (tb *TRPCBridge) handleJobCreate(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling job.create tRPC call")

	var req TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("invalid request body: %v", err))
		return
	}

	// Extract job data
	var customerID int64
	var title, description, priority string

	if input, ok := req.Input.(map[string]interface{}); ok {
		if cid, exists := input["customerId"]; exists {
			if cidStr, ok := cid.(string); ok {
				customerID, _ = strconv.ParseInt(cidStr, 10, 64)
			}
		}
		if t, exists := input["title"]; exists {
			title, _ = t.(string)
		}
		if d, exists := input["description"]; exists {
			description, _ = d.(string)
		}
		if p, exists := input["priority"]; exists {
			priority, _ = p.(string)
		}
	}

	if customerID == 0 || title == "" {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("customer ID and title are required"))
		return
	}

	// Call the gRPC service
	ctx := context.Background()
	resp, err := tb.hvacService.CreateJob(ctx, &hvacv1.CreateJobRequest{
		CustomerId:  customerID,
		Title:       title,
		Description: description,
		Priority:    priority,
	})

	if err != nil {
		tb.sendTRPCResponse(w, nil, fmt.Errorf("failed to create job: %v", err))
		return
	}

	job := tb.convertJob(resp.Job)
	tb.sendTRPCResponse(w, job, nil)
}

// System handlers

// handleSystemHealth handles system.health tRPC calls
func (tb *TRPCBridge) handleSystemHealth(w http.ResponseWriter, r *http.Request) {
	tb.log.Info("🔍 Handling system.health tRPC call")

	// Simple health check response
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"services": map[string]string{
			"database": "connected",
			"grpc":     "running",
			"trpc":     "bridged",
		},
	}

	tb.sendTRPCResponse(w, health, nil)
}