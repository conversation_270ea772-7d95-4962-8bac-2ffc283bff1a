package workflow

import (
	"context"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

// TemporalService handles Temporal workflow operations
type TemporalService struct {
	client client.Client
	worker worker.Worker
	config *WorkflowConfig
	logger *zap.Logger
}

// WorkflowConfig represents workflow configuration
type WorkflowConfig struct {
	TemporalAddress   string `yaml:"temporal_address"`
	Namespace         string `yaml:"namespace"`
	TaskQueue         string `yaml:"task_queue"`
	RedisAddr         string `yaml:"redis_addr"`
	RedisPassword     string `yaml:"redis_password"`
	RedisDB           int    `yaml:"redis_db"`
	WorkerConcurrency int    `yaml:"worker_concurrency"`
}

// DefaultWorkflowConfig returns default workflow configuration
func DefaultWorkflowConfig() *WorkflowConfig {
	return &WorkflowConfig{
		TemporalAddress:   "localhost:7233",
		Namespace:         "default",
		TaskQueue:         "hvac-crm-tasks",
		RedisAddr:         "localhost:6379",
		RedisPassword:     "",
		RedisDB:           0,
		WorkerConcurrency: 10,
	}
}

// NewTemporalService creates a new Temporal service instance
func NewTemporalService(config *WorkflowConfig, logger *zap.Logger) (*TemporalService, error) {
	if config == nil {
		config = DefaultWorkflowConfig()
	}

	// Create Temporal client
	c, err := client.Dial(client.Options{
		HostPort:  config.TemporalAddress,
		Namespace: config.Namespace,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Temporal client: %w", err)
	}

	// Create worker
	w := worker.New(c, config.TaskQueue, worker.Options{})

	return &TemporalService{
		client: c,
		worker: w,
		config: config,
		logger: logger,
	}, nil
}

// RegisterWorkflow registers a workflow with the worker
func (s *TemporalService) RegisterWorkflow(workflowFunc interface{}) {
	s.worker.RegisterWorkflow(workflowFunc)
}

// RegisterActivity registers an activity with the worker
func (s *TemporalService) RegisterActivity(activityFunc interface{}) {
	s.worker.RegisterActivity(activityFunc)
}

// StartWorker starts the Temporal worker
func (s *TemporalService) StartWorker(ctx context.Context) error {
	s.logger.Info("Starting Temporal worker", zap.String("task_queue", s.config.TaskQueue))
	return s.worker.Run(worker.InterruptCh())
}

// StopWorker stops the Temporal worker
func (s *TemporalService) StopWorker() {
	s.worker.Stop()
	s.logger.Info("Temporal worker stopped")
}

// ExecuteWorkflow executes a workflow
func (s *TemporalService) ExecuteWorkflow(ctx context.Context, workflowID string, workflowType string, args ...interface{}) (client.WorkflowRun, error) {
	options := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: s.config.TaskQueue,
	}

	return s.client.ExecuteWorkflow(ctx, options, workflowType, args...)
}

// GetWorkflowResult gets workflow execution result
func (s *TemporalService) GetWorkflowResult(ctx context.Context, workflowID, runID string, valuePtr interface{}) error {
	workflowRun := s.client.GetWorkflow(ctx, workflowID, runID)
	return workflowRun.Get(ctx, valuePtr)
}

// BackgroundJobService handles background job processing with Asynq
type BackgroundJobService struct {
	client *asynq.Client
	server *asynq.Server
	mux    *asynq.ServeMux
	config *WorkflowConfig
	logger *zap.Logger
}

// NewBackgroundJobService creates a new background job service
func NewBackgroundJobService(config *WorkflowConfig, logger *zap.Logger) (*BackgroundJobService, error) {
	if config == nil {
		config = DefaultWorkflowConfig()
	}

	// Redis connection options
	redisOpt := asynq.RedisClientOpt{
		Addr:     config.RedisAddr,
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	}

	// Create client
	client := asynq.NewClient(redisOpt)

	// Create server
	server := asynq.NewServer(redisOpt, asynq.Config{
		Concurrency: config.WorkerConcurrency,
		Queues: map[string]int{
			"critical": 6,
			"default":  3,
			"low":      1,
		},
	})

	// Create multiplexer
	mux := asynq.NewServeMux()

	return &BackgroundJobService{
		client: client,
		server: server,
		mux:    mux,
		config: config,
		logger: logger,
	}, nil
}

// EnqueueJob enqueues a background job
func (s *BackgroundJobService) EnqueueJob(ctx context.Context, taskType string, payload []byte, opts ...asynq.Option) (*asynq.TaskInfo, error) {
	task := asynq.NewTask(taskType, payload)
	return s.client.Enqueue(task, opts...)
}

// RegisterHandler registers a job handler
func (s *BackgroundJobService) RegisterHandler(pattern string, handler func(context.Context, *asynq.Task) error) {
	s.mux.HandleFunc(pattern, handler)
}

// StartJobWorker starts the background job worker
func (s *BackgroundJobService) StartJobWorker() error {
	s.logger.Info("Starting background job worker")
	return s.server.Run(s.mux)
}

// StopJobWorker stops the background job worker
func (s *BackgroundJobService) StopJobWorker() {
	s.server.Shutdown()
	s.logger.Info("Background job worker stopped")
}

// HVAC Workflow Definitions

// CustomerOnboardingWorkflow handles customer onboarding process
func CustomerOnboardingWorkflow(ctx workflow.Context, customerID string) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting customer onboarding workflow", "customerID", customerID)

	// Activity options
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 5,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	// Step 1: Send welcome email
	err := workflow.ExecuteActivity(ctx, "SendWelcomeEmail", customerID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to send welcome email: %w", err)
	}

	// Step 2: Create customer profile
	err = workflow.ExecuteActivity(ctx, "CreateCustomerProfile", customerID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to create customer profile: %w", err)
	}

	// Step 3: Schedule initial consultation
	err = workflow.ExecuteActivity(ctx, "ScheduleInitialConsultation", customerID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to schedule consultation: %w", err)
	}

	logger.Info("Customer onboarding workflow completed", "customerID", customerID)
	return nil
}

// ServiceTicketWorkflow handles service ticket processing
func ServiceTicketWorkflow(ctx workflow.Context, ticketID string) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting service ticket workflow", "ticketID", ticketID)

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 10,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute * 5,
			MaximumAttempts:    5,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	// Step 1: Assign technician
	err := workflow.ExecuteActivity(ctx, "AssignTechnician", ticketID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to assign technician: %w", err)
	}

	// Step 2: Send notification to customer
	err = workflow.ExecuteActivity(ctx, "NotifyCustomer", ticketID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to notify customer: %w", err)
	}

	// Step 3: Wait for completion signal
	var completed bool
	selector := workflow.NewSelector(ctx)

	// Wait for completion signal or timeout
	selector.AddReceive(workflow.GetSignalChannel(ctx, "ticket_completed"), func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &completed)
	})

	// Timeout after 24 hours
	selector.AddFuture(workflow.NewTimer(ctx, time.Hour*24), func(f workflow.Future) {
		logger.Warn("Service ticket workflow timed out", "ticketID", ticketID)
	})

	selector.Select(ctx)

	if completed {
		// Step 4: Send completion notification
		err = workflow.ExecuteActivity(ctx, "SendCompletionNotification", ticketID).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to send completion notification: %w", err)
		}
	}

	logger.Info("Service ticket workflow completed", "ticketID", ticketID)
	return nil
}

// WorkflowFoundation combines all workflow functionality
type WorkflowFoundation struct {
	Temporal      *TemporalService
	BackgroundJob *BackgroundJobService
	Config        *WorkflowConfig
	Logger        *zap.Logger
}

// NewWorkflowFoundation creates a complete workflow foundation
func NewWorkflowFoundation(config *WorkflowConfig, logger *zap.Logger) (*WorkflowFoundation, error) {
	if config == nil {
		config = DefaultWorkflowConfig()
	}

	temporal, err := NewTemporalService(config, logger)
	if err != nil {
		logger.Warn("Failed to create Temporal service", zap.Error(err))
		temporal = nil // Continue without Temporal if not available
	}

	backgroundJob, err := NewBackgroundJobService(config, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create background job service: %w", err)
	}

	return &WorkflowFoundation{
		Temporal:      temporal,
		BackgroundJob: backgroundJob,
		Config:        config,
		Logger:        logger,
	}, nil
}
