# 🚀 HVAC CRM - Foundations Package

## Szybkie Fundamenty dla Wszystkich Kluczowych Bibliotek

Ten pakiet zawiera podstawowe integracje wszystkich najważniejszych bibliotek Go dla rozszerzenia możliwości HVAC CRM backend.

### 📁 Struktura Foundations

```
foundations/
├── auth/           # JWT + Bcrypt Authentication
├── web/            # Gin Web Framework
├── storage/        # MinIO File Storage
├── documents/      # PDF + Image Processing
├── workflow/       # Temporal + Background Jobs
├── monitoring/     # Zap Logging + Prometheus
├── email/          # Enhanced Email Processing
├── validation/     # Input Validation
└── middleware/     # HTTP Middleware Stack
```

### 🎯 Cel

Każdy moduł zawiera:
- ✅ Podstawową konfigurację
- ✅ Przykładowe użycie
- ✅ Interfejsy do dalszego rozwoju
- ✅ Testy jednostkowe
- ✅ Dokumentację

### 🔄 Następne Kroki

1. **Implementacja podstaw** - Szybkie fundamenty (DONE)
2. **Integracja z Kratos** - Połączenie z istniejącym systemem
3. **Rozbudowa funkcjonalności** - Szczegółowe implementacje
4. **Optymalizacja** - Performance tuning

### 📋 Status Implementacji

- [x] Struktura katalogów
- [x] Auth Foundation (JWT + Bcrypt)
- [x] Web Foundation (Gin Server)
- [x] Storage Foundation (MinIO)
- [x] Documents Foundation (PDF + Images)
- [x] Workflow Foundation (Temporal + Background Jobs)
- [x] Monitoring Foundation (Zap + Prometheus)
- [x] Email Foundation (IMAP/SMTP)
- [x] Validation Foundation (go-playground/validator)
- [x] Middleware Foundation (Complete Stack)
- [x] Foundation Manager (Integration Layer)
- [x] Demo Application
- [x] Build System (Makefile)

### 🚀 Szybki Start

```bash
# 1. Pobierz dependencies
make deps

# 2. Zbuduj aplikację
make build

# 3. Uruchom demo
make run-demo

# 4. Sprawdź health check
curl http://localhost:8080/api/v1/public/health
```

### 🔧 Dostępne Endpointy

#### Publiczne (bez autoryzacji)
- `GET /api/v1/public/health` - Status systemu
- `POST /api/v1/public/auth/login` - Logowanie
- `POST /api/v1/public/auth/register` - Rejestracja

#### Chronione (wymagana autoryzacja)
- `GET /api/v1/customers` - Lista klientów
- `POST /api/v1/customers` - Tworzenie klienta
- `GET /api/v1/tickets` - Lista zgłoszeń
- `POST /api/v1/tickets` - Tworzenie zgłoszenia
- `POST /api/v1/files/upload` - Upload plików
- `POST /api/v1/emails/send` - Wysyłanie emaili

#### Admin (rola admin)
- `GET /api/v1/admin/metrics` - Metryki systemu
- `GET /api/v1/admin/users` - Zarządzanie użytkownikami

### 🛠️ Konfiguracja

Wszystkie foundations mają domyślną konfigurację, ale można ją nadpisać:

```go
config := foundations.DefaultFoundationConfig()

// Nadpisz konfigurację auth
config.Auth.JWTSecret = "your-secret-key"
config.Auth.TokenDuration = 24 * time.Hour

// Nadpisz konfigurację storage
config.Storage.Endpoint = "your-minio-endpoint"
config.Storage.AccessKeyID = "your-access-key"

manager, err := foundations.NewFoundationManager(config)
```

### 🧪 Testowanie

```bash
# Wszystkie testy
make test

# Testy konkretnego foundation
make test-auth
make test-web
make test-storage

# Coverage report
make coverage
```
