package monitoring

import (
	"fmt"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LoggerService handles structured logging with Zap
type LoggerService struct {
	logger *zap.Logger
	config *MonitoringConfig
}

// MonitoringConfig represents monitoring configuration
type MonitoringConfig struct {
	LogLevel        string `yaml:"log_level"`        // debug, info, warn, error
	LogFormat       string `yaml:"log_format"`       // json, console
	LogOutput       string `yaml:"log_output"`       // stdout, file
	LogFile         string `yaml:"log_file"`
	EnableMetrics   bool   `yaml:"enable_metrics"`
	MetricsPort     string `yaml:"metrics_port"`
	ServiceName     string `yaml:"service_name"`
	ServiceVersion  string `yaml:"service_version"`
	Environment     string `yaml:"environment"`
}

// DefaultMonitoringConfig returns default monitoring configuration
func DefaultMonitoringConfig() *MonitoringConfig {
	return &MonitoringConfig{
		LogLevel:       "info",
		LogFormat:      "json",
		LogOutput:      "stdout",
		LogFile:        "/var/log/hvac-crm/app.log",
		EnableMetrics:  true,
		MetricsPort:    "9090",
		ServiceName:    "hvac-crm-backend",
		ServiceVersion: "1.0.0",
		Environment:    "development",
	}
}

// NewLoggerService creates a new logger service instance
func NewLoggerService(config *MonitoringConfig) (*LoggerService, error) {
	if config == nil {
		config = DefaultMonitoringConfig()
	}

	// Parse log level
	level, err := zapcore.ParseLevel(config.LogLevel)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// Configure encoder
	var encoderConfig zapcore.EncoderConfig
	var encoder zapcore.Encoder

	if config.LogFormat == "json" {
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.TimeKey = "timestamp"
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// Configure output
	var writeSyncer zapcore.WriteSyncer
	if config.LogOutput == "file" {
		file, err := os.OpenFile(config.LogFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
		writeSyncer = zapcore.AddSync(file)
	} else {
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	// Create core
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// Create logger with additional fields
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel)).With(
		zap.String("service", config.ServiceName),
		zap.String("version", config.ServiceVersion),
		zap.String("environment", config.Environment),
	)

	return &LoggerService{
		logger: logger,
		config: config,
	}, nil
}

// Logger returns the underlying zap logger
func (s *LoggerService) Logger() *zap.Logger {
	return s.logger
}

// Info logs an info message
func (s *LoggerService) Info(msg string, fields ...zap.Field) {
	s.logger.Info(msg, fields...)
}

// Error logs an error message
func (s *LoggerService) Error(msg string, fields ...zap.Field) {
	s.logger.Error(msg, fields...)
}

// Warn logs a warning message
func (s *LoggerService) Warn(msg string, fields ...zap.Field) {
	s.logger.Warn(msg, fields...)
}

// Debug logs a debug message
func (s *LoggerService) Debug(msg string, fields ...zap.Field) {
	s.logger.Debug(msg, fields...)
}

// Sync flushes any buffered log entries
func (s *LoggerService) Sync() error {
	return s.logger.Sync()
}

// MetricsService handles Prometheus metrics
type MetricsService struct {
	// HTTP metrics
	HTTPRequestsTotal    *prometheus.CounterVec
	HTTPRequestDuration  *prometheus.HistogramVec
	HTTPRequestsInFlight prometheus.Gauge

	// Business metrics
	CustomersTotal       prometheus.Counter
	ServiceTicketsTotal  *prometheus.CounterVec
	EmailsProcessedTotal *prometheus.CounterVec
	WorkflowsTotal       *prometheus.CounterVec

	// System metrics
	DatabaseConnections prometheus.Gauge
	RedisConnections    prometheus.Gauge
	MemoryUsage         prometheus.Gauge
	CPUUsage            prometheus.Gauge

	config *MonitoringConfig
}

// NewMetricsService creates a new metrics service instance
func NewMetricsService(config *MonitoringConfig) *MetricsService {
	if config == nil {
		config = DefaultMonitoringConfig()
	}

	return &MetricsService{
		// HTTP metrics
		HTTPRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "endpoint", "status"},
		),
		HTTPRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint"},
		),
		HTTPRequestsInFlight: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "http_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
		),

		// Business metrics
		CustomersTotal: promauto.NewCounter(
			prometheus.CounterOpts{
				Name: "customers_total",
				Help: "Total number of customers",
			},
		),
		ServiceTicketsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "service_tickets_total",
				Help: "Total number of service tickets",
			},
			[]string{"status", "priority"},
		),
		EmailsProcessedTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "emails_processed_total",
				Help: "Total number of emails processed",
			},
			[]string{"type", "status"},
		),
		WorkflowsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "workflows_total",
				Help: "Total number of workflows executed",
			},
			[]string{"type", "status"},
		),

		// System metrics
		DatabaseConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections",
				Help: "Number of active database connections",
			},
		),
		RedisConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "redis_connections",
				Help: "Number of active Redis connections",
			},
		),
		MemoryUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
		),
		CPUUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "CPU usage percentage",
			},
		),

		config: config,
	}
}

// RecordHTTPRequest records HTTP request metrics
func (m *MetricsService) RecordHTTPRequest(method, endpoint, status string, duration time.Duration) {
	m.HTTPRequestsTotal.WithLabelValues(method, endpoint, status).Inc()
	m.HTTPRequestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
}

// IncHTTPRequestsInFlight increments in-flight HTTP requests
func (m *MetricsService) IncHTTPRequestsInFlight() {
	m.HTTPRequestsInFlight.Inc()
}

// DecHTTPRequestsInFlight decrements in-flight HTTP requests
func (m *MetricsService) DecHTTPRequestsInFlight() {
	m.HTTPRequestsInFlight.Dec()
}

// RecordCustomer records customer metrics
func (m *MetricsService) RecordCustomer() {
	m.CustomersTotal.Inc()
}

// RecordServiceTicket records service ticket metrics
func (m *MetricsService) RecordServiceTicket(status, priority string) {
	m.ServiceTicketsTotal.WithLabelValues(status, priority).Inc()
}

// RecordEmailProcessed records email processing metrics
func (m *MetricsService) RecordEmailProcessed(emailType, status string) {
	m.EmailsProcessedTotal.WithLabelValues(emailType, status).Inc()
}

// RecordWorkflow records workflow metrics
func (m *MetricsService) RecordWorkflow(workflowType, status string) {
	m.WorkflowsTotal.WithLabelValues(workflowType, status).Inc()
}

// UpdateSystemMetrics updates system metrics
func (m *MetricsService) UpdateSystemMetrics(dbConns, redisConns int, memoryBytes, cpuPercent float64) {
	m.DatabaseConnections.Set(float64(dbConns))
	m.RedisConnections.Set(float64(redisConns))
	m.MemoryUsage.Set(memoryBytes)
	m.CPUUsage.Set(cpuPercent)
}

// MonitoringFoundation combines all monitoring functionality
type MonitoringFoundation struct {
	Logger  *LoggerService
	Metrics *MetricsService
	Config  *MonitoringConfig
}

// NewMonitoringFoundation creates a complete monitoring foundation
func NewMonitoringFoundation(config *MonitoringConfig) (*MonitoringFoundation, error) {
	if config == nil {
		config = DefaultMonitoringConfig()
	}

	logger, err := NewLoggerService(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create logger service: %w", err)
	}

	metrics := NewMetricsService(config)

	return &MonitoringFoundation{
		Logger:  logger,
		Metrics: metrics,
		Config:  config,
	}, nil
}
