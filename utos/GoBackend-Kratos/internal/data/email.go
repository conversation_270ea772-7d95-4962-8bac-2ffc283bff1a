package data

import (
	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/biz"
)

// NewSimpleEmailService creates a new simple email service
func NewSimpleEmailService(c *conf.Email, logger log.Logger) *email.SimpleEmailService {
	return email.NewSimpleEmailService(c, logger)
}

// NewDatabaseEmailStore creates a new database-backed email store
func NewDatabaseEmailStore(emailUsecase *biz.EmailUsecase, logger log.Logger) email.EmailStoreInterface {
	return email.NewDatabaseEmailStore(emailUsecase, logger)
}