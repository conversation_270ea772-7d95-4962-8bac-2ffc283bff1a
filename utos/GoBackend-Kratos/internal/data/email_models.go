package data

import (
	"time"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// StringArray represents a PostgreSQL string array
type StringArray []string

// Scan implements the sql.Scanner interface
func (s *StringArray) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}

	switch v := value.(type) {
	case string:
		// Handle PostgreSQL array format: {item1,item2,item3}
		if v == "{}" || v == "" {
			*s = []string{}
			return nil
		}
		// Remove braces and split by comma
		v = strings.Trim(v, "{}")
		if v == "" {
			*s = []string{}
			return nil
		}
		*s = strings.Split(v, ",")
		return nil
	case []byte:
		return s.Scan(string(v))
	default:
		return fmt.Errorf("cannot scan %T into StringArray", value)
	}
}

// Value implements the driver.Valuer interface
func (s StringArray) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	if len(s) == 0 {
		return "{}", nil
	}
	return "{" + strings.Join(s, ",") + "}", nil
}

// JSONMap represents a JSON object stored in database
type JSONMap map[string]interface{}

// Scan implements the sql.Scanner interface
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, j)
	case string:
		return json.Unmarshal([]byte(v), j)
	default:
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}
}

// Value implements the driver.Valuer interface
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// 📧 Email represents the database model for emails
type Email struct {
	ID         int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	MessageID  string     `gorm:"uniqueIndex;not null" json:"message_id"`
	From       string     `gorm:"not null" json:"from"`
	To         StringArray `gorm:"type:text[]" json:"to"`
	CC         StringArray `gorm:"type:text[]" json:"cc"`
	BCC        StringArray `gorm:"type:text[]" json:"bcc"`
	Subject    string     `gorm:"not null" json:"subject"`
	Body       string     `gorm:"type:text" json:"body"`
	HTMLBody   string     `gorm:"type:text" json:"html_body"`
	Headers    string     `gorm:"type:text" json:"headers"`
	Priority   string     `gorm:"default:normal" json:"priority"`
	Status     string     `gorm:"default:pending" json:"status"`
	SentAt     *time.Time `json:"sent_at"`
	ReceivedAt *time.Time `json:"received_at"`
	CreatedAt  time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time  `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Analysis    *EmailAnalysis     `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"analysis,omitempty"`
	Attachments []*EmailAttachment `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"attachments,omitempty"`
}

// 🔍 EmailAnalysis represents the database model for email analysis
type EmailAnalysis struct {
	ID               int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID          int64     `gorm:"not null;index" json:"email_id"`
	SentimentScore   *float64  `gorm:"type:decimal(3,2)" json:"sentiment_score"`
	UrgencyLevel     string    `gorm:"default:low" json:"urgency_level"`
	DetectedIntent   string    `gorm:"default:other" json:"detected_intent"`
	DetectedEntities JSONMap   `gorm:"type:jsonb" json:"detected_entities"`
	KeyPhrases       StringArray `gorm:"type:text[]" json:"key_phrases"`
	LanguageCode     string    `gorm:"default:en" json:"language_code"`
	IsSpam           bool      `gorm:"default:false" json:"is_spam"`
	ConfidenceScore  *float64  `gorm:"type:decimal(3,2)" json:"confidence_score"`
	ProcessingTime   *int      `json:"processing_time"` // milliseconds
	HVACRelevance    *float64  `gorm:"type:decimal(3,2)" json:"hvac_relevance"`
	Category         string    `json:"category"`
	Priority         string    `json:"priority"`
	ActionItems      StringArray `gorm:"type:text[]" json:"action_items"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Email *Email `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
}

// 📎 EmailAttachment represents the database model for email attachments
type EmailAttachment struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID     int64     `gorm:"not null;index" json:"email_id"`
	Filename    string    `gorm:"not null" json:"filename"`
	ContentType string    `json:"content_type"`
	Size        int64     `json:"size"`
	TextContent string    `gorm:"type:text" json:"text_content"`
	IsProcessed bool      `gorm:"default:false" json:"is_processed"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Email *Email `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
}

// TableName returns the table name for Email
func (Email) TableName() string {
	return "emails"
}

// TableName returns the table name for EmailAnalysis
func (EmailAnalysis) TableName() string {
	return "email_analysis"
}

// TableName returns the table name for EmailAttachment
func (EmailAttachment) TableName() string {
	return "email_attachments"
}

// BeforeCreate hook for Email
func (e *Email) BeforeCreate(tx *gorm.DB) error {
	if e.Priority == "" {
		e.Priority = "normal"
	}
	if e.Status == "" {
		e.Status = "pending"
	}
	return nil
}

// BeforeCreate hook for EmailAnalysis
func (ea *EmailAnalysis) BeforeCreate(tx *gorm.DB) error {
	if ea.UrgencyLevel == "" {
		ea.UrgencyLevel = "low"
	}
	if ea.DetectedIntent == "" {
		ea.DetectedIntent = "other"
	}
	if ea.LanguageCode == "" {
		ea.LanguageCode = "en"
	}
	return nil
}

// GetSentiment returns sentiment as string based on sentiment score
func (ea *EmailAnalysis) GetSentiment() string {
	if ea.SentimentScore == nil {
		return "neutral"
	}
	
	score := *ea.SentimentScore
	if score > 0.1 {
		return "positive"
	} else if score < -0.1 {
		return "negative"
	}
	return "neutral"
}

// IsHighPriority checks if email analysis indicates high priority
func (ea *EmailAnalysis) IsHighPriority() bool {
	return ea.UrgencyLevel == "high" || ea.UrgencyLevel == "critical" ||
		   ea.Priority == "high" || ea.Priority == "urgent"
}

// IsHVACRelevant checks if email is HVAC relevant
func (ea *EmailAnalysis) IsHVACRelevant() bool {
	if ea.HVACRelevance == nil {
		return false
	}
	return *ea.HVACRelevance > 0.5
}

// GetProcessingTimeMs returns processing time in milliseconds
func (ea *EmailAnalysis) GetProcessingTimeMs() int {
	if ea.ProcessingTime == nil {
		return 0
	}
	return *ea.ProcessingTime
}

// HasAttachments checks if email has attachments
func (e *Email) HasAttachments() bool {
	return len(e.Attachments) > 0
}

// GetAttachmentCount returns number of attachments
func (e *Email) GetAttachmentCount() int {
	return len(e.Attachments)
}

// GetProcessedAttachmentCount returns number of processed attachments
func (e *Email) GetProcessedAttachmentCount() int {
	count := 0
	for _, attachment := range e.Attachments {
		if attachment.IsProcessed {
			count++
		}
	}
	return count
}

// IsRecent checks if email was received in the last 24 hours
func (e *Email) IsRecent() bool {
	if e.ReceivedAt == nil {
		return time.Since(e.CreatedAt) < 24*time.Hour
	}
	return time.Since(*e.ReceivedAt) < 24*time.Hour
}

// GetDisplayFrom returns a clean display version of the from address
func (e *Email) GetDisplayFrom() string {
	// Extract name from "Name <<EMAIL>>" format
	if strings.Contains(e.From, "<") && strings.Contains(e.From, ">") {
		parts := strings.Split(e.From, "<")
		if len(parts) > 0 {
			name := strings.TrimSpace(parts[0])
			if name != "" {
				return name
			}
		}
	}
	return e.From
}

// GetShortSubject returns a truncated version of the subject
func (e *Email) GetShortSubject(maxLength int) string {
	if len(e.Subject) <= maxLength {
		return e.Subject
	}
	return e.Subject[:maxLength-3] + "..."
}
