package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/common"
)

// 🚀 Enhanced Customer Repository with Go 1.23 Generics
// Type-safe, high-performance customer data operations

// ==========================================
// ENHANCED CUSTOMER MODEL
// ==========================================

// EnhancedCustomer implements HVACEntity interface with advanced features
type EnhancedCustomer struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id" validate:"omitempty,min=1"`
	Name      string    `gorm:"not null" json:"name" validate:"required,min=2,max=100"`
	Email     string    `gorm:"uniqueIndex;not null" json:"email" validate:"required,email"`
	Phone     string    `json:"phone" validate:"omitempty,min=10,max=20"`
	Address   string    `json:"address" validate:"omitempty,max=500"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// HVAC-specific fields
	PreferredTechnician string                 `json:"preferred_technician"`
	ServiceHistory      []string               `gorm:"type:text[]" json:"service_history"`
	Metadata            map[string]interface{} `gorm:"type:jsonb" json:"metadata"`
	IsActive            bool                   `gorm:"default:true" json:"is_active"`
	Priority            string                 `gorm:"default:'normal'" json:"priority" validate:"oneof=low normal high urgent"`
}

// Implement HVACEntity interface
func (c *EnhancedCustomer) GetID() int64 {
	return c.ID
}

func (c *EnhancedCustomer) SetID(id int64) {
	c.ID = id
}

func (c *EnhancedCustomer) GetCreatedAt() time.Time {
	return c.CreatedAt
}

func (c *EnhancedCustomer) GetUpdatedAt() time.Time {
	return c.UpdatedAt
}

func (c *EnhancedCustomer) Validate() error {
	if c.Name == "" {
		return fmt.Errorf("customer name is required")
	}
	if c.Email == "" {
		return fmt.Errorf("customer email is required")
	}
	if !strings.Contains(c.Email, "@") {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

func (c *EnhancedCustomer) TableName() string {
	return "customers"
}

// Implement Searchable interface
func (c *EnhancedCustomer) SearchFields() []string {
	return []string{"name", "email", "phone", "address"}
}

func (c *EnhancedCustomer) MatchesSearch(query string) bool {
	query = strings.ToLower(query)
	return strings.Contains(strings.ToLower(c.Name), query) ||
		strings.Contains(strings.ToLower(c.Email), query) ||
		strings.Contains(strings.ToLower(c.Phone), query) ||
		strings.Contains(strings.ToLower(c.Address), query)
}

// Implement Cacheable interface
func (c *EnhancedCustomer) CacheKey() string {
	return fmt.Sprintf("customer:%d", c.ID)
}

func (c *EnhancedCustomer) CacheTTL() time.Duration {
	return 10 * time.Minute
}

// ==========================================
// ENHANCED CUSTOMER REPOSITORY
// ==========================================

// EnhancedCustomerRepo implements the generic Repository interface
type EnhancedCustomerRepo struct {
	data      *Data
	log       *log.Helper
	validator *validator.Validate
}

// NewEnhancedCustomerRepo creates a new enhanced customer repository
func NewEnhancedCustomerRepo(data *Data, logger log.Logger) common.Repository[*EnhancedCustomer] {
	return &EnhancedCustomerRepo{
		data:      data,
		log:       log.NewHelper(logger),
		validator: validator.New(),
	}
}

// Create creates a new customer
func (r *EnhancedCustomerRepo) Create(ctx context.Context, customer *EnhancedCustomer) (*EnhancedCustomer, error) {
	if err := customer.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	if err := r.validator.Struct(customer); err != nil {
		return nil, fmt.Errorf("struct validation failed: %w", err)
	}

	if err := r.data.db.WithContext(ctx).Create(customer).Error; err != nil {
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	r.log.WithContext(ctx).Infof("Created customer: %d", customer.ID)
	return customer, nil
}

// GetByID retrieves a customer by ID
func (r *EnhancedCustomerRepo) GetByID(ctx context.Context, id int64) (*EnhancedCustomer, error) {
	var customer EnhancedCustomer

	if err := r.data.db.WithContext(ctx).First(&customer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("customer not found: %d", id)
		}
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}

	return &customer, nil
}

// Update updates a customer
func (r *EnhancedCustomerRepo) Update(ctx context.Context, customer *EnhancedCustomer) (*EnhancedCustomer, error) {
	if err := customer.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	if err := r.validator.Struct(customer); err != nil {
		return nil, fmt.Errorf("struct validation failed: %w", err)
	}

	if err := r.data.db.WithContext(ctx).Save(customer).Error; err != nil {
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	r.log.WithContext(ctx).Infof("Updated customer: %d", customer.ID)
	return customer, nil
}

// Delete deletes a customer
func (r *EnhancedCustomerRepo) Delete(ctx context.Context, id int64) error {
	result := r.data.db.WithContext(ctx).Delete(&EnhancedCustomer{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete customer: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("customer not found: %d", id)
	}

	r.log.WithContext(ctx).Infof("Deleted customer: %d", id)
	return nil
}

// List retrieves customers with pagination and filtering
func (r *EnhancedCustomerRepo) List(ctx context.Context, criteria common.SearchCriteria[*EnhancedCustomer]) (*common.SearchResult[*EnhancedCustomer], error) {
	var customers []*EnhancedCustomer
	var total int64

	query := r.data.db.WithContext(ctx).Model(&EnhancedCustomer{})

	// Apply filters
	if criteria.Query != "" {
		query = query.Where("name ILIKE ? OR email ILIKE ? OR phone ILIKE ?",
			"%"+criteria.Query+"%", "%"+criteria.Query+"%", "%"+criteria.Query+"%")
	}

	// Apply custom filters
	for field, value := range criteria.Filters {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	// Apply date range filter
	if criteria.DateRange != nil {
		query = query.Where("created_at BETWEEN ? AND ?", criteria.DateRange.Start, criteria.DateRange.End)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count customers: %w", err)
	}

	// Apply sorting
	if criteria.SortBy != "" {
		order := "ASC"
		if criteria.SortOrder == "desc" {
			order = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", criteria.SortBy, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if criteria.PageSize > 0 {
		offset := (criteria.Page - 1) * criteria.PageSize
		query = query.Offset(offset).Limit(criteria.PageSize)
	}

	// Execute query
	if err := query.Find(&customers).Error; err != nil {
		return nil, fmt.Errorf("failed to list customers: %w", err)
	}

	totalPages := int(total) / criteria.PageSize
	if int(total)%criteria.PageSize > 0 {
		totalPages++
	}

	return &common.SearchResult[*EnhancedCustomer]{
		Items:      customers,
		Total:      total,
		Page:       criteria.Page,
		PageSize:   criteria.PageSize,
		TotalPages: totalPages,
	}, nil
}

// Search performs advanced search operations
func (r *EnhancedCustomerRepo) Search(ctx context.Context, criteria common.SearchCriteria[*EnhancedCustomer]) (*common.SearchResult[*EnhancedCustomer], error) {
	// For now, use the same implementation as List
	// In a real implementation, this could use full-text search, Elasticsearch, etc.
	return r.List(ctx, criteria)
}

// Count returns the total number of customers matching the filters
func (r *EnhancedCustomerRepo) Count(ctx context.Context, filters map[string]interface{}) (int64, error) {
	var count int64
	query := r.data.db.WithContext(ctx).Model(&EnhancedCustomer{})

	for field, value := range filters {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count customers: %w", err)
	}

	return count, nil
}

// CreateBatch creates multiple customers in a single transaction
func (r *EnhancedCustomerRepo) CreateBatch(ctx context.Context, customers []*EnhancedCustomer) ([]*EnhancedCustomer, error) {
	if len(customers) == 0 {
		return customers, nil
	}

	// Validate all customers first
	for i, customer := range customers {
		if err := customer.Validate(); err != nil {
			return nil, fmt.Errorf("validation failed for customer %d: %w", i, err)
		}
		if err := r.validator.Struct(customer); err != nil {
			return nil, fmt.Errorf("struct validation failed for customer %d: %w", i, err)
		}
	}

	// Create in transaction
	err := r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&customers).Error; err != nil {
			return fmt.Errorf("failed to create customers batch: %w", err)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	r.log.WithContext(ctx).Infof("Created %d customers in batch", len(customers))
	return customers, nil
}

// UpdateBatch updates multiple customers in a single transaction
func (r *EnhancedCustomerRepo) UpdateBatch(ctx context.Context, customers []*EnhancedCustomer) ([]*EnhancedCustomer, error) {
	if len(customers) == 0 {
		return customers, nil
	}

	// Validate all customers first
	for i, customer := range customers {
		if err := customer.Validate(); err != nil {
			return nil, fmt.Errorf("validation failed for customer %d: %w", i, err)
		}
		if err := r.validator.Struct(customer); err != nil {
			return nil, fmt.Errorf("struct validation failed for customer %d: %w", i, err)
		}
	}

	// Update in transaction
	err := r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, customer := range customers {
			if err := tx.Save(customer).Error; err != nil {
				return fmt.Errorf("failed to update customer %d: %w", customer.ID, err)
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	r.log.WithContext(ctx).Infof("Updated %d customers in batch", len(customers))
	return customers, nil
}

// DeleteBatch deletes multiple customers by IDs
func (r *EnhancedCustomerRepo) DeleteBatch(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	result := r.data.db.WithContext(ctx).Delete(&EnhancedCustomer{}, ids)
	if result.Error != nil {
		return fmt.Errorf("failed to delete customers batch: %w", result.Error)
	}

	r.log.WithContext(ctx).Infof("Deleted %d customers in batch", result.RowsAffected)
	return nil
}

// Exists checks if a customer exists by ID
func (r *EnhancedCustomerRepo) Exists(ctx context.Context, id int64) (bool, error) {
	var count int64
	err := r.data.db.WithContext(ctx).Model(&EnhancedCustomer{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check customer existence: %w", err)
	}
	return count > 0, nil
}

// ExistsByField checks if a customer exists by a specific field
func (r *EnhancedCustomerRepo) ExistsByField(ctx context.Context, field string, value interface{}) (bool, error) {
	var count int64
	err := r.data.db.WithContext(ctx).Model(&EnhancedCustomer{}).Where(fmt.Sprintf("%s = ?", field), value).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check customer existence by %s: %w", field, err)
	}
	return count > 0, nil
}
