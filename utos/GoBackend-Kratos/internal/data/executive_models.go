package data

import (
	"time"
	"database/sql/driver"
	"fmt"
	"gorm.io/gorm"
)

// 🧠 Executive AI Assistant Data Models
// Comprehensive models for AI-powered email automation and management

// ==========================================
// EMAIL TRIAGE MODELS
// ==========================================

// EmailTriage represents AI-powered email classification and triage decisions
type EmailTriage struct {
	ID       int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID  int64 `gorm:"not null;uniqueIndex" json:"email_id"`

	// Triage Decision
	TriageAction   TriageAction `gorm:"type:varchar(20);not null" json:"triage_action"`
	TriageReason   string       `gorm:"type:text;not null" json:"triage_reason"`
	ConfidenceScore float64     `gorm:"type:decimal(3,2);not null" json:"confidence_score"`

	// Priority Classification
	PriorityLevel PriorityLevel `gorm:"type:varchar(20);not null;default:normal" json:"priority_level"`
	UrgencyScore  *float64      `gorm:"type:decimal(3,2)" json:"urgency_score"`

	// Category Classification
	EmailCategory string  `gorm:"type:varchar(50);not null;default:general" json:"email_category"`
	Subcategory   *string `gorm:"type:varchar(100)" json:"subcategory"`

	// HVAC-Specific Classification
	HVACServiceType *string `gorm:"type:varchar(100)" json:"hvac_service_type"`
	EquipmentType   *string `gorm:"type:varchar(100)" json:"equipment_type"`
	CustomerType    *string `gorm:"type:varchar(50)" json:"customer_type"`

	// AI Analysis Results
	DetectedIntent   *string         `gorm:"type:varchar(200)" json:"detected_intent"`
	DetectedEntities JSONMap         `gorm:"type:jsonb;default:'{}'" json:"detected_entities"`
	KeyPhrases       StringArray     `gorm:"type:text[]" json:"key_phrases"`
	Sentiment        *SentimentType  `gorm:"type:varchar(20)" json:"sentiment"`
	SentimentScore   *float64        `gorm:"type:decimal(3,2)" json:"sentiment_score"`

	// Processing Metadata
	AIModelUsed      string `gorm:"type:varchar(100);not null;default:'gemma-3-4b'" json:"ai_model_used"`
	ProcessingTimeMs *int   `json:"processing_time_ms"`
	ProcessedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"processed_at"`

	// Audit Trail
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Email *Email `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
}

// ==========================================
// EMAIL RESPONSE DRAFTS MODELS
// ==========================================

// EmailResponseDraft represents AI-generated email response drafts
type EmailResponseDraft struct {
	ID        int64  `gorm:"primaryKey;autoIncrement" json:"id"`
	EmailID   int64  `gorm:"not null" json:"email_id"`
	TriageID  *int64 `json:"triage_id"`

	// Draft Content
	DraftSubject  *string `gorm:"type:text" json:"draft_subject"`
	DraftBody     string  `gorm:"type:text;not null" json:"draft_body"`
	DraftHTMLBody *string `gorm:"type:text" json:"draft_html_body"`

	// Response Type
	ResponseType ResponseType `gorm:"type:varchar(50);not null" json:"response_type"`
	ResponseTone ResponseTone `gorm:"type:varchar(30);default:professional" json:"response_tone"`

	// Recipients
	ToAddresses  StringArray `gorm:"type:text[];not null" json:"to_addresses"`
	CCAddresses  StringArray `gorm:"type:text[]" json:"cc_addresses"`
	BCCAddresses StringArray `gorm:"type:text[]" json:"bcc_addresses"`

	// AI Generation Details
	AIModelUsed        string  `gorm:"type:varchar(100);not null;default:'gemma-3-4b'" json:"ai_model_used"`
	GenerationPrompt   *string `gorm:"type:text" json:"generation_prompt"`
	GenerationContext  JSONMap `gorm:"type:jsonb;default:'{}'" json:"generation_context"`

	// Quality Metrics
	QualityScore    *float64 `gorm:"type:decimal(3,2)" json:"quality_score"`
	RelevanceScore  *float64 `gorm:"type:decimal(3,2)" json:"relevance_score"`
	ToneMatchScore  *float64 `gorm:"type:decimal(3,2)" json:"tone_match_score"`

	// Status and Approval
	Status           DraftStatus `gorm:"type:varchar(30);not null;default:draft" json:"status"`
	ApprovedBy       *string     `gorm:"type:varchar(100)" json:"approved_by"`
	ApprovedAt       *time.Time  `json:"approved_at"`
	RejectionReason  *string     `gorm:"type:text" json:"rejection_reason"`

	// Sending Details
	SentAt          *time.Time     `json:"sent_at"`
	SentMessageID   *string        `gorm:"type:varchar(255)" json:"sent_message_id"`
	DeliveryStatus  *DeliveryStatus `gorm:"type:varchar(30)" json:"delivery_status"`

	// Audit Trail
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Email  *Email       `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
	Triage *EmailTriage `gorm:"foreignKey:TriageID;constraint:OnDelete:CASCADE" json:"triage,omitempty"`
}

// ==========================================
// EXECUTIVE MEMORY BANK MODELS
// ==========================================

// ExecutiveMemoryBank represents long-term context and learning for AI
type ExecutiveMemoryBank struct {
	ID int64 `gorm:"primaryKey;autoIncrement" json:"id"`

	// Memory Classification
	MemoryType     MemoryType `gorm:"type:varchar(50);not null" json:"memory_type"`
	MemoryCategory string     `gorm:"type:varchar(100);not null" json:"memory_category"`

	// Memory Content
	Title   string  `gorm:"type:varchar(200);not null" json:"title"`
	Content string  `gorm:"type:text;not null" json:"content"`
	Summary *string `gorm:"type:text" json:"summary"`

	// Context and References
	EntityType *string `gorm:"type:varchar(50)" json:"entity_type"`
	EntityID   *string `gorm:"type:varchar(100)" json:"entity_id"`
	EntityName *string `gorm:"type:varchar(200)" json:"entity_name"`

	// Source Information
	SourceType      SourceType `gorm:"type:varchar(50);not null" json:"source_type"`
	SourceID        *int64     `json:"source_id"`
	SourceReference *string    `gorm:"type:text" json:"source_reference"`

	// AI Analysis
	ExtractedEntities JSONMap      `gorm:"type:jsonb;default:'{}'" json:"extracted_entities"`
	KeyConcepts       StringArray  `gorm:"type:text[]" json:"key_concepts"`
	RelatedMemories   IntArray     `gorm:"type:bigint[]" json:"related_memories"`

	// Quality and Verification
	ConfidenceScore float64    `gorm:"type:decimal(3,2);not null" json:"confidence_score"`
	IsVerified      bool       `gorm:"default:false" json:"is_verified"`
	VerifiedBy      *string    `gorm:"type:varchar(100)" json:"verified_by"`
	VerifiedAt      *time.Time `json:"verified_at"`

	// Usage Tracking
	AccessCount      int       `gorm:"default:0" json:"access_count"`
	LastAccessedAt   *time.Time `json:"last_accessed_at"`
	RelevanceScore   float64   `gorm:"type:decimal(3,2);default:0.5" json:"relevance_score"`

	// Lifecycle Management
	ExpiresAt *time.Time `json:"expires_at"`
	IsActive  bool       `gorm:"default:true" json:"is_active"`

	// Audit Trail
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// ==========================================
// WORKFLOW AUTOMATION MODELS
// ==========================================

// EmailWorkflowRule represents automated action rules for email processing
type EmailWorkflowRule struct {
	ID          int64  `gorm:"primaryKey;autoIncrement" json:"id"`
	RuleName    string `gorm:"type:varchar(200);not null;uniqueIndex" json:"rule_name"`
	Description *string `gorm:"type:text" json:"description"`
	IsActive    bool   `gorm:"default:true" json:"is_active"`

	// Trigger Conditions
	TriggerConditions JSONMap `gorm:"type:jsonb;not null" json:"trigger_conditions"`

	// Actions to Execute
	Actions JSONMap `gorm:"type:jsonb;not null" json:"actions"`

	// Priority and Execution
	Priority       int `gorm:"default:100" json:"priority"`
	ExecutionOrder int `gorm:"default:1" json:"execution_order"`

	// Performance Metrics
	ExecutionCount   int        `gorm:"default:0" json:"execution_count"`
	SuccessCount     int        `gorm:"default:0" json:"success_count"`
	FailureCount     int        `gorm:"default:0" json:"failure_count"`
	LastExecutedAt   *time.Time `json:"last_executed_at"`

	// Audit Trail
	CreatedBy string    `gorm:"type:varchar(100);not null" json:"created_by"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Executions []*EmailWorkflowExecution `gorm:"foreignKey:RuleID;constraint:OnDelete:CASCADE" json:"executions,omitempty"`
}

// EmailWorkflowExecution represents workflow execution history
type EmailWorkflowExecution struct {
	ID      int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	RuleID  int64 `gorm:"not null" json:"rule_id"`
	EmailID int64 `gorm:"not null" json:"email_id"`

	// Execution Details
	ExecutionStatus ExecutionStatus `gorm:"type:varchar(30);not null" json:"execution_status"`
	ExecutionResult JSONMap         `gorm:"type:jsonb;default:'{}'" json:"execution_result"`

	// Actions Executed
	ActionsExecuted JSONMap `gorm:"type:jsonb;default:'[]'" json:"actions_executed"`
	ActionsFailed   JSONMap `gorm:"type:jsonb;default:'[]'" json:"actions_failed"`

	// Performance
	StartedAt        time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	ExecutionTimeMs  *int       `json:"execution_time_ms"`

	// Error Handling
	ErrorMessage *string `gorm:"type:text" json:"error_message"`
	ErrorDetails JSONMap `gorm:"type:jsonb" json:"error_details"`
	RetryCount   int     `gorm:"default:0" json:"retry_count"`

	// Audit Trail
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`

	// Relationships
	Rule  *EmailWorkflowRule `gorm:"foreignKey:RuleID;constraint:OnDelete:CASCADE" json:"rule,omitempty"`
	Email *Email             `gorm:"foreignKey:EmailID;constraint:OnDelete:CASCADE" json:"email,omitempty"`
}

// ==========================================
// CALENDAR INTEGRATION MODELS
// ==========================================

// CalendarEvent represents meeting scheduling and management
type CalendarEvent struct {
	ID      int64   `gorm:"primaryKey;autoIncrement" json:"id"`
	EventID *string `gorm:"type:varchar(255);uniqueIndex" json:"event_id"`

	// Event Details
	Title       string  `gorm:"type:varchar(500);not null" json:"title"`
	Description *string `gorm:"type:text" json:"description"`
	Location    *string `gorm:"type:varchar(500)" json:"location"`

	// Timing
	StartTime time.Time `gorm:"not null" json:"start_time"`
	EndTime   time.Time `gorm:"not null" json:"end_time"`
	Timezone  string    `gorm:"type:varchar(100);default:'UTC'" json:"timezone"`
	IsAllDay  bool      `gorm:"default:false" json:"is_all_day"`

	// Participants
	OrganizerEmail string  `gorm:"type:varchar(255);not null" json:"organizer_email"`
	Attendees      JSONMap `gorm:"type:jsonb;default:'[]'" json:"attendees"`

	// Meeting Type
	MeetingType MeetingType `gorm:"type:varchar(50);default:general" json:"meeting_type"`
	CustomerID  *int64      `json:"customer_id"`
	JobID       *int64      `json:"job_id"`

	// Status
	Status EventStatus `gorm:"type:varchar(30);default:confirmed" json:"status"`

	// AI Integration
	CreatedByAI   bool     `gorm:"default:false" json:"created_by_ai"`
	AIConfidence  *float64 `gorm:"type:decimal(3,2)" json:"ai_confidence"`
	AIReasoning   *string  `gorm:"type:text" json:"ai_reasoning"`

	// Source Information
	SourceEmailID *int64     `json:"source_email_id"`
	SourceType    SourceType `gorm:"type:varchar(50);default:manual" json:"source_type"`

	// Audit Trail
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	SourceEmail *Email `gorm:"foreignKey:SourceEmailID;constraint:OnDelete:SET NULL" json:"source_email,omitempty"`
}

// ==========================================
// PERFORMANCE ANALYTICS MODELS
// ==========================================

// ExecutiveAIMetrics represents performance metrics and analytics
type ExecutiveAIMetrics struct {
	ID int64 `gorm:"primaryKey;autoIncrement" json:"id"`

	// Metric Details
	MetricName     string  `gorm:"type:varchar(100);not null" json:"metric_name"`
	MetricCategory string  `gorm:"type:varchar(50);not null" json:"metric_category"`
	MetricValue    float64 `gorm:"type:decimal(10,4);not null" json:"metric_value"`
	MetricUnit     *string `gorm:"type:varchar(20)" json:"metric_unit"`

	// Context
	ContextData JSONMap `gorm:"type:jsonb;default:'{}'" json:"context_data"`

	// Time Period
	PeriodStart time.Time `gorm:"not null" json:"period_start"`
	PeriodEnd   time.Time `gorm:"not null" json:"period_end"`

	// Audit Trail
	RecordedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"recorded_at"`
}

// ==========================================
// ENUMS AND TYPES
// ==========================================

type TriageAction string
const (
	TriageActionRespond  TriageAction = "respond"
	TriageActionNotify   TriageAction = "notify"
	TriageActionIgnore   TriageAction = "ignore"
	TriageActionEscalate TriageAction = "escalate"
)

type PriorityLevel string
const (
	PriorityUrgent PriorityLevel = "urgent"
	PriorityHigh   PriorityLevel = "high"
	PriorityNormal PriorityLevel = "normal"
	PriorityLow    PriorityLevel = "low"
)

type SentimentType string
const (
	SentimentPositive SentimentType = "positive"
	SentimentNeutral  SentimentType = "neutral"
	SentimentNegative SentimentType = "negative"
	SentimentAngry    SentimentType = "angry"
)

type ResponseType string
const (
	ResponseTypeReply      ResponseType = "reply"
	ResponseTypeForward    ResponseType = "forward"
	ResponseTypeNewThread  ResponseType = "new_thread"
)

type ResponseTone string
const (
	ResponseToneProfessional ResponseTone = "professional"
	ResponseToneFriendly     ResponseTone = "friendly"
	ResponseToneFormal       ResponseTone = "formal"
	ResponseToneCasual       ResponseTone = "casual"
	ResponseToneUrgent       ResponseTone = "urgent"
)

type DraftStatus string
const (
	DraftStatusDraft         DraftStatus = "draft"
	DraftStatusPendingReview DraftStatus = "pending_review"
	DraftStatusApproved      DraftStatus = "approved"
	DraftStatusRejected      DraftStatus = "rejected"
	DraftStatusSent          DraftStatus = "sent"
	DraftStatusFailed        DraftStatus = "failed"
)

type DeliveryStatus string
const (
	DeliveryStatusPending   DeliveryStatus = "pending"
	DeliveryStatusSent      DeliveryStatus = "sent"
	DeliveryStatusDelivered DeliveryStatus = "delivered"
	DeliveryStatusFailed    DeliveryStatus = "failed"
	DeliveryStatusBounced   DeliveryStatus = "bounced"
)

type MemoryType string
const (
	MemoryTypeCustomerPreference    MemoryType = "customer_preference"
	MemoryTypeCommunicationPattern  MemoryType = "communication_pattern"
	MemoryTypeBusinessRule          MemoryType = "business_rule"
	MemoryTypeContactInfo           MemoryType = "contact_info"
	MemoryTypeRecurringIssue        MemoryType = "recurring_issue"
	MemoryTypeImportantDetail       MemoryType = "important_detail"
)

type SourceType string
const (
	SourceTypeEmail    SourceType = "email"
	SourceTypeCall     SourceType = "call"
	SourceTypeMeeting  SourceType = "meeting"
	SourceTypeDocument SourceType = "document"
	SourceTypeManual   SourceType = "manual"
)

type ExecutionStatus string
const (
	ExecutionStatusPending   ExecutionStatus = "pending"
	ExecutionStatusRunning   ExecutionStatus = "running"
	ExecutionStatusCompleted ExecutionStatus = "completed"
	ExecutionStatusFailed    ExecutionStatus = "failed"
	ExecutionStatusCancelled ExecutionStatus = "cancelled"
)

type MeetingType string
const (
	MeetingTypeGeneral          MeetingType = "general"
	MeetingTypeHVACConsultation MeetingType = "hvac_consultation"
	MeetingTypeSiteVisit        MeetingType = "site_visit"
	MeetingTypeFollowUp         MeetingType = "follow_up"
	MeetingTypeEmergency        MeetingType = "emergency"
)

type EventStatus string
const (
	EventStatusTentative EventStatus = "tentative"
	EventStatusConfirmed EventStatus = "confirmed"
	EventStatusCancelled EventStatus = "cancelled"
)

// ==========================================
// CUSTOM TYPES FOR ARRAYS
// ==========================================

// IntArray represents an array of integers for PostgreSQL
type IntArray []int64

func (a IntArray) Value() (driver.Value, error) {
	if len(a) == 0 {
		return "{}", nil
	}

	result := "{"
	for i, v := range a {
		if i > 0 {
			result += ","
		}
		result += fmt.Sprintf("%d", v)
	}
	result += "}"
	return result, nil
}

func (a *IntArray) Scan(value interface{}) error {
	if value == nil {
		*a = IntArray{}
		return nil
	}

	switch v := value.(type) {
	case string:
		// Parse PostgreSQL array format
		if v == "{}" {
			*a = IntArray{}
			return nil
		}
		// Simple parsing - in production, use a proper parser
		*a = IntArray{} // Simplified for now
		return nil
	default:
		return fmt.Errorf("cannot scan %T into IntArray", value)
	}
}

// ==========================================
// TABLE NAMES
// ==========================================

func (EmailTriage) TableName() string {
	return "email_triage"
}

func (EmailResponseDraft) TableName() string {
	return "email_response_drafts"
}

func (ExecutiveMemoryBank) TableName() string {
	return "executive_memory_bank"
}

func (EmailWorkflowRule) TableName() string {
	return "email_workflow_rules"
}

func (EmailWorkflowExecution) TableName() string {
	return "email_workflow_executions"
}

func (CalendarEvent) TableName() string {
	return "calendar_events"
}

func (ExecutiveAIMetrics) TableName() string {
	return "executive_ai_metrics"
}

// ==========================================
// GORM HOOKS
// ==========================================

func (e *EmailTriage) BeforeCreate(tx *gorm.DB) error {
	if e.ProcessedAt.IsZero() {
		e.ProcessedAt = time.Now()
	}
	return nil
}

func (d *EmailResponseDraft) BeforeCreate(tx *gorm.DB) error {
	if d.Status == "" {
		d.Status = DraftStatusDraft
	}
	return nil
}

func (m *ExecutiveMemoryBank) BeforeCreate(tx *gorm.DB) error {
	if m.RelevanceScore == 0 {
		m.RelevanceScore = 0.5
	}
	return nil
}

func (r *EmailWorkflowRule) BeforeCreate(tx *gorm.DB) error {
	if r.Priority == 0 {
		r.Priority = 100
	}
	if r.ExecutionOrder == 0 {
		r.ExecutionOrder = 1
	}
	return nil
}

func (e *CalendarEvent) BeforeCreate(tx *gorm.DB) error {
	if e.Timezone == "" {
		e.Timezone = "UTC"
	}
	if e.Status == "" {
		e.Status = EventStatusConfirmed
	}
	return nil
}