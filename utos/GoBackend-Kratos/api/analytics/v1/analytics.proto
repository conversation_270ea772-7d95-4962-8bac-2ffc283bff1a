syntax = "proto3";

package api.analytics.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "gobackend-hvac-kratos/api/analytics/v1;v1";

// 📊 Analytics Service - Advanced Dashboard Analytics API
service AnalyticsService {
  // Get executive dashboard data
  rpc GetExecutiveDashboard(GetExecutiveDashboardRequest) returns (GetExecutiveDashboardResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/dashboard/executive"
    };
  }

  // Get customer insights dashboard
  rpc GetCustomerInsightsDashboard(GetCustomerInsightsDashboardRequest) returns (GetCustomerInsightsDashboardResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/dashboard/customer"
    };
  }

  // Get operational dashboard
  rpc GetOperationalDashboard(GetOperationalDashboardRequest) returns (GetOperationalDashboardResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/dashboard/operations"
    };
  }

  // Get performance trends
  rpc GetPerformanceTrends(GetPerformanceTrendsRequest) returns (GetPerformanceTrendsResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/trends/performance"
    };
  }

  // Get KPIs
  rpc GetKPIs(GetKPIsRequest) returns (GetKPIsResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/kpis"
    };
  }

  // Update KPI
  rpc UpdateKPI(UpdateKPIRequest) returns (UpdateKPIResponse) {
    option (google.api.http) = {
      post: "/api/v1/analytics/kpis"
      body: "*"
    };
  }

  // Get real-time metrics
  rpc GetRealTimeMetrics(GetRealTimeMetricsRequest) returns (GetRealTimeMetricsResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/metrics/realtime"
    };
  }

  // Get dashboard widgets
  rpc GetDashboardWidgets(GetDashboardWidgetsRequest) returns (GetDashboardWidgetsResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/widgets"
    };
  }

  // Create dashboard widget
  rpc CreateDashboardWidget(CreateDashboardWidgetRequest) returns (CreateDashboardWidgetResponse) {
    option (google.api.http) = {
      post: "/api/v1/analytics/widgets"
      body: "*"
    };
  }

  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse) {
    option (google.api.http) = {
      get: "/api/v1/analytics/health"
    };
  }
}

// ============================================================================
// 📊 REQUEST/RESPONSE MESSAGES
// ============================================================================

// Executive Dashboard
message GetExecutiveDashboardRequest {}

message GetExecutiveDashboardResponse {
  string dashboard_type = 1;
  google.protobuf.Timestamp last_updated = 2;
  ExecutiveDashboardSummary data = 3;
  repeated DashboardWidget widgets = 4;
}

message ExecutiveDashboardSummary {
  string period = 1;
  double today_revenue = 2;
  int32 today_jobs = 3;
  double today_satisfaction = 4;
  double today_efficiency = 5;
  double week_revenue = 6;
  int32 week_jobs = 7;
  double month_revenue = 8;
  int32 month_jobs = 9;
}

// Customer Insights Dashboard
message GetCustomerInsightsDashboardRequest {}

message GetCustomerInsightsDashboardResponse {
  string dashboard_type = 1;
  google.protobuf.Timestamp last_updated = 2;
  repeated CustomerInsightsDashboard data = 3;
  repeated DashboardWidget widgets = 4;
}

message CustomerInsightsDashboard {
  string loyalty_tier = 1;
  int32 customer_count = 2;
  double avg_lifetime_value = 3;
  double avg_satisfaction = 4;
  double avg_churn_risk = 5;
  double tier_total_revenue = 6;
}

// Operational Dashboard
message GetOperationalDashboardRequest {}

message GetOperationalDashboardResponse {
  string dashboard_type = 1;
  google.protobuf.Timestamp last_updated = 2;
  OperationalAnalytics data = 3;
  repeated DashboardWidget widgets = 4;
}

message OperationalAnalytics {
  uint32 id = 1;
  google.protobuf.Timestamp analysis_date = 2;
  int32 total_active_jobs = 3;
  int32 completed_jobs = 4;
  int32 cancelled_jobs = 5;
  int32 emergency_jobs = 6;
  int64 average_response_time_ms = 7;
  int64 average_completion_time_ms = 8;
  double technician_efficiency = 9;
  double equipment_utilization = 10;
  double customer_satisfaction = 11;
  double first_time_fix_rate = 12;
  double callback_rate = 13;
  double parts_availability = 14;
  double fuel_costs = 15;
  double overtime_hours = 16;
}

// Performance Trends
message GetPerformanceTrendsRequest {
  int32 weeks = 1; // Number of weeks to fetch
}

message GetPerformanceTrendsResponse {
  repeated PerformanceTrendsDashboard trends = 1;
}

message PerformanceTrendsDashboard {
  google.protobuf.Timestamp week_start = 1;
  double avg_efficiency = 2;
  double avg_satisfaction = 3;
  double avg_first_time_fix = 4;
  int32 total_jobs = 5;
  double avg_response_hours = 6;
}

// KPIs
message GetKPIsRequest {
  string category = 1; // Optional filter by category
}

message GetKPIsResponse {
  repeated KPITracking kpis = 1;
}

message UpdateKPIRequest {
  string kpi_name = 1;
  string category = 2;
  double value = 3;
  optional double target = 4;
}

message UpdateKPIResponse {
  bool success = 1;
  string message = 2;
}

message KPITracking {
  uint32 id = 1;
  string kpi_name = 2;
  string kpi_category = 3;
  double current_value = 4;
  optional double target_value = 5;
  optional double previous_value = 6;
  string trend_direction = 7;
  optional double trend_percentage = 8;
  google.protobuf.Timestamp measurement_date = 9;
  google.protobuf.Timestamp measurement_time = 10;
  optional double alert_threshold_min = 11;
  optional double alert_threshold_max = 12;
  bool is_alert_triggered = 13;
  string notes = 14;
}

// Real-time Metrics
message GetRealTimeMetricsRequest {}

message GetRealTimeMetricsResponse {
  google.protobuf.Struct metrics = 1;
}

// Dashboard Widgets
message GetDashboardWidgetsRequest {
  string dashboard_category = 1;
}

message GetDashboardWidgetsResponse {
  repeated DashboardWidget widgets = 1;
}

message CreateDashboardWidgetRequest {
  string widget_name = 1;
  string widget_type = 2;
  string dashboard_category = 3;
  string data_source = 4;
  int32 refresh_interval = 5;
  google.protobuf.Struct widget_config = 6;
  bool is_active = 7;
}

message CreateDashboardWidgetResponse {
  bool success = 1;
  string message = 2;
  DashboardWidget widget = 3;
}

message DashboardWidget {
  uint32 id = 1;
  string widget_name = 2;
  string widget_type = 3;
  string dashboard_category = 4;
  string data_source = 5;
  int32 refresh_interval = 6;
  google.protobuf.Struct widget_config = 7;
  bool is_active = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// Health Check
message HealthCheckRequest {}

message HealthCheckResponse {
  bool healthy = 1;
  string message = 2;
  google.protobuf.Timestamp timestamp = 3;
}