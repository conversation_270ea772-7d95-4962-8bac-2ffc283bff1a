// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: api/analytics/v1/analytics.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Executive Dashboard
type GetExecutiveDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetExecutiveDashboardRequest) Reset() {
	*x = GetExecutiveDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExecutiveDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExecutiveDashboardRequest) ProtoMessage() {}

func (x *GetExecutiveDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExecutiveDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetExecutiveDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{0}
}

type GetExecutiveDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DashboardType string                     `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp     `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          *ExecutiveDashboardSummary `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget         `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *GetExecutiveDashboardResponse) Reset() {
	*x = GetExecutiveDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExecutiveDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExecutiveDashboardResponse) ProtoMessage() {}

func (x *GetExecutiveDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExecutiveDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetExecutiveDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{1}
}

func (x *GetExecutiveDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetExecutiveDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetExecutiveDashboardResponse) GetData() *ExecutiveDashboardSummary {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetExecutiveDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type ExecutiveDashboardSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Period            string  `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`
	TodayRevenue      float64 `protobuf:"fixed64,2,opt,name=today_revenue,json=todayRevenue,proto3" json:"today_revenue,omitempty"`
	TodayJobs         int32   `protobuf:"varint,3,opt,name=today_jobs,json=todayJobs,proto3" json:"today_jobs,omitempty"`
	TodaySatisfaction float64 `protobuf:"fixed64,4,opt,name=today_satisfaction,json=todaySatisfaction,proto3" json:"today_satisfaction,omitempty"`
	TodayEfficiency   float64 `protobuf:"fixed64,5,opt,name=today_efficiency,json=todayEfficiency,proto3" json:"today_efficiency,omitempty"`
	WeekRevenue       float64 `protobuf:"fixed64,6,opt,name=week_revenue,json=weekRevenue,proto3" json:"week_revenue,omitempty"`
	WeekJobs          int32   `protobuf:"varint,7,opt,name=week_jobs,json=weekJobs,proto3" json:"week_jobs,omitempty"`
	MonthRevenue      float64 `protobuf:"fixed64,8,opt,name=month_revenue,json=monthRevenue,proto3" json:"month_revenue,omitempty"`
	MonthJobs         int32   `protobuf:"varint,9,opt,name=month_jobs,json=monthJobs,proto3" json:"month_jobs,omitempty"`
}

func (x *ExecutiveDashboardSummary) Reset() {
	*x = ExecutiveDashboardSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutiveDashboardSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutiveDashboardSummary) ProtoMessage() {}

func (x *ExecutiveDashboardSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutiveDashboardSummary.ProtoReflect.Descriptor instead.
func (*ExecutiveDashboardSummary) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{2}
}

func (x *ExecutiveDashboardSummary) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

func (x *ExecutiveDashboardSummary) GetTodayRevenue() float64 {
	if x != nil {
		return x.TodayRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodayJobs() int32 {
	if x != nil {
		return x.TodayJobs
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodaySatisfaction() float64 {
	if x != nil {
		return x.TodaySatisfaction
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetTodayEfficiency() float64 {
	if x != nil {
		return x.TodayEfficiency
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetWeekRevenue() float64 {
	if x != nil {
		return x.WeekRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetWeekJobs() int32 {
	if x != nil {
		return x.WeekJobs
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetMonthRevenue() float64 {
	if x != nil {
		return x.MonthRevenue
	}
	return 0
}

func (x *ExecutiveDashboardSummary) GetMonthJobs() int32 {
	if x != nil {
		return x.MonthJobs
	}
	return 0
}

// Customer Insights Dashboard
type GetCustomerInsightsDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCustomerInsightsDashboardRequest) Reset() {
	*x = GetCustomerInsightsDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerInsightsDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerInsightsDashboardRequest) ProtoMessage() {}

func (x *GetCustomerInsightsDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerInsightsDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerInsightsDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{3}
}

type GetCustomerInsightsDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DashboardType string                       `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp       `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          []*CustomerInsightsDashboard `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget           `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *GetCustomerInsightsDashboardResponse) Reset() {
	*x = GetCustomerInsightsDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerInsightsDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerInsightsDashboardResponse) ProtoMessage() {}

func (x *GetCustomerInsightsDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerInsightsDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerInsightsDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomerInsightsDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetCustomerInsightsDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetCustomerInsightsDashboardResponse) GetData() []*CustomerInsightsDashboard {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetCustomerInsightsDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type CustomerInsightsDashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoyaltyTier      string  `protobuf:"bytes,1,opt,name=loyalty_tier,json=loyaltyTier,proto3" json:"loyalty_tier,omitempty"`
	CustomerCount    int32   `protobuf:"varint,2,opt,name=customer_count,json=customerCount,proto3" json:"customer_count,omitempty"`
	AvgLifetimeValue float64 `protobuf:"fixed64,3,opt,name=avg_lifetime_value,json=avgLifetimeValue,proto3" json:"avg_lifetime_value,omitempty"`
	AvgSatisfaction  float64 `protobuf:"fixed64,4,opt,name=avg_satisfaction,json=avgSatisfaction,proto3" json:"avg_satisfaction,omitempty"`
	AvgChurnRisk     float64 `protobuf:"fixed64,5,opt,name=avg_churn_risk,json=avgChurnRisk,proto3" json:"avg_churn_risk,omitempty"`
	TierTotalRevenue float64 `protobuf:"fixed64,6,opt,name=tier_total_revenue,json=tierTotalRevenue,proto3" json:"tier_total_revenue,omitempty"`
}

func (x *CustomerInsightsDashboard) Reset() {
	*x = CustomerInsightsDashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerInsightsDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerInsightsDashboard) ProtoMessage() {}

func (x *CustomerInsightsDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerInsightsDashboard.ProtoReflect.Descriptor instead.
func (*CustomerInsightsDashboard) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerInsightsDashboard) GetLoyaltyTier() string {
	if x != nil {
		return x.LoyaltyTier
	}
	return ""
}

func (x *CustomerInsightsDashboard) GetCustomerCount() int32 {
	if x != nil {
		return x.CustomerCount
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgLifetimeValue() float64 {
	if x != nil {
		return x.AvgLifetimeValue
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgSatisfaction() float64 {
	if x != nil {
		return x.AvgSatisfaction
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetAvgChurnRisk() float64 {
	if x != nil {
		return x.AvgChurnRisk
	}
	return 0
}

func (x *CustomerInsightsDashboard) GetTierTotalRevenue() float64 {
	if x != nil {
		return x.TierTotalRevenue
	}
	return 0
}

// Operational Dashboard
type GetOperationalDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetOperationalDashboardRequest) Reset() {
	*x = GetOperationalDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOperationalDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOperationalDashboardRequest) ProtoMessage() {}

func (x *GetOperationalDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOperationalDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetOperationalDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{6}
}

type GetOperationalDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DashboardType string                 `protobuf:"bytes,1,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
	LastUpdated   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	Data          *OperationalAnalytics  `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Widgets       []*DashboardWidget     `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *GetOperationalDashboardResponse) Reset() {
	*x = GetOperationalDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOperationalDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOperationalDashboardResponse) ProtoMessage() {}

func (x *GetOperationalDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOperationalDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetOperationalDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{7}
}

func (x *GetOperationalDashboardResponse) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

func (x *GetOperationalDashboardResponse) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

func (x *GetOperationalDashboardResponse) GetData() *OperationalAnalytics {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetOperationalDashboardResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type OperationalAnalytics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                      uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AnalysisDate            *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=analysis_date,json=analysisDate,proto3" json:"analysis_date,omitempty"`
	TotalActiveJobs         int32                  `protobuf:"varint,3,opt,name=total_active_jobs,json=totalActiveJobs,proto3" json:"total_active_jobs,omitempty"`
	CompletedJobs           int32                  `protobuf:"varint,4,opt,name=completed_jobs,json=completedJobs,proto3" json:"completed_jobs,omitempty"`
	CancelledJobs           int32                  `protobuf:"varint,5,opt,name=cancelled_jobs,json=cancelledJobs,proto3" json:"cancelled_jobs,omitempty"`
	EmergencyJobs           int32                  `protobuf:"varint,6,opt,name=emergency_jobs,json=emergencyJobs,proto3" json:"emergency_jobs,omitempty"`
	AverageResponseTimeMs   int64                  `protobuf:"varint,7,opt,name=average_response_time_ms,json=averageResponseTimeMs,proto3" json:"average_response_time_ms,omitempty"`
	AverageCompletionTimeMs int64                  `protobuf:"varint,8,opt,name=average_completion_time_ms,json=averageCompletionTimeMs,proto3" json:"average_completion_time_ms,omitempty"`
	TechnicianEfficiency    float64                `protobuf:"fixed64,9,opt,name=technician_efficiency,json=technicianEfficiency,proto3" json:"technician_efficiency,omitempty"`
	EquipmentUtilization    float64                `protobuf:"fixed64,10,opt,name=equipment_utilization,json=equipmentUtilization,proto3" json:"equipment_utilization,omitempty"`
	CustomerSatisfaction    float64                `protobuf:"fixed64,11,opt,name=customer_satisfaction,json=customerSatisfaction,proto3" json:"customer_satisfaction,omitempty"`
	FirstTimeFixRate        float64                `protobuf:"fixed64,12,opt,name=first_time_fix_rate,json=firstTimeFixRate,proto3" json:"first_time_fix_rate,omitempty"`
	CallbackRate            float64                `protobuf:"fixed64,13,opt,name=callback_rate,json=callbackRate,proto3" json:"callback_rate,omitempty"`
	PartsAvailability       float64                `protobuf:"fixed64,14,opt,name=parts_availability,json=partsAvailability,proto3" json:"parts_availability,omitempty"`
	FuelCosts               float64                `protobuf:"fixed64,15,opt,name=fuel_costs,json=fuelCosts,proto3" json:"fuel_costs,omitempty"`
	OvertimeHours           float64                `protobuf:"fixed64,16,opt,name=overtime_hours,json=overtimeHours,proto3" json:"overtime_hours,omitempty"`
}

func (x *OperationalAnalytics) Reset() {
	*x = OperationalAnalytics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalAnalytics) ProtoMessage() {}

func (x *OperationalAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalAnalytics.ProtoReflect.Descriptor instead.
func (*OperationalAnalytics) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{8}
}

func (x *OperationalAnalytics) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OperationalAnalytics) GetAnalysisDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AnalysisDate
	}
	return nil
}

func (x *OperationalAnalytics) GetTotalActiveJobs() int32 {
	if x != nil {
		return x.TotalActiveJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetCompletedJobs() int32 {
	if x != nil {
		return x.CompletedJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetCancelledJobs() int32 {
	if x != nil {
		return x.CancelledJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetEmergencyJobs() int32 {
	if x != nil {
		return x.EmergencyJobs
	}
	return 0
}

func (x *OperationalAnalytics) GetAverageResponseTimeMs() int64 {
	if x != nil {
		return x.AverageResponseTimeMs
	}
	return 0
}

func (x *OperationalAnalytics) GetAverageCompletionTimeMs() int64 {
	if x != nil {
		return x.AverageCompletionTimeMs
	}
	return 0
}

func (x *OperationalAnalytics) GetTechnicianEfficiency() float64 {
	if x != nil {
		return x.TechnicianEfficiency
	}
	return 0
}

func (x *OperationalAnalytics) GetEquipmentUtilization() float64 {
	if x != nil {
		return x.EquipmentUtilization
	}
	return 0
}

func (x *OperationalAnalytics) GetCustomerSatisfaction() float64 {
	if x != nil {
		return x.CustomerSatisfaction
	}
	return 0
}

func (x *OperationalAnalytics) GetFirstTimeFixRate() float64 {
	if x != nil {
		return x.FirstTimeFixRate
	}
	return 0
}

func (x *OperationalAnalytics) GetCallbackRate() float64 {
	if x != nil {
		return x.CallbackRate
	}
	return 0
}

func (x *OperationalAnalytics) GetPartsAvailability() float64 {
	if x != nil {
		return x.PartsAvailability
	}
	return 0
}

func (x *OperationalAnalytics) GetFuelCosts() float64 {
	if x != nil {
		return x.FuelCosts
	}
	return 0
}

func (x *OperationalAnalytics) GetOvertimeHours() float64 {
	if x != nil {
		return x.OvertimeHours
	}
	return 0
}

// Performance Trends
type GetPerformanceTrendsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Weeks int32 `protobuf:"varint,1,opt,name=weeks,proto3" json:"weeks,omitempty"` // Number of weeks to fetch
}

func (x *GetPerformanceTrendsRequest) Reset() {
	*x = GetPerformanceTrendsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPerformanceTrendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPerformanceTrendsRequest) ProtoMessage() {}

func (x *GetPerformanceTrendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPerformanceTrendsRequest.ProtoReflect.Descriptor instead.
func (*GetPerformanceTrendsRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{9}
}

func (x *GetPerformanceTrendsRequest) GetWeeks() int32 {
	if x != nil {
		return x.Weeks
	}
	return 0
}

type GetPerformanceTrendsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trends []*PerformanceTrendsDashboard `protobuf:"bytes,1,rep,name=trends,proto3" json:"trends,omitempty"`
}

func (x *GetPerformanceTrendsResponse) Reset() {
	*x = GetPerformanceTrendsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPerformanceTrendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPerformanceTrendsResponse) ProtoMessage() {}

func (x *GetPerformanceTrendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPerformanceTrendsResponse.ProtoReflect.Descriptor instead.
func (*GetPerformanceTrendsResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{10}
}

func (x *GetPerformanceTrendsResponse) GetTrends() []*PerformanceTrendsDashboard {
	if x != nil {
		return x.Trends
	}
	return nil
}

type PerformanceTrendsDashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeekStart        *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=week_start,json=weekStart,proto3" json:"week_start,omitempty"`
	AvgEfficiency    float64                `protobuf:"fixed64,2,opt,name=avg_efficiency,json=avgEfficiency,proto3" json:"avg_efficiency,omitempty"`
	AvgSatisfaction  float64                `protobuf:"fixed64,3,opt,name=avg_satisfaction,json=avgSatisfaction,proto3" json:"avg_satisfaction,omitempty"`
	AvgFirstTimeFix  float64                `protobuf:"fixed64,4,opt,name=avg_first_time_fix,json=avgFirstTimeFix,proto3" json:"avg_first_time_fix,omitempty"`
	TotalJobs        int32                  `protobuf:"varint,5,opt,name=total_jobs,json=totalJobs,proto3" json:"total_jobs,omitempty"`
	AvgResponseHours float64                `protobuf:"fixed64,6,opt,name=avg_response_hours,json=avgResponseHours,proto3" json:"avg_response_hours,omitempty"`
}

func (x *PerformanceTrendsDashboard) Reset() {
	*x = PerformanceTrendsDashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerformanceTrendsDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceTrendsDashboard) ProtoMessage() {}

func (x *PerformanceTrendsDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceTrendsDashboard.ProtoReflect.Descriptor instead.
func (*PerformanceTrendsDashboard) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{11}
}

func (x *PerformanceTrendsDashboard) GetWeekStart() *timestamppb.Timestamp {
	if x != nil {
		return x.WeekStart
	}
	return nil
}

func (x *PerformanceTrendsDashboard) GetAvgEfficiency() float64 {
	if x != nil {
		return x.AvgEfficiency
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgSatisfaction() float64 {
	if x != nil {
		return x.AvgSatisfaction
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgFirstTimeFix() float64 {
	if x != nil {
		return x.AvgFirstTimeFix
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetTotalJobs() int32 {
	if x != nil {
		return x.TotalJobs
	}
	return 0
}

func (x *PerformanceTrendsDashboard) GetAvgResponseHours() float64 {
	if x != nil {
		return x.AvgResponseHours
	}
	return 0
}

// KPIs
type GetKPIsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"` // Optional filter by category
}

func (x *GetKPIsRequest) Reset() {
	*x = GetKPIsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKPIsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKPIsRequest) ProtoMessage() {}

func (x *GetKPIsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKPIsRequest.ProtoReflect.Descriptor instead.
func (*GetKPIsRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{12}
}

func (x *GetKPIsRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type GetKPIsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kpis []*KPITracking `protobuf:"bytes,1,rep,name=kpis,proto3" json:"kpis,omitempty"`
}

func (x *GetKPIsResponse) Reset() {
	*x = GetKPIsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKPIsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKPIsResponse) ProtoMessage() {}

func (x *GetKPIsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKPIsResponse.ProtoReflect.Descriptor instead.
func (*GetKPIsResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{13}
}

func (x *GetKPIsResponse) GetKpis() []*KPITracking {
	if x != nil {
		return x.Kpis
	}
	return nil
}

type UpdateKPIRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KpiName  string   `protobuf:"bytes,1,opt,name=kpi_name,json=kpiName,proto3" json:"kpi_name,omitempty"`
	Category string   `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	Value    float64  `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
	Target   *float64 `protobuf:"fixed64,4,opt,name=target,proto3,oneof" json:"target,omitempty"`
}

func (x *UpdateKPIRequest) Reset() {
	*x = UpdateKPIRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKPIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKPIRequest) ProtoMessage() {}

func (x *UpdateKPIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKPIRequest.ProtoReflect.Descriptor instead.
func (*UpdateKPIRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateKPIRequest) GetKpiName() string {
	if x != nil {
		return x.KpiName
	}
	return ""
}

func (x *UpdateKPIRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *UpdateKPIRequest) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *UpdateKPIRequest) GetTarget() float64 {
	if x != nil && x.Target != nil {
		return *x.Target
	}
	return 0
}

type UpdateKPIResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *UpdateKPIResponse) Reset() {
	*x = UpdateKPIResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKPIResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKPIResponse) ProtoMessage() {}

func (x *UpdateKPIResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKPIResponse.ProtoReflect.Descriptor instead.
func (*UpdateKPIResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateKPIResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateKPIResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type KPITracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	KpiName           string                 `protobuf:"bytes,2,opt,name=kpi_name,json=kpiName,proto3" json:"kpi_name,omitempty"`
	KpiCategory       string                 `protobuf:"bytes,3,opt,name=kpi_category,json=kpiCategory,proto3" json:"kpi_category,omitempty"`
	CurrentValue      float64                `protobuf:"fixed64,4,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	TargetValue       *float64               `protobuf:"fixed64,5,opt,name=target_value,json=targetValue,proto3,oneof" json:"target_value,omitempty"`
	PreviousValue     *float64               `protobuf:"fixed64,6,opt,name=previous_value,json=previousValue,proto3,oneof" json:"previous_value,omitempty"`
	TrendDirection    string                 `protobuf:"bytes,7,opt,name=trend_direction,json=trendDirection,proto3" json:"trend_direction,omitempty"`
	TrendPercentage   *float64               `protobuf:"fixed64,8,opt,name=trend_percentage,json=trendPercentage,proto3,oneof" json:"trend_percentage,omitempty"`
	MeasurementDate   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=measurement_date,json=measurementDate,proto3" json:"measurement_date,omitempty"`
	MeasurementTime   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=measurement_time,json=measurementTime,proto3" json:"measurement_time,omitempty"`
	AlertThresholdMin *float64               `protobuf:"fixed64,11,opt,name=alert_threshold_min,json=alertThresholdMin,proto3,oneof" json:"alert_threshold_min,omitempty"`
	AlertThresholdMax *float64               `protobuf:"fixed64,12,opt,name=alert_threshold_max,json=alertThresholdMax,proto3,oneof" json:"alert_threshold_max,omitempty"`
	IsAlertTriggered  bool                   `protobuf:"varint,13,opt,name=is_alert_triggered,json=isAlertTriggered,proto3" json:"is_alert_triggered,omitempty"`
	Notes             string                 `protobuf:"bytes,14,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *KPITracking) Reset() {
	*x = KPITracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KPITracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KPITracking) ProtoMessage() {}

func (x *KPITracking) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KPITracking.ProtoReflect.Descriptor instead.
func (*KPITracking) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{16}
}

func (x *KPITracking) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KPITracking) GetKpiName() string {
	if x != nil {
		return x.KpiName
	}
	return ""
}

func (x *KPITracking) GetKpiCategory() string {
	if x != nil {
		return x.KpiCategory
	}
	return ""
}

func (x *KPITracking) GetCurrentValue() float64 {
	if x != nil {
		return x.CurrentValue
	}
	return 0
}

func (x *KPITracking) GetTargetValue() float64 {
	if x != nil && x.TargetValue != nil {
		return *x.TargetValue
	}
	return 0
}

func (x *KPITracking) GetPreviousValue() float64 {
	if x != nil && x.PreviousValue != nil {
		return *x.PreviousValue
	}
	return 0
}

func (x *KPITracking) GetTrendDirection() string {
	if x != nil {
		return x.TrendDirection
	}
	return ""
}

func (x *KPITracking) GetTrendPercentage() float64 {
	if x != nil && x.TrendPercentage != nil {
		return *x.TrendPercentage
	}
	return 0
}

func (x *KPITracking) GetMeasurementDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MeasurementDate
	}
	return nil
}

func (x *KPITracking) GetMeasurementTime() *timestamppb.Timestamp {
	if x != nil {
		return x.MeasurementTime
	}
	return nil
}

func (x *KPITracking) GetAlertThresholdMin() float64 {
	if x != nil && x.AlertThresholdMin != nil {
		return *x.AlertThresholdMin
	}
	return 0
}

func (x *KPITracking) GetAlertThresholdMax() float64 {
	if x != nil && x.AlertThresholdMax != nil {
		return *x.AlertThresholdMax
	}
	return 0
}

func (x *KPITracking) GetIsAlertTriggered() bool {
	if x != nil {
		return x.IsAlertTriggered
	}
	return false
}

func (x *KPITracking) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

// Real-time Metrics
type GetRealTimeMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRealTimeMetricsRequest) Reset() {
	*x = GetRealTimeMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRealTimeMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRealTimeMetricsRequest) ProtoMessage() {}

func (x *GetRealTimeMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRealTimeMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetRealTimeMetricsRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{17}
}

type GetRealTimeMetricsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics *structpb.Struct `protobuf:"bytes,1,opt,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *GetRealTimeMetricsResponse) Reset() {
	*x = GetRealTimeMetricsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRealTimeMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRealTimeMetricsResponse) ProtoMessage() {}

func (x *GetRealTimeMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRealTimeMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetRealTimeMetricsResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{18}
}

func (x *GetRealTimeMetricsResponse) GetMetrics() *structpb.Struct {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// Dashboard Widgets
type GetDashboardWidgetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DashboardCategory string `protobuf:"bytes,1,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
}

func (x *GetDashboardWidgetsRequest) Reset() {
	*x = GetDashboardWidgetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDashboardWidgetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDashboardWidgetsRequest) ProtoMessage() {}

func (x *GetDashboardWidgetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDashboardWidgetsRequest.ProtoReflect.Descriptor instead.
func (*GetDashboardWidgetsRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{19}
}

func (x *GetDashboardWidgetsRequest) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

type GetDashboardWidgetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Widgets []*DashboardWidget `protobuf:"bytes,1,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *GetDashboardWidgetsResponse) Reset() {
	*x = GetDashboardWidgetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDashboardWidgetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDashboardWidgetsResponse) ProtoMessage() {}

func (x *GetDashboardWidgetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDashboardWidgetsResponse.ProtoReflect.Descriptor instead.
func (*GetDashboardWidgetsResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{20}
}

func (x *GetDashboardWidgetsResponse) GetWidgets() []*DashboardWidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type CreateDashboardWidgetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WidgetName        string           `protobuf:"bytes,1,opt,name=widget_name,json=widgetName,proto3" json:"widget_name,omitempty"`
	WidgetType        string           `protobuf:"bytes,2,opt,name=widget_type,json=widgetType,proto3" json:"widget_type,omitempty"`
	DashboardCategory string           `protobuf:"bytes,3,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
	DataSource        string           `protobuf:"bytes,4,opt,name=data_source,json=dataSource,proto3" json:"data_source,omitempty"`
	RefreshInterval   int32            `protobuf:"varint,5,opt,name=refresh_interval,json=refreshInterval,proto3" json:"refresh_interval,omitempty"`
	WidgetConfig      *structpb.Struct `protobuf:"bytes,6,opt,name=widget_config,json=widgetConfig,proto3" json:"widget_config,omitempty"`
	IsActive          bool             `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
}

func (x *CreateDashboardWidgetRequest) Reset() {
	*x = CreateDashboardWidgetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDashboardWidgetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDashboardWidgetRequest) ProtoMessage() {}

func (x *CreateDashboardWidgetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDashboardWidgetRequest.ProtoReflect.Descriptor instead.
func (*CreateDashboardWidgetRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{21}
}

func (x *CreateDashboardWidgetRequest) GetWidgetName() string {
	if x != nil {
		return x.WidgetName
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetWidgetType() string {
	if x != nil {
		return x.WidgetType
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *CreateDashboardWidgetRequest) GetRefreshInterval() int32 {
	if x != nil {
		return x.RefreshInterval
	}
	return 0
}

func (x *CreateDashboardWidgetRequest) GetWidgetConfig() *structpb.Struct {
	if x != nil {
		return x.WidgetConfig
	}
	return nil
}

func (x *CreateDashboardWidgetRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type CreateDashboardWidgetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool             `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Widget  *DashboardWidget `protobuf:"bytes,3,opt,name=widget,proto3" json:"widget,omitempty"`
}

func (x *CreateDashboardWidgetResponse) Reset() {
	*x = CreateDashboardWidgetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDashboardWidgetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDashboardWidgetResponse) ProtoMessage() {}

func (x *CreateDashboardWidgetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDashboardWidgetResponse.ProtoReflect.Descriptor instead.
func (*CreateDashboardWidgetResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{22}
}

func (x *CreateDashboardWidgetResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateDashboardWidgetResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateDashboardWidgetResponse) GetWidget() *DashboardWidget {
	if x != nil {
		return x.Widget
	}
	return nil
}

type DashboardWidget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WidgetName        string                 `protobuf:"bytes,2,opt,name=widget_name,json=widgetName,proto3" json:"widget_name,omitempty"`
	WidgetType        string                 `protobuf:"bytes,3,opt,name=widget_type,json=widgetType,proto3" json:"widget_type,omitempty"`
	DashboardCategory string                 `protobuf:"bytes,4,opt,name=dashboard_category,json=dashboardCategory,proto3" json:"dashboard_category,omitempty"`
	DataSource        string                 `protobuf:"bytes,5,opt,name=data_source,json=dataSource,proto3" json:"data_source,omitempty"`
	RefreshInterval   int32                  `protobuf:"varint,6,opt,name=refresh_interval,json=refreshInterval,proto3" json:"refresh_interval,omitempty"`
	WidgetConfig      *structpb.Struct       `protobuf:"bytes,7,opt,name=widget_config,json=widgetConfig,proto3" json:"widget_config,omitempty"`
	IsActive          bool                   `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DashboardWidget) Reset() {
	*x = DashboardWidget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardWidget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardWidget) ProtoMessage() {}

func (x *DashboardWidget) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardWidget.ProtoReflect.Descriptor instead.
func (*DashboardWidget) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{23}
}

func (x *DashboardWidget) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DashboardWidget) GetWidgetName() string {
	if x != nil {
		return x.WidgetName
	}
	return ""
}

func (x *DashboardWidget) GetWidgetType() string {
	if x != nil {
		return x.WidgetType
	}
	return ""
}

func (x *DashboardWidget) GetDashboardCategory() string {
	if x != nil {
		return x.DashboardCategory
	}
	return ""
}

func (x *DashboardWidget) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *DashboardWidget) GetRefreshInterval() int32 {
	if x != nil {
		return x.RefreshInterval
	}
	return 0
}

func (x *DashboardWidget) GetWidgetConfig() *structpb.Struct {
	if x != nil {
		return x.WidgetConfig
	}
	return nil
}

func (x *DashboardWidget) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *DashboardWidget) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DashboardWidget) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Health Check
type HealthCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{24}
}

type HealthCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Healthy   bool                   `protobuf:"varint,1,opt,name=healthy,proto3" json:"healthy,omitempty"`
	Message   string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analytics_v1_analytics_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analytics_v1_analytics_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_analytics_v1_analytics_proto_rawDescGZIP(), []int{25}
}

func (x *HealthCheckResponse) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *HealthCheckResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HealthCheckResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

var File_api_analytics_v1_analytics_proto protoreflect.FileDescriptor

var file_api_analytics_v1_analytics_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x1e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x83, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x76, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x07, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x07,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0xd5, 0x02, 0x0a, 0x19, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x52, 0x65, 0x76, 0x65, 0x6e,
	0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x6a, 0x6f, 0x62, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x4a, 0x6f, 0x62,
	0x73, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x73, 0x61, 0x74, 0x69, 0x73,
	0x66, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x29, 0x0a, 0x10, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x74, 0x6f, 0x64, 0x61,
	0x79, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x77,
	0x65, 0x65, 0x6b, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x77, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x77, 0x65, 0x65, 0x6b, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0c, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x4a, 0x6f, 0x62, 0x73, 0x22,
	0x25, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x8a, 0x02, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x07, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x73, 0x22, 0x92, 0x02, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79,
	0x54, 0x69, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x61,
	0x76, 0x67, 0x5f, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x61, 0x76, 0x67, 0x4c, 0x69, 0x66, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x76, 0x67,
	0x5f, 0x73, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0f, 0x61, 0x76, 0x67, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x76, 0x67, 0x5f, 0x63, 0x68, 0x75, 0x72,
	0x6e, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x61, 0x76,
	0x67, 0x43, 0x68, 0x75, 0x72, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x69,
	0x65, 0x72, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x74, 0x69, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x80, 0x02, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x3b, 0x0a, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x52, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0xe6, 0x05,
	0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x4a, 0x6f, 0x62,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6a,
	0x6f, 0x62, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x65, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x61, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x33,
	0x0a, 0x15, 0x74, 0x65, 0x63, 0x68, 0x6e, 0x69, 0x63, 0x69, 0x61, 0x6e, 0x5f, 0x65, 0x66, 0x66,
	0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x74,
	0x65, 0x63, 0x68, 0x6e, 0x69, 0x63, 0x69, 0x61, 0x6e, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x33, 0x0a, 0x15, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x14, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x74, 0x69,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x15, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a,
	0x13, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x78, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x61, 0x72, 0x74, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x70,
	0x61, 0x72, 0x74, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x66, 0x75, 0x65, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x68, 0x6f, 0x75, 0x72,
	0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6d,
	0x65, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x22, 0x33, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x22, 0x64, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x65,
	0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x74,
	0x72, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x06, 0x74, 0x72, 0x65, 0x6e, 0x64,
	0x73, 0x22, 0xa3, 0x02, 0x0a, 0x1a, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x39, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x76, 0x67, 0x5f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0d, 0x61, 0x76, 0x67, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x61, 0x74, 0x69, 0x73, 0x66,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x61, 0x76,
	0x67, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a,
	0x12, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x66, 0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x61, 0x76, 0x67, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x76, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x61, 0x76, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x22, 0x2c, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4b, 0x50,
	0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x44, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4b, 0x50, 0x49, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x6b, 0x70, 0x69, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x50, 0x49, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x6b, 0x70, 0x69, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x10,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x50, 0x49, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x6b, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6b, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x47, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x50, 0x49, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd2,
	0x05, 0x0a, 0x0b, 0x4b, 0x50, 0x49, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x6b, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6b, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6b, 0x70, 0x69,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6b, 0x70, 0x69, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x26, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x01, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x74, 0x72, 0x65, 0x6e, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x10, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x0f, 0x74, 0x72, 0x65, 0x6e,
	0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x45,
	0x0a, 0x10, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f,
	0x6d, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x11, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x4d, 0x69, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x48, 0x04,
	0x52, 0x11, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x4d, 0x61, 0x78, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x13,
	0x0a, 0x11, 0x5f, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x78, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x4f, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31,
	0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x22, 0x4b, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2d, 0x0a, 0x12, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x5a,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a,
	0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x52, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0xb6, 0x02, 0x0a, 0x1c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x3c, 0x0a, 0x0d, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x06, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x22, 0xaf, 0x03, 0x0a, 0x0f, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x3c, 0x0a, 0x0d, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x83, 0x01, 0x0a,
	0x13, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x32, 0x8f, 0x0c, 0x0a, 0x10, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x12, 0xbb, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12,
	0xae, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x30, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x12, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0xa3, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2f, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x6e, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x4b, 0x50, 0x49,
	0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x50, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x50, 0x49, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x6b, 0x70, 0x69, 0x73, 0x12, 0x77, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4b, 0x50, 0x49, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x50, 0x49,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4b, 0x50, 0x49, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1b, 0x22, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x6b, 0x70, 0x69, 0x73, 0x3a, 0x01, 0x2a, 0x12,
	0x9b, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x95, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x9e, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x6f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2d, 0x68, 0x76, 0x61, 0x63, 0x2d, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_analytics_v1_analytics_proto_rawDescOnce sync.Once
	file_api_analytics_v1_analytics_proto_rawDescData = file_api_analytics_v1_analytics_proto_rawDesc
)

func file_api_analytics_v1_analytics_proto_rawDescGZIP() []byte {
	file_api_analytics_v1_analytics_proto_rawDescOnce.Do(func() {
		file_api_analytics_v1_analytics_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_analytics_v1_analytics_proto_rawDescData)
	})
	return file_api_analytics_v1_analytics_proto_rawDescData
}

var file_api_analytics_v1_analytics_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_api_analytics_v1_analytics_proto_goTypes = []interface{}{
	(*GetExecutiveDashboardRequest)(nil),         // 0: api.analytics.v1.GetExecutiveDashboardRequest
	(*GetExecutiveDashboardResponse)(nil),        // 1: api.analytics.v1.GetExecutiveDashboardResponse
	(*ExecutiveDashboardSummary)(nil),            // 2: api.analytics.v1.ExecutiveDashboardSummary
	(*GetCustomerInsightsDashboardRequest)(nil),  // 3: api.analytics.v1.GetCustomerInsightsDashboardRequest
	(*GetCustomerInsightsDashboardResponse)(nil), // 4: api.analytics.v1.GetCustomerInsightsDashboardResponse
	(*CustomerInsightsDashboard)(nil),            // 5: api.analytics.v1.CustomerInsightsDashboard
	(*GetOperationalDashboardRequest)(nil),       // 6: api.analytics.v1.GetOperationalDashboardRequest
	(*GetOperationalDashboardResponse)(nil),      // 7: api.analytics.v1.GetOperationalDashboardResponse
	(*OperationalAnalytics)(nil),                 // 8: api.analytics.v1.OperationalAnalytics
	(*GetPerformanceTrendsRequest)(nil),          // 9: api.analytics.v1.GetPerformanceTrendsRequest
	(*GetPerformanceTrendsResponse)(nil),         // 10: api.analytics.v1.GetPerformanceTrendsResponse
	(*PerformanceTrendsDashboard)(nil),           // 11: api.analytics.v1.PerformanceTrendsDashboard
	(*GetKPIsRequest)(nil),                       // 12: api.analytics.v1.GetKPIsRequest
	(*GetKPIsResponse)(nil),                      // 13: api.analytics.v1.GetKPIsResponse
	(*UpdateKPIRequest)(nil),                     // 14: api.analytics.v1.UpdateKPIRequest
	(*UpdateKPIResponse)(nil),                    // 15: api.analytics.v1.UpdateKPIResponse
	(*KPITracking)(nil),                          // 16: api.analytics.v1.KPITracking
	(*GetRealTimeMetricsRequest)(nil),            // 17: api.analytics.v1.GetRealTimeMetricsRequest
	(*GetRealTimeMetricsResponse)(nil),           // 18: api.analytics.v1.GetRealTimeMetricsResponse
	(*GetDashboardWidgetsRequest)(nil),           // 19: api.analytics.v1.GetDashboardWidgetsRequest
	(*GetDashboardWidgetsResponse)(nil),          // 20: api.analytics.v1.GetDashboardWidgetsResponse
	(*CreateDashboardWidgetRequest)(nil),         // 21: api.analytics.v1.CreateDashboardWidgetRequest
	(*CreateDashboardWidgetResponse)(nil),        // 22: api.analytics.v1.CreateDashboardWidgetResponse
	(*DashboardWidget)(nil),                      // 23: api.analytics.v1.DashboardWidget
	(*HealthCheckRequest)(nil),                   // 24: api.analytics.v1.HealthCheckRequest
	(*HealthCheckResponse)(nil),                  // 25: api.analytics.v1.HealthCheckResponse
	(*timestamppb.Timestamp)(nil),                // 26: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                      // 27: google.protobuf.Struct
}
var file_api_analytics_v1_analytics_proto_depIdxs = []int32{
	26, // 0: api.analytics.v1.GetExecutiveDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	2,  // 1: api.analytics.v1.GetExecutiveDashboardResponse.data:type_name -> api.analytics.v1.ExecutiveDashboardSummary
	23, // 2: api.analytics.v1.GetExecutiveDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 3: api.analytics.v1.GetCustomerInsightsDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	5,  // 4: api.analytics.v1.GetCustomerInsightsDashboardResponse.data:type_name -> api.analytics.v1.CustomerInsightsDashboard
	23, // 5: api.analytics.v1.GetCustomerInsightsDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 6: api.analytics.v1.GetOperationalDashboardResponse.last_updated:type_name -> google.protobuf.Timestamp
	8,  // 7: api.analytics.v1.GetOperationalDashboardResponse.data:type_name -> api.analytics.v1.OperationalAnalytics
	23, // 8: api.analytics.v1.GetOperationalDashboardResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	26, // 9: api.analytics.v1.OperationalAnalytics.analysis_date:type_name -> google.protobuf.Timestamp
	11, // 10: api.analytics.v1.GetPerformanceTrendsResponse.trends:type_name -> api.analytics.v1.PerformanceTrendsDashboard
	26, // 11: api.analytics.v1.PerformanceTrendsDashboard.week_start:type_name -> google.protobuf.Timestamp
	16, // 12: api.analytics.v1.GetKPIsResponse.kpis:type_name -> api.analytics.v1.KPITracking
	26, // 13: api.analytics.v1.KPITracking.measurement_date:type_name -> google.protobuf.Timestamp
	26, // 14: api.analytics.v1.KPITracking.measurement_time:type_name -> google.protobuf.Timestamp
	27, // 15: api.analytics.v1.GetRealTimeMetricsResponse.metrics:type_name -> google.protobuf.Struct
	23, // 16: api.analytics.v1.GetDashboardWidgetsResponse.widgets:type_name -> api.analytics.v1.DashboardWidget
	27, // 17: api.analytics.v1.CreateDashboardWidgetRequest.widget_config:type_name -> google.protobuf.Struct
	23, // 18: api.analytics.v1.CreateDashboardWidgetResponse.widget:type_name -> api.analytics.v1.DashboardWidget
	27, // 19: api.analytics.v1.DashboardWidget.widget_config:type_name -> google.protobuf.Struct
	26, // 20: api.analytics.v1.DashboardWidget.created_at:type_name -> google.protobuf.Timestamp
	26, // 21: api.analytics.v1.DashboardWidget.updated_at:type_name -> google.protobuf.Timestamp
	26, // 22: api.analytics.v1.HealthCheckResponse.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 23: api.analytics.v1.AnalyticsService.GetExecutiveDashboard:input_type -> api.analytics.v1.GetExecutiveDashboardRequest
	3,  // 24: api.analytics.v1.AnalyticsService.GetCustomerInsightsDashboard:input_type -> api.analytics.v1.GetCustomerInsightsDashboardRequest
	6,  // 25: api.analytics.v1.AnalyticsService.GetOperationalDashboard:input_type -> api.analytics.v1.GetOperationalDashboardRequest
	9,  // 26: api.analytics.v1.AnalyticsService.GetPerformanceTrends:input_type -> api.analytics.v1.GetPerformanceTrendsRequest
	12, // 27: api.analytics.v1.AnalyticsService.GetKPIs:input_type -> api.analytics.v1.GetKPIsRequest
	14, // 28: api.analytics.v1.AnalyticsService.UpdateKPI:input_type -> api.analytics.v1.UpdateKPIRequest
	17, // 29: api.analytics.v1.AnalyticsService.GetRealTimeMetrics:input_type -> api.analytics.v1.GetRealTimeMetricsRequest
	19, // 30: api.analytics.v1.AnalyticsService.GetDashboardWidgets:input_type -> api.analytics.v1.GetDashboardWidgetsRequest
	21, // 31: api.analytics.v1.AnalyticsService.CreateDashboardWidget:input_type -> api.analytics.v1.CreateDashboardWidgetRequest
	24, // 32: api.analytics.v1.AnalyticsService.HealthCheck:input_type -> api.analytics.v1.HealthCheckRequest
	1,  // 33: api.analytics.v1.AnalyticsService.GetExecutiveDashboard:output_type -> api.analytics.v1.GetExecutiveDashboardResponse
	4,  // 34: api.analytics.v1.AnalyticsService.GetCustomerInsightsDashboard:output_type -> api.analytics.v1.GetCustomerInsightsDashboardResponse
	7,  // 35: api.analytics.v1.AnalyticsService.GetOperationalDashboard:output_type -> api.analytics.v1.GetOperationalDashboardResponse
	10, // 36: api.analytics.v1.AnalyticsService.GetPerformanceTrends:output_type -> api.analytics.v1.GetPerformanceTrendsResponse
	13, // 37: api.analytics.v1.AnalyticsService.GetKPIs:output_type -> api.analytics.v1.GetKPIsResponse
	15, // 38: api.analytics.v1.AnalyticsService.UpdateKPI:output_type -> api.analytics.v1.UpdateKPIResponse
	18, // 39: api.analytics.v1.AnalyticsService.GetRealTimeMetrics:output_type -> api.analytics.v1.GetRealTimeMetricsResponse
	20, // 40: api.analytics.v1.AnalyticsService.GetDashboardWidgets:output_type -> api.analytics.v1.GetDashboardWidgetsResponse
	22, // 41: api.analytics.v1.AnalyticsService.CreateDashboardWidget:output_type -> api.analytics.v1.CreateDashboardWidgetResponse
	25, // 42: api.analytics.v1.AnalyticsService.HealthCheck:output_type -> api.analytics.v1.HealthCheckResponse
	33, // [33:43] is the sub-list for method output_type
	23, // [23:33] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_analytics_v1_analytics_proto_init() }
func file_api_analytics_v1_analytics_proto_init() {
	if File_api_analytics_v1_analytics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_analytics_v1_analytics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExecutiveDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExecutiveDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutiveDashboardSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerInsightsDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerInsightsDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerInsightsDashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOperationalDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOperationalDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalAnalytics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPerformanceTrendsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPerformanceTrendsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerformanceTrendsDashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKPIsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKPIsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKPIRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKPIResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KPITracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRealTimeMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRealTimeMetricsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDashboardWidgetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDashboardWidgetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDashboardWidgetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDashboardWidgetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardWidget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analytics_v1_analytics_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_analytics_v1_analytics_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_api_analytics_v1_analytics_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_analytics_v1_analytics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_analytics_v1_analytics_proto_goTypes,
		DependencyIndexes: file_api_analytics_v1_analytics_proto_depIdxs,
		MessageInfos:      file_api_analytics_v1_analytics_proto_msgTypes,
	}.Build()
	File_api_analytics_v1_analytics_proto = out.File
	file_api_analytics_v1_analytics_proto_rawDesc = nil
	file_api_analytics_v1_analytics_proto_goTypes = nil
	file_api_analytics_v1_analytics_proto_depIdxs = nil
}
