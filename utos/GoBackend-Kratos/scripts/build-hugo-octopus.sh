#!/bin/bash

# 🐙 Hugo-Enhanced Octopus Interface Build Script
# This script builds the Hugo static site for the Octopus dashboard

set -e

echo "🐙 Building Hugo-Enhanced Octopus Interface..."

# Change to the project root
cd "$(dirname "$0")/.."

# Ensure Hugo binary exists
if [ ! -f "./hugo" ]; then
    echo "📥 Downloading Hugo..."
    curl -L https://github.com/gohugoio/hugo/releases/download/v0.121.1/hugo_extended_0.121.1_linux-amd64.tar.gz | tar -xz hugo
    chmod +x hugo
fi

# Create output directory
mkdir -p web/octopus/static

# Build Hugo site
echo "🏗️ Building Hugo site..."
cd web/hugo-octopus
../../hugo --minify --destination ../octopus/static

echo "✅ Hugo build completed!"

# Verify build
if [ -f "../octopus/static/index.html" ]; then
    echo "✅ Hugo site built successfully!"
    echo "📁 Output directory: web/octopus/static/"
    echo "🌐 Dashboard will be available at: http://localhost:8083/"
else
    echo "❌ Hugo build failed - index.html not found"
    exit 1
fi

# Show build summary
echo ""
echo "🐙 HUGO-ENHANCED OCTOPUS INTERFACE BUILD SUMMARY"
echo "================================================"
echo "✅ Hugo static site generated"
echo "✅ Modern dashboard theme applied"
echo "✅ Real-time WebSocket integration ready"
echo "✅ Responsive design with dark theme"
echo "✅ Chart.js visualization included"
echo "✅ Tailwind CSS styling applied"
echo ""
echo "🚀 Ready to start Octopus Interface!"
echo "   Run: make run-octopus"
echo "   Or:  go run cmd/octopus/main.go"
echo ""