-- 🐙 Octopus Dashboard Database Schema Creation
-- Creates missing tables and columns for full dashboard functionality

-- Create schemas if they don't exist
CREATE SCHEMA IF NOT EXISTS transcription;
CREATE SCHEMA IF NOT EXISTS billionmail;

-- 👥 Customer Analytics Table
CREATE TABLE IF NOT EXISTS customer_analytics (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    lifetime_value DECIMAL(10,2) DEFAULT 0,
    churn_probability DECIMAL(3,2) DEFAULT 0,
    avg_satisfaction DECIMAL(3,2) DEFAULT 0,
    total_interactions INTEGER DEFAULT 0,
    last_interaction_date TIMESTAMP,
    risk_score DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add missing columns to customers table
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS last_contact_date TIMES<PERSON>MP,
ADD COLUMN IF NOT EXISTS satisfaction_score DECIMAL(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) DEFAULT 'low';

-- 📞 Transcription Tables
CREATE TABLE IF NOT EXISTS transcription.call_transcriptions (
    id SERIAL PRIMARY KEY,
    call_id VARCHAR(100) UNIQUE,
    caller_phone VARCHAR(20),
    caller_company VARCHAR(200),
    call_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    call_duration INTEGER DEFAULT 0, -- in seconds
    transcription_text TEXT,
    confidence_score DECIMAL(5,2) DEFAULT 0,
    hvac_relevance BOOLEAN DEFAULT false,
    urgency_level VARCHAR(20) DEFAULT 'normal', -- normal, high, critical
    sentiment VARCHAR(20) DEFAULT 'neutral',
    keywords TEXT[],
    customer_id INTEGER REFERENCES customers(id),
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transcription.transcription_emails (
    id SERIAL PRIMARY KEY,
    email_id VARCHAR(100),
    subject VARCHAR(500),
    sender_email VARCHAR(200),
    processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    transcription_result TEXT,
    confidence_score DECIMAL(5,2) DEFAULT 0,
    hvac_relevance BOOLEAN DEFAULT false,
    urgency_level VARCHAR(20) DEFAULT 'normal',
    customer_id INTEGER REFERENCES customers(id),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📧 BillionMail Tables
CREATE TABLE IF NOT EXISTS billionmail.email_messages (
    id SERIAL PRIMARY KEY,
    message_id VARCHAR(200) UNIQUE,
    mailbox VARCHAR(100),
    sender_email VARCHAR(200),
    sender_name VARCHAR(200),
    recipient_email VARCHAR(200),
    subject VARCHAR(500),
    body_text TEXT,
    body_html TEXT,
    received_date TIMESTAMP,
    is_read BOOLEAN DEFAULT false,
    is_flagged BOOLEAN DEFAULT false,
    has_attachments BOOLEAN DEFAULT false,
    attachment_count INTEGER DEFAULT 0,
    message_size INTEGER DEFAULT 0,
    folder VARCHAR(100) DEFAULT 'INBOX',
    labels TEXT[],
    customer_id INTEGER REFERENCES customers(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS billionmail.email_attachments (
    id SERIAL PRIMARY KEY,
    email_id INTEGER REFERENCES billionmail.email_messages(id),
    filename VARCHAR(500),
    content_type VARCHAR(100),
    file_size INTEGER,
    attachment_data BYTEA,
    is_processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🤝 Customer Interactions Table
CREATE TABLE IF NOT EXISTS customer_interactions (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    interaction_type VARCHAR(50), -- email, call, visit, service
    interaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    subject VARCHAR(500),
    description TEXT,
    sentiment VARCHAR(20) DEFAULT 'neutral', -- very_negative, negative, neutral, positive, very_positive
    hvac_relevance BOOLEAN DEFAULT false,
    urgency_level VARCHAR(20) DEFAULT 'normal',
    outcome VARCHAR(100),
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date TIMESTAMP,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📊 Dashboard Metrics Cache Table
CREATE TABLE IF NOT EXISTS dashboard_metrics_cache (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) UNIQUE,
    metric_value JSONB,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- 🔔 System Alerts Table
CREATE TABLE IF NOT EXISTS system_alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50), -- critical, warning, info
    title VARCHAR(200),
    message TEXT,
    source VARCHAR(100),
    is_acknowledged BOOLEAN DEFAULT false,
    acknowledged_by VARCHAR(100),
    acknowledged_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- 📈 Performance Metrics Table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(100),
    metric_type VARCHAR(100), -- cpu_usage, memory_usage, response_time, etc.
    metric_value DECIMAL(10,4),
    unit VARCHAR(20), -- %, ms, bytes, etc.
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data for testing
INSERT INTO customer_analytics (customer_id, lifetime_value, churn_probability, avg_satisfaction)
SELECT 
    id,
    RANDOM() * 10000 + 1000, -- Random lifetime value between 1000-11000
    RANDOM() * 0.3, -- Random churn probability 0-30%
    RANDOM() * 2 + 3 -- Random satisfaction 3-5
FROM customers
ON CONFLICT DO NOTHING;

-- Insert sample transcription data
INSERT INTO transcription.call_transcriptions (
    call_id, caller_phone, caller_company, call_duration, 
    transcription_text, confidence_score, hvac_relevance, urgency_level
) VALUES 
('CALL001', '+1234567890', 'ABC Corp', 300, 'Customer calling about HVAC maintenance', 95.5, true, 'normal'),
('CALL002', '+1234567891', 'XYZ Ltd', 180, 'Emergency HVAC repair needed', 92.3, true, 'critical'),
('CALL003', '+1234567892', 'HVAC Pro', 420, 'Routine service appointment scheduling', 88.7, true, 'normal')
ON CONFLICT (call_id) DO NOTHING;

-- Insert sample email data
INSERT INTO billionmail.email_messages (
    message_id, mailbox, sender_email, sender_name, subject, 
    body_text, has_attachments, folder
) VALUES 
('MSG001', '<EMAIL>', '<EMAIL>', 'John Smith', 'HVAC System Not Working', 'Our HVAC system stopped working this morning', false, 'INBOX'),
('MSG002', '<EMAIL>', '<EMAIL>', 'Jane Doe', 'Maintenance Contract Renewal', 'We need to renew our maintenance contract', false, 'INBOX'),
('MSG003', '<EMAIL>', '<EMAIL>', 'Mike Johnson', 'Parts Order Confirmation', 'Confirming parts order for HVAC repair', true, 'INBOX')
ON CONFLICT (message_id) DO NOTHING;

-- Insert sample customer interactions
INSERT INTO customer_interactions (
    customer_id, interaction_type, subject, description, 
    sentiment, hvac_relevance, urgency_level
) VALUES 
(1, 'email', 'Service Request', 'Customer requested routine maintenance', 'positive', true, 'normal'),
(1, 'call', 'Emergency Repair', 'Customer called about broken AC unit', 'negative', true, 'critical'),
(1, 'visit', 'Installation Complete', 'Successfully installed new HVAC system', 'very_positive', true, 'normal')
ON CONFLICT DO NOTHING;

-- Insert sample alerts
INSERT INTO system_alerts (alert_type, title, message, source) VALUES 
('warning', 'High Customer Churn Risk', '5 customers have high churn probability', 'customer_analytics'),
('info', 'Daily Backup Complete', 'Database backup completed successfully', 'system'),
('critical', 'Service Response Time High', 'Average response time exceeded threshold', 'performance_monitor')
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customer_analytics_customer_id ON customer_analytics(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_analytics_churn ON customer_analytics(churn_probability);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_timestamp ON transcription.call_transcriptions(call_timestamp);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_urgency ON transcription.call_transcriptions(urgency_level);
CREATE INDEX IF NOT EXISTS idx_email_messages_received ON billionmail.email_messages(received_date);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_date ON customer_interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_type ON customer_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_service ON performance_metrics(service_name, recorded_at);

-- Update customers table with sample contact dates
UPDATE customers 
SET last_contact_date = CURRENT_TIMESTAMP - (RANDOM() * INTERVAL '30 days')
WHERE last_contact_date IS NULL;

COMMIT;
