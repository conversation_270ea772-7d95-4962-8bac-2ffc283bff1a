#!/bin/bash

# 🧪 Bytebase Database Management Testing Script
# Tests all database management functionality in GoBackend HVAC Kratos

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "$test_name"
        ((TESTS_PASSED++))
    else
        print_error "$test_name"
        ((TESTS_FAILED++))
    fi
}

echo "🧪 =============================================="
echo "🗄️ Bytebase Database Management Testing Suite"
echo "🧪 =============================================="
echo ""

# Basic connectivity tests
print_status "🔍 Testing basic connectivity..."

run_test "Bytebase Health Check" "curl -f http://localhost:8092/healthz"
run_test "Bytebase API Status" "curl -f http://localhost:8092/api/v1/actuator/info"
run_test "PostgreSQL Connection" "docker-compose exec -T postgres pg_isready -U hvac_user -d hvac_db"

echo ""
print_status "🗄️ Testing database schema management..."

# Test schema validation
run_test "Validate Current Schema" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '\''public'\'''"

# Test table existence
run_test "Check Customers Table" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM customers'"
run_test "Check Jobs Table" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM jobs'"

# Test BillionMail schema
run_test "Check BillionMail Schema" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM billionmail.domains'"
run_test "Check Email Templates" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM billionmail.email_templates'"

echo ""
print_status "📊 Testing database performance and indexes..."

# Test index existence
run_test "Check Customer Email Index" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT indexname FROM pg_indexes WHERE tablename = '\''customers'\'' AND indexname = '\''idx_customers_email'\'''"
run_test "Check Job Status Index" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT indexname FROM pg_indexes WHERE tablename = '\''jobs'\'' AND indexname = '\''idx_jobs_status'\'''"

# Test triggers
run_test "Check Updated At Triggers" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name LIKE '\''%updated_at%'\'''"

echo ""
print_status "🔄 Testing migration capabilities..."

# Test migration file existence
run_test "Check Migration Files" "test -f migrations/001_initial_hvac_schema.sql && test -f migrations/002_billionmail_integration.sql"
run_test "Check Rollback Files" "test -f migrations/rollback/001_rollback_initial_hvac_schema.sql && test -f migrations/rollback/002_rollback_billionmail_integration.sql"

echo ""
print_status "🔍 Testing data integrity..."

# Test foreign key constraints
run_test "Test Customer-Job Relationship" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM jobs j JOIN customers c ON j.customer_id = c.id'"

# Test data validation
run_test "Test Job Status Constraints" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT DISTINCT status FROM jobs'"
run_test "Test Email Domain Constraints" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM billionmail.domains WHERE active = true'"

echo ""
print_status "🛠️ Testing Bytebase API functionality..."

# Test Bytebase API endpoints (if available)
run_test "Bytebase Instance Info" "curl -f http://localhost:8092/api/v1/instances"
run_test "Bytebase Database List" "curl -f http://localhost:8092/api/v1/databases"

echo ""
print_status "📈 Testing database monitoring..."

# Test database statistics
run_test "Database Size Check" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT pg_size_pretty(pg_database_size('\''hvac_db'\''))"
run_test "Table Statistics" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del FROM pg_stat_user_tables'"

echo ""
print_status "🔒 Testing security and permissions..."

# Test user permissions
run_test "Check User Permissions" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT has_table_privilege('\''hvac_user'\'', '\''customers'\'', '\''SELECT'\'')"
run_test "Check Schema Permissions" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT has_schema_privilege('\''hvac_user'\'', '\''billionmail'\'', '\''USAGE'\'')"

echo ""
print_status "🧪 Testing CRUD operations..."

# Test basic CRUD operations
run_test "Insert Test Customer" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'INSERT INTO customers (name, email, phone) VALUES ('\''Test Customer'\'', '\''<EMAIL>'\'', '\''+1234567890'\'') ON CONFLICT (email) DO NOTHING'"
run_test "Update Test Customer" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'UPDATE customers SET phone = '\''+0987654321'\'' WHERE email = '\''<EMAIL>'\'''"
run_test "Select Test Customer" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT id, name, email FROM customers WHERE email = '\''<EMAIL>'\'''"

echo ""
print_status "🔄 Testing backup and recovery readiness..."

# Test backup capabilities
run_test "Test Database Dump" "docker-compose exec -T postgres pg_dump -U hvac_user hvac_db --schema-only > /dev/null"
run_test "Test Schema Export" "docker-compose exec -T postgres pg_dump -U hvac_user hvac_db --data-only --table=customers > /dev/null"

echo ""
print_status "📊 Testing performance metrics..."

# Test query performance
run_test "Test Query Performance" "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'EXPLAIN ANALYZE SELECT c.name, COUNT(j.id) as job_count FROM customers c LEFT JOIN jobs j ON c.id = j.customer_id GROUP BY c.id, c.name'"

echo ""
echo "🎯 =============================================="
echo "📊 Test Results Summary"
echo "🎯 =============================================="
echo ""
echo -e "✅ Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "❌ Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "📊 Total Tests:  $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    print_success "🎉 ALL TESTS PASSED! Bytebase integration is working perfectly! 🚀🗄️"
    echo ""
    echo "🔥 Database management is enterprise-ready!"
    echo "🗄️ HVAC CRM + Bytebase = DATABASE POWERHOUSE! 💪"
else
    echo ""
    print_warning "⚠️  Some tests failed. Check the logs and configuration."
    echo ""
    echo "🔧 Troubleshooting tips:"
    echo "   1. Ensure all containers are running: docker-compose ps"
    echo "   2. Check Bytebase logs: docker-compose logs bytebase"
    echo "   3. Verify database connectivity: docker-compose exec postgres psql -U hvac_user -d hvac_db"
    echo "   4. Check migration files in ./migrations/"
fi

echo ""
print_status "📋 Service Status:"
docker-compose ps

echo ""
print_status "🔗 Useful URLs:"
echo "   🏠 HVAC Backend:      http://localhost:8080"
echo "   📧 BillionMail UI:    http://localhost:8090"
echo "   🗄️  Bytebase UI:       http://localhost:8092"
echo "   📊 Jaeger Tracing:    http://localhost:16686"

echo ""
print_status "🗄️ Bytebase Features:"
echo "   📊 Schema Browser:    http://localhost:8092/db"
echo "   📝 Migration Center:  http://localhost:8092/migration"
echo "   👥 User Management:   http://localhost:8092/setting/member"
echo "   🔒 Security Center:   http://localhost:8092/setting/security"

exit $TESTS_FAILED
