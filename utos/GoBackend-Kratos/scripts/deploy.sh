#!/bin/bash

# 🚀 GoBackend Kratos Deployment Script

set -e

echo "🚀 Deploying GoBackend HVAC Kratos..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_warning "Docker is not running. Starting Docker..."
    sudo systemctl start docker
fi

# Build Docker image
print_status "Building Docker image..."
docker build -t gobackend-hvac-kratos:latest .

# Start services with docker-compose
print_status "Starting services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Health check
print_status "Performing health check..."
if curl -f http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    print_success "Health check passed! 🎉"
else
    print_warning "Health check failed. Check logs with: docker-compose logs"
fi

print_success "Deployment completed! 🚀"
print_status "Services available at:"
print_status "  - HTTP API: http://localhost:8080"
print_status "  - gRPC API: localhost:9000"
print_status "  - MCP Server: localhost:8081"
print_status "  - Jaeger UI: http://localhost:16686"