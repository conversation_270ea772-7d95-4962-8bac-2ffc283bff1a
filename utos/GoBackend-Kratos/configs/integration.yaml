# HVAC-Remix Integration Configuration
integration:
  hvac_remix:
    enabled: true
    frontend_url: "http://localhost:3000"
    api_prefix: "/api/gobackend"
    websocket_path: "/ws"
    
  cors:
    allowed_origins:
      - "http://localhost:3000"
      - "https://hvac-remix.app"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]
    
  trpc:
    enabled: true
    endpoint: "/api/trpc"
    batch_requests: true
    max_batch_size: 10

# AI Services
ai:
  lm_studio:
    endpoint: "http://************:1234"
    model: "gemma-3-4b-it-qat"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30s
    
  copilotkit_bridge:
    enabled: true
    streaming: true
    context_window: 32000

# Real-time Features
realtime:
  websocket:
    enabled: true
    port: 8080
    path: "/ws"
    ping_interval: 30s
    
  notifications:
    job_updates: true
    system_alerts: true
    ai_analysis: true

# Monitoring
monitoring:
  metrics:
    enabled: true
    endpoint: "/metrics"
    
  health:
    enabled: true
    endpoint: "/health"
    
  tracing:
    enabled: true
    jaeger_endpoint: "http://localhost:14268/api/traces"
