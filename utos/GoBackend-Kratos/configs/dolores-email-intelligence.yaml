# 📧 Dolores Email Intelligence Configuration
# Comprehensive email and transcription analysis for HVAC CRM

# 🔌 IMAP Configuration for <PERSON> Account
dolores_imap:
  host: "imap.gmail.com"
  port: 993
  username: "<EMAIL>"
  password: "${DOLORES_EMAIL_PASSWORD}"
  mailbox: "INBOX"
  poll_interval: "5m"
  batch_size: 10
  enable_tls: true

# 🤖 AI Analysis Configuration
ai_analysis:
  # Gemma 3 Configuration
  gemma3:
    ollama_url: "http://localhost:1234"
    model_name: "gemma3:4b-instruct"
    max_tokens: 8192
    temperature: 0.7
    top_p: 0.9
    context_window: 128000
    timeout: "60s"
    retry_attempts: 3
    enable_vision: true
    image_resolution: 896

  # Email Analysis Settings
  email_analysis:
    max_attachment_size: ********  # 10MB
    supported_formats: ["txt", "docx", "pdf", "xlsx", "csv"]
    ollama_url: "http://localhost:1234"
    vector_db_path: "./data/dolores_vectordb"

# 📝 Transcription Processing
transcription:
  # File patterns that indicate transcription content
  file_patterns:
    - "transcript"
    - "transcription"
    - "call"
    - "recording"
    - "conversation"
    - "meeting"
    - "audio"
    - "voice"

  # Supported file extensions
  extensions:
    - ".txt"
    - ".docx"
    - ".pdf"

  # Processing settings
  max_segments: 1000
  confidence_threshold: 0.6
  speaker_detection: true

# 🏷️ NLP Pipeline Configuration
nlp:
  # Intent Classification
  intent_classes:
    - "inquiry"
    - "service_request"
    - "complaint"
    - "upsell_interest"
    - "emergency"
    - "follow_up"

  # Entity Recognition
  entities:
    hvac_equipment:
      - "air conditioning"
      - "ac"
      - "hvac"
      - "heat pump"
      - "furnace"
      - "boiler"
      - "ductwork"
      - "vents"
      - "thermostat"
      - "compressor"
      - "condenser"
      - "evaporator"
      - "split system"
      - "central air"
      - "mini split"
      - "ductless"

    urgency_indicators:
      - "urgent"
      - "emergency"
      - "asap"
      - "immediately"
      - "broken"
      - "not working"
      - "failed"
      - "stopped"

    technical_terms:
      - "refrigerant"
      - "freon"
      - "filter"
      - "maintenance"
      - "repair"
      - "installation"
      - "cooling"
      - "heating"
      - "ventilation"
      - "temperature"
      - "humidity"

# 👤 Customer Enrichment
customer_enrichment:
  # Communication style analysis
  communication_styles:
    - "direct"
    - "polite"
    - "technical"
    - "brief"
    - "detailed"
    - "standard"

  # Technical knowledge levels
  knowledge_levels:
    - "novice"
    - "basic"
    - "intermediate"
    - "advanced"
    - "expert"

  # Customer types
  customer_types:
    - "residential"
    - "commercial"
    - "industrial"

  # Satisfaction scoring
  satisfaction:
    positive_words: ["good", "great", "excellent", "satisfied", "happy", "pleased", "thank"]
    negative_words: ["bad", "terrible", "awful", "angry", "frustrated", "disappointed", "problem"]
    default_score: 5.0
    positive_score: 8.0
    negative_score: 3.0

# ⚡ Workflow Triggers
workflow_triggers:
  # High priority email trigger
  high_priority:
    conditions:
      priority: ["high", "urgent"]
    actions:
      - "notify_manager"
      - "create_urgent_ticket"
    delay: "0s"

  # Service request trigger
  service_request:
    conditions:
      intent: ["service_request"]
    actions:
      - "create_service_ticket"
      - "assign_technician"
    delay: "5m"

  # Complaint trigger
  complaint:
    conditions:
      intent: ["complaint"]
      sentiment: ["negative"]
    actions:
      - "escalate_to_manager"
      - "schedule_follow_up"
    delay: "0s"

  # Transcription follow-up
  transcription_follow_up:
    conditions:
      has_transcription: true
      follow_up_required: true
    actions:
      - "schedule_follow_up_call"
      - "send_summary_email"
    delay: "1h"

# 📊 Business Intelligence
business_intelligence:
  # Lead scoring weights
  lead_scoring:
    base_score: 50.0
    priority_weights:
      urgent: 30
      high: 20
      medium: 10
      low: 0
    sentiment_weights:
      positive: 15
      neutral: 0
      negative: -10
    transcription_bonus: 20
    service_request_bonus: 25

  # Revenue estimation
  revenue_estimation:
    base_service_call: 500.0
    equipment_work_bonus: 1000.0
    urgent_premium: 200.0
    multiple_request_multiplier: 300.0

  # Service categories
  service_categories:
    - "installation"
    - "repair"
    - "maintenance"
    - "emergency"
    - "general"

# 💾 Storage Configuration
storage:
  # Database settings
  database:
    type: "postgresql"
    connection_string: "*************************************************/hvacdb?sslmode=disable"
    table_prefix: "dolores_"

  # MinIO Object Storage
  minio:
    endpoint: "**************:9000"
    access_key_id: "koldbringer"
    secret_access_key: "Blaeritipol1"
    use_ssl: false
    region: "us-east-1"
    default_bucket: "dolores-attachments"

    # Performance settings
    part_size: 67108864        # 64MB
    max_retries: 3
    connect_timeout: "30s"
    request_timeout: "5m"

    # Security settings
    enable_encryption: true

    # Lifecycle settings
    default_expiration: "2y"

  # Hybrid Storage Strategy
  hybrid_strategy:
    small_file_threshold: 1048576  # 1MB - store in PostgreSQL
    large_file_storage: "minio"    # Use MinIO for large files
    metadata_storage: "database"   # Always store metadata in PostgreSQL
    enable_compression: true
    enable_deduplication: true

  # File storage (legacy/backup)
  file_storage:
    type: "minio"
    path: "./data/dolores_files"
    max_file_size: ********0  # 100MB

  # Retention policies
  retention:
    raw_emails: "1y"
    processed_results: "2y"
    analytics_data: "5y"
    temp_files: "7d"

# 📈 Monitoring & Metrics
monitoring:
  # Metrics collection
  metrics:
    enabled: true
    collection_interval: "1m"
    retention_period: "30d"

  # Health checks
  health_checks:
    imap_connection: "30s"
    ai_service: "60s"
    database: "30s"

  # Alerting
  alerts:
    email_processing_errors:
      threshold: 5
      window: "10m"
    high_processing_time:
      threshold: "5m"
      window: "1h"
    connection_failures:
      threshold: 3
      window: "5m"

# 🔒 Security
security:
  # Email security
  email:
    verify_ssl: true
    allowed_senders: []  # Empty means all senders allowed
    blocked_senders: []

  # Data encryption
  encryption:
    encrypt_stored_emails: true
    encryption_key: "${ENCRYPTION_KEY}"

  # Access control
  access_control:
    require_authentication: true
    allowed_roles: ["admin", "manager", "analyst"]

# 🚀 Performance
performance:
  # Concurrency settings
  concurrency:
    max_concurrent_emails: 5
    max_concurrent_ai_requests: 3
    worker_pool_size: 10

  # Caching
  caching:
    enabled: true
    ttl: "1h"
    max_size: 1000

  # Rate limiting
  rate_limiting:
    ai_requests_per_minute: 30
    email_processing_per_minute: 60

# 🔧 Development & Testing
development:
  # Debug settings
  debug:
    enabled: false
    log_level: "info"
    log_ai_requests: false
    log_email_content: false

  # Testing
  testing:
    mock_ai_responses: false
    test_email_account: "<EMAIL>"
    dry_run_mode: false
