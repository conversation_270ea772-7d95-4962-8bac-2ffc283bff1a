# 🧠 Executive AI Assistant Configuration
# Comprehensive AI-powered email automation and management system

# ==========================================
# AI MODELS CONFIGURATION
# ==========================================
ai_models:
  primary_model: "gemma-3-4b-it"
  fallback_model: "gemma-3b-instruct"
  confidence_threshold: 0.7
  
  # LM Studio Integration
  lm_studio:
    endpoint: "http://localhost:1234"
    model_name: "gemma-3-4b-it-qat"
    max_tokens: 4096
    temperature: 0.3
    timeout_seconds: 30
  
  # Bielik V3 Integration
  bielik:
    endpoint: "http://localhost:8080/bielik"
    model_name: "bielik-v3"
    max_tokens: 2048
    temperature: 0.2
    timeout_seconds: 25

# ==========================================
# TRIAGE CONFIGURATION
# ==========================================
triage:
  # Decision Thresholds
  auto_respond_threshold: 0.85
  escalation_threshold: 0.9
  confidence_threshold: 0.7
  
  # Processing Limits
  max_processing_time_ms: 30000
  max_concurrent_analysis: 10
  retry_attempts: 3
  
  # HVAC-Specific Settings
  hvac_keywords:
    emergency: ["emergency", "urgent", "broken", "not working", "no heat", "no cooling", "leak", "flooding"]
    repair: ["repair", "fix", "broken", "problem", "issue", "malfunction", "not working"]
    maintenance: ["maintenance", "service", "tune-up", "check", "inspection", "clean", "filter"]
    installation: ["install", "new", "replace", "upgrade", "setup", "installation"]
    quote: ["quote", "estimate", "price", "cost", "how much", "pricing"]
  
  equipment_types:
    ac: ["air conditioning", "ac", "a/c", "cooling", "conditioner", "central air"]
    heating: ["heating", "furnace", "boiler", "heat pump", "heater", "radiator"]
    ventilation: ["ventilation", "vent", "ductwork", "air flow", "exhaust", "fan"]
    thermostat: ["thermostat", "temperature control", "programmable", "smart thermostat"]
  
  urgency_indicators: ["emergency", "urgent", "asap", "immediately", "critical", "broken", "flooding", "no heat", "no cooling"]

# ==========================================
# RESPONSE GENERATION CONFIGURATION
# ==========================================
response_generation:
  # Default Settings
  default_tone: "professional"
  max_response_length: 2000
  min_response_length: 50
  require_approval: true
  
  # Quality Thresholds
  min_quality_score: 0.7
  min_relevance_score: 0.6
  min_tone_match_score: 0.7
  
  # Templates
  use_templates: true
  template_enhancement: true
  
  # Response Tones
  available_tones:
    - "professional"
    - "friendly"
    - "formal"
    - "casual"
    - "urgent"
  
  # Auto-Send Conditions
  auto_send_conditions:
    emergency_response: true
    quote_acknowledgment: false
    appointment_confirmation: false
    complaint_response: false

# ==========================================
# MEMORY BANK CONFIGURATION
# ==========================================
memory_bank:
  # Retention Settings
  memory_retention_days: 365
  min_confidence_for_memory: 0.6
  max_memories_per_entity: 100
  
  # Memory Types
  memory_types:
    customer_preference:
      retention_days: 730
      min_confidence: 0.7
    communication_pattern:
      retention_days: 365
      min_confidence: 0.6
    business_rule:
      retention_days: 1095
      min_confidence: 0.8
    contact_info:
      retention_days: 1095
      min_confidence: 0.9
    recurring_issue:
      retention_days: 365
      min_confidence: 0.8
    important_detail:
      retention_days: 730
      min_confidence: 0.7
  
  # Cleanup Settings
  cleanup_interval_hours: 24
  low_relevance_threshold: 0.1
  inactive_memory_threshold_days: 180

# ==========================================
# WORKFLOW AUTOMATION CONFIGURATION
# ==========================================
workflow_automation:
  # General Settings
  enable_automation: true
  max_concurrent_workflows: 5
  workflow_timeout_seconds: 120
  
  # Default Workflow Rules
  default_rules:
    emergency_escalation:
      enabled: true
      priority: 1
      conditions:
        - field: "hvac_service_type"
          operator: "equals"
          value: "emergency"
        - field: "urgency_score"
          operator: "greater_than"
          value: 0.8
      actions:
        - type: "escalate"
          target: "emergency_team"
        - type: "notify"
          recipients: ["<EMAIL>"]
        - type: "priority"
          level: "urgent"
    
    complaint_handling:
      enabled: true
      priority: 2
      conditions:
        - field: "sentiment"
          operator: "in"
          value: ["negative", "angry"]
        - field: "detected_intent"
          operator: "contains"
          value: "complaint"
      actions:
        - type: "draft_response"
          template: "complaint_acknowledgment"
        - type: "escalate"
          target: "customer_service"
        - type: "schedule_follow_up"
          hours: 24
    
    quote_processing:
      enabled: true
      priority: 3
      conditions:
        - field: "detected_intent"
          operator: "contains"
          value: "quote"
        - field: "hvac_service_type"
          operator: "in"
          value: ["installation", "repair"]
      actions:
        - type: "create_lead"
          source: "email"
        - type: "draft_response"
          template: "quote_request"
        - type: "assign"
          team: "sales"

# ==========================================
# CALENDAR INTEGRATION CONFIGURATION
# ==========================================
calendar_integration:
  # Default Settings
  default_meeting_duration_minutes: 30
  business_hours_start: "08:00"
  business_hours_end: "17:00"
  timezone: "America/New_York"
  
  # Meeting Types
  meeting_types:
    hvac_consultation:
      duration_minutes: 60
      buffer_minutes: 15
    site_visit:
      duration_minutes: 120
      buffer_minutes: 30
    follow_up:
      duration_minutes: 30
      buffer_minutes: 15
    emergency:
      duration_minutes: 45
      buffer_minutes: 0
  
  # Availability Settings
  max_meetings_per_day: 8
  min_notice_hours: 2
  max_advance_days: 30
  
  # Integration Settings
  calendar_provider: "google" # google, outlook, caldav
  sync_interval_minutes: 15
  auto_create_events: false

# ==========================================
# PERFORMANCE & MONITORING CONFIGURATION
# ==========================================
performance:
  # Metrics Collection
  enable_metrics_collection: true
  metrics_retention_days: 90
  metrics_aggregation_interval_minutes: 15
  
  # Performance Targets
  target_response_time_ms: 5000
  target_triage_accuracy: 0.95
  target_draft_quality: 0.85
  target_automation_rate: 0.80
  
  # Monitoring
  health_check_interval_seconds: 30
  alert_thresholds:
    error_rate: 0.05
    response_time_ms: 10000
    queue_size: 100
    memory_usage_percent: 85
  
  # Logging
  log_level: "info"
  log_format: "json"
  log_retention_days: 30

# ==========================================
# SECURITY CONFIGURATION
# ==========================================
security:
  # Authentication
  require_authentication: true
  jwt_secret: "${JWT_SECRET}"
  token_expiry_hours: 24
  
  # Authorization
  rbac_enabled: true
  admin_roles: ["admin", "manager"]
  user_roles: ["user", "technician"]
  
  # Data Protection
  encrypt_sensitive_data: true
  mask_email_addresses: false
  audit_log_enabled: true
  
  # Rate Limiting
  rate_limit_enabled: true
  requests_per_minute: 100
  burst_limit: 200

# ==========================================
# DATABASE CONFIGURATION
# ==========================================
database:
  # Connection Settings
  host: "**************"
  port: 5432
  database: "hvacdb"
  username: "hvacdb"
  password: "blaeritipol"
  sslmode: "disable"
  
  # Pool Settings
  max_open_connections: 25
  max_idle_connections: 10
  connection_max_lifetime_minutes: 60
  
  # Performance
  query_timeout_seconds: 30
  transaction_timeout_seconds: 60
  
  # Migrations
  auto_migrate: true
  migration_path: "./migrations"

# ==========================================
# EXTERNAL INTEGRATIONS
# ==========================================
integrations:
  # Email Service
  email_service:
    smtp_host: "serwer2440139.home.pl"
    smtp_port: 587
    smtp_username: "<EMAIL>"
    smtp_password: "Blaeritipol1"
    from_address: "<EMAIL>"
    from_name: "HVAC AI Assistant"
  
  # Notification Services
  notifications:
    slack:
      enabled: false
      webhook_url: ""
      channel: "#hvac-alerts"
    
    teams:
      enabled: false
      webhook_url: ""
    
    email:
      enabled: true
      alert_recipients: ["<EMAIL>"]
  
  # External APIs
  external_apis:
    weather_api:
      enabled: true
      api_key: "${WEATHER_API_KEY}"
      endpoint: "https://api.openweathermap.org/data/2.5"
    
    maps_api:
      enabled: true
      api_key: "${MAPS_API_KEY}"
      endpoint: "https://maps.googleapis.com/maps/api"

# ==========================================
# DEVELOPMENT & TESTING CONFIGURATION
# ==========================================
development:
  # Debug Settings
  debug_mode: false
  verbose_logging: false
  mock_ai_responses: false
  
  # Testing
  test_mode: false
  test_email_domain: "test.hvac.com"
  test_data_retention_hours: 24
  
  # Development Tools
  enable_profiling: false
  enable_tracing: false
  hot_reload: false

# ==========================================
# FEATURE FLAGS
# ==========================================
feature_flags:
  # Core Features
  email_triage: true
  response_drafting: true
  memory_bank: true
  workflow_automation: true
  calendar_integration: true
  
  # Advanced Features
  sentiment_analysis: true
  intent_detection: true
  entity_extraction: true
  pattern_recognition: true
  predictive_analytics: false
  
  # Experimental Features
  voice_processing: false
  image_analysis: false
  multilingual_support: false
  advanced_nlp: false

# ==========================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# ==========================================
environments:
  development:
    ai_models:
      confidence_threshold: 0.5
    triage:
      auto_respond_threshold: 0.7
    response_generation:
      require_approval: false
    performance:
      log_level: "debug"
  
  staging:
    ai_models:
      confidence_threshold: 0.6
    triage:
      auto_respond_threshold: 0.8
    response_generation:
      require_approval: true
    performance:
      log_level: "info"
  
  production:
    ai_models:
      confidence_threshold: 0.7
    triage:
      auto_respond_threshold: 0.85
    response_generation:
      require_approval: true
    performance:
      log_level: "warn"
      enable_metrics_collection: true