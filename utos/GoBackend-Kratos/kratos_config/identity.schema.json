{"$id": "https://schemas.ory.sh/presets/kratos/identity.email.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "HVAC CRM User Identity", "type": "object", "properties": {"traits": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "title": "E-Mail", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}, "verification": {"via": "email"}, "recovery": {"via": "email"}}}, "name": {"type": "object", "properties": {"first": {"title": "First Name", "type": "string"}, "last": {"title": "Last Name", "type": "string"}}}, "company": {"type": "string", "title": "Company Name"}, "phone": {"type": "string", "title": "Phone Number"}, "role": {"type": "string", "title": "User Role", "enum": ["admin", "technician", "customer", "manager"], "default": "customer"}, "hvac_preferences": {"type": "object", "properties": {"service_area": {"type": "string", "title": "Service Area"}, "preferred_contact": {"type": "string", "enum": ["email", "phone", "sms"], "default": "email"}, "system_type": {"type": "string", "enum": ["residential", "commercial", "industrial"], "default": "residential"}}}}, "required": ["email"], "additionalProperties": false}}}