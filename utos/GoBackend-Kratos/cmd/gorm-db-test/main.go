package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 GORM PostgreSQL Connection Test")
	fmt.Println("===================================")

	// Database connection string (external server with correct credentials)
	dsn := "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable"

	// Open database connection with GORM
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Successfully connected to PostgreSQL with GORM!")

	// Test query
	var result struct {
		Version string
	}

	err = db.Raw("SELECT version() as version").Scan(&result).Error
	if err != nil {
		log.Fatalf("Failed to execute test query: %v", err)
	}

	fmt.Printf("📊 PostgreSQL Version: %s\n", result.Version)

	// Test creating a simple table
	type TestTable struct {
		ID   uint   `gorm:"primaryKey"`
		Name string `gorm:"size:100"`
	}

	err = db.AutoMigrate(&TestTable{})
	if err != nil {
		log.Fatalf("Failed to migrate test table: %v", err)
	}

	fmt.Println("✅ Successfully created test table!")

	// Insert test data
	testRecord := TestTable{Name: "HVAC Test Record"}
	err = db.Create(&testRecord).Error
	if err != nil {
		log.Fatalf("Failed to insert test record: %v", err)
	}

	fmt.Printf("✅ Successfully inserted test record with ID: %d\n", testRecord.ID)

	// Query test data
	var count int64
	err = db.Model(&TestTable{}).Count(&count).Error
	if err != nil {
		log.Fatalf("Failed to count records: %v", err)
	}

	fmt.Printf("📊 Total records in test table: %d\n", count)

	// Clean up - drop test table
	err = db.Migrator().DropTable(&TestTable{})
	if err != nil {
		log.Printf("Warning: Failed to drop test table: %v", err)
	} else {
		fmt.Println("🧹 Successfully cleaned up test table!")
	}

	fmt.Println("🎉 All database tests passed successfully!")
}
