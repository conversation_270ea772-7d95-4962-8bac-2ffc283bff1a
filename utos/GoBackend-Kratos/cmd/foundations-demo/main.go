package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gobackend-hvac-kratos/internal/foundations"
	"gobackend-hvac-kratos/internal/foundations/validation"

	"go.uber.org/zap"
)

func main() {
	fmt.Println("🚀 HVAC CRM - Foundations Demo Starting...")

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize foundation manager with default config
	config := foundations.DefaultFoundationConfig()

	// Override some config values from environment
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.Auth.JWTSecret = jwtSecret
	}

	if minioEndpoint := os.Getenv("MINIO_ENDPOINT"); minioEndpoint != "" {
		config.Storage.Endpoint = minioEndpoint
	}

	if minioAccessKey := os.Getenv("MINIO_ACCESS_KEY"); minioAccessKey != "" {
		config.Storage.AccessKeyID = minioAccessKey
	}

	if minioSecretKey := os.Getenv("MINIO_SECRET_KEY"); minioSecretKey != "" {
		config.Storage.SecretAccessKey = minioSecretKey
	}

	// Create foundation manager
	manager, err := foundations.NewFoundationManager(config)
	if err != nil {
		log.Fatalf("Failed to create foundation manager: %v", err)
	}

	// Setup web server
	if err := manager.SetupWebServer(); err != nil {
		log.Fatalf("Failed to setup web server: %v", err)
	}

	// Start all services
	if err := manager.StartServices(ctx); err != nil {
		log.Fatalf("Failed to start services: %v", err)
	}

	// Start web server in goroutine
	go func() {
		manager.Logger.Info("Starting web server on port " + config.Web.Port)
		if err := manager.Web.StartServer(); err != nil {
			manager.Logger.Error("Web server failed", zap.Error(err))
		}
	}()

	// Demo some functionality
	demoFoundations(manager)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	manager.Logger.Info("🎯 HVAC CRM Foundations Demo is running!")
	manager.Logger.Info("📊 Health check: http://localhost:" + config.Web.Port + "/api/v1/public/health")
	manager.Logger.Info("🔧 API endpoints available at: http://localhost:" + config.Web.Port + "/api/v1/")
	manager.Logger.Info("Press Ctrl+C to stop...")

	<-sigChan
	manager.Logger.Info("Shutdown signal received, stopping services...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := manager.StopServices(shutdownCtx); err != nil {
		manager.Logger.Error("Error during shutdown", zap.Error(err))
	}

	manager.Logger.Info("🏁 HVAC CRM Foundations Demo stopped successfully")
}

func demoFoundations(manager *foundations.FoundationManager) {
	logger := manager.Logger

	logger.Info("🔍 Demonstrating Foundation Capabilities...")

	// Demo Auth Foundation
	if manager.Auth != nil {
		logger.Info("🔐 Auth Foundation Demo")

		// Generate JWT token
		token, err := manager.Auth.JWT.GenerateToken("user123", "<EMAIL>", "admin", "tenant1")
		if err != nil {
			logger.Error("Failed to generate token", zap.Error(err))
		} else {
			logger.Info("✅ JWT token generated successfully", zap.String("token_preview", token[:20]+"..."))
		}

		// Hash password
		hashedPassword, err := manager.Auth.Password.HashPassword("testpassword123")
		if err != nil {
			logger.Error("Failed to hash password", zap.Error(err))
		} else {
			logger.Info("✅ Password hashed successfully")

			// Verify password
			if manager.Auth.Password.CheckPassword("testpassword123", hashedPassword) {
				logger.Info("✅ Password verification successful")
			}
		}
	}

	// Demo Storage Foundation
	if manager.Storage != nil {
		logger.Info("💾 Storage Foundation Demo")

		// Test MinIO connection by listing buckets
		ctx := context.Background()
		files, err := manager.Storage.MinIO.ListFiles(ctx, "", "")
		if err != nil {
			logger.Warn("MinIO connection test failed", zap.Error(err))
		} else {
			logger.Info("✅ MinIO connection successful", zap.Int("files_count", len(files)))
		}
	}

	// Demo Validation Foundation
	if manager.Validation != nil {
		logger.Info("✅ Validation Foundation Demo")

		// Test customer validation
		customerReq := &validation.CustomerRequest{
			FirstName:  "John",
			LastName:   "Doe",
			Email:      "<EMAIL>",
			Phone:      "123456789",
			Address:    "123 Main St",
			City:       "Warsaw",
			PostalCode: "00-001",
		}

		result := manager.Validation.ValidateCustomerRequest(customerReq)
		if result.Valid {
			logger.Info("✅ Customer validation passed")
		} else {
			logger.Info("❌ Customer validation failed", zap.Int("errors", len(result.Errors)))
		}
	}

	// Demo Email Foundation
	if manager.Email != nil {
		logger.Info("📧 Email Foundation Demo")

		// Test email connection (without actually connecting to avoid errors)
		logger.Info("✅ Email service initialized (connection test skipped in demo)")
	}

	// Demo Workflow Foundation
	if manager.Workflow != nil {
		logger.Info("⚡ Workflow Foundation Demo")

		if manager.Workflow.BackgroundJob != nil {
			logger.Info("✅ Background job service initialized")
		}

		if manager.Workflow.Temporal != nil {
			logger.Info("✅ Temporal workflow service initialized")
		}
	}

	// Demo Documents Foundation
	if manager.Documents != nil {
		logger.Info("📄 Documents Foundation Demo")
		logger.Info("✅ PDF processing service initialized")
	}

	// Demo Monitoring Foundation
	if manager.Monitoring != nil {
		logger.Info("📊 Monitoring Foundation Demo")

		// Record some demo metrics
		manager.Monitoring.Metrics.RecordCustomer()
		manager.Monitoring.Metrics.RecordServiceTicket("new", "high")
		manager.Monitoring.Metrics.RecordEmailProcessed("customer", "success")
		manager.Monitoring.Metrics.RecordWorkflow("onboarding", "completed")

		logger.Info("✅ Demo metrics recorded")
	}

	logger.Info("🎉 Foundation demonstration completed!")
	logger.Info("📈 Status:", zap.Any("foundations", manager.GetStatus()))
}
