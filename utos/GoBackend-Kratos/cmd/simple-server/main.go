package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// TRPCResponse represents a tRPC-style response
type TRPCResponse struct {
	Result interface{} `json:"result"`
	Error  *TRPCError  `json:"error,omitempty"`
}

// TRPCError represents a tRPC error
type TRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Customer represents a customer
type Customer struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// Job represents a job
type Job struct {
	ID          string    `json:"id"`
	CustomerID  string    `json:"customerId"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	ScheduledAt *time.Time `json:"scheduledDate,omitempty"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Data  interface{} `json:"data"`
	Total int32       `json:"total"`
	Page  int32       `json:"page"`
	Limit int32       `json:"limit"`
}

// Mock data
var mockCustomers = []*Customer{
	{
		ID:        "1",
		Name:      "Jan Kowalski",
		Email:     "<EMAIL>",
		Phone:     "+48 123 456 789",
		Address:   "ul. Testowa 1, 00-001 Warszawa",
		Status:    "active",
		CreatedAt: time.Now().Add(-24 * time.Hour),
		UpdatedAt: time.Now(),
	},
	{
		ID:        "2",
		Name:      "Anna Nowak",
		Email:     "<EMAIL>",
		Phone:     "+48 987 654 321",
		Address:   "ul. Przykładowa 2, 00-002 Kraków",
		Status:    "active",
		CreatedAt: time.Now().Add(-48 * time.Hour),
		UpdatedAt: time.Now(),
	},
	{
		ID:        "3",
		Name:      "Piotr Wiśniewski",
		Email:     "<EMAIL>",
		Phone:     "+48 555 123 456",
		Address:   "ul. Klimatyczna 15, 30-001 Kraków",
		Status:    "active",
		CreatedAt: time.Now().Add(-72 * time.Hour),
		UpdatedAt: time.Now(),
	},
	{
		ID:        "4",
		Name:      "Maria Dąbrowska",
		Email:     "<EMAIL>",
		Phone:     "+48 666 789 012",
		Address:   "ul. Wentylacyjna 8, 50-001 Wrocław",
		Status:    "active",
		CreatedAt: time.Now().Add(-96 * time.Hour),
		UpdatedAt: time.Now(),
	},
	{
		ID:        "5",
		Name:      "Tomasz Lewandowski",
		Email:     "<EMAIL>",
		Phone:     "+48 777 345 678",
		Address:   "ul. Grzewcza 22, 80-001 Gdańsk",
		Status:    "inactive",
		CreatedAt: time.Now().Add(-120 * time.Hour),
		UpdatedAt: time.Now(),
	},
}

var mockJobs = []*Job{
	{
		ID:          "1",
		CustomerID:  "1",
		Title:       "Naprawa klimatyzacji",
		Description: "Klimatyzacja nie chłodzi prawidłowo",
		Status:      "pending",
		Priority:    "high",
		CreatedAt:   time.Now().Add(-2 * time.Hour),
		UpdatedAt:   time.Now(),
	},
	{
		ID:          "2",
		CustomerID:  "2",
		Title:       "Przegląd systemu HVAC",
		Description: "Rutynowy przegląd systemu wentylacji",
		Status:      "scheduled",
		Priority:    "medium",
		CreatedAt:   time.Now().Add(-1 * time.Hour),
		UpdatedAt:   time.Now(),
	},
	{
		ID:          "3",
		CustomerID:  "3",
		Title:       "Instalacja nowej klimatyzacji",
		Description: "Montaż systemu klimatyzacji w biurze",
		Status:      "in_progress",
		Priority:    "high",
		CreatedAt:   time.Now().Add(-3 * time.Hour),
		UpdatedAt:   time.Now(),
	},
	{
		ID:          "4",
		CustomerID:  "4",
		Title:       "Naprawa systemu grzewczego",
		Description: "Awaria pompy ciepła",
		Status:      "pending",
		Priority:    "urgent",
		CreatedAt:   time.Now().Add(-30 * time.Minute),
		UpdatedAt:   time.Now(),
	},
	{
		ID:          "5",
		CustomerID:  "1",
		Title:       "Konserwacja filtrów",
		Description: "Wymiana filtrów w systemie wentylacji",
		Status:      "completed",
		Priority:    "low",
		CreatedAt:   time.Now().Add(-6 * time.Hour),
		UpdatedAt:   time.Now(),
	},
}

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// Helper function to send tRPC response
func sendTRPCResponse(w http.ResponseWriter, result interface{}, err error) {
	w.Header().Set("Content-Type", "application/json")

	response := TRPCResponse{}

	if err != nil {
		response.Error = &TRPCError{
			Code:    500,
			Message: err.Error(),
		}
		w.WriteHeader(http.StatusInternalServerError)
	} else {
		response.Result = result
		w.WriteHeader(http.StatusOK)
	}

	json.NewEncoder(w).Encode(response)
}

// Customer handlers
func handleCustomerList(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling customer.list tRPC call")

	result := PaginatedResponse{
		Data:  mockCustomers,
		Total: int32(len(mockCustomers)),
		Page:  1,
		Limit: 10,
	}

	sendTRPCResponse(w, result, nil)
}

func handleCustomerGet(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling customer.get tRPC call")

	// For demo, return first customer
	if len(mockCustomers) > 0 {
		sendTRPCResponse(w, mockCustomers[0], nil)
	} else {
		sendTRPCResponse(w, nil, fmt.Errorf("customer not found"))
	}
}

// Job handlers
func handleJobList(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling job.list tRPC call")

	result := PaginatedResponse{
		Data:  mockJobs,
		Total: int32(len(mockJobs)),
		Page:  1,
		Limit: 10,
	}

	sendTRPCResponse(w, result, nil)
}

func handleJobGet(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling job.get tRPC call")

	// For demo, return first job
	if len(mockJobs) > 0 {
		sendTRPCResponse(w, mockJobs[0], nil)
	} else {
		sendTRPCResponse(w, nil, fmt.Errorf("job not found"))
	}
}

// System health handler
func handleSystemHealth(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling system.health tRPC call")

	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"services": map[string]string{
			"database": "mocked",
			"grpc":     "mocked",
			"trpc":     "active",
		},
	}

	sendTRPCResponse(w, health, nil)
}

// Analytics handler
func handleAnalyticsMetrics(w http.ResponseWriter, r *http.Request) {
	log.Println("🔍 Handling analytics.metrics tRPC call")

	// Calculate metrics from mock data
	totalCustomers := len(mockCustomers)
	activeCustomers := 0
	for _, customer := range mockCustomers {
		if customer.Status == "active" {
			activeCustomers++
		}
	}

	totalJobs := len(mockJobs)
	activeJobs := 0
	completedJobs := 0
	pendingJobs := 0

	for _, job := range mockJobs {
		switch job.Status {
		case "in_progress", "scheduled":
			activeJobs++
		case "completed":
			completedJobs++
		case "pending":
			pendingJobs++
		}
	}

	metrics := map[string]interface{}{
		"totalCustomers":    totalCustomers,
		"activeCustomers":   activeCustomers,
		"totalJobs":         totalJobs,
		"activeJobs":        activeJobs,
		"completedJobs":     completedJobs,
		"pendingJobs":       pendingJobs,
		"completedToday":    2, // Mock value
		"aiInsights":        15, // Mock value
		"revenue": map[string]interface{}{
			"today":  12500.00,
			"month":  85000.00,
			"trend":  8.5,
		},
		"satisfaction": map[string]interface{}{
			"average": 4.7,
			"trend":   5.2,
		},
		"lastUpdated": time.Now(),
	}

	sendTRPCResponse(w, metrics, nil)
}

func main() {
	router := mux.NewRouter()

	// Apply CORS middleware
	router.Use(corsMiddleware)

	// tRPC endpoints
	router.HandleFunc("/api/trpc/customer.list", handleCustomerList).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/customer.get", handleCustomerGet).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/job.list", handleJobList).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/job.get", handleJobGet).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/system.health", handleSystemHealth).Methods("POST", "OPTIONS")
	router.HandleFunc("/api/trpc/analytics.metrics", handleAnalyticsMetrics).Methods("POST", "OPTIONS")

	// Health check endpoint
	router.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
	}).Methods("GET")

	log.Println("🚀 Simple tRPC Bridge Server starting on :8081")
	log.Println("📡 tRPC endpoints available at /api/trpc/*")
	log.Println("🏥 Health check available at /health")

	if err := http.ListenAndServe(":8081", router); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
