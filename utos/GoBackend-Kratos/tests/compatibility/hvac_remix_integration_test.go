package compatibility

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/gorilla/websocket"
)

// 🧪 HVAC-Remix Integration Test Suite
// Tests compatibility between GoBackend-Kratos and hvac-remix frontend

type HVACRemixCompatibilityTest struct {
	server     *httptest.Server
	wsUpgrader websocket.Upgrader
	testData   *TestDataSet
}

type TestDataSet struct {
	Customers []TestCustomer `json:"customers"`
	Jobs      []TestJob      `json:"jobs"`
	AIRequests []TestAIRequest `json:"ai_requests"`
	Emails    []TestEmail    `json:"emails"`
}

type TestCustomer struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Address  string `json:"address"`
	Type     string `json:"type"` // residential, commercial, industrial
}

type TestJob struct {
	ID          string    `json:"id"`
	CustomerID  string    `json:"customerId"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	Type        string    `json:"type"`
	ScheduledDate *time.Time `json:"scheduledDate,omitempty"`
}

type TestAIRequest struct {
	Description    string                 `json:"description"`
	CustomerHistory map[string]interface{} `json:"customerHistory,omitempty"`
	UrgencyLevel   string                 `json:"urgencyLevel"`
	Context        map[string]interface{} `json:"context"`
}

type TestEmail struct {
	ID      string `json:"id"`
	From    string `json:"from"`
	To      []string `json:"to"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

// 🚀 Test Suite Setup
func TestHVACRemixCompatibility(t *testing.T) {
	suite := &HVACRemixCompatibilityTest{
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		testData: generateTestData(),
	}

	// Setup test server
	suite.setupTestServer()
	defer suite.server.Close()

	t.Run("API_Endpoints_Compatibility", suite.testAPIEndpointsCompatibility)
	t.Run("Data_Types_Compatibility", suite.testDataTypesCompatibility)
	t.Run("Real_Time_Communication", suite.testRealTimeCommunication)
	t.Run("AI_Service_Integration", suite.testAIServiceIntegration)
	t.Run("Error_Handling_Compatibility", suite.testErrorHandlingCompatibility)
	t.Run("Performance_Requirements", suite.testPerformanceRequirements)
}

// 🔧 Test Server Setup
func (suite *HVACRemixCompatibilityTest) setupTestServer() {
	mux := http.NewServeMux()

	// Customer endpoints
	mux.HandleFunc("/api/v1/customers", suite.handleCustomers)
	mux.HandleFunc("/api/v1/customers/", suite.handleCustomerByID)

	// Job endpoints
	mux.HandleFunc("/api/v1/jobs", suite.handleJobs)
	mux.HandleFunc("/api/v1/jobs/", suite.handleJobByID)

	// AI endpoints
	mux.HandleFunc("/api/v1/ai/analyze", suite.handleAIAnalyze)
	mux.HandleFunc("/api/v1/ai/chat", suite.handleAIChat)

	// Email endpoints
	mux.HandleFunc("/api/v1/emails", suite.handleEmails)
	mux.HandleFunc("/api/v1/emails/process", suite.handleEmailProcess)

	// System endpoints
	mux.HandleFunc("/api/v1/system/health", suite.handleSystemHealth)
	mux.HandleFunc("/api/v1/system/metrics", suite.handleSystemMetrics)

	// WebSocket endpoint
	mux.HandleFunc("/ws/realtime", suite.handleWebSocket)

	suite.server = httptest.NewServer(mux)
}

// 📡 API Endpoints Compatibility Tests
func (suite *HVACRemixCompatibilityTest) testAPIEndpointsCompatibility(t *testing.T) {
	baseURL := suite.server.URL

	t.Run("Customer_API_Compatibility", func(t *testing.T) {
		// Test GET /api/v1/customers
		resp, err := http.Get(baseURL + "/api/v1/customers")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))

		var customers []TestCustomer
		err = json.NewDecoder(resp.Body).Decode(&customers)
		require.NoError(t, err)
		assert.NotEmpty(t, customers)

		// Validate customer structure matches hvac-remix expectations
		customer := customers[0]
		assert.NotEmpty(t, customer.ID)
		assert.NotEmpty(t, customer.Name)
		assert.NotEmpty(t, customer.Email)
		assert.Contains(t, []string{"residential", "commercial", "industrial"}, customer.Type)
	})

	t.Run("Job_API_Compatibility", func(t *testing.T) {
		// Test GET /api/v1/jobs
		resp, err := http.Get(baseURL + "/api/v1/jobs")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var jobs []TestJob
		err = json.NewDecoder(resp.Body).Decode(&jobs)
		require.NoError(t, err)
		assert.NotEmpty(t, jobs)

		// Validate job structure
		job := jobs[0]
		assert.NotEmpty(t, job.ID)
		assert.NotEmpty(t, job.CustomerID)
		assert.Contains(t, []string{"pending", "scheduled", "in_progress", "completed", "cancelled"}, job.Status)
		assert.Contains(t, []string{"low", "medium", "high", "urgent"}, job.Priority)
		assert.Contains(t, []string{"installation", "maintenance", "repair", "inspection", "emergency"}, job.Type)
	})

	t.Run("AI_API_Compatibility", func(t *testing.T) {
		// Test AI analysis endpoint
		aiRequest := suite.testData.AIRequests[0]
		requestBody, _ := json.Marshal(aiRequest)

		resp, err := http.Post(
			baseURL+"/api/v1/ai/analyze",
			"application/json",
			strings.NewReader(string(requestBody)),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var aiResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&aiResponse)
		require.NoError(t, err)

		// Validate AI response structure matches hvac-remix expectations
		assert.Contains(t, aiResponse, "analysis")
		assert.Contains(t, aiResponse, "confidence")
		assert.Contains(t, aiResponse, "category")
		assert.Contains(t, aiResponse, "urgency")
		assert.Contains(t, aiResponse, "recommendations")

		confidence, ok := aiResponse["confidence"].(float64)
		assert.True(t, ok)
		assert.GreaterOrEqual(t, confidence, 0.0)
		assert.LessOrEqual(t, confidence, 1.0)
	})
}

// 🔄 Data Types Compatibility Tests
func (suite *HVACRemixCompatibilityTest) testDataTypesCompatibility(t *testing.T) {
	t.Run("Customer_Type_Compatibility", func(t *testing.T) {
		customer := suite.testData.Customers[0]

		// Test JSON serialization/deserialization
		jsonData, err := json.Marshal(customer)
		require.NoError(t, err)

		var deserializedCustomer TestCustomer
		err = json.Unmarshal(jsonData, &deserializedCustomer)
		require.NoError(t, err)

		assert.Equal(t, customer, deserializedCustomer)
	})

	t.Run("Job_Type_Compatibility", func(t *testing.T) {
		job := suite.testData.Jobs[0]

		jsonData, err := json.Marshal(job)
		require.NoError(t, err)

		var deserializedJob TestJob
		err = json.Unmarshal(jsonData, &deserializedJob)
		require.NoError(t, err)

		assert.Equal(t, job.ID, deserializedJob.ID)
		assert.Equal(t, job.Status, deserializedJob.Status)
		assert.Equal(t, job.Priority, deserializedJob.Priority)
	})

	t.Run("AI_Request_Type_Compatibility", func(t *testing.T) {
		aiRequest := suite.testData.AIRequests[0]

		jsonData, err := json.Marshal(aiRequest)
		require.NoError(t, err)

		var deserializedRequest TestAIRequest
		err = json.Unmarshal(jsonData, &deserializedRequest)
		require.NoError(t, err)

		assert.Equal(t, aiRequest.Description, deserializedRequest.Description)
		assert.Equal(t, aiRequest.UrgencyLevel, deserializedRequest.UrgencyLevel)
	})
}

// 📡 Real-Time Communication Tests
func (suite *HVACRemixCompatibilityTest) testRealTimeCommunication(t *testing.T) {
	t.Run("WebSocket_Connection", func(t *testing.T) {
		// Convert HTTP URL to WebSocket URL
		wsURL := strings.Replace(suite.server.URL, "http://", "ws://", 1) + "/ws/realtime"

		// Connect to WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Test sending a message
		testMessage := map[string]interface{}{
			"type": "job_update",
			"data": map[string]interface{}{
				"jobId": "test-job-1",
				"status": "in_progress",
			},
		}

		err = conn.WriteJSON(testMessage)
		require.NoError(t, err)

		// Test receiving a response
		var response map[string]interface{}
		err = conn.ReadJSON(&response)
		require.NoError(t, err)

		assert.Contains(t, response, "type")
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "timestamp")
	})

	t.Run("Real_Time_Updates", func(t *testing.T) {
		// Test real-time job updates
		wsURL := strings.Replace(suite.server.URL, "http://", "ws://", 1) + "/ws/realtime"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Subscribe to job updates
		subscribeMsg := map[string]interface{}{
			"type": "subscribe",
			"channel": "job_updates",
		}

		err = conn.WriteJSON(subscribeMsg)
		require.NoError(t, err)

		// Simulate job update
		updateMsg := map[string]interface{}{
			"type": "job_status_change",
			"data": map[string]interface{}{
				"jobId": "test-job-1",
				"oldStatus": "pending",
				"newStatus": "in_progress",
				"timestamp": time.Now(),
			},
		}

		err = conn.WriteJSON(updateMsg)
		require.NoError(t, err)

		// Verify update received
		var response map[string]interface{}
		err = conn.ReadJSON(&response)
		require.NoError(t, err)

		// The mock server echoes back the message, so we expect the update message
		assert.Contains(t, []string{"job_status_change", "subscribe"}, response["type"])

		if response["type"] == "job_status_change" {
			data, ok := response["data"].(map[string]interface{})
			if ok {
				assert.Equal(t, "test-job-1", data["jobId"])
			}
		}
	})
}

// 🤖 AI Service Integration Tests
func (suite *HVACRemixCompatibilityTest) testAIServiceIntegration(t *testing.T) {
	baseURL := suite.server.URL

	t.Run("AI_Analysis_Integration", func(t *testing.T) {
		aiRequest := TestAIRequest{
			Description: "Customer reports heating system not working, temperature dropping rapidly",
			UrgencyLevel: "high",
			Context: map[string]interface{}{
				"customerType": "residential",
				"systemAge": 5,
				"lastService": "2023-10-15",
			},
		}

		requestBody, _ := json.Marshal(aiRequest)
		resp, err := http.Post(
			baseURL+"/api/v1/ai/analyze",
			"application/json",
			strings.NewReader(string(requestBody)),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		var aiResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&aiResponse)
		require.NoError(t, err)

		// Validate AI response matches hvac-remix expectations
		assert.Contains(t, aiResponse, "analysis")
		assert.Contains(t, aiResponse, "category")
		assert.Contains(t, aiResponse, "urgency")
		assert.Contains(t, aiResponse, "recommendations")
		assert.Contains(t, aiResponse, "estimatedCost")
		assert.Contains(t, aiResponse, "requiredParts")

		category := aiResponse["category"].(string)
		assert.Contains(t, []string{"heating", "cooling", "ventilation", "electrical", "general"}, category)

		urgency := aiResponse["urgency"].(string)
		assert.Contains(t, []string{"low", "medium", "high"}, urgency)
	})

	t.Run("AI_Chat_Integration", func(t *testing.T) {
		chatRequest := map[string]interface{}{
			"message": "What should I do if my HVAC system is making strange noises?",
			"context": []string{"residential", "heating", "maintenance"},
		}

		requestBody, _ := json.Marshal(chatRequest)
		resp, err := http.Post(
			baseURL+"/api/v1/ai/chat",
			"application/json",
			strings.NewReader(string(requestBody)),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		var chatResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&chatResponse)
		require.NoError(t, err)

		assert.Contains(t, chatResponse, "response")
		assert.Contains(t, chatResponse, "confidence")
		assert.Contains(t, chatResponse, "suggestions")

		response := chatResponse["response"].(string)
		assert.NotEmpty(t, response)
		assert.Greater(t, len(response), 50) // Ensure meaningful response
	})
}

// ⚠️ Error Handling Compatibility Tests
func (suite *HVACRemixCompatibilityTest) testErrorHandlingCompatibility(t *testing.T) {
	baseURL := suite.server.URL

	t.Run("Invalid_Request_Handling", func(t *testing.T) {
		// Test invalid JSON
		resp, err := http.Post(
			baseURL+"/api/v1/ai/analyze",
			"application/json",
			strings.NewReader("invalid json"),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusBadRequest, resp.StatusCode)

		var errorResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&errorResponse)
		require.NoError(t, err)

		assert.Contains(t, errorResponse, "error")
		assert.Contains(t, errorResponse, "message")
		assert.Contains(t, errorResponse, "code")
	})

	t.Run("Not_Found_Handling", func(t *testing.T) {
		resp, err := http.Get(baseURL + "/api/v1/customers/nonexistent-id")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)

		var errorResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&errorResponse)
		require.NoError(t, err)

		assert.Contains(t, errorResponse, "error")
		assert.Equal(t, "CUSTOMER_NOT_FOUND", errorResponse["code"])
	})
}

// ⚡ Performance Requirements Tests
func (suite *HVACRemixCompatibilityTest) testPerformanceRequirements(t *testing.T) {
	baseURL := suite.server.URL

	t.Run("Response_Time_Requirements", func(t *testing.T) {
		start := time.Now()

		resp, err := http.Get(baseURL + "/api/v1/customers")
		require.NoError(t, err)
		defer resp.Body.Close()

		duration := time.Since(start)

		// hvac-remix expects responses under 500ms for list operations
		assert.Less(t, duration, 500*time.Millisecond)
	})

	t.Run("AI_Analysis_Performance", func(t *testing.T) {
		aiRequest := suite.testData.AIRequests[0]
		requestBody, _ := json.Marshal(aiRequest)

		start := time.Now()

		resp, err := http.Post(
			baseURL+"/api/v1/ai/analyze",
			"application/json",
			strings.NewReader(string(requestBody)),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		duration := time.Since(start)

		// AI analysis should complete within 3 seconds
		assert.Less(t, duration, 3*time.Second)
	})

	t.Run("Concurrent_Request_Handling", func(t *testing.T) {
		const numRequests = 10
		results := make(chan error, numRequests)

		for i := 0; i < numRequests; i++ {
			go func() {
				resp, err := http.Get(baseURL + "/api/v1/system/health")
				if err != nil {
					results <- err
					return
				}
				defer resp.Body.Close()

				if resp.StatusCode != http.StatusOK {
					results <- fmt.Errorf("unexpected status: %d", resp.StatusCode)
					return
				}

				results <- nil
			}()
		}

		// Wait for all requests to complete
		for i := 0; i < numRequests; i++ {
			err := <-results
			assert.NoError(t, err)
		}
	})
}

// 🔧 HTTP Handlers for Test Server
func (suite *HVACRemixCompatibilityTest) handleCustomers(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	switch r.Method {
	case "GET":
		json.NewEncoder(w).Encode(suite.testData.Customers)
	case "POST":
		var customer TestCustomer
		if err := json.NewDecoder(r.Body).Decode(&customer); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		customer.ID = fmt.Sprintf("cust-%d", time.Now().Unix())
		suite.testData.Customers = append(suite.testData.Customers, customer)
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(customer)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

func (suite *HVACRemixCompatibilityTest) handleCustomerByID(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	id := strings.TrimPrefix(r.URL.Path, "/api/v1/customers/")

	for _, customer := range suite.testData.Customers {
		if customer.ID == id {
			json.NewEncoder(w).Encode(customer)
			return
		}
	}

	errorResponse := map[string]interface{}{
		"error": "Customer not found",
		"code": "CUSTOMER_NOT_FOUND",
		"message": fmt.Sprintf("Customer with ID %s not found", id),
	}
	w.WriteHeader(http.StatusNotFound)
	json.NewEncoder(w).Encode(errorResponse)
}

func (suite *HVACRemixCompatibilityTest) handleJobs(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	switch r.Method {
	case "GET":
		json.NewEncoder(w).Encode(suite.testData.Jobs)
	case "POST":
		var job TestJob
		if err := json.NewDecoder(r.Body).Decode(&job); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		job.ID = fmt.Sprintf("job-%d", time.Now().Unix())
		suite.testData.Jobs = append(suite.testData.Jobs, job)
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(job)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

func (suite *HVACRemixCompatibilityTest) handleJobByID(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	id := strings.TrimPrefix(r.URL.Path, "/api/v1/jobs/")

	for _, job := range suite.testData.Jobs {
		if job.ID == id {
			json.NewEncoder(w).Encode(job)
			return
		}
	}

	http.Error(w, "Job not found", http.StatusNotFound)
}

func (suite *HVACRemixCompatibilityTest) handleAIAnalyze(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var request TestAIRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		errorResponse := map[string]interface{}{
			"error": "Invalid JSON",
			"code": "INVALID_REQUEST",
			"message": "Request body must be valid JSON",
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(errorResponse)
		return
	}

	// Mock AI analysis response
	response := map[string]interface{}{
		"analysis": "Based on the description, this appears to be a heating system malfunction requiring immediate attention.",
		"confidence": 0.85,
		"category": "heating",
		"urgency": request.UrgencyLevel,
		"recommendations": []string{
			"Schedule emergency technician visit",
			"Check thermostat settings",
			"Inspect heating unit for obvious issues",
		},
		"estimatedCost": map[string]interface{}{
			"min": 150,
			"max": 500,
			"currency": "USD",
		},
		"requiredParts": []string{
			"Thermostat",
			"Heating element",
		},
		"estimatedDuration": "2-4 hours",
		"processingTime": 250,
	}

	json.NewEncoder(w).Encode(response)
}

func (suite *HVACRemixCompatibilityTest) handleAIChat(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var request map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Mock AI chat response
	response := map[string]interface{}{
		"response": "Strange noises from your HVAC system could indicate several issues. First, check if the air filter needs replacement. If the noise persists, it's recommended to schedule a professional inspection as it could be a sign of mechanical problems that require immediate attention.",
		"confidence": 0.92,
		"suggestions": []string{
			"Check and replace air filter",
			"Schedule professional inspection",
			"Monitor system performance",
		},
		"modelUsed": "gemma-3-4b",
		"tokensUsed": 156,
	}

	json.NewEncoder(w).Encode(response)
}

func (suite *HVACRemixCompatibilityTest) handleEmails(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(suite.testData.Emails)
}

func (suite *HVACRemixCompatibilityTest) handleEmailProcess(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Mock email processing response
	response := map[string]interface{}{
		"success": true,
		"triageResult": map[string]interface{}{
			"action": "respond",
			"priority": "high",
			"category": "service_request",
			"confidence": 0.88,
		},
		"processingTime": "1.2s",
	}

	json.NewEncoder(w).Encode(response)
}

func (suite *HVACRemixCompatibilityTest) handleSystemHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	health := map[string]interface{}{
		"status": "healthy",
		"services": map[string]interface{}{
			"database": map[string]interface{}{
				"status": "up",
				"latency": 15,
				"lastCheck": time.Now(),
			},
			"ai": map[string]interface{}{
				"status": "up",
				"latency": 250,
				"lastCheck": time.Now(),
			},
			"email": map[string]interface{}{
				"status": "up",
				"latency": 45,
				"lastCheck": time.Now(),
			},
		},
		"memory": map[string]interface{}{
			"used": 512,
			"total": 2048,
			"percentage": 25,
		},
		"cpu": map[string]interface{}{
			"usage": 15.5,
			"cores": 4,
		},
	}

	json.NewEncoder(w).Encode(health)
}

func (suite *HVACRemixCompatibilityTest) handleSystemMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	metrics := map[string]interface{}{
		"requests": map[string]interface{}{
			"total": 1250,
			"successful": 1198,
			"failed": 52,
			"avgResponseTime": 185,
		},
		"jobs": map[string]interface{}{
			"total": 45,
			"pending": 8,
			"inProgress": 12,
			"completed": 25,
		},
		"ai": map[string]interface{}{
			"analysisRequests": 156,
			"avgConfidence": 0.87,
			"avgProcessingTime": 245,
		},
	}

	json.NewEncoder(w).Encode(metrics)
}

func (suite *HVACRemixCompatibilityTest) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := suite.wsUpgrader.Upgrade(w, r, nil)
	if err != nil {
		return
	}
	defer conn.Close()

	for {
		var message map[string]interface{}
		err := conn.ReadJSON(&message)
		if err != nil {
			break
		}

		// Echo message back with timestamp
		response := map[string]interface{}{
			"type": message["type"],
			"data": message["data"],
			"timestamp": time.Now(),
			"echo": true,
		}

		err = conn.WriteJSON(response)
		if err != nil {
			break
		}
	}
}

// 📊 Test Data Generation
func generateTestData() *TestDataSet {
	now := time.Now()
	scheduledDate := now.Add(24 * time.Hour)

	return &TestDataSet{
		Customers: []TestCustomer{
			{
				ID: "cust-1",
				Name: "John Smith",
				Email: "<EMAIL>",
				Phone: "******-0123",
				Address: "123 Main St, Anytown, ST 12345",
				Type: "residential",
			},
			{
				ID: "cust-2",
				Name: "ABC Corporation",
				Email: "<EMAIL>",
				Phone: "******-0456",
				Address: "456 Business Ave, Corporate City, ST 67890",
				Type: "commercial",
			},
		},
		Jobs: []TestJob{
			{
				ID: "job-1",
				CustomerID: "cust-1",
				Title: "Heating System Repair",
				Description: "Customer reports heating system not working properly",
				Status: "pending",
				Priority: "high",
				Type: "repair",
				ScheduledDate: &scheduledDate,
			},
			{
				ID: "job-2",
				CustomerID: "cust-2",
				Title: "HVAC Maintenance",
				Description: "Quarterly maintenance for commercial HVAC system",
				Status: "scheduled",
				Priority: "medium",
				Type: "maintenance",
				ScheduledDate: &scheduledDate,
			},
		},
		AIRequests: []TestAIRequest{
			{
				Description: "Customer reports heating system not working, temperature dropping rapidly",
				UrgencyLevel: "high",
				Context: map[string]interface{}{
					"customerType": "residential",
					"systemAge": 5,
					"lastService": "2023-10-15",
				},
			},
		},
		Emails: []TestEmail{
			{
				ID: "email-1",
				From: "<EMAIL>",
				To: []string{"<EMAIL>"},
				Subject: "Urgent: Heating System Not Working",
				Body: "Hello, my heating system stopped working this morning and the temperature in my house is dropping rapidly. Please help!",
			},
		},
	}
}
